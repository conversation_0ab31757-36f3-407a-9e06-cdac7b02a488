# Feature Flags

This document describes the available feature flags in the ADT Tech Backend application.

## Overview

Feature flags allow you to enable or disable specific functionality without changing code. This is useful for:
- Development environments where you want to disable certain features
- Testing scenarios
- Gradual rollouts
- Troubleshooting

## Available Feature Flags

### Reports (`ENABLE_REPORTS`)

**Default:** `true`

Controls whether report functionality is enabled, including:
- Report generation schedules
- Report subscription processing
- Monthly ADT completion emails
- Job queues for report processing
- Report-related API endpoints

**Usage:**
```bash
# Disable reports
ENABLE_REPORTS=false

# Enable reports (default)
ENABLE_REPORTS=true
```

### Alerts (`ENABLE_ALERTS`)

**Default:** `true`

Controls whether alert functionality is enabled, including:
- Alert schedules
- Alert processing
- Alert notifications

**Usage:**
```bash
# Disable alerts
ENABLE_ALERTS=false

# Enable alerts (default)
ENABLE_ALERTS=true
```

## Configuration

### Environment Variables

Set these environment variables in your `.env` file or system environment:

```bash
# Feature flags
ENABLE_REPORTS=true
ENABLE_ALERTS=true
```

### Using the Feature Flags Helper

The application provides a centralized feature flags helper at `config/features.js`:

```javascript
const { 
    isReportsEnabled, 
    isAlertsEnabled, 
    getFeatureFlags,
    logFeatureFlags 
} = require('./config/features');

// Check individual flags
if (isReportsEnabled()) {
    // Reports are enabled
}

if (isAlertsEnabled()) {
    // Alerts are enabled
}

// Get all flags
const flags = getFeatureFlags();
console.log(flags); // { reports: true, alerts: true, ... }

// Log all flags status
logFeatureFlags();
```

## Impact of Disabling Features

### Disabling Reports (`ENABLE_REPORTS=false`)

When reports are disabled:
- Report generation schedules will not run
- Job queues will not be initialized
- Monthly ADT completion emails will not be sent
- Report subscription processing will be skipped
- Job monitoring endpoints will return 503 status

### Disabling Alerts (`ENABLE_ALERTS=false`)

When alerts are disabled:
- Alert schedules will not run
- Alert processing will be skipped
- Alert notifications will not be sent

## Development

### Memory Leak Prevention

The application includes safeguards to prevent memory leaks from event listeners:
- Process listeners are only registered once
- Cleanup functions are available for development
- Maximum listeners limit is increased to prevent warnings

### Development Scripts

Use the clean development script to prevent memory leaks during development:

```bash
# Regular development (may accumulate listeners on restart)
npm run dev

# Clean development (proper cleanup)
npm run dev:clean
```

## Production Considerations

In production environments:
- Both flags default to `true` (enabled)
- Feature flags are logged during startup
- Redis connection is required when reports are enabled
- Job queues and monitoring are only available when reports are enabled

## Troubleshooting

### Common Issues

1. **Job queue not available**: Ensure `ENABLE_REPORTS=true`
2. **Reports not generating**: Check that `ENABLE_REPORTS=true` and Redis is connected
3. **Memory leak warnings**: Use `npm run dev:clean` for development

### Debugging

Enable feature flag logging by checking the console output during server startup:

```
=== Feature Flags Status ===
Reports: ENABLED
Alerts: ENABLED
Job Queues: ENABLED
Schedules: ENABLED
============================
```

## Adding New Feature Flags

To add a new feature flag:

1. Add the environment variable check in `config/features.js`
2. Update the production configuration in `config/production.js`
3. Add conditional logic where the feature is used
4. Update this documentation

Example:
```javascript
// In config/features.js
const isNewFeatureEnabled = () => {
    return process.env.ENABLE_NEW_FEATURE !== 'false';
};

// In your code
if (isNewFeatureEnabled()) {
    // Feature code here
}
```
