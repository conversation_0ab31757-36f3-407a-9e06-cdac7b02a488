const mongoose = require("mongoose");
const { PAGE_TYPE, CUSTOM_TAB_TYPE } = require("../types/common.type");
const { Schema, Types } = mongoose;

const customTabSchema = new Schema(
  {
    title: { type: String, required: false },
    description: { type: String, required: false },
    accountId: { type: Types.ObjectId, ref: "account" },
    userId: { type: Types.ObjectId, ref: "user" },
    filters: { type: [Schema.Types.Mixed], required: false },        
    slug: { type: String, required: false },
    type: {
      type: String,
      enum: [CUSTOM_TAB_TYPE.customTab, CUSTOM_TAB_TYPE.combineTab],
      default: CUSTOM_TAB_TYPE.customTab
    },
    page: {
      type: String,
      enum: [PAGE_TYPE.ADMISSION, PAGE_TYPE.HOSPITAL, PAGE_TYPE.OVERALL, PAGE_TYPE.DECEASED, PAGE_TYPE.COMMUNITY_TRANSFER],
      default: PAGE_TYPE.HOSPITAL
    },
    isPublic: { type: <PERSON>olean, default: false }, // For superadmin auto-visibility
    isUniversal: { type: Boolean, default: false }, // For universal access to all account users
    shareCount: { type: Number, default: 0 }, // Performance optimization
  },
  { timestamps: true }
);

mongoose.model("customTab", customTabSchema);
