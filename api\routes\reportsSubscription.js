const { authenticate } = require("../middleware/authenticate");
const { getSubscriptionByPage, createSubscription, deleteSubscription, updateSubscription } = require("../helpers/reportsSubscription");

const route = require("express").Router();

// Create new subscription
route.post("/", authenticate, async (req, res) => {
  try {
    let subscription = await createSubscription(req);
    res.send(subscription);
  } catch (error) {
    res.status(500).send(error);
  }
});

route.get("/all", authenticate, async (req, res) => {
  try {
    const subscriptions = await getSubscriptionByPage(req);
    res.send(subscriptions);
  } catch (error) {
    res.status(500).send(error);
  }
});

route.delete("/:id", authenticate, async (req, res) => {
  try {
    const subscription = await deleteSubscription(req.params.id);
    res.send(subscription);
  } catch (error) {
    res.status(500).send(error);
  }
});

route.put("/:id", authenticate, async (req, res) => {
  try {
    const subscription = await updateSubscription(req.params.id, req.body);
    res.send(subscription);
  } catch (error) {
    res.status(500).send(error);
  }
});

module.exports = route;
