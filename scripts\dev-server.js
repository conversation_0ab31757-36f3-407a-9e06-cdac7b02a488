/**
 * Development server script with proper cleanup
 * This script helps prevent memory leaks during development
 */

const { spawn } = require('child_process');
const path = require('path');

let serverProcess = null;

const startServer = () => {
    console.log('Starting development server...');
    
    serverProcess = spawn('node', ['src/server.js'], {
        cwd: path.join(__dirname, '..'),
        stdio: 'inherit',
        env: { ...process.env, NODE_ENV: 'development' }
    });

    serverProcess.on('error', (error) => {
        console.error('Server process error:', error);
    });

    serverProcess.on('exit', (code, signal) => {
        console.log(`Server process exited with code ${code} and signal ${signal}`);
        serverProcess = null;
    });
};

const stopServer = () => {
    if (serverProcess) {
        console.log('Stopping server...');
        serverProcess.kill('SIGTERM');
        
        // Force kill after 5 seconds if graceful shutdown fails
        setTimeout(() => {
            if (serverProcess) {
                console.log('Force killing server...');
                serverProcess.kill('SIGKILL');
            }
        }, 5000);
    }
};

const restartServer = () => {
    console.log('Restarting server...');
    stopServer();
    
    // Wait a bit before restarting to ensure cleanup
    setTimeout(() => {
        startServer();
    }, 1000);
};

// Handle process signals
process.on('SIGINT', () => {
    console.log('\nReceived SIGINT, shutting down gracefully...');
    stopServer();
    process.exit(0);
});

process.on('SIGTERM', () => {
    console.log('\nReceived SIGTERM, shutting down gracefully...');
    stopServer();
    process.exit(0);
});

// Start the server
startServer();

// Export functions for potential use
module.exports = { startServer, stopServer, restartServer };
