const httpStatus = require("http-status");
const ApiError = require("../utilis/ApiError");
const { roleRights } = require("../../config/role");
const config = require("../../config");
const mongoose = require("mongoose");
const User = mongoose.model("user");

const authWithRole =
	(...requiredRights) =>
	async (req, res, next) => {
		try {
			const incomingAccessToken = req.header("x-auth");
			const refreshToken = req.cookies[config.refreshCookieName];
			const { accessToken, userQuery } = await User.findByToken(req, res, incomingAccessToken, refreshToken);
			const user = await userQuery.populate("role");

			if (!user) {
				throw new Error("User not found");
			}

			if (requiredRights.length) {
				let roleName = user.role.name ? user.role.name.toLowerCase() : null;				
				const userRights = roleRights.get(roleName);
				const hasRequiredRights = requiredRights.every((requiredRight) => userRights.includes(requiredRight));
				if (!hasRequiredRights && req.params.userId !== user.id) {
					return reject(new ApiError(httpStatus.FORBIDDEN, "Forbidden"));
				}
			}
			req.user = user;
			req.token = accessToken;
			next();
		} catch (e) {
			res.status(401).send({ status: 401 });
		}
	};

module.exports = authWithRole;
