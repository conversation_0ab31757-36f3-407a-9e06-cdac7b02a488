import React from 'react';
import { FormControl, InputLabel, Select, MenuItem, FormHelperText } from '@mui/material';

const LabeledSelect = ({
    label,
    value,
    onChange,
    error = false,
    helperText = '',
    options = [],
    multiple = false,
    children,
    disabled = false,
    ...props
}) => (
    <FormControl fullWidth error={error} size={props.size || 'medium'} sx={props.sx}>
        {label && <InputLabel>{label}</InputLabel>}
        <Select
            value={value}
            label={label}
            onChange={onChange}
            multiple={multiple}
            disabled={disabled}
            {...props}
        >
            {children
                ? children
                : options.map(opt => (
                    <MenuItem key={opt.value} value={opt.value}>
                        {opt.label}
                    </MenuItem>
                ))}
        </Select>
        {helperText && <FormHelperText>{helperText}</FormHelperText>}
    </FormControl>
);

export default LabeledSelect; 