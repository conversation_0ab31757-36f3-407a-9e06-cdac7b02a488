const express = require("express");
const router = express.Router();
const { Queue } = require("bullmq");
const { Worker } = require("bullmq");
const { getAllUsersCount } = require("../helpers/user");
const configModule = require("../../config/redis-connection");
const { sendTestEmail } = require("../../sendEmail");
const puppeteer = require('puppeteer');
const authWithRole = require("../middleware/auth-with-role");

const myQueue = new Queue("healthQueue", {
    connection: configModule.REDIS_CONNECTOR,
});

async function addJobs(req) {
    await myQueue.add("healthQueueJob", req, configModule.REDIS_DEFAULT_REMOVE_CONFIG);
}

const worker = new Worker(
    "healthQueue",
    async (job) => {
        await sendTestEmail("All schedular setting good", job.data.email, job.data?.data)
        return job;
    },
    {
        connection: configModule.REDIS_CONNECTOR,
        autorun: true,
    }
);


worker.on("active", (job) => {
    console.log(`Processing send report to user with id ${job.id}`);
});

worker.on("completed", (job, returnValue) => {
    console.log(`Completed sent report to user with id ${job.id}`);
});

worker.on("error", (failedReason) => {
    // console.log(failedReason, 'failedReason');
    // console.error(`Job encountered an error`, failedReason);
});

router.get("/", async (req, res, next) => {
    try {
        const response = await getAllUsersCount();
        if (response) {
            if (req.query && req.query.email) {
                const resPdf = await generatePDF()
                await addJobs({ ...req.query, data: resPdf });
            }
            res.send("All good!");
        }
    } catch (err) {        
        next(err)
    }
});

router.get("/remove-jobs", authWithRole('manageAccount'), async (req, res, next) => {
    const reportQueue = new Queue('reportSchedular', {
        connection: configModule.REDIS_CONNECTOR,
    });
    await reportQueue.obliterate();
    return res.send("Removed successful")
});

const generatePDF = async () => {
    const browser = await puppeteer.launch(
        {
            headless: 'new',
            args: [
                "--disable-gpu",
                "--disable-dev-shm-usage",
                "--disable-setuid-sandbox",
                "--no-sandbox"
            ]
        }
    );
    let htmlContent = `<html><h1>This is test</h1></html>`
    const page = await browser.newPage();
    await page.setContent(htmlContent);
    //await page.pdf({ path: outputPath, format: "A4" });;

    const buffer = await page.pdf({ format: "A4" });
    const base64 = buffer.toString('base64');
    await browser.close();
    return base64;
}

module.exports = router;