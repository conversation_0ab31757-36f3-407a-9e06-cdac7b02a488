
import axios from "../../axios";
import { useEffect, useState } from "react";

const ValidationCell = ({ id }) => {
  const [validation, setValidation] = useState(null);

  const getValidation = async () => {
    let re = await axios.get(`/api/validation/${id}`);
    setValidation(re.data);
  };

  useEffect(() => {
    if (!id) return;
    getValidation();
    // eslint-disable-next-line
  }, []);

  return <span>{validation?.label}</span>;
};
export default ValidationCell;
