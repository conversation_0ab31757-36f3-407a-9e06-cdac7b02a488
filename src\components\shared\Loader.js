import * as React from "react";
import { Box } from "@mui/system";
import { CircularProgress } from "@mui/material";

export default function Loader({ children, loading, style = {}, className }) {
	if (loading) {
		return (
			<Box sx={{ display: "flex", justifyContent: "center", alignItems: "center", ...style }} className={className}>
				<CircularProgress />
			</Box>
		);
	}
	return children || null;
}
