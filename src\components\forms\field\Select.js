import PropTypes from "prop-types";

const Select = (props) => {
  const {
    children,
    className,
    options,
    label,
    placeholder,
    ...restProps
  } = props;

  return (
    <>
      {label && (<label className={`ffmr fs12`}>Patient Suffix</label>)}
      <select
        {...restProps}
        className={`ffml fs16`}
      >
        {placeholder && <option value="">{placeholder}</option>}

        {options && options.length > 0 && (
          options.map((item) => (
            <option value={item.value}>{item.label}</option>
          ))
        )}

      </select>
    </>
  );
};

Select.propTypes = {

  options: PropTypes.array,

  onClick: PropTypes.func,

  label: PropTypes.string,

  placeholder: PropTypes.string,
};

export default Select;
