import { useCallback } from "react";
import { Toolbar, Grid } from "@mui/material";
import DashboardHeader from "../header/DashboardHeader";
import DateFilterComponent from "../filter/DateFilterComponent";
import { setFilterDateRange } from "../../../store/reducers/quick-glace.slice";
import { useDispatch, useSelector } from "react-redux";


const QuickGlaceHeader = () => {
    const dispatch = useDispatch();
    const { filter } = useSelector((state) => state.quickGlace);
    const onChangeFilter = useCallback((value) => {
        dispatch(setFilterDateRange(value));
    }, [dispatch]);

    return (
        <DashboardHeader>
            <Toolbar sx={{ height: 91 }}>
                <Grid
                    container
                    direction={"row"}
                    spacing={2}
                    justifyContent="flex-end"
                    alignItems="center"
                >
                    <Grid item xs={6}>
                        <DateFilterComponent
                            onChangeFilter={onChangeFilter}
                            filter={filter}
                        />
                    </Grid>
                </Grid>
            </Toolbar>
        </DashboardHeader>
    );
};

export default QuickGlaceHeader;
