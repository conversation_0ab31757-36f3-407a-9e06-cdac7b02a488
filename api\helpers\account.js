const mongoose = require("mongoose");
const { questionTypes, questionTypesHospital } = require("../utilis/common");
const { setQuestionsByAccount } = require("./question");
const { ACCOUNT_PERCENTAGE_BY, PAGE_TYPE } = require("../../types/common.type");
const Account = mongoose.model("account");

// Create new account
const createAccount = async (data) => {
	const existingAccount = await Account.findOne({ name: data.name });

	if (existingAccount) {
		throw new Error("Account name already taken.");
	}
	let questionsData = questionTypes;
	if (data && !data.dashboardAccess) {
		data.dashboardAccess = [
			PAGE_TYPE.ADMISSION,
			PAGE_TYPE.HOSPITAL,
			PAGE_TYPE.COMMUNITY_TRANSFER,
			PAGE_TYPE.DECEASED,
			PAGE_TYPE.OVERALL,
		];
	} else {
		questionsData = questionTypesHospital;
	}
	if (data && !data.percentageBy) {
		data.percentageBy = ACCOUNT_PERCENTAGE_BY.CENSUS;
	}
	const localAccount = new Account(data);
	const saved = await localAccount.save().catch((e) => e);
	if (saved) {
		questionTypes.forEach(async (question) => {
			await setQuestionsByAccount(question.type, question?.transferType, saved?._id, question?.filterValue);
		});
	}
	return saved;
};

const getAccount = async (id, user) => {
	if (user.role.slug === "super") {
		localAccount = await Account.find();
	} else {
		user.populate("accounts");
		const accounts = user?.accounts || [];
		const accountIds = [];
		if (accounts.length > 0) {
			// await accounts.forEach(async (account) => {
			//   await Account.findByIdAndUpdate(account._id, {
			//     dashboardAccess: [PAGE_TYPE.ADMISSION, PAGE_TYPE.HOSPITAL, PAGE_TYPE.COMMUNITY_TRANSFER, PAGE_TYPE.DECEASED, PAGE_TYPE.OVERALL],
			//     percentageBy: ACCOUNT_PERCENTAGE_BY.CENSUS,
			//   });
			// });
			accounts.map((item) => {
				if (item.access) {
					accountIds.push(item.accountId);
				}
			});
		}
		localAccount = await Account.find({ _id: { $in: accountIds } });
	}
	return localAccount;
};

const getAllAccounts = async () => {
	return await Account.find();
};

const updateAccount = async (id, body) => {
	const existingAccount = await Account.findOne({ _id: { $ne: id }, name: body.name });

	if (existingAccount) {
		throw new Error("Account name already taken.");
	}

	await Account.findByIdAndUpdate(mongoose.Types.ObjectId(id), body);
	const account = await Account.findOne({ _id: id });

	return account;
};

module.exports = { createAccount, getAccount, getAllAccounts, updateAccount };
