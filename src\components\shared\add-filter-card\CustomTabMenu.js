import React, { useState } from 'react';
import {
    IconButton,
    Tooltip,
    Popover,
    Typography,
    Button,
    Box,
    Menu,
    MenuItem,
    ListItemIcon,
    ListItemText
} from '@mui/material';
import DeleteOutlineOutlinedIcon from '@mui/icons-material/DeleteOutlineOutlined';
import EditOutlinedIcon from '@mui/icons-material/EditOutlined';
import ShareIcon from '@mui/icons-material/Share';
import MoreVertIcon from '@mui/icons-material/MoreVert';
import VisibilityOffIcon from '@mui/icons-material/VisibilityOff';
import VisibilityIcon from '@mui/icons-material/Visibility';
import { deleteCustomTabById } from '../../../services/custom-tab.service';
import { ADD_NOTIFICATION } from '../../../store/types';
import { useDispatch, useSelector } from 'react-redux';
import { setIsCustomTabAdded } from '../../../store/reducers/common.slice';
import ShareCustomTabDialog from './ShareCustomTabDialog';
import { AUTH_ROLES } from '../../../types/auth.type';
import { toggleSharedTabVisibility } from '../../../services/custom-tab-sharing.service';

const CustomTabMenu = ({ ele, onEdit, handleAddCustomCard, selectedCards }) => {
    const [menuAnchorEl, setMenuAnchorEl] = useState(null);
    const [popoverAnchorEl, setPopoverAnchorEl] = useState(null);
    const [shareDialogOpen, setShareDialogOpen] = useState(false);
    const [loading, setLoading] = useState(false);
    const dispatch = useDispatch();
    const isCustomTabAdded = useSelector((state) => state.common.isCustomTabAdded);
    const currentUser = useSelector((state) => state.auth);

    // Helper function to check if user has global delete permissions
    const hasGlobalDeletePermission = () => {
        const userRole = currentUser?.role?.slug;
        return userRole === AUTH_ROLES.SUPER || userRole === AUTH_ROLES.TOTAL;
    };

    if (!ele?.isCustomTab || !ele?._id) return null; // early return if not eligible

    const isCreator = currentUser?._id === ele?.userId;
    const isShared = ele?.isShared || false;
    const isHidden = ele?.isHidden || false;
    const shouldShowDeleteForEveryone = isCreator || hasGlobalDeletePermission();

    const handleMenuOpen = (event) => {
        event.stopPropagation();
        event.preventDefault();
        setMenuAnchorEl(event.currentTarget);
    };

    const handleMenuClose = (e) => {
        if (e) {
            e.preventDefault();
            e.stopPropagation();
        }
        setMenuAnchorEl(null);
    };

    const handleDeleteClick = (event) => {
        event.preventDefault();
        event.stopPropagation();
        handleMenuClose(event);
        setPopoverAnchorEl(event.currentTarget);
    };

    const handleShareClick = (event) => {
        event.preventDefault();
        event.stopPropagation();
        handleMenuClose(event);
        setShareDialogOpen(true);
    };

    const handlePopoverClose = (e) => {
        if (e) {
            e.preventDefault();
            e.stopPropagation();
        }
        setPopoverAnchorEl(null);
    };

    const handleShareDialogClose = () => {
        setShareDialogOpen(false);
    };

    const handleShareSuccess = (selectedUsers) => {
        // Optional callback for when share is successful
        // You can add any additional logic here if needed
    };

    const confirmDelete = async (event) => {
        if (event) {
            event.preventDefault();
            event.stopPropagation();
        }
        handlePopoverClose();
        setLoading(true);
        try {
            const res = await deleteCustomTabById(ele._id);
            dispatch({
                type: ADD_NOTIFICATION,
                payload: {
                    type: res?.success || res ? 'success' : 'error',
                    label: res?.message || (res ? 'Custom tab deleted successfully!' : 'Failed to delete custom tab'),
                    id: 'customTabDeleted',
                },
            });
            dispatch(setIsCustomTabAdded(!isCustomTabAdded));
        } catch (error) {
            dispatch({
                type: ADD_NOTIFICATION,
                payload: {
                    type: 'error',
                    label: 'Error deleting custom tab',
                    id: 'customTabDeleted',
                },
            });
        } finally {
            setLoading(false);
        }
    };

    const handleEditClick = (e) => {
        e.preventDefault();
        e.stopPropagation();
        handleMenuClose();
        if (onEdit) {
            onEdit(ele, { isCreator });
        }
    };

    const handleToggleVisibility = async (e) => {
        e.preventDefault();
        e.stopPropagation();
        handleMenuClose();

        try {
            const newHiddenState = !isHidden;

            // If hiding the tab and it's currently selected, remove it from the page
            if (newHiddenState && selectedCards && handleAddCustomCard && selectedCards.includes(ele.value)) {
                handleAddCustomCard(ele.value);
            }

            await toggleSharedTabVisibility(ele.shareId, newHiddenState);

            dispatch({
                type: ADD_NOTIFICATION,
                payload: {
                    type: 'success',
                    label: newHiddenState ? 'Tab hidden successfully' : 'Tab unhidden successfully',
                    id: 'tabVisibilityToggled',
                },
            });

            // Trigger refresh of the tab list
            dispatch(setIsCustomTabAdded(!isCustomTabAdded));
        } catch (error) {
            dispatch({
                type: ADD_NOTIFICATION,
                payload: {
                    type: 'error',
                    label: 'Failed to toggle tab visibility',
                    id: 'tabVisibilityError',
                },
            });
        }
    };

    return (
        <>
            <Tooltip title="More actions">
                <IconButton
                    className="more-actions-icon"
                    sx={{ opacity: 1, transition: 'opacity 0.3s' }}
                    onClick={handleMenuOpen}
                    size="medium"
                    disableFocusRipple
                    disableRipple
                >
                    <MoreVertIcon sx={{ color: "#fff" }} />
                </IconButton>
            </Tooltip>

            <Menu
                anchorEl={menuAnchorEl}
                open={Boolean(menuAnchorEl)}
                onClose={handleMenuClose}
                anchorOrigin={{
                    vertical: 'bottom',
                    horizontal: 'right',
                }}
                transformOrigin={{
                    vertical: 'top',
                    horizontal: 'right',
                }}
                onClick={(e) => e.stopPropagation()}
            >
                <MenuItem onClick={handleEditClick} dense>
                    <ListItemIcon>
                        <EditOutlinedIcon fontSize="small" />
                    </ListItemIcon>
                    <ListItemText primary={isCreator ? "Edit" : "View"} />
                </MenuItem>
                {isCreator && !isShared && (
                    <MenuItem onClick={handleShareClick} dense>
                        <ListItemIcon>
                            <ShareIcon fontSize="small" />
                        </ListItemIcon>
                        <ListItemText primary="Share" />
                    </MenuItem>
                )}

                {/* Hide/Unhide option for shared tabs */}
                {isShared && !isCreator && (
                    <MenuItem onClick={handleToggleVisibility} dense>
                        <ListItemIcon>
                            {isHidden ? <VisibilityIcon fontSize="small" /> : <VisibilityOffIcon fontSize="small" />}
                        </ListItemIcon>
                        <ListItemText primary={isHidden ? "Unhide" : "Hide"} />
                    </MenuItem>
                )}

                <MenuItem onClick={handleDeleteClick} dense>
                    <ListItemIcon>
                        <DeleteOutlineOutlinedIcon fontSize="small" color="error" />
                    </ListItemIcon>
                    <ListItemText primary={shouldShowDeleteForEveryone ? "Delete for everyone" : "Remove from my list"} />
                </MenuItem>
            </Menu>

            {/* Share Dialog Component */}
            <ShareCustomTabDialog
                open={shareDialogOpen}
                onClose={handleShareDialogClose}
                customTab={ele}
                onShareSuccess={handleShareSuccess}
            />

            <Popover
                open={Boolean(popoverAnchorEl)}
                anchorEl={popoverAnchorEl}
                onClose={handlePopoverClose}
                anchorOrigin={{
                    vertical: 'bottom',
                    horizontal: 'center',
                }}
                transformOrigin={{
                    vertical: 'top',
                    horizontal: 'center',
                }}
                onClick={(e) => e.stopPropagation()}
            >
                <Box sx={{ p: 2, display: 'flex', flexDirection: 'column', alignItems: 'center', gap: 1 }}>
                    <Typography>
                        {shouldShowDeleteForEveryone
                            ? "Are you sure you want to delete this tab?\nThis will remove it for all users who have access to it."
                            : "Are you sure you want to remove this tab from your list?"
                        }
                    </Typography>
                    <Box sx={{ display: 'flex', gap: 1, mt: 1 }}>
                        <Button
                            color="error"
                            variant="contained"
                            size="small"
                            onClick={confirmDelete}
                            disabled={loading}
                        >
                            {shouldShowDeleteForEveryone ? "Yes, Delete for everyone" : "Yes, Remove"}
                        </Button>
                        <Button
                            variant="outlined"
                            size="small"
                            onClick={handlePopoverClose}
                            disabled={loading}
                        >
                            Cancel
                        </Button>
                    </Box>
                </Box>
            </Popover>
        </>
    );
};

export default CustomTabMenu;
