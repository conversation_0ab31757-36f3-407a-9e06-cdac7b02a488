import { useEffect, useState } from "react";
import {
    <PERSON><PERSON>, DialogTitle, DialogContent,
    Button, Box, MenuItem,
    Typography,
    Paper, Divider,
    IconButton,
    FormControl,
    InputLabel,
    Select
} from "@mui/material";
import DeleteIcon from '@mui/icons-material/Delete';
import { getAllCardForCustomTab } from "../../../services/hospital.service";
import { DEFAULT_CARD_FILTER } from "../../../store/reducers/hospital.slice";
import { DEFAULT_CARD_FILTER as DEFAULT_CARD_FILTER_ADMISSION } from "../../../store/reducers/admission.slice";
import { updateFilterListData } from "../../../utilis/hospital-common";
import { updateFilterListData as updateFilterListDataCommunity } from "../../../utilis/community-common";
import { updateFilterListData as updateFilterListDataDeceased } from "../../../utilis/deceased-common";
import { updateFilterListData as updateFilterListDataOverall } from "../../../utilis/overall-common";
import { DEFAULT_COMMUNITY_DATA } from "../../../store/reducers/community-transfer.slice";
import { getAllCardForCommunity } from "../../../services/community-transfer.service";
import { getAllCardForDeceased } from "../../../services/deceased.service";
import { DEFAULT_DECEASED_DATA } from "../../../store/reducers/deceased.slice";
import { getAllCardForAdmissions } from "../../../services/admission.service";
import { updateFilterListDataBoth } from "../../../utilis/admission-common";
import { HOSPITAL_CARDS_LABELS, HOSPITAL_CARDS_TYPE } from "../../../types/hospital.type";
import { ADMISSION_CARDS_LABELS, ADMISSION_CARDS_TYPE } from "../../../types/admission.type";
import { CO_TRANSFER_CARDS_TYPE, COMMUNITY_CARD_LABELS } from "../../../types/community-transfer.type";
import { DECEASED_CARDS_LABELS, DECEASED_CARDS_TYPE } from "../../../types/deceased.type";
import { createCustomTab, getDynamicDataTabById } from "../../../services/custom-tab.service";
import { useDispatch, useSelector } from "react-redux";
import { ADD_NOTIFICATION } from "../../../store/types";
import { getQuestionsData } from "../../../services/dynamic-data-tab.service";
import { ADT_SUB_TYPES, CUSTOM_TAB_TYPE } from "../../../types/common.type";
import { setIsCustomTabAdded } from "../../../store/reducers/common.slice";
import moment from "moment";
import ErrorPopover from '../custom-popover/ErrorPopover';
import LabeledSelect from './LabeledSelect';
import LabeledTextField from './LabeledTextField';
import LabeledTextarea from './LabeledTextarea';
import LoadingBlock from './LoadingBlock';
import DialogActionsRow from './DialogActionsRow';
import ItemAutocomplete from "./ItemAutocomplete";
import { selectedTabAccess } from "../../../utilis/common";
import { DEFAULT_OVERALL_DATA } from "../../../store/reducers/overall.slice";
import { getAllOverallPatient } from "../../../services/overall.service";
import { OVERALL_CARDS_LABELS, OVERALL_CARDS_TYPE } from "../../../types/overall.type";
import { CUSTOM_TAB_PAGE_TYPE } from "../../../types/pages.type";
import { PAGE_TYPE } from "../../../types/pages.type";



const validPageTypes = Object.entries(CUSTOM_TAB_PAGE_TYPE).filter(
    ([key]) => key !== "ADT"
);

const CARD_CONFIG = {
    [CUSTOM_TAB_PAGE_TYPE.HOSPITAL]: [
        { value: HOSPITAL_CARDS_TYPE.UNPLANNED, label: HOSPITAL_CARDS_LABELS.unplannedHospitalTransfer },
        { value: HOSPITAL_CARDS_TYPE.PLANNED, label: HOSPITAL_CARDS_LABELS.plannedHospitalTransfer },
        { value: HOSPITAL_CARDS_TYPE.TOTAL, label: HOSPITAL_CARDS_LABELS.total },
    ],
    [CUSTOM_TAB_PAGE_TYPE.COMMUNITY_TRANSFER]: [
        { value: CO_TRANSFER_CARDS_TYPE.AMA, label: COMMUNITY_CARD_LABELS.AMA },
        { value: CO_TRANSFER_CARDS_TYPE.SNF, label: COMMUNITY_CARD_LABELS.SNF },
        { value: CO_TRANSFER_CARDS_TYPE.SAFE_DISCHARGE, label: COMMUNITY_CARD_LABELS.safeDischarge },
        { value: CO_TRANSFER_CARDS_TYPE.TOTAL, label: COMMUNITY_CARD_LABELS.total },
    ],
    [CUSTOM_TAB_PAGE_TYPE.DECEASED]: [
        { value: DECEASED_CARDS_TYPE.TOTAL, label: DECEASED_CARDS_LABELS.total },
    ],
    [CUSTOM_TAB_PAGE_TYPE.OVERALL_INCOMING]: [
        { value: OVERALL_CARDS_TYPE.TOTAL_INCOMING, label: OVERALL_CARDS_LABELS.totalIncoming },
    ],
    [CUSTOM_TAB_PAGE_TYPE.OVERALL_OUTGOING]: [
        { value: OVERALL_CARDS_TYPE.TOTAL_OUTGOING, label: OVERALL_CARDS_LABELS.totalOutgoing },
    ],
    [CUSTOM_TAB_PAGE_TYPE.ADMISSION]: [
        { value: ADMISSION_CARDS_TYPE.ADMISSION, label: ADMISSION_CARDS_LABELS.admission },
        { value: ADMISSION_CARDS_TYPE.READMISSION, label: ADMISSION_CARDS_LABELS.readmission },
        { value: ADMISSION_CARDS_TYPE.TOTAL, label: ADMISSION_CARDS_LABELS.total },
    ],
};

const PAGE_CONFIG = {
    [CUSTOM_TAB_PAGE_TYPE.HOSPITAL]: {
        cardFilter: DEFAULT_CARD_FILTER,
        getCards: getAllCardForCustomTab,
        updateFn: updateFilterListData,
        query: {
            forType: "transfer",
            forTransferType: ["hospitalTransfer"],
            isCustom: true,
        },
    },
    [CUSTOM_TAB_PAGE_TYPE.COMMUNITY_TRANSFER]: {
        cardFilter: DEFAULT_COMMUNITY_DATA,
        getCards: getAllCardForCommunity,
        updateFn: updateFilterListDataCommunity,
        query: {
            forType: "transfer",
            forTransferType: [ADT_SUB_TYPES.AMA, ADT_SUB_TYPES.SAFE_DISCHARGE, ADT_SUB_TYPES.SNF],
            isCustom: true,
        },
    },
    [CUSTOM_TAB_PAGE_TYPE.DECEASED]: {
        cardFilter: DEFAULT_DECEASED_DATA,
        getCards: getAllCardForDeceased,
        updateFn: updateFilterListDataDeceased,
        query: {
            forType: "transfer",
            forTransferType: ["deceased"],
            isCustom: true,
        },
    },
    [CUSTOM_TAB_PAGE_TYPE.ADMISSION]: {
        cardFilter: DEFAULT_CARD_FILTER_ADMISSION,
        getCards: getAllCardForAdmissions,
        updateFn: updateFilterListDataBoth,
        query: {
            isCustom: true,
        },
    },
    [CUSTOM_TAB_PAGE_TYPE.OVERALL_INCOMING]: {
        cardFilter: DEFAULT_OVERALL_DATA,
        getCards: getAllOverallPatient,
        updateFn: updateFilterListDataOverall,
        query: {
            isCustom: true,
        },
    },
    [CUSTOM_TAB_PAGE_TYPE.OVERALL_OUTGOING]: {
        cardFilter: DEFAULT_OVERALL_DATA,
        getCards: getAllOverallPatient,
        updateFn: updateFilterListDataOverall,
        query: {
            isCustom: true,
        },
    },
};

const LABEL_MAP = {
    [CUSTOM_TAB_PAGE_TYPE.ADMISSION]: ADMISSION_CARDS_LABELS,
    [CUSTOM_TAB_PAGE_TYPE.COMMUNITY_TRANSFER]: COMMUNITY_CARD_LABELS,
    [CUSTOM_TAB_PAGE_TYPE.DECEASED]: DECEASED_CARDS_LABELS,
    [CUSTOM_TAB_PAGE_TYPE.HOSPITAL]: HOSPITAL_CARDS_LABELS,
    [CUSTOM_TAB_PAGE_TYPE.OVERALL_INCOMING]: OVERALL_CARDS_LABELS,
    [CUSTOM_TAB_PAGE_TYPE.OVERALL_OUTGOING]: OVERALL_CARDS_LABELS,
};

const AddCombineTabDialog = ({ handleClose, filter, page, id = null, isCreator: isCreatorProp = true }) => {
    const selectedTab = selectedTabAccess();

    const isEditMode = Boolean(id);
    const [dataPointCount, setDataPointCount] = useState(1);
    const [inputValues, setInputValues] = useState([""]);
    const [cardItemData, setCardItemData] = useState([]);
    const [selectedCardItems, setSelectedCardItems] = useState([
        {
            filters: [
                {
                    card: { value: "", label: "", questionId: null },
                    items: [],
                    isMainCard: false
                }
            ],
            dashboard: ""
        }
    ]);
    const [cachedData, setCachedData] = useState({});
    const [errorPopover, setErrorPopover] = useState({ open: false, message: "" });
    const dispatch = useDispatch();
    const { isCustomTabAdded } = useSelector((state) => state.common);
    const [title, setTitle] = useState("");
    const [description, setDescription] = useState("");
    const [errorMessages, setErrorMessages] = useState([]);
    const [fieldErrors, setFieldErrors] = useState({
        title: false,
        dataPoints: [], // For each data point
    });
    const [isSaving, setIsSaving] = useState(false);
    const [titleError, setTitleError] = useState(false);
    const [isCreator, setIsCreator] = useState(isCreatorProp);

    useEffect(() => {
        if (isEditMode) {
            const fetchTabData = async () => {
                try {
                    const response = await getDynamicDataTabById(id);
                    const tabData = response;

                    if (tabData) {
                        setIsCreator(isCreatorProp);

                        setTitle(tabData.title || "");
                        setDescription(tabData.description || "");

                        const filters = tabData.filters || [];
                        setDataPointCount(filters.length || 1);

                        const inputValuesEdit = [];
                        const newCachedData = [];
                        const pendingFilters = [];

                        await Promise.all(
                            filters.map(async (filterGroup, index) => {
                                const dashboard = filterGroup.dashboard;
                                inputValuesEdit.push(dashboard);

                                // Set loading=true before fetch
                                setCardItemData((prev) => {
                                    const updated = [...prev];
                                    updated[index] = { loading: true };
                                    return updated;
                                });

                                let result = cachedData[dashboard];
                                if (!result) {
                                    result = await fetchCardAndItemOptions(dashboard);
                                    newCachedData[dashboard] = result;
                                }

                                // Set loading=false after fetch
                                setCardItemData((prev) => {
                                    const updated = [...prev];
                                    updated[index] = { loading: false, ...result };
                                    return updated;
                                });

                                const filtersArray = (filterGroup.filters || []).map((filter, filterIndex) => {
                                    if (!filter.card?.value && filter.card?.label) {
                                        filter.card.value = filter.card.label;
                                    }

                                    return {
                                        card: filter.card,
                                        items: filter.items || [],
                                        isMainCard:
                                            index === filters.length - 1 &&
                                            filterIndex === (filterGroup.filters.length - 1),
                                    };
                                });

                                pendingFilters[index] = {
                                    dashboard,
                                    filters: filtersArray,
                                };
                            })
                        );

                        // Wait for a small delay to ensure cardItemData is updated
                        setTimeout(() => {
                            setSelectedCardItems(pendingFilters);
                        }, 100);

                        setCachedData((prevCache) => ({ ...prevCache, ...newCachedData }));
                        setInputValues(inputValuesEdit);
                    } else {
                        setIsCreator(isCreatorProp);                        
                    }
                } catch (err) {
                    console.error("Failed to fetch tab data", err);
                }
            };

            fetchTabData();
        }
    }, [id, isCreatorProp]); // eslint-disable-line react-hooks/exhaustive-deps

    async function fetchCardAndItemOptions(dataPoint) {
        const cards = [];
        const items = [];

        const config = PAGE_CONFIG[dataPoint];
        if (!config) return { cards, items };

        const updatedFilter = {
            ...filter,
            isSkipCustomCard: true,
            ...(filter?.startDate && filter?.endDate
                ? {
                    startDate: moment(filter.endDate).subtract(30, "days").toDate(),
                    endDate: filter.endDate,
                }
                : {}),
        };

        const res = await config.getCards(updatedFilter);

        const query = { page, ...config.query };
        const dynamicCards = await getQuestionsData(query);

        const labelMap = LABEL_MAP[dataPoint] ?? {};
        const cardFilter = config.cardFilter;

        let result;

        if (dataPoint === CUSTOM_TAB_PAGE_TYPE.ADMISSION) {
            result = await config.updateFn(
                { ...res?.data, dynamicCards },
                cardFilter,
                null,
                cardFilter?.mainPriorityData,
                [],
                false
            );
        } else if (dataPoint === CUSTOM_TAB_PAGE_TYPE.HOSPITAL) {
            result = await config.updateFn(
                cardFilter,
                null,
                { ...res, dynamicCards },
                cardFilter?.priorityData,
                [],
                false
            );
        } else if (dataPoint === CUSTOM_TAB_PAGE_TYPE.OVERALL_INCOMING || dataPoint === CUSTOM_TAB_PAGE_TYPE.OVERALL_OUTGOING) {
            result = await config.updateFn(
                cardFilter,
                { ...res, dynamicCards },
                cardFilter?.priorityData,
                [],
                false
            );
        } else {
            result = await config.updateFn(
                cardFilter,
                { ...res?.data, dynamicCards },
                cardFilter?.priorityData,
                [],
                false
            );
        }

        const latestListData = dataPoint === CUSTOM_TAB_PAGE_TYPE.ADMISSION ? result?.patientList : result;

        if (!latestListData) return { cards, items };

        for (const [key, value] of Object.entries(latestListData)) {
            if (!Array.isArray(value) || /_?customtab/i.test(key)) continue;

            const dynamicCard = dynamicCards.find(dc => dc.accessor === key);
            const label = dynamicCard?.label ?? dynamicCard?.tableLabel ?? labelMap[key] ?? key;

            if (dataPoint === CUSTOM_TAB_PAGE_TYPE.OVERALL_INCOMING && key === OVERALL_CARDS_TYPE.NINETY_DAYS_DATA) continue;

            cards.push({
                value: key,
                label,
                questionId: dynamicCard?._id ?? null,
                dataPoint,
            });

            for (const ele of value) {
                if (ele?.isSpacialItem) continue;

                items.push({
                    value: ele._id,
                    label: ele?.label ?? ele?.name ?? ele?.title,
                    parentId: key,
                    questionId: dynamicCard?._id ?? null,
                });
            }
        }

        if (CARD_CONFIG[dataPoint]) {
            cards.push(...CARD_CONFIG[dataPoint].map(card => ({
                ...card,
                questionId: null,
                dataPoint,
                isTotalCard: true,
            })));
        }

        return { cards, items };
    }

    const handleCountChange = (e) => {
        const value = e.target.value;

        // Allow clearing the input (intermediate empty state)
        if (value === "") {
            setDataPointCount("");
            return;
        }

        const newCount = parseInt(value);
        if (isNaN(newCount) || newCount < 0) return;

        setDataPointCount(newCount);

        setInputValues((prev) =>
            newCount > prev.length ? [...prev, ...Array(newCount - prev.length).fill("")] : prev.slice(0, newCount)
        );

        setCardItemData((prev) =>
            newCount > prev.length
                ? [...prev, ...Array(newCount - prev.length).fill({ loading: false, filters: [{ items: [], card: { value: "", label: "", questionId: null }, isMainCard: false }] })]
                : prev.slice(0, newCount)
        );

        setSelectedCardItems((prev) =>
            newCount > prev.length
                ? [...prev, ...Array(newCount - prev.length).fill({ filters: [{ card: "", items: [] }] })]
                : prev.slice(0, newCount)
        );
    };

    const handleInputChange = async (index, value) => {
        // Update input values
        const updatedInputs = [...inputValues];
        updatedInputs[index] = value;

        setInputValues(updatedInputs);

        // Reset the selected filters for this data point because the page type has changed
        setSelectedCardItems((prev) => {
            const updated = [...prev];
            updated[index] = {
                filters: [{ items: [], card: { value: "", label: "", questionId: null }, isMainCard: false }],
                dashboard: value,
            };
            return updated;
        });

        // Use cached if exists
        if (cachedData[value]) {
            setCardItemData((prev) => {
                const updated = [...prev];
                updated[index] = { loading: false, ...cachedData[value] };
                return updated;
            });
            return;
        }

        // Set loading while fetching
        setCardItemData((prev) => {
            const updated = [...prev];
            updated[index] = { loading: true, filters: [{ items: [], card: { value: "", label: "", questionId: null }, isMainCard: false }] };
            return updated;
        });

        // Fetch and set new data
        const result = await fetchCardAndItemOptions(value);
        setCachedData((prevCache) => ({ ...prevCache, [value]: result }));

        setCardItemData((prev) => {
            const updated = [...prev];
            updated[index] = { loading: false, ...result };
            return updated;
        });
    };

    const handleCardItemChange = (dpIndex, filterIndex, field, value, isMainCard = false, questionId = null) => {
        setSelectedCardItems((prev) => {
            const updated = [...prev];

            // Ensure the data point exists
            const existingDataPoint = updated[dpIndex] || {
                filters: [],
                dashboard: inputValues[dpIndex],
                questionId,
            };

            // Ensure filters array is properly cloned
            const filters = [...(existingDataPoint.filters || [])];

            // Ensure the filter at filterIndex exists with safe defaults
            filters[filterIndex] = filters[filterIndex] || {
                card: { value: "", label: "", questionId: null },
                items: [],
                isMainCard,
            };

            if (field === "card") {
                filters[filterIndex] = {
                    ...filters[filterIndex],
                    card: value,
                    items: [], // Reset items when changing card
                    isMainCard,
                };
            } else if (field === "items") {
                filters[filterIndex] = {
                    ...filters[filterIndex],
                    items: value,
                    isMainCard,
                };
            }

            // Assign the updated filters back to the data point
            existingDataPoint.filters = filters;
            existingDataPoint.dashboard = inputValues[dpIndex]; // Ensure dashboard always syncs

            updated[dpIndex] = existingDataPoint;
            return updated;
        });
    };

    const handleConfirm = async () => {
        const errors = [];
        const newFieldErrors = {
            title: false,
            dataPoints: [],
        };

        if (dataPointCount < 1) {
            errors.push("At least one data point is required.");
        }

        for (let i = 0; i < dataPointCount; i++) {
            let dataPointError = {
                dashboard: false,
                filters: [],
            };

            if (!selectedCardItems[i]?.dashboard) {
                errors.push(`Please select a data point for Data Point ${i + 1}`);
                dataPointError.dashboard = true;
            }

            const filters = selectedCardItems[i]?.filters || [];

            filters.forEach((filter, filterIndex) => {
                let filterError = {
                    card: false,
                    items: false,
                };

                if (!filter?.card?.value) {
                    errors.push(`Please select a card for Filter ${filterIndex + 1} in Data Point ${i + 1}`);
                    filterError.card = true;
                }

                if (filter.items.length === 0) {
                    if (!filter?.card?.isTotalCard && !filter.isMainCard) {
                        errors.push(`Please select at least one item for Filter ${filterIndex + 1} in Data Point ${i + 1}`);
                        filterError.items = true;
                    }
                }

                dataPointError.filters.push(filterError);
            });

            newFieldErrors.dataPoints.push(dataPointError);
        }

        if (!title.trim()) {
            errors.push("Title is required.");
            newFieldErrors.title = true;
        }

        if (errors.length > 0) {
            setErrorMessages(errors);
            setErrorPopover({ open: true });
            setFieldErrors(newFieldErrors);
            setTitleError(newFieldErrors.title);
            return;
        }

        setIsSaving(true);
        try {
            let res = await createCustomTab({
                page,
                filters: selectedCardItems,
                title: title.trim(),
                description: description.trim(),
                id,
                isEditMode,
                type: CUSTOM_TAB_TYPE.combineTab
            });

            if (res && res.status === 400) {
                errors.push("Title already exists, Please try another title");
                setErrorMessages(errors);
                setErrorPopover({ open: true });
                setTitleError(true);
                return;
            } else if (res) {
                dispatch({
                    type: ADD_NOTIFICATION,
                    payload: {
                        type: "success",
                        label: "Custom tab added successfully!",
                        id: "customTabAdded",
                    },
                });
            }
            dispatch(setIsCustomTabAdded(isCustomTabAdded ? false : true));
            handleClose();
        } catch (error) {
            console.error('Error saving custom tab:', error);
            dispatch({
                type: ADD_NOTIFICATION,
                payload: {
                    type: "error",
                    label: "Failed to save custom tab. Please try again.",
                    id: "customTabError",
                },
            });
        } finally {
            setIsSaving(false);
        }
    };

    const addMoreCardItem = (dpIndex) => {
        setSelectedCardItems((prev) => {
            const updated = [...prev];

            // Ensure existing data point
            const existingDataPoint = updated[dpIndex] || { filters: [], dashboard: inputValues[dpIndex] };

            existingDataPoint.filters = [...(existingDataPoint.filters || [])];

            // Add new filter
            existingDataPoint.filters.push({ card: { value: "", label: "", questionId: null }, items: [], isMainCard: false });

            // After adding, loop through all filters and set isMainCard only for last filter of last DP
            updated.forEach((dp, dpI) => {
                if (!dp) return;
                dp.filters = dp.filters.map((filter, filterI) => ({
                    ...filter,
                    isMainCard: dpI === dataPointCount - 1 && filterI === (dp.filters.length - 1),
                }));
            });

            updated[dpIndex] = existingDataPoint;
            return updated;
        });
    };

    const handleRemoveFilter = (cardIndex, filterIndex) => {
        const updated = [...selectedCardItems];
        updated[cardIndex].filters = updated[cardIndex].filters.filter((_, i) => i !== filterIndex);
        setSelectedCardItems(updated); // Assuming you're using useState
    };

    const isAnyDataPointLoading = () => {
        return cardItemData.some(data => data?.loading);
    };

    const handleTitleChange = (e) => {
        setTitle(e.target.value);
        if (titleError) {
            setTitleError(false);
        }
    };

    return (
        <>
            <ErrorPopover
                open={errorPopover.open}
                onClose={() => setErrorPopover({ open: false })}
                errorMessages={errorMessages}
            />

            <Dialog open={true} onClose={handleClose} maxWidth="md" fullWidth>
                <DialogTitle>{isEditMode ? isCreator ? 'Edit Combine Data From Separate Dashboard tabs' : 'View Combine Data From Separate Dashboard tabs' : 'Create Combine Data From Separate Dashboard tabs'}</DialogTitle>
                <DialogContent sx={{ pt: 2 }}>
                    <LabeledSelect
                        label="Number of Data Points"
                        value={dataPointCount}
                        onChange={handleCountChange}
                        options={[
                            { label: '1', value: 1 },
                            { label: '2', value: 2 },
                            { label: '3', value: 3 }
                        ]}
                        error={false}
                        sx={{ mt: 2, width: 160 }}
                        size="medium"
                        disabled={!isCreator}
                    />

                    <Box display="flex" flexDirection="column" gap={2} mt={2}>
                        {inputValues.map((val, index) => (
                            <Paper key={index} elevation={1} sx={{ p: 2, borderRadius: 2, backgroundColor: "#fafafa" }}>
                                <Typography variant="subtitle1" gutterBottom>
                                    Data Point {index + 1}
                                </Typography>

                                <FormControl fullWidth sx={{ mb: 1 }} size="medium">
                                    <InputLabel>Select Data Point</InputLabel>
                                    <Select
                                        value={val}
                                        label="Select Data Point"
                                        onChange={(e) => handleInputChange(index, e.target.value)}
                                        disabled={!isCreator}
                                    >
                                        {(() => {
                                            const filteredBySelectedTab = validPageTypes.filter(([_, value]) => {
                                                // If selectedTab includes 'overall', show both OVERALL_INCOMING and OVERALL_OUTGOING
                                                if (selectedTab.includes(PAGE_TYPE.OVERALL)) {
                                                    if (value === CUSTOM_TAB_PAGE_TYPE.OVERALL_INCOMING || value === CUSTOM_TAB_PAGE_TYPE.OVERALL_OUTGOING) {
                                                        return true;
                                                    }
                                                }
                                                return selectedTab.includes(value);
                                            });

                                            // Gather all selected values except for the current index
                                            const selectedValues = inputValues.filter((v, i) => i !== index);

                                            if (index === 0) {
                                                // First selection only current page, still filtered by selectedTab
                                                return filteredBySelectedTab
                                                    .filter(([_, value]) => {
                                                        // If current page is overall, show both OVERALL_INCOMING and OVERALL_OUTGOING
                                                        if (page === PAGE_TYPE.OVERALL) {
                                                            return value === CUSTOM_TAB_PAGE_TYPE.OVERALL_INCOMING ||
                                                                value === CUSTOM_TAB_PAGE_TYPE.OVERALL_OUTGOING;
                                                        }
                                                        // For other pages, only show matching page
                                                        return value === page;
                                                    })
                                                    .filter(([_, value]) => {
                                                        // If OVERALL_INCOMING is already selected, don't show OVERALL_OUTGOING
                                                        if (selectedValues.includes(CUSTOM_TAB_PAGE_TYPE.OVERALL_INCOMING) &&
                                                            value === CUSTOM_TAB_PAGE_TYPE.OVERALL_OUTGOING) {
                                                            return false;
                                                        }
                                                        // If OVERALL_OUTGOING is already selected, don't show OVERALL_INCOMING
                                                        if (selectedValues.includes(CUSTOM_TAB_PAGE_TYPE.OVERALL_OUTGOING) &&
                                                            value === CUSTOM_TAB_PAGE_TYPE.OVERALL_INCOMING) {
                                                            return false;
                                                        }
                                                        return value === val || !selectedValues.includes(value);
                                                    });
                                            } else {
                                                // For all other indices, show all filteredBySelectedTab options except those already selected elsewhere
                                                return filteredBySelectedTab.filter(([_, value]) => {
                                                    // If OVERALL_INCOMING is already selected, don't show OVERALL_OUTGOING
                                                    if (selectedValues.includes(CUSTOM_TAB_PAGE_TYPE.OVERALL_INCOMING) &&
                                                        value === CUSTOM_TAB_PAGE_TYPE.OVERALL_OUTGOING) {
                                                        return false;
                                                    }
                                                    // If OVERALL_OUTGOING is already selected, don't show OVERALL_INCOMING
                                                    if (selectedValues.includes(CUSTOM_TAB_PAGE_TYPE.OVERALL_OUTGOING) &&
                                                        value === CUSTOM_TAB_PAGE_TYPE.OVERALL_INCOMING) {
                                                        return false;
                                                    }
                                                    return value === val || !selectedValues.includes(value);
                                                });
                                            }
                                        })().map(([label, value]) => (
                                            <MenuItem key={value} value={value}>
                                                {label.charAt(0).toUpperCase() + label.slice(1).toLowerCase()}
                                            </MenuItem>
                                        ))}
                                    </Select>
                                </FormControl>

                                {cardItemData[index]?.loading ? (
                                    <LoadingBlock size={30} message="Loading card & item options..." />
                                ) : (
                                    <>
                                        <Divider sx={{ my: 2 }} />

                                        {(selectedCardItems[index]?.filters || []).map((filter, filterIndex) => {
                                            const isMainCard = index === dataPointCount - 1 && filterIndex === (selectedCardItems[index]?.filters.length - 1);

                                            return (
                                                <Box key={filterIndex} display="flex" gap={2} flexDirection={{ xs: "column", sm: "row" }} mt={2}>
                                                    <LabeledSelect
                                                        label="Select Card"
                                                        value={filter?.card?.value || ""}
                                                        disabled={!isCreator}
                                                        onChange={(ele) => {
                                                            const selectedValue = ele.target.value;
                                                            const selectedCard = cardItemData[index]?.cards?.find((card) => card.value === selectedValue);
                                                            handleCardItemChange(index, filterIndex, "card", selectedCard, isMainCard);
                                                        }}
                                                        options={cardItemData[index]?.cards
                                                            ?.filter((card) => {
                                                                const selectedValues = (selectedCardItems[index]?.filters || [])
                                                                    .filter((_, i) => i !== filterIndex)
                                                                    .map((f) => f.card?.value);
                                                                return !selectedValues.includes(card.value);
                                                            })
                                                            ?.map(card => ({ label: card.label, value: card.value })) || []}
                                                        error={fieldErrors?.dataPoints[index]?.filters[filterIndex]?.card}
                                                        size="medium"
                                                    />

                                                    {!filter?.card?.isTotalCard && (
                                                        <ItemAutocomplete
                                                            disabled={!isCreator}
                                                            label="Select Items"
                                                            value={Array.isArray(filter?.items) ? filter.items : []}
                                                            onChange={(e, newValue) => {
                                                                // Handled inside the component already — you can skip if you're using handleCardItemChange there
                                                            }}
                                                            error={fieldErrors?.dataPoints[index]?.filters[filterIndex]?.items}
                                                            helperText=""
                                                            cardItemData={cardItemData}
                                                            index={index}
                                                            filterIndex={filterIndex}
                                                            filter={filter}
                                                            isMainCard={isMainCard}
                                                            handleCardItemChange={handleCardItemChange}
                                                            fieldErrors={fieldErrors}
                                                        />
                                                    )}

                                                    {selectedCardItems[index]?.filters.length > 1 && (
                                                        <IconButton
                                                            disabled={!isCreator}
                                                            aria-label="Remove Filter"
                                                            color="error"
                                                            size="small"
                                                            onClick={() => handleRemoveFilter(index, filterIndex)}
                                                            sx={{
                                                                mt: 1,
                                                                width: 36,
                                                                height: 36,
                                                                padding: 1,
                                                                alignSelf: "flex-start"
                                                            }}
                                                        >
                                                            <DeleteIcon fontSize="small" />
                                                        </IconButton>
                                                    )}
                                                </Box>
                                            )
                                        })}

                                        <Box mt={1}>
                                            <Button
                                                variant="outlined"
                                                size="small"
                                                onClick={() => addMoreCardItem(index)}
                                                sx={{ mt: 1 }}
                                                disabled={!isCreator}
                                            >
                                                Add Filter
                                            </Button>
                                        </Box>
                                    </>
                                )}
                            </Paper>
                        ))}
                    </Box>
                    <LabeledTextField
                        label="Tab Title"
                        fullWidth
                        margin="normal"
                        value={title}
                        onChange={handleTitleChange}
                        sx={{ mt: 5 }}
                        error={titleError}
                        helperText={titleError ? "Title already exists or is required" : ""}
                        disabled={!isCreator}
                    />
                    <LabeledTextarea
                        label="Description"
                        minRows={4}
                        maxRows={8}
                        value={description}
                        onChange={(e) => setDescription(e.target.value)}
                        placeholder="Enter description"
                        style={{
                            width: '100%',
                            padding: '8px 12px',
                            fontSize: '14px',
                            borderRadius: '4px',
                            borderColor: '#ccc',
                            resize: 'vertical',
                        }}
                        disabled={!isCreator}                        
                    />
                </DialogContent>

                <DialogActionsRow
                    onCancel={handleClose}
                    onSave={handleConfirm}
                    loading={isSaving}
                    disabled={isSaving || (isEditMode && isAnyDataPointLoading()) || !isCreator}
                    cancelText="Cancel"
                    saveText="Save"
                    loadingText="Saving..."
                />

            </Dialog>
        </>
    );
};

export default AddCombineTabDialog; 