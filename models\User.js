const mongoose = require("mongoose");
const { Schema, Types } = mongoose;
const jwt = require("jsonwebtoken");
const bcrypt = require("bcryptjs");
const { generateAuthTokens, renewTokens, attachRefreshToken } = require("../utils/auth");
const config = require("../config");

const UserSchema = new Schema({
	fullName: { type: String, required: true },
	email: { type: String, required: true, trim: true, lowercase: true },
	password: String,
	accounts: [
		{
			accountId: { type: Types.ObjectId, ref: "account" },
			access: { type: Boolean, default: false },
		},
	],
	facilities: [
		{
			facilityId: { type: Types.ObjectId, ref: "facility" },
			access: Boolean,
			read: Boolean,
			write: Boolean,
			accountId: Types.ObjectId,
		},
	],
	createdBy: { type: Types.ObjectId, ref: "user" },
	type: String, // super || admin || user
	requirePasswordReset: <PERSON><PERSON><PERSON>,
	resetPasswordToken: String,
	onBoarded: Boolean,
	accepted: <PERSON><PERSON><PERSON>,
	jobTitle: String,
	avatar: String,
	canAddFacilities: Boolean,
	hashedKey: String,
	timeZone: Object,
	tenantUserId: String,
	role: { type: Types.ObjectId, ref: "role", populate: true },
});

UserSchema.methods.generateAuthTokens = async (user) => {
	const accessPayload = { _id: user._id.toHexString(), access: "auth" };

	return await generateAuthTokens(user, accessPayload);
};

UserSchema.statics.findByCredentials = function (email, password) {
	var User = this;
	const trimmedEmail = email?.trim();
	return User.findOne({ email: { $regex: trimmedEmail, $options: "i" } })
		.populate("role")
		.then(async (user) => {
			return new Promise(async (resolve, reject) => {
				if (!user) {
					await bcrypt.compare(password, "c90ed70f-6948-4d27-a97b-cc1ac84f4036", (err, res) => {
						reject("Username or Password are not correct");
					});
				} else {
					await bcrypt.compare(password, user.password, (err, res) => {
						if (res) {
							resolve(user);
						} else {
							reject("Username or Password are not correct");
						}
					});
				}
			});
		});
};

UserSchema.statics.findByToken = async function (req, res, accessToken, refreshToken) {
	let decoded;
	const User = this;
	try {
		decoded = jwt.verify(accessToken, config.accessTokenSecretKey);
		return {
			userQuery: User.findOne({
				_id: decoded._id,
				"tokens.token": accessToken,
				"tokens.access": "auth",
			}),
			accessToken,
		};
	} catch (err) {
		const decodedAccessToken = jwt.decode(accessToken);
		if (err.name === "TokenExpiredError") {
			const {
				accessToken: newAccessToken,
				refreshToken: newRefreshToken,
				isRefreshTokenRenewed,
			} = await renewTokens(User, decodedAccessToken._id, accessToken, refreshToken);

			if (isRefreshTokenRenewed) {
				attachRefreshToken(res, newRefreshToken);
			}

			return {
				userQuery: User.findOne({
					_id: decodedAccessToken._id,
					"tokens.token": accessToken,
					"tokens.access": "auth",
				}),
				accessToken: newAccessToken,
			};
		}
	}
};

UserSchema.pre("save", async function (next) {
	if (!this.isModified("password")) return next();
	try {
		const salt = await bcrypt.genSalt(10);
		this.password = await bcrypt.hash(this.password, salt);
		return next();
	} catch (err) {
		return next(err);
	}
});

mongoose.model("user", UserSchema);
