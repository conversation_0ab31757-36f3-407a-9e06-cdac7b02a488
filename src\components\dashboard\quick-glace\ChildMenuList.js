import * as React from "react";
import MenuItem from "@mui/material/MenuItem";
import styles from "./FilterMenuButton.module.scss";
import { ListItemText, MenuList, Typography } from "@mui/material";
import { QUICK_GLACE_OPTIONS } from "../../../data/quick-glace.data";
import _ from "lodash";

export default function ChildMenuList(props) {
    const { item, selectedId, handleChildChange, removeFilter } = props;
    const selectedParentData = _.find(QUICK_GLACE_OPTIONS, { id: selectedId });
    const childData = selectedParentData?.children || [];

    if (childData) {
        return (
            <MenuList>
                {childData.map((ele, index) => (
                    <MenuItem
                        dense={false}
                        sx={{ textAlign: "center" }}
                        key={`${ele.id}-child-${index}`}
                        onClick={() => handleChildChange(ele)}
                        disableRipple
                        divider={true}
                    >
                        <ListItemText>
                            <Typography className={styles.parentMenuItemText}>
                                {ele.label}
                            </Typography>
                        </ListItemText>
                    </MenuItem>
                ))}
                <MenuItem
                    key={"removeFilter"}
                    onClick={() => removeFilter(item)}
                    disableRipple
                >
                    <ListItemText>
                        <Typography className={styles.removeText}>Remove Filter</Typography>
                    </ListItemText>
                </MenuItem>
            </MenuList>
        );
    }
}
