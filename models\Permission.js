const mongoose = require("mongoose");
const { Schema } = mongoose;
var slug = require("mongoose-slug-generator");

mongoose.plugin(slug);


const PermissionSchema = new Schema(
    {
        name: { type: String, required: true },
        slug: { type: String, slug: "name", unique: true },
        active: { type: Boolean, default: true }        
    },
    { timestamps: true },
);

const Permission = mongoose.model("permission", PermissionSchema);

module.exports = Permission
