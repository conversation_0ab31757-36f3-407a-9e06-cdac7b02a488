const _ = require("lodash");
const moment = require("moment");
const { toStartFilterDate } = require("../date-format");
const { CHART_FILTER_DAY_OF } = require("../../../types/report.type");
const { DECEASED_CARDS_TYPE, HOSPITAL_CARDS_TYPE, OVERALL_CARDS_TYPE, ADT_TABLE_TYPE, ADMISSION_FILTER_TYPE, CUSTOM_TAB_TYPE, CO_TRANSFER_CARDS_TYPE } = require("../../../types/common.type");

const get30DaysFromDates = async (startDateA, endDateB, days = 29) => {
    var dateStart = await moment(startDateA);
    var dateEnd = await moment(endDateB);

    let betweenMonthsLatest = [];
    let interimEndDate = dateEnd.clone();

    while (dateStart <= interimEndDate) {
        if (betweenMonthsLatest.length == 0) {
            betweenMonthsLatest.push(dateEnd.format('YYYY-MM-DD'));
        } else {
            betweenMonthsLatest.push(interimEndDate.subtract(1, "day").format('YYYY-MM-DD'));
        }
        interimEndDate.subtract(days, 'days');
    }
    return betweenMonthsLatest;
}

const itemPercentageChart = (total, mainTotalVal = null) => {
    let percentage = 0;
    if (total !== 0 && mainTotalVal !== 0) {
        percentage = (total / mainTotalVal) * 100;
    }
    percentage = percentage ? percentage : 0;

    return `${percentage.toFixed(2)}%`;
}


const is30Days = (startDate, endDate, daysCheck = 29) => {
    const momentStartDate = moment(startDate, "YYYY-MM-DD");
    const momentEndDate = moment(endDate, "YYYY-MM-DD");
    const dayDiff = momentEndDate.diff(momentStartDate, "days");
    if (dayDiff == daysCheck) {
        return true;
    }
    return false;

}

const isFullWeek = (startDate, endDate) => {
    const momentStartDate = moment(startDate, "YYYY-MM-DD");
    const momentEndDate = moment(endDate, "YYYY-MM-DD");

    const startOfWeek = moment(momentStartDate).startOf("week").format("YYYY-MM-DD")
    const endOfWeek = moment(momentEndDate).endOf("week").format("YYYY-MM-DD")
    return momentStartDate.isSame(startOfWeek) && momentEndDate.isSame(endOfWeek);
}

const isFullMonth = (startDate, endDate) => {
    const momentStartDate = moment(startDate, "YYYY-MM-DD");
    const momentEndDate = moment(endDate, "YYYY-MM-DD");
    const startOfMonth = moment(momentStartDate).startOf("month").format("YYYY-MM-DD");
    const endOfMonth = moment(momentEndDate).endOf("month").format("YYYY-MM-DD")

    return momentStartDate.isSame(startOfMonth) && momentEndDate.isSame(endOfMonth);
}

const isFullYear = (startDate, endDate) => {
    const momentStartDate = moment(startDate, "YYYY-MM-DD");
    const momentEndDate = moment(endDate, "YYYY-MM-DD");

    const startOfYear = moment(momentStartDate).startOf("year").format("YYYY-MM-DD")
    const endOfYear = moment(momentEndDate).endOf("year").format("YYYY-MM-DD")

    return momentStartDate.isSame(startOfYear) && momentEndDate.isSame(endOfYear);
}

const chartGroupBy = async (data, duration, admissionTotal = 0, censusByPeriod = [], queryFilters) => {
    let formatted = [];
    let dates = [];
    let uniqueDates = [];
    if (duration === CHART_FILTER_DAY_OF.THIRTY || duration === CHART_FILTER_DAY_OF.SEVEN) {
        formatted = await data.map((elem) => {
            return {
                x: moment(elem.x).format("YYYY-MM-DD"),
                y: elem.y,
                total: elem.total,
            };
        });
        const thirtyDaysDates = await get30DaysFromDates(queryFilters.startDate, queryFilters.endDate, duration === CHART_FILTER_DAY_OF.THIRTY ? 29 : 6);
        dates = formatted.map((elem) => elem.x);
        uniqueDates = thirtyDaysDates;
    } else {
        formatted = await data.map((elem) => {
            return {
                x: moment(elem.x).startOf(duration).format("YYYY-MM-DD"),
                y: elem.y,
                total: elem.total,
            };
        });
        dates = await formatted.map((elem) => elem.x);
        uniqueDates = await dates.filter((x, index) => dates.indexOf(x) === index);
    }
    let isBroken = false;
    let latestData = [];
    let index = 0;
    for await (let x of uniqueDates) {
        if (!isBroken) {
            let y = await formatted.filter((elem) => elem.x === x).reduce((y, elem) => y + elem.y, 0);
            const percentage = await itemPercentageChart(y, admissionTotal, "percentage") || 0;

            if (duration === CHART_FILTER_DAY_OF.MONTH) {
                let startOfMonth = moment(x).startOf("month").format('YYYY-MM-DD');
                let endOfMonth = moment(x).endOf("month").format('YYYY-MM-DD');
                if (uniqueDates.length - 1 == index) {
                    let fullMonthRes = isFullMonth(x, moment(queryFilters.endDate).format("YYYY-MM-DD"))
                    isBroken = fullMonthRes ? false : true
                }
                if (index == 0) {
                    let fullMonthRes = isFullMonth(moment(queryFilters.startDate).format("YYYY-MM-DD"), endOfMonth)
                    isBroken = fullMonthRes ? false : true
                }
                if (!isBroken) {
                    if (censusByPeriod && censusByPeriod.monthly && censusByPeriod.monthly.length > 0) {
                        let selectedCensus = _.filter(censusByPeriod.monthly, { startDate: startOfMonth });
                        let percentageByMonth = 0
                        if (selectedCensus && selectedCensus.length > 0) {
                            let censusInfo = selectedCensus[0]
                            percentageByMonth = await itemPercentageChart(y, censusInfo.censusAverage, "percentage") || 0;
                        } else {
                            percentageByMonth = await itemPercentageChart(y, admissionTotal, "percentage") || 0;
                        }
                        latestData.push({ x: moment(x).format("MMMM"), y, percentage: percentageByMonth });
                    } else {
                        latestData.push({ x: moment(x).format("MMMM"), y, percentage });
                    }
                }
                isBroken = false
            } else if (duration === CHART_FILTER_DAY_OF.THIRTY || duration === CHART_FILTER_DAY_OF.SEVEN) {
                let days = duration === CHART_FILTER_DAY_OF.SEVEN ? 6 : 29
                let endDateLabel = moment(x).format("MM-DD");
                let startDateLabel = uniqueDates.length - 1 === index ? moment(queryFilters.startDate).format("MM-DD") : moment(x).subtract(days, "days").format("MM-DD");
                let endOfMDate = moment(x).format('YYYY-MM-DD');
                let startOfMDate = moment(x).subtract(days, "days").format('YYYY-MM-DD');

                if (uniqueDates.length - 1 == index) {
                    let fullMonthRes = is30Days(moment(queryFilters.startDate).format("YYYY-MM-DD"), x, days + 1);
                    isBroken = fullMonthRes ? false : true
                }
                if (index == 0) {
                    let startOfMDateFirst = moment(x).subtract(days, "days").format('YYYY-MM-DD');
                    let fullMonthRes = is30Days(startOfMDateFirst, x, days)
                    isBroken = fullMonthRes ? false : true
                }
                if (!isBroken) {
                    let yData = formatted.reduce((acc, obj) => {
                        if (obj.x >= startOfMDate && obj.x <= endOfMDate) {
                            acc += parseInt(obj.y);
                        }
                        return acc;
                    }, 0);

                    const percentageData = itemPercentageChart(yData, admissionTotal, "percentage") || 0;

                    if (censusByPeriod && censusByPeriod.thirtyDays && censusByPeriod.thirtyDays.length > 0) {
                        let selectedCensus = _.filter(censusByPeriod.thirtyDays, { endDate: endOfMDate });
                        let percentageByMonth = 0
                        if (selectedCensus && selectedCensus.length > 0) {
                            let censusInfo = selectedCensus[0]
                            percentageByMonth = itemPercentageChart(yData, censusInfo.censusAverage, "percentage") || 0;
                        } else {
                            percentageByMonth = itemPercentageChart(yData, admissionTotal, "percentage") || 0;
                        }
                        latestData.push({
                            x: `(${startDateLabel})-(${endDateLabel})`,
                            y: yData,
                            percentage: percentageByMonth,
                            endDate: endOfMDate
                        });
                    } else {
                        latestData.push({
                            x: `(${startDateLabel})-(${endDateLabel})`,
                            y: yData,
                            percentage: percentageData,
                            endDate: endOfMDate
                        });
                    }
                }
                isBroken = false;
            } else if (duration === CHART_FILTER_DAY_OF.YEAR) {
                let startOfYear = moment(x).startOf("year").format('YYYY');
                let endOfYear = moment(x).endOf("year").format('YYY-MM-DD');
                if (uniqueDates.length - 1 == index) {
                    let fullMonthRes = isFullYear(x, moment(queryFilters.endDate).format("YYYY-MM-DD"))
                    isBroken = fullMonthRes ? false : true
                }
                if (index == 0) {
                    let fullMonthRes = isFullYear(moment(queryFilters.startDate).format("YYYY-MM-DD"), endOfYear)
                    isBroken = fullMonthRes ? false : true
                }
                if (!isBroken) {
                    if (censusByPeriod && censusByPeriod.yearly && censusByPeriod.yearly.length > 0) {

                        let selectedCensus = _.filter(censusByPeriod.yearly, { startDate: startOfYear });
                        let percentageByYear = 0;
                        if (selectedCensus && selectedCensus.length > 0) {
                            let censusInfo = selectedCensus[0]
                            percentageByYear = itemPercentageChart(y, censusInfo.censusAverage, "percentage") || 0;
                        } else {
                            percentageByYear = itemPercentageChart(y, admissionTotal, "percentage") || 0;
                        }
                        latestData.push({ x: moment(x).format("YYYY"), y, percentage: percentageByYear });
                    } else {
                        latestData.push({ x: moment(x).format("YYYY"), y, percentage });
                    }
                }
                isBroken = false
            } else if (duration === CHART_FILTER_DAY_OF.WEEK) {
                let endOfWeek = moment(x).endOf("week").format('YYY-MM-DD');
                if (uniqueDates.length - 1 == index) {
                    let fullMonthRes = isFullWeek(x, moment(queryFilters.endDate).format("YYYY-MM-DD"))
                    isBroken = fullMonthRes ? false : true
                }
                if (index == 0) {
                    let fullMonthRes = isFullWeek(moment(queryFilters.startDate).format("YYYY-MM-DD"), endOfWeek)
                    isBroken = fullMonthRes ? false : true
                }
                if (!isBroken) {
                    latestData.push({
                        x: `(${moment(x).startOf("week").format("MM-DD")})-(${moment(x)
                            .endOf("week")
                            .format("MM-DD")})`,
                        y,
                        percentage,
                    });
                }
                isBroken = false;
            } else {
                latestData.push({ x: moment(x).format("MM-DD-YYYY"), y, percentage });
            }
        }
        index++
    }

    if (latestData.length) {
        latestData = await latestData.filter(item => item);
    }
    if (duration === CHART_FILTER_DAY_OF.THIRTY || duration === CHART_FILTER_DAY_OF.SEVEN) {
        return await _.sortBy(latestData, "endDate");
    } else {
        return latestData;
    }
}


const chartsData = async (data, queryFilters = null) => {
    let chartsData = [];
    if (queryFilters) {
        const start = queryFilters.startDate;
        const end = await toStartFilterDate(queryFilters.endDate);
        let loop = new Date(start);
        while (loop <= end) {
            let currDate = moment(loop).format("YYYY-MM-DD");
            let dateOfADTExist = _.filter(data, { dateOfADT: currDate });
            if (dateOfADTExist && dateOfADTExist.length > 0) {
                chartsData.push({
                    x: currDate,
                    y: dateOfADTExist.length,
                    total: dateOfADTExist.length,
                });
            } else {
                chartsData.push({
                    x: currDate,
                    y: 0,
                    total: 0,
                });
            }
            let newDate = loop.setDate(loop.getDate() + 1);
            loop = new Date(newDate);
        }
    } else {
        if (data && data.length > 0) {
            data.map((ele) => {
                chartsData.push({
                    x: ele.dateOfADT,
                    y: 1,
                    total: 1,
                });
            });
        }
    }

    if (chartsData.length > 0) {
        return _.sortBy(chartsData, "x");
    } else {
        return chartsData;
    }
}


const updateChartArrData = async (dataArr, filterType = null, type = null, filterObj = null) => {

    let filterBy = filterType;
    if (!filterType) {
        filterBy = buttonFilterType;
    }
    let chartDataArr = []

    if (type == "filter" && filterObj) {
        chartDataArr = await chartsData(dataArr, filterObj);
    } else {
        chartDataArr = await chartsData(dataArr, queryFilters);
    }

    let filtersLatest = filterObj ? filterObj : queryFilters

    const latestChartData = await chartGroupBy(chartDataArr, filterBy, filterObj?.censusAverage, filterObj?.censusByPeriod, filtersLatest);
    return { filterData: latestChartData, filterPatients: dataArr };
}

const filterByCheckbox = async (filter = null, isAllChecked = false, dataFilters) => {
    const { arrData, chartData, latestButtonFilterType, filterObj } = dataFilters
    if (isAllChecked) {        
        return await updateChartArrData(chartData, latestButtonFilterType, "filter", filterObj);
    } else {
        let newFilters = _.filter(arrData, filter);
        let newChartFilters = _.filter(chartData, filter);        
        return await updateChartArrData(newChartFilters, latestButtonFilterType, "filter", filterObj);
    }
}

const ADTfilterOptions = async (data) => {
    const {
        newChecked,
        arrData = [],
        chartData = [],
        latestButtonFilterType = null,
        filterObj,
        filterType = "initial",
        originalData,
        selectedCardItem
    } = data;
    const { childId, type } = selectedCardItem;
    if (childId === "reHospitalization") {
        if (newChecked.length === 1) {
            let filterDataObj = Object();
            filterDataObj.reHospitalization = newChecked[0] == "newHospitalizations" ? false : true;
            return await filterByCheckbox(filterDataObj, false, { arrData, chartData, latestButtonFilterType, filterObj });
        } else {
            return await filterByCheckbox(null, true, { arrData, chartData, latestButtonFilterType, filterObj });
        }
    }
    if (childId === "wasAdmitted") {
        if (newChecked.length === 1) {
            let filterDataObj = Object();
            filterDataObj.wasAdmitted = newChecked[0] === "DC" ? true : false;
            return await filterByCheckbox(filterDataObj, false, { arrData, chartData, latestButtonFilterType, filterObj });
        } else {
            return await filterByCheckbox(null, true, { arrData, chartData, latestButtonFilterType, filterObj });
        }
    }
    if (childId === "wasReturned") {
        if (newChecked.length === 1) {
            let filterDataObj = Object();
            filterDataObj.wasReturned = newChecked[0] === "returned" ? true : false;
            return await filterByCheckbox(filterDataObj, false, { arrData, chartData, latestButtonFilterType, filterObj });
        } else {
            return await filterByCheckbox(null, true, { arrData, chartData, latestButtonFilterType, filterObj });
        }
    }
    if (childId === "day") {
        let newFilters = _.filter(arrData, ({ day }) => {
            return _.every([_.includes(newChecked, day)]);
        });
        //setPatientData(newFilters);
        let newChartFilters = _.filter(chartData, ({ day }) => {
            return _.every([_.includes(newChecked, day)]);
        });
        //setSelectedCheckBoxItem(newChartFilters);
        await updateChartArrData(newChartFilters, latestButtonFilterType, "filter", filterObj);
    }
    if (childId === "shiftName") {
        let newFilters = _.filter(arrData, ({ shiftName }) => {
            return _.every([_.includes(newChecked, shiftName)]);
        });
        //setPatientData(newFilters);
        let newChartFilters = _.filter(chartData, ({ shiftName }) => {
            return _.every([_.includes(newChecked, shiftName)]);
        });
        //setSelectedCheckBoxItem(newChartFilters);
        return await updateChartArrData(newChartFilters, latestButtonFilterType, "filter", filterObj);
    }
    if (
        childId === "90Days" ||
        childId === "60Days" ||
        childId === "90DaysDeceased" ||
        childId === "90DaysOverall"
    ) {

        let ninetyDaysDataIds = [];
        let filterDataObj = [];
        if (filterDataObj.length == 0) {
            if (childId === "90Days") {
                filterDataObj = originalData.ninetyDaysData
            }
            if (childId === "60Days") {
                filterDataObj = originalData.sixtyDaysData
            }
            if (childId === "90DaysDeceased") {
                filterDataObj = originalData.deceasedNinetyDaysChartCountData
            }
            if (childId === "90DaysOverall") {
                filterDataObj = originalData.overAllNinetyDaysChartCountData
            }
        }
        let dataOriginal = filterObj?.filterData ? filterObj.filterData : filterDataObj;
        if (filterType === "reload") {
            if (childId === "90Days") {
                dataOriginal = originalData.ninetyDaysData
            }
            if (childId === "60Days") {
                dataOriginal = originalData.sixtyDaysData
            }
            if (childId === "90DaysDeceased") {
                dataOriginal = originalData.deceasedNinetyDaysChartCountData
            }
            if (childId === "90DaysOverall") {
                dataOriginal = originalData.overAllNinetyDaysChartCountData
            }
        }
        let newChartFilters = chartData
        if (newChecked.length > 0) {
            let ninetyDaysDataFilter = _.filter(dataOriginal, ({ _id }) =>
                _.every([_.includes(newChecked, _id)])
            );
            if (ninetyDaysDataFilter && ninetyDaysDataFilter.length > 0) {
                ninetyDaysDataFilter.map(
                    item => (ninetyDaysDataIds = [...ninetyDaysDataIds, ...item.ids])
                );
            }
            newChartFilters = _.filter(chartData, ({ _id }) =>
                _.every([_.includes(ninetyDaysDataIds, _id)])
            );
        }
        return await updateChartArrData(newChartFilters, latestButtonFilterType, "filter", filterObj);
    }
    if (type === ADT_TABLE_TYPE.GROUP) {
        if (newChecked && newChecked.length > 0) {
            let newFilters = _.filter(arrData, ({ filterId }) => {
                return _.every([_.includes(newChecked, filterId)]);
            });
            //setPatientData(newFilters);
            let newChartFilters = _.filter(chartData, ({ filterId }) => {
                return _.every([_.includes(newChecked, filterId)]);
            });
        }
        return await updateChartArrData(newChartFilters, latestButtonFilterType, "filter", filterObj);
    }
    if (type === ADT_TABLE_TYPE.GROUP_ARRAY) {
        let newFilters = await _.filter(arrData, ({ dxIds }) => {
            const matchedIds = _.intersection(newChecked, dxIds);
            if (matchedIds.length > 0) {
                return true;
            } else {
                return false;
            }
        });
        //setPatientData(newFilters);
        let newChartFilters = await _.filter(chartData, ({ dxIds }) => {
            const matchedIds = _.intersection(newChecked, dxIds);
            if (matchedIds.length > 0) {
                return true;
            } else {
                return false;
            }
        });
        //setSelectedCheckBoxItem(newChartFilters);
        return await updateChartArrData(newChartFilters, latestButtonFilterType, "filter", filterObj, queryFilters);
    }
}

const overAllFilterOptions = async (data) => {
    const {
        newChecked,
        chartMainDataArr = [],
        latestButtonFilterType = null,
        filterObj = null,
        filterType = "initial",
        originalData,
        filters,
        mainData = []
    } = data;

    if (filters.type === OVERALL_CARDS_TYPE.NINETY_DAYS_DATA) {
        let ninetyDaysDataIds = [];
        let dataOriginal = filterObj?.filterData ? filterObj.filterData : filterData;
        if (filterType === "reload") {
            dataOriginal = originalData.ninetyDaysData
        }
        let ninetyDaysDataFilter = _.filter(dataOriginal, ({ _id }) =>
            _.every([_.includes(newChecked, _id)])
        );
        if (ninetyDaysDataFilter && ninetyDaysDataFilter.length > 0) {
            ninetyDaysDataFilter.map(
                (item) => (ninetyDaysDataIds = [...ninetyDaysDataIds, ...item.ids])
            );
        }
        let newChartFilters = chartMainDataArr
        if (newChecked.length > 0) {
            newChartFilters = _.filter(mainData, ({ _id }) => !_.includes(ninetyDaysDataIds, _id));
        }
        return await updateChartArrData(newChartFilters, latestButtonFilterType, "filter", filterObj);
    } else {
        const isTotalCard = filters.type === OVERALL_CARDS_TYPE.TOTAL ||
            filters.type === OVERALL_CARDS_TYPE.TOTAL_INCOMING ||
            filters.type === OVERALL_CARDS_TYPE.TOTAL_OUTGOING ||
            filterObj?.question?.customTab?.type === CUSTOM_TAB_TYPE.combineTab;
        let newChartFilters = [];

        if (!isTotalCard) {
            const question = filters.question;
            if (question?.isCustom) {
                newChartFilters = await processCustomChartFilters(
                    chartMainDataArr,
                    filters,
                    newChecked
                );
            } else {
                newChartFilters = newChecked?.length > 0 ? _.filter(chartMainDataArr, ({ filterId }) => {
                    return _.every([_.includes(newChecked, filterId)]);
                }) : chartMainDataArr;
            }
        } else {
            newChartFilters = chartMainDataArr;
        }
        return await updateChartArrData(newChartFilters, latestButtonFilterType, "filter", filterObj);
    }
}

const hospitalFilterOptions = async (data) => {
    const {
        newChecked,
        chartMainDataArr = [],
        latestButtonFilterType = null,
        filterObj = null,
        filterType = "initial",
        originalData,
        filters,
        mainData = []
    } = data;

    if (filters.type === HOSPITAL_CARDS_TYPE.DX_DATA) {
        let newChartFilters = await _.filter(mainData, ({ dxIds }) => {
            const matchedIds = _.intersection(newChecked, dxIds);
            if (matchedIds.length > 0) {
                return true;
            } else {
                return false;
            }
        });
        return await updateChartArrData(newChartFilters, latestButtonFilterType, "filter", filterObj);
    } else if (filters.type === HOSPITAL_CARDS_TYPE.NINETY_DAYS_DATA) {
        let ninetyDaysDataIds = [];
        let dataOriginal = filterObj?.filterData ? filterObj.filterData : filterData;

        if (filterType === "reload") {
            dataOriginal = originalData.ninetyDaysData
        }

        let ninetyDaysDataFilter = _.filter(dataOriginal, ({ _id }) =>
            _.every([_.includes(newChecked, _id)])
        );
        if (ninetyDaysDataFilter && ninetyDaysDataFilter.length > 0) {
            ninetyDaysDataFilter.map(
                (item) => {
                    ninetyDaysDataIds = [...ninetyDaysDataIds, ...item.ids]
                }
            );
        }

        let newChartFilters = _.filter(mainData, ({ _id }) => !_.includes(ninetyDaysDataIds, _id));

        return await updateChartArrData(newChartFilters, latestButtonFilterType, "filter", filterObj);
    } else if (filters.type === HOSPITAL_CARDS_TYPE.DAYS_DATA) {

        let newChartFilters = _.filter(mainData, ({ day }) => {
            return _.every([_.includes(newChecked, day)]);
        });
        return await updateChartArrData(newChartFilters, latestButtonFilterType, "filter", filterObj);
    } else if (filters.type === HOSPITAL_CARDS_TYPE.SHIFT_DATA) {

        let newChartFilters = _.filter(mainData, ({ shiftName }) => {
            return _.every([_.includes(newChecked, shiftName)]);
        });
        return await updateChartArrData(newChartFilters, latestButtonFilterType, "filter", filterObj);
    } else if (filters.type === HOSPITAL_CARDS_TYPE.HOSPITALIZATIONS) {
        if (newChecked.length === 1) {
            let filter = Object();
            if (newChecked[0] == "newHospitalizations") {
                filter.reHospitalization = false;
            }
            if (newChecked[0] == "reHospitalizations") {
                filter.reHospitalization = true;
            }
            let newChartFilters = _.filter(mainData, filter);

            return await updateChartArrData(newChartFilters, latestButtonFilterType, "filter", filterObj);
        } else {
            return await updateChartArrData(mainData, latestButtonFilterType, "filter", filterObj);
        }
    } else if (filters.type === HOSPITAL_CARDS_TYPE.DCER_DATA) {
        if (newChecked.length === 1) {
            let filter = Object();
            if (newChecked[0] === "DC") {
                filter.wasAdmitted = true;
            }
            if (newChecked[0] === "ER") {
                filter.wasAdmitted = false;
            }
            let newChartFilters = _.filter(mainData, filter);

            return await updateChartArrData(newChartFilters, latestButtonFilterType, "filter", filterObj);
        } else {

            return await updateChartArrData(mainData, latestButtonFilterType, "filter", filterObj);
        }
    } else if (filters.type === HOSPITAL_CARDS_TYPE.RETURNS_DATA) {
        let filter = Object();
        if (newChecked.length === 1) {
            if (newChecked[0] === "Returned") {
                filter.wasReturned = true;
            }
            if (newChecked[0] === "Didn't Return") {
                filter.wasReturned = false;
            }
            let newChartFilters = _.filter(mainData, filter);
            return await updateChartArrData(newChartFilters, latestButtonFilterType, "filter", filterObj);
        } else {
            return await updateChartArrData(mainData, latestButtonFilterType, "filter", filterObj);
        }
    } else {
        const isTotalCard = filters.type === HOSPITAL_CARDS_TYPE.TOTAL ||
            filters.type === HOSPITAL_CARDS_TYPE.PLANNED ||
            filters.type === HOSPITAL_CARDS_TYPE.UNPLANNED ||
            filterObj?.question?.customTab?.type === CUSTOM_TAB_TYPE.combineTab;
        let newChartFilters = [];

        if (!isTotalCard) {
            const question = filters.question;

            if (question?.isCustom) {
                newChartFilters = await processCustomChartFilters(
                    mainData,
                    filters,
                    newChecked
                );
            } else {
                newChartFilters = _.filter(mainData, ({ filterId }) => {
                    return _.every([_.includes(newChecked, filterId)]);
                });
            }
        } else {
            newChartFilters = mainData;
        }
        return await updateChartArrData(newChartFilters, latestButtonFilterType, "filter", filterObj);
    }
}

const filterOptions = async (data) => {
    const {
        newChecked,
        chartMainDataArr = [],
        latestButtonFilterType = null,
        filterObj = null,
        filterType = "initial",
        originalData,
        filters
    } = data;

    if (filters.type === DECEASED_CARDS_TYPE.NINETY_DAYS_DATA) {
        let ninetyDaysDataIds = [];
        let dataOriginal = filterObj?.filterData ? filterObj.filterData : filterData;
        if (filterType === "reload") {
            dataOriginal = originalData.ninetyDaysData
        }
        let ninetyDaysDataFilter = await _.filter(dataOriginal, async ({ _id }) =>
            await _.every([_.includes(newChecked, _id)])
        );
        if (ninetyDaysDataFilter && ninetyDaysDataFilter.length > 0) {
            await ninetyDaysDataFilter.map(
                (item) => {
                    ninetyDaysDataIds = [...ninetyDaysDataIds, ...item.ids]
                }
            );
        }
        let newChartFilters = await _.filter(chartMainDataArr, async ({ _id }) =>
            await _.every([_.includes(ninetyDaysDataIds, _id)])
        );
        return await updateChartArrData(newChartFilters, latestButtonFilterType, "filter", filterObj);
    } else {
        const question = filters?.question;
        let newChartFilters = [];
        const isTotalCard = filters.type === HOSPITAL_CARDS_TYPE.TOTAL ||
            filters.type === CO_TRANSFER_CARDS_TYPE.SAFE_DISCHARGE ||
            filters.type === CO_TRANSFER_CARDS_TYPE.AMA ||
            filters.type === CO_TRANSFER_CARDS_TYPE.SNF ||
            filterObj?.question?.customTab?.type === CUSTOM_TAB_TYPE.combineTab;

        if (!isTotalCard) {
            if (question?.isCustom) {
                newChartFilters = await processCustomChartFilters(
                    chartMainDataArr,
                    filters,
                    newChecked
                );
            } else {
                newChartFilters = await _.filter(chartMainDataArr, async ({ filterId }) => {
                    return await _.every([_.includes(newChecked, filterId)]);
                });
            }
        } else {
            newChartFilters = chartMainDataArr;
        }

        return await updateChartArrData(newChartFilters, latestButtonFilterType, "filter", filterObj);
    }
}

const admissionFilterOptions = async (data) => {
    const {
        newChecked,
        mainData,
        latestButtonFilterType = null,
        filterObj,
        filters,
        defaultPatientData
    } = data

    if (filters.type === HOSPITAL_CARDS_TYPE.DX_DATA) {
        let newFilters = await _.filter(defaultPatientData, ({ dxIds }) => {
            const matchedIds = _.intersection(newChecked, dxIds);
            if (matchedIds.length > 0) {
                return true;
            } else {
                return false;
            }
        });
        //await setPatientData(newFilters);
        let newChartFilters = await _.filter(mainData, ({ dxIds }) => {
            const matchedIds = _.intersection(newChecked, dxIds);
            if (matchedIds.length > 0) {
                return true;
            } else {
                return false;
            }
        });
        return await updateChartArrData(newChartFilters, latestButtonFilterType, "filter", filterObj);
    } else if (filters.type === HOSPITAL_CARDS_TYPE.NINETY_DAYS_DATA) {
        let ninetyDaysDataIds = [];
        let ninetyDaysDataFilter = _.filter(data, ({ _id }) =>
            _.every([_.includes(newChecked, _id)])
        );
        if (ninetyDaysDataFilter && ninetyDaysDataFilter.length > 0) {
            ninetyDaysDataFilter.map(
                (item) => (ninetyDaysDataIds = [...ninetyDaysDataIds, ...item.ids])
            );
        }
        let newFilters = _.filter(defaultPatientData, ({ _id }) =>
            _.every([_.includes(ninetyDaysDataIds, _id)])
        );
        //await setPatientData(newFilters);
        let newChartFilters = _.filter(mainData, ({ _id }) =>
            _.every([_.includes(ninetyDaysDataIds, _id)])
        );
        return await updateChartArrData(newChartFilters, latestButtonFilterType, "filter", filterObj);
    } else if (filters.type === HOSPITAL_CARDS_TYPE.DAYS_DATA) {
        let newFilters = _.filter(defaultPatientData, ({ day }) => {
            return _.every([_.includes(newChecked, day)]);
        });
        //await setPatientData(newFilters);

        let newChartFilters = _.filter(mainData, ({ day }) => {
            return _.every([_.includes(newChecked, day)]);
        });
        return await updateChartArrData(newChartFilters, latestButtonFilterType, "filter", filterObj);
    } else if (filters.type === HOSPITAL_CARDS_TYPE.SHIFT_DATA) {
        let newFilters = _.filter(defaultPatientData, ({ shiftName }) => {
            return _.every([_.includes(newChecked, shiftName)]);
        });
        //await setPatientData(newFilters);
        let newChartFilters = _.filter(mainData, ({ shiftName }) => {
            return _.every([_.includes(newChecked, shiftName)]);
        });
        return await updateChartArrData(newChartFilters, latestButtonFilterType, "filter", filterObj);
    } else if (filters.type === HOSPITAL_CARDS_TYPE.HOSPITALIZATIONS) {
        if (newChecked.length === 1) {
            let filter = Object();
            if (newChecked[0] == "newHospitalizations") {
                filter.reHospitalization = false;
            }
            if (newChecked[0] == "reHospitalizations") {
                filter.reHospitalization = true;
            }
            let newFilters = _.filter(defaultPatientData, filter);
            //await setPatientData(newFilters);
            let newChartFilters = _.filter(mainData, filter);

            return await updateChartArrData(newChartFilters, latestButtonFilterType, "filter", filterObj);
        } else {
            //await setPatientData(defaultPatientData);
            return await updateChartArrData(mainData, latestButtonFilterType, "filter", filterObj);
        }
    } else if (filters.type === HOSPITAL_CARDS_TYPE.DCER_DATA) {
        if (newChecked.length === 1) {
            let filter = Object();
            if (newChecked[0] === "DC") {
                filter.wasAdmitted = true;
            }
            if (newChecked[0] === "ER") {
                filter.wasAdmitted = false;
            }
            let newFilters = _.filter(defaultPatientData, filter);
            //await setPatientData(newFilters);
            let newChartFilters = _.filter(mainData, filter);
            return await updateChartArrData(newChartFilters, latestButtonFilterType, "filter", filterObj);
        } else {
            await setPatientData(defaultPatientData);
            await updateChartArrData(mainData, latestButtonFilterType, "filter", filterObj);
        }
    } else if (filters.type === HOSPITAL_CARDS_TYPE.RETURNS_DATA) {
        let filter = Object();
        if (newChecked.length === 1) {
            if (newChecked[0] === "Returned") {
                filter.wasReturned = true;
            }
            if (newChecked[0] === "Didn't Return") {
                filter.wasReturned = false;
            }
            let newFilters = _.filter(defaultPatientData, filter);
            //await setPatientData(newFilters);
            let newChartFilters = _.filter(mainData, filter);
            return await updateChartArrData(newChartFilters, latestButtonFilterType, "filter", filterObj);
        } else {
            //await setPatientData(defaultPatientData);
            return await updateChartArrData(mainData, latestButtonFilterType, "filter", filterObj);
        }
    } else {
        let newFilters = _.filter(defaultPatientData, ({ filterId }) => {
            return _.every([_.includes(newChecked, filterId)]);
        });
        // await setPatientData(newFilters);

        let newChartFilters = _.filter(mainData, ({ filterId }) => {
            return _.every([_.includes(newChecked, filterId)]);
        });
        return await updateChartArrData(newChartFilters, latestButtonFilterType, "filter", filterObj);
    }
}

const updateADTChartFilter = async (cardFilter, selectedCardItem) => {
    let latestCardFilter = cardFilter;
    const selectedPriority = _.find(cardFilter.mainPriorityData, {
        childId: selectedCardItem.childId,
        cardId: selectedCardItem.cardId,
    });
    if (selectedPriority) {
        const idx = cardFilter.mainPriorityData.findIndex(
            (p) => p.childId === selectedCardItem.childId && p.cardId === selectedCardItem.cardId
        );
        let selectedCardPriority = cardFilter.mainPriorityData.slice(0, idx);
        let updatedFilter = Object();
        for await (const [key, value] of Object.entries(cardFilter)) {
            if (key == "adtAdmitPatientIds") {
                updatedFilter[key] = selectedCardPriority;
            } else {
                updatedFilter[key] = value;
            }
        }
        latestCardFilter = updatedFilter;
    }
    return latestCardFilter;
};

const updateAdmissionChartFilter = async (cardFilter, type) => {
    let latestCardFilter = cardFilter;
    const selectedPriority = _.find(cardFilter.mainPriorityData, { type: type });
    if (selectedPriority) {
        let idx = cardFilter.mainPriorityData.findIndex((elem) => elem.type === type);
        if (idx !== -1) {
            let selectedCardPriority = [];
            let selectedFilters = [];
            let updatedFilter = Object();
            if (cardFilter.mainPriorityData.length > 1) {
                selectedCardPriority = cardFilter.mainPriorityData.slice(0, idx);
                selectedFilters = _.map(selectedCardPriority, "type");
                let alreadySaved = false;
                selectedCardPriority
                    .slice(0)
                    .reverse()
                    .map((element) => {
                        if (element.filterType === ADMISSION_FILTER_TYPE.ADT && !alreadySaved) {
                            alreadySaved = true;
                            updatedFilter["admissionsIds"] = element.ids;
                            updatedFilter["ADTFilter"] = true;
                        }
                    });
            }
            for await (const [key, value] of Object.entries(cardFilter)) {
                if (_.includes(selectedFilters, key)) {
                    updatedFilter[key] = value;
                } else {
                    updatedFilter[key] = [];
                }
            }
            latestCardFilter = updatedFilter;
        }
    } else {
        if (cardFilter.mainPriorityData.length > 0) {
            let adtPriorityData = _.filter(cardFilter.mainPriorityData, { filterType: ADMISSION_FILTER_TYPE.ADT });
            if (adtPriorityData.length > 0) {
                let lastElement = adtPriorityData.slice(-1);
                if (lastElement.length > 0) {
                    latestCardFilter = Object.assign({}, latestCardFilter, {
                        admissionsIds: lastElement[0]["ids"],
                        ADTFilter: true,
                    });
                }
            }
        }
    }
    return latestCardFilter
};

module.exports = {
    updateAdmissionChartFilter,
    updateADTChartFilter,
    ADTfilterOptions,
    admissionFilterOptions,
    hospitalFilterOptions,
    filterOptions,
    chartsData,
    chartGroupBy,
    overAllFilterOptions
};
