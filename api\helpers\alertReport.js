const mongoose = require("mongoose");
const AlertReport = mongoose.model("alertReport");
const slugify = require('slugify');

function createSlug(name) {
    const slug = slugify(name, {
        lower: true, // Converts to lowercase
        strict: true, // Removes special characters
    });
    return `${slug}_custom`;
}

const generateUniqueSlug = (slug, existingAlerts) => {
    let uniqueSlug = slug;
    let counter = 1;

    // Check if the slug already exists in customAlert
    const slugExists = (checkSlug) =>
        existingAlerts.some(alert => alert.slug === checkSlug);

    while (slugExists(uniqueSlug)) {
        uniqueSlug = `${slug}_${counter}`; // Append a counter to the slug
        counter++;
    }

    return uniqueSlug;
};

const createOrUpdateCustomOne = async (req, res) => {
    try {
        const { user } = req;
        const { facilityIds = [], userId, id, name, filter, page, pageType, alerts, type, isRising, isDropping, isTransferNeedWork } = req.body;
        const { accountid } = req.headers;

        if (!facilityIds || facilityIds.length === 0) {
            return res.status(400).json({ message: 'Missing facilityIds in request body' });
        }

        const accountId = req.body.accountId
            ? mongoose.Types.ObjectId(req.body.accountId)
            : mongoose.Types.ObjectId(accountid);

        const slug = createSlug(name);

        // Create a new custom alert object
        const newCustomAlert = {
            name,
            filter,
            cardFilter: filter?.cardFilter,
            transferType: filter?.transferType,
            alertType: filter?.alertType ?? "main",
            alertTypeId: filter?.alertTypeId ?? null,
            refSlug: filter?.refSlug ?? null,
        };

        let savedReports;

        if (id) {
            // Update existing report
            const existingReport = await AlertReport.findById(id);

            if (existingReport) {
                newCustomAlert.slug = generateUniqueSlug(slug, existingReport.customAlert);

                existingReport.type = type;
                existingReport.isRising = isRising ?? false;
                existingReport.isDropping = isDropping ?? false;
                existingReport.isTransferNeedWork = isTransferNeedWork ?? false;
                existingReport.alerts = { ...existingReport.alerts, [slug]: true };

                // Initialize __v if null
                if (existingReport.__v === null) {
                    existingReport.__v = 0;
                }

                // Add or initialize custom alerts
                if (existingReport.customAlert?.length > 0) {
                    existingReport.customAlert.push(newCustomAlert);
                } else {
                    existingReport.customAlert = [newCustomAlert];
                }

                savedReports = await existingReport.save();
            } else {
                return res.status(404).json({ message: 'Report not found' });
            }
        } else {
            // Create new reports for each facility
            newCustomAlert.slug = generateUniqueSlug(slug, []);

            const promises = facilityIds.map(async (facilityId) => {
                const newAlertReport = new AlertReport({
                    facilityId: mongoose.Types.ObjectId(facilityId),
                    accountId,
                    userId: userId || user._id,
                    page: page || pageType,
                    alerts: { ...alerts, [slug]: true },
                    type,
                    isRising: isRising ?? false,
                    isDropping: isDropping ?? false,
                    isTransferNeedWork: isTransferNeedWork ?? false,
                    customAlert: [newCustomAlert],
                });

                return await newAlertReport.save();
            });

            savedReports = await Promise.all(promises);
        }

        return savedReports;
    } catch (error) {
        console.error('Error creating/updating report:', error);
        return res.status(500).json({ message: 'An error occurred while creating or updating the report', error });
    }
};


// Create or update an alert report
const createOrUpdateOne = async (req, res) => {
    const { user } = req;
    const { facilityIds = [], userId } = req.body;
    const { accountid } = req.headers;

    if (!facilityIds) return { status: 400, message: 'Missing facilityIds in request body' };
    let savedReports
    try {
        const accountId = req.body && req.body.accountId ? mongoose.Types.ObjectId(req.body.accountId) : mongoose.Types.ObjectId(accountid);
        // Check if the alert report already exists
        await Promise.all(facilityIds.map(async facilityId => {
            const existingReport = await AlertReport.findOne({
                accountId,
                facilityId: mongoose.Types.ObjectId(facilityId),
                userId: userId ? userId : user._id,
                page: req.body.page,
            });

            if (existingReport) {
                // Update existing report
                existingReport.alerts = req?.body?.alerts;
                existingReport.type = req?.body?.type;
                existingReport.isRising = req?.body?.isRising ?? false;
                existingReport.isDropping = req?.body?.isDropping ?? false;
                existingReport.isTransferNeedWork = req?.body?.isTransferNeedWork ?? false;
                await existingReport.save();
                savedReports = existingReport
                return existingReport;
            } else {
                // Create new report
                const newAlertReport = new AlertReport({
                    facilityId: mongoose.Types.ObjectId(facilityId),
                    accountId,
                    userId: userId ? userId : user._id,
                    page: req.body.page || req.body.pageType,
                    alerts: req?.body?.alerts,
                    type: req?.body?.type,
                    isRising: req?.body?.isRising ?? false,
                    isDropping: req?.body?.isDropping ?? false,
                    isTransferNeedWork: req?.body?.isTransferNeedWork ?? false,
                });
                savedReports = await newAlertReport.save();
            }
        }));
        return savedReports;
    } catch (error) {
        console.error("Error in createOrUpdateOne:", error);
        return error
    }
};

// Fetch all alert reports based on query
const getAll = async (req) => {
    const { accountid, facilityid } = req.headers;
    const { page, facilityId, userId } = req.query;
    const { user } = req;
    try {
        const filters = await AlertReport.findOne({
            accountId: req.query && req.query.accountId ? mongoose.Types.ObjectId(req.query.accountId) : mongoose.Types.ObjectId(accountid),
            facilityId: facilityId ? mongoose.Types.ObjectId(facilityId) : mongoose.Types.ObjectId(facilityid),
            userId: userId ? mongoose.Types.ObjectId(userId) : mongoose.Types.ObjectId(user._id),
            page,
        });
        return filters
    } catch (error) {
        console.error("Error in getAll:", error);
        return error.message
    }
};

// Fetch a specific alert report by ID
const getSetting = async (req, res) => {
    const { id } = req.query;

    try {
        const filter = await AlertReport.findOne({ _id: mongoose.Types.ObjectId(id) });

        if (filter) {
            return res.status(200).json({ success: true, data: filter });
        } else {
            return res.status(404).json({ success: false, message: "Alert report not found" });
        }
    } catch (error) {
        console.error("Error in getSetting:", error);
        return res.status(500).json({ success: false, message: "Server error", error: error.message });
    }
};

// Delete a specific alert report by ID
const deleteOne = async (req, res) => {
    const { id } = req.params;

    try {
        const deletedReport = await AlertReport.findByIdAndDelete(id);

        if (deletedReport) {
            return res.status(200).json({ success: true, message: "Alert report deleted", data: deletedReport });
        } else {
            return res.status(404).json({ success: false, message: "Alert report not found" });
        }
    } catch (error) {
        console.error("Error in deleteOne:", error);
        return res.status(500).json({ success: false, message: "Server error", error: error.message });
    }
};

// Delete custom alert report by ID
const deleteCustomAlert = async (req, res) => {
    const { id } = req.params;
    const { slug, selectedAlertId } = req.query;
    if (slug && selectedAlertId) {
        try {
            // Step 1: Find the document by selectedAlertId
            const alertReport = await AlertReport.findById(selectedAlertId);

            // Step 2: Check if the slug exists in customAlert array
            const alertExists = alertReport.customAlert.some(alert => alert.slug === slug);

            if (alertExists) {
                // Step 3: Remove the customAlert item by slug
                await AlertReport.updateOne(
                    { _id: selectedAlertId },
                    {
                        $pull: {
                            customAlert: { slug: slug } // Remove item with matching slug
                        },
                        $set: {
                            updatedAt: new Date() // Update the timestamp
                        }
                    }
                );
                console.log(`Custom alert with slug '${slug}' removed.`);
            } else {
                console.log("No matching custom alert found.");
            }
        } catch (error) {
            console.error("Error while processing custom alert:", error);
            res.status(500).json({ error: "Internal server error" });
        }
    }
    return res.status(200).json({ success: true, message: "Alert report deleted", data: null });
    // try {
    //     const deletedReport = await AlertReport.findByIdAndDelete(id);

    //     if (deletedReport) {
    //         return res.status(200).json({ success: true, message: "Alert report deleted", data: deletedReport });
    //     } else {
    //         return res.status(404).json({ success: false, message: "Alert report not found" });
    //     }
    // } catch (error) {
    //     console.error("Error in deleteOne:", error);
    //     return res.status(500).json({ success: false, message: "Server error", error: error.message });
    // }
};

module.exports = {
    createOrUpdateOne,
    getAll,
    getSetting,
    deleteOne,
    createOrUpdateCustomOne,
    deleteCustomAlert
};
