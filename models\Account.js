const mongoose = require("mongoose");
const { PAGE_TYPE, ACCOUNT_PERCENTAGE_BY } = require("../types/common.type");
const { Schema, Types } = mongoose;

const AccountSchema = new Schema(
  {
    name: String,
    active: Boolean,
    type: String,
    createdBy: { type: Types.ObjectId, ref: "user" },
    dashboardAccess: [{
      type: String,
      enum: [PAGE_TYPE.ADMISSION, PAGE_TYPE.HOSPITAL, PAGE_TYPE.COMMUNITY_TRANSFER, PAGE_TYPE.DECEASED, PAGE_TYPE.OVERALL],
      require: false
    }],
    percentageBy : {
      type: String,
      enum: [ACCOUNT_PERCENTAGE_BY.BED, ACCOUNT_PERCENTAGE_BY.CENSUS],
      require: false,
      default: ACCOUNT_PERCENTAGE_BY.CENSUS
    }
  },
  { timestamps: true }
);

mongoose.model("account", AccountSchema);
