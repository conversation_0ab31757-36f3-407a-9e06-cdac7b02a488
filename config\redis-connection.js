
const IORedis = require('ioredis');

const REDIS_CONNECTOR = {
  host: process.env.REDIS_HOST || '127.0.0.1',
  port: process.env.REDIS_PORT || 6379,
  maxRetriesPerRequest: null, // ✅ Required for BullMQ
  enableReadyCheck: false,    // ✅ Helps avoid unnecessary delays
  retryDelayOnFailover: 100,
  maxRetriesPerRequest: 3,
  lazyConnect: true, // Don't connect immediately
  connectTimeout: 5000, // 5 second timeout
  commandTimeout: 5000, // 5 second command timeout
};

const REDIS_DEFAULT_REMOVE_CONFIG = {
  removeOnComplete: true,
  removeOnFail: {
    age: 24 * 3600,
  },
};

const createRedisConnection = () => {
  const redis = new IORedis(REDIS_CONNECTOR);

  // Handle connection errors gracefully
  redis.on('error', (error) => {
    if (error.code === 'ECONNREFUSED') {
      console.warn('⚠️  Redis connection refused - Redis server may not be running');
      console.warn('🔧 Please start Redis server or check connection settings');
    } else {
      console.warn('⚠️  Redis connection error:', error.message);
    }
    // Don't throw or crash - just log the warning
  });

  redis.on('connect', () => {
    console.log('✅ Redis connected successfully');
  });

  redis.on('ready', () => {
    console.log('🚀 Redis ready for operations');
  });

  redis.on('close', () => {
    console.warn('⚠️  Redis connection closed');
  });

  redis.on('reconnecting', () => {
    console.log('🔄 Redis reconnecting...');
  });

  return redis;
};


module.exports = { REDIS_CONNECTOR, REDIS_DEFAULT_REMOVE_CONFIG, createRedisConnection };