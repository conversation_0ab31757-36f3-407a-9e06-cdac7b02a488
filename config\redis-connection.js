
const IORedis = require('ioredis');

const REDIS_CONNECTOR = {
  host: process.env.REDIS_HOST,
  port: process.env.REDIS_PORT,
  maxRetriesPerRequest: null, // ✅ Required for BullMQ
  enableReadyCheck: false,    // ✅ Helps avoid unnecessary delays
};

const REDIS_DEFAULT_REMOVE_CONFIG = {
  removeOnComplete: true,
  removeOnFail: {
    age: 24 * 3600,
  },
};

const createRedisConnection = () => new IORedis(REDIS_CONNECTOR);


module.exports = { REDIS_CONNECTOR, REDIS_DEFAULT_REMOVE_CONFIG, createRedisConnection };