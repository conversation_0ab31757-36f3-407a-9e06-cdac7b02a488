const { Queue } = require('bullmq');
const configModule = require('../../config/redis-connection');
const setUpWorker = require('./worker');



const myQueue = new Queue('REPORTS', {
    connection: configModule.createRedisConnection(),
    ...configModule.REDIS_DEFAULT_REMOVE_CONFIG,
});

myQueue.setMaxListeners(200); // Increase the limit

myQueue.LOCK_RENEW_TIME = 60 * 2000;

myQueue.setMaxListeners(myQueue.getMaxListeners() + 100);

setUpWorker();

const addJobToQueue = (data) => {
    console.log(`📌 Adding job: ${data.name} ${data?._id}`);
    return myQueue.add(
        data.jobName,
        data,
        {
            removeOnComplete: true,
            removeOnFail: false,  // Keep failed jobs for debugging
            attempts: 3,          // Retry job 3 times if it fails
            backoff: {
                type: "exponential",
                delay: 5000, // Wait 5s before retry
            },
            maxStalledCount: 10,   // Increase stall limit
        }
    );
};

module.exports = addJobToQueue;