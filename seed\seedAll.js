const mongoose = require('mongoose');
const readline = require('readline');
const Role = require("../models/Role");
require("../models/User");
require("../models/Account");
require("../models/PercentageAgainst");
require("../models/Log");
require("../models/QuestionOrder");
require("../models/Question");
require("../models/Facility");
require("../models/Validation");
const User = mongoose.model("user");
const { seedQuestions } = require('./new/question.seed');
const { seedRoles } = require('./new/role.seed');
const { seedAccount } = require('./new/account.seed');
const { seedFacility } = require('./new/facility.seed');
const { createAdmin } = require('./new/user.seed');
const { seedQuestionsOrders } = require('./new/questionOrder.seed');
const { seedValidations } = require('./new/validation.seed');

const promptUser = (query) => {
    return new Promise((resolve) => {
        const rl = readline.createInterface({
            input: process.stdin,
            output: process.stdout
        });

        rl.question(query, (answer) => {
            rl.close();
            resolve(answer.trim());
        });
    });
};

const seedDatabase = async () => {
    try {
        const mongoURL = await promptUser("📝 Enter MongoDB URI: ");
        const seedEmail = await promptUser("📧 Enter Seed Email: ");
        const seedPassword = await promptUser("🔒 Enter Seed Password: ");
        const accountName = await promptUser("🏢 Enter Account Name: ");
        const facilityName = await promptUser("🏢 Enter Facility Name: ");
        
        let percentageBy;
        while (true) {
            percentageBy = await promptUser("📊 Enter Percentage By (census/bed): ");
            if (percentageBy.toLowerCase() === 'census' || percentageBy.toLowerCase() === 'bed') {
                percentageBy = percentageBy.toLowerCase();
                break;
            } else {
                console.log("❌ Invalid input. Please enter 'census' or 'bed'.");
            }
        }

        if (!mongoURL || !seedEmail || !seedPassword || !accountName || !percentageBy || !facilityName) {
            console.error("❌ All inputs are required. Exiting.");
            process.exit(1);
        }

        console.log("\n🔗 Connecting to MongoDB...");
        await mongoose.connect(mongoURL, {
            useNewUrlParser: true,
            useUnifiedTopology: true,
        });
        console.log('✅ Connected to MongoDB.');

        const existingRoles = await Role.find({});
        const existingAdmins = await User.find({ type: 'admin' });

        if (existingRoles.length > 0 || existingAdmins.length > 0) {
            console.log("⚠️ Database is not empty. Cannot continue with seed process.");
            await mongoose.connection.close();
            process.exit(0);
        }

        const roles = await seedRoles();
        const admin = await createAdmin(seedEmail, seedPassword, roles);
        const account = await seedAccount(accountName, percentageBy, admin._id);
        const facility = await seedFacility(facilityName, account._id);
        await seedQuestions(facility._id);
        await seedValidations(facility._id);
        await seedQuestionsOrders(account._id);

        admin.accounts.push({
            accountId: account._id,
            access: true
        });

        await admin.save();
        console.log('✅ Admin user assigned to the created account.');

        await mongoose.connection.close();
        console.log("🎉 Seeding process completed successfully.");
        process.exit(0);
    } catch (err) {
        console.error('❌ Error during seeding process:', err.message);
        await mongoose.connection.close();
        process.exit(1);
    }
};

seedDatabase();
