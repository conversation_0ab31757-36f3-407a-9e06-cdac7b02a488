import { <PERSON>rid, Stack, Divider, Typography, Box } from "@mui/material";
import classNames from "classnames";
import { useMemo } from "react";
import { calcProPercentsBasedOnFilterAndDays } from "../../../../utilis/common";
import { CUSTOM_TAB_PAGE_TYPE, PAGE_TYPE } from "../../../../types/pages.type";
import styles from "./DynamicDataCardTotalCard.module.scss";

// Utility: Map PAGE_TYPE to styles
const getPageTypeStyle = (type) => {
    const map = {
        [PAGE_TYPE.HOSPITAL]: styles.gradientTextPrimaryHospital,
        [PAGE_TYPE.COMMUNITY_TRANSFER]: styles.gradientTextPrimaryCommunityTransfer,
        [PAGE_TYPE.DECEASED]: styles.gradientTextPrimaryDeceased,
        [PAGE_TYPE.ADMISSION]: styles.gradientTextPrimaryAdmission,
        [PAGE_TYPE.OVERALL]: styles.gradientTextPrimaryOverall,
        [PAGE_TYPE.ADT]: styles.gradientTextPrimaryAdt,
    };
    return map[type] || styles.gradientTextPrimary;
};

const getBorderStyle = (type) => {
    const map = {
        [PAGE_TYPE.HOSPITAL]: styles.borderHospital,
        [PAGE_TYPE.COMMUNITY_TRANSFER]: styles.borderCommunityTransfer,
        [PAGE_TYPE.DECEASED]: styles.borderDeceased,
        [PAGE_TYPE.ADMISSION]: styles.borderAdmission,
        [PAGE_TYPE.OVERALL]: styles.borderOverall,
        [PAGE_TYPE.ADT]: styles.borderAdt,
    };
    return map[type] || null;
};

const SubTotalCardItem = ({
    title,
    total,
    percentage,
    loading,
    borderColorStyle,
    page,
    handleCardClick,
    isSelectedTotalCard,
    averageCensus,
    filter,
    projectionDays,
    isComparingAgainstAvgCensus,
    lockedTotalBy,
    transferType,
    isFirstItemInPriorityData,
    isFilterSelected
}) => {
    const isMatching = title === page || (page === PAGE_TYPE.OVERALL && (title === CUSTOM_TAB_PAGE_TYPE.OVERALL_INCOMING || title === CUSTOM_TAB_PAGE_TYPE.OVERALL_OUTGOING));
    const isSelected = isSelectedTotalCard && isMatching;

    // Calculate percentage for sub-total item
    const percentageTotal = useMemo(() => calcProPercentsBasedOnFilterAndDays(
        percentage,
        { ...filter },
        projectionDays,
        true,
        isComparingAgainstAvgCensus ||
        lockedTotalBy === "census" ||
        (isFirstItemInPriorityData && (!transferType || transferType.length === 0))
    ), [percentage, filter, projectionDays, isComparingAgainstAvgCensus, lockedTotalBy, isFirstItemInPriorityData, transferType]);

    return (
        <Stack
            sx={{
                width: { xs: "100%", sm: "98%", md: "95%" },
                borderRadius: "8px",
                cursor: isMatching ? "pointer" : "default",
                position: "relative",
                transition: "all 0.3s ease",
                minHeight: { xs: "60px", sm: "65px", md: "70px" },
                padding: { xs: "8px 4px", sm: "10px 6px", md: "12px 8px" }
            }}
            className={loading ? "skeleton" : undefined}
        >
            {/* Current Page Badge - Top Right */}
            {isMatching && (
                <div style={{
                    position: 'absolute',
                    top: '8px',
                    right: '8px',
                    background: 'linear-gradient(135deg, #1976d2 0%, #42a5f5 100%)',
                    color: 'white',
                    padding: '4px 12px',
                    borderRadius: '16px',
                    fontSize: '9px',
                    fontWeight: '700',
                    zIndex: 2,
                    boxShadow: '0 3px 8px rgba(25, 118, 210, 0.4)',
                    letterSpacing: '0.8px',
                    transform: 'translateY(-50%)',
                    transition: 'all 0.2s ease'
                }}>
                    CURRENT
                </div>
            )}
            
            {/* Selected Checkmark - Left Center */}
            {isSelected && (
                <div style={{
                    position: 'absolute',
                    top: '50%',
                    left: '20px',
                    color: '#1976d2',
                    fontSize: '24px',
                    fontWeight: '900',
                    zIndex: 2,
                    transform: 'translateY(-50%)',
                    transition: 'all 0.2s ease',
                    textShadow: '0 2px 4px rgba(25, 118, 210, 0.3)'
                }}>
                    ✓
                </div>
            )}

            <div
                className={classNames(
                    styles.projectionContainer,
                    styles.withSubTotalCard,
                    loading && styles.skeleton,
                    // Apply page-specific border color only when matching
                    isMatching && borderColorStyle,
                    isSelected && styles.selectedCard
                )}
                style={
                    isSelected ? {
                        boxShadow: '0 4px 20px rgba(25, 118, 210, 0.25)',
                        transform: 'scale(1.02) translateY(-2px)',
                        borderWidth: '2px',
                        backgroundColor: 'rgba(25, 118, 210, 0.04)',
                        borderRadius: '8px',
                        transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)'
                    } : isMatching ? {
                        // Matching cards use page-specific border color from CSS
                        borderWidth: '1px',
                        backgroundColor: 'rgba(25, 118, 210, 0.02)',
                        boxShadow: '0 2px 8px rgba(0, 0, 0, 0.05)',
                        position: 'relative',
                        overflow: 'visible',
                        borderRadius: '8px',
                        transition: 'all 0.3s ease'
                    } : {
                        // Non-matching cards get grey border
                        border: '1px solid rgba(0, 0, 0, 0.08)',
                        backgroundColor: '#fafafa',
                        borderRadius: '8px',
                        transition: 'all 0.3s ease'
                    }
                }
                onClick={isMatching ? handleCardClick : undefined}
                onMouseEnter={(e) => {
                    if (isMatching && !loading) {
                        e.currentTarget.style.transform = 'scale(1.01) translateY(-0.5px)';
                        e.currentTarget.style.boxShadow = '0 2px 8px rgba(25, 118, 210, 0.2)';
                    }
                }}
                onMouseLeave={(e) => {
                    if (isMatching && !loading) {
                        e.currentTarget.style.transform = isSelected ? 'scale(1.02) translateY(-2px)' : 'scale(1) translateY(0)';
                        e.currentTarget.style.boxShadow = isSelected ? '0 4px 20px rgba(25, 118, 210, 0.25)' : '0 2px 8px rgba(0, 0, 0, 0.05)';
                    }
                }}
            >
                <Box
                    sx={{
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        gap: '6px',
                        marginBottom: '4px'
                    }}
                >
                    <Typography
                        variant="subtitle2"
                        className={classNames(
                            styles.projectionTitle,
                            isSelected && styles.selectedTitle
                        )}
                        sx={{
                            ...isMatching && !isSelected ? {
                                color: '#1976d2',
                                fontWeight: 600
                            } : {
                                color: '#424242',
                                fontWeight: 500
                            },
                            fontSize: { xs: '11px', sm: '12px', md: '13px' },
                            letterSpacing: '0.3px',
                            wordWrap: 'break-word',
                            overflowWrap: 'break-word',
                            hyphens: 'auto',
                            lineHeight: { xs: 1.2, sm: 1.3, md: 1.4 },
                            textAlign: 'center'
                        }}
                    >
                        {loading ? (
                            <span className="skeleton-text" style={{ visibility: "hidden" }}>{title}</span>
                        ) : (
                            title.charAt(0).toUpperCase() + title.slice(1)
                        )}
                    </Typography>


                </Box>
                <Stack alignItems="center" spacing={0.5}>
                    <Typography
                        variant="body2"
                        className={classNames(
                            styles.projectionSubTitle,
                            isSelected && styles.selectedTotal
                        )}
                        sx={{
                            ...isMatching ? {
                                color: '#1976d2',
                                fontWeight: 700,
                                fontSize: { xs: '16px', sm: '18px', md: '20px' }
                            } : {
                                color: '#212121',
                                fontWeight: 600,
                                fontSize: { xs: '14px', sm: '16px', md: '18px' }
                            },
                            lineHeight: 1,
                            textAlign: 'center'
                        }}
                    >
                        {loading ? (
                            <span className="skeleton-text" style={{ visibility: "hidden" }}>{total}</span>
                        ) : total}
                    </Typography>
                    {(!isFilterSelected || title === page) && (
                        <Typography
                            variant="caption"
                            sx={{
                                fontSize: { xs: '9px', sm: '10px', md: '11px' },
                                color: isMatching ? '#1976d2' : '#757575',
                                opacity: isMatching ? 1 : 0.8,
                                fontWeight: isMatching ? 500 : 400,
                                textAlign: 'center'
                            }}
                        >
                            {loading ? (
                                <span className="skeleton-text" style={{ visibility: "hidden" }}>(0.0%)</span>
                            ) : `(${percentageTotal}%)`}
                        </Typography>
                    )}
                </Stack>
            </div>
        </Stack>
    );
};

const DynamicDataCardTotalCard = ({
    loading = false,
    total = 0,
    averageCensus = 0,
    filter = "",
    projectionDays = 1,
    question,
    data,
    subTitleText = "",
    filterTotal = 0,
    page,
    handleCardClick,
    isSelectedTotalCard,
    priorityNumber,
    lockedTotalBy,
    isComparingAgainstAvgCensus,
    transferType,
    cardFilter,
    isDisabled = false
}) => {

    const isFilterSelected = useMemo(() => {
        return cardFilter?.priorityData?.length > 0 || (transferType != null && transferType?.length > 0);
    }, [cardFilter, transferType]);

    const cardItem = useMemo(() => data?.find((item) => item?.id === question?.accessor), [data, question]);
    const totalValue = useMemo(() => calcProPercentsBasedOnFilterAndDays(cardItem?.total || 0, filter, projectionDays), [cardItem, filter, projectionDays]);
    const isFirstItemInPriorityData = priorityNumber === 1;
    
    const percentage = useMemo(() => calcProPercentsBasedOnFilterAndDays(
        cardItem?.percentage,
        { ...filter },
        projectionDays,
        true,
        isComparingAgainstAvgCensus ||
        lockedTotalBy === "census" ||
        (isFirstItemInPriorityData && (!transferType || transferType.length === 0))
    ), [cardItem?.percentage, filter, projectionDays, isComparingAgainstAvgCensus, lockedTotalBy, isFirstItemInPriorityData, transferType]);

    const textColorStyle = useMemo(() => getPageTypeStyle(cardItem?.parentCard), [cardItem]);
    const borderColorStyle = useMemo(() => getBorderStyle(cardItem?.parentCard), [cardItem]);

    const subTotalItems = useMemo(() => (
        Object.entries(cardItem?.totalByPage || {}).map(([key, value]) => {
            const percentage = calcProPercentsBasedOnFilterAndDays(value?.percentage || 0, filter, projectionDays);
            return {
                key,
                total: calcProPercentsBasedOnFilterAndDays(value?.total || 0, filter, projectionDays),
                percentage
            }
        })
    ), [cardItem, filter, projectionDays]);

    if (isDisabled) {
        return null;
    }
    return (
        <div className={classNames("df aic p-t-20 p-b-25", styles.totalOverall, styles.withSubTotalCardMain)}
            style={{
                background: 'linear-gradient(to bottom, #ffffff 0%, #fafafa 100%)',
                borderRadius: '12px',
                padding: '24px',
                boxShadow: '0 2px 12px rgba(0, 0, 0, 0.04)'
            }}
        >
            <Grid container spacing={{ xs: 1, sm: 2, md: 3 }} sx={{ flexWrap: 'nowrap' }}>
                {/* Total Section */}
                <Grid item xs={5.5} sm={6} md={6}>
                    <Stack alignItems="center" justifyContent="center" sx={{ height: '100%' }}>
                        <Box
                            className={classNames("ffmar-bold", "df", "aic", "fw700", "fs49", styles.withSubTotalCard, loading && styles.skeleton, textColorStyle)}
                            sx={{
                                fontSize: { xs: '36px', sm: '42px', md: '48px' },
                                letterSpacing: '-1px',
                                marginBottom: '8px'
                            }}
                        >
                            {loading ? <span className="skeleton-text">0</span> : totalValue}
                        </Box>
                        {percentage && !isFilterSelected && (
                            <Box className={classNames(styles.textNumberContainer, styles.textNumberSubContainer)}>
                                <Box
                                    className={classNames(
                                        textColorStyle,
                                        loading && styles.skeleton,
                                        loading && "skeleton-text"
                                    )}
                                    sx={{
                                        fontSize: { xs: '14px', sm: '15px', md: '16px' },
                                        fontWeight: 600,
                                        marginBottom: '4px'
                                    }}
                                >{`(${loading
                                    ? 0
                                    : calcProPercentsBasedOnFilterAndDays(
                                        percentage,
                                        filter,
                                        projectionDays,
                                        filterTotal,
                                        averageCensus
                                    )
                                    }%)`}</Box>
                                <Box
                                    className={classNames(styles.gradientTextSubtitle, loading && "skeleton-text")}
                                    sx={{
                                        fontSize: { xs: '11px', sm: '12px', md: '13px' },
                                        opacity: 0.8,
                                        fontWeight: 500
                                    }}
                                >
                                    {loading ? (
                                        <span className="skeleton-text" style={{ visibility: "hidden" }}>
                                            {subTitleText}
                                        </span>
                                    ) : (
                                        subTitleText
                                    )}
                                </Box>
                            </Box>
                        )}
                    </Stack>
                </Grid>

                <Divider
                    orientation="vertical"
                    flexItem
                    sx={{
                        mx: { xs: 0.5, sm: 1, md: 2 },
                        borderColor: 'rgba(0, 0, 0, 0.06)',
                        borderWidth: '1.5px'
                    }}
                />

                {/* Sub Total Section */}
                <Grid item xs={6} sm={5} md={5}>
                    <Stack
                        spacing={{ xs: 1, sm: 1.5 }}
                        justifyContent="center"
                        alignItems="center"
                        sx={{
                            height: "100%",
                            paddingY: { xs: 0.5, sm: 1 }
                        }}
                    >
                        {subTotalItems.map(({ key, total, percentage }) => (
                            <SubTotalCardItem
                                key={key}
                                title={key}
                                total={total}
                                percentage={percentage}
                                page={page}
                                loading={loading}
                                borderColorStyle={borderColorStyle}
                                handleCardClick={handleCardClick}
                                isSelectedTotalCard={isSelectedTotalCard}
                                averageCensus={averageCensus}
                                filter={filter}
                                projectionDays={projectionDays}
                                isComparingAgainstAvgCensus={isComparingAgainstAvgCensus}
                                lockedTotalBy={lockedTotalBy}
                                transferType={transferType}
                                isFilterSelected={isFilterSelected}
                            />
                        ))}
                    </Stack>
                </Grid>
            </Grid>
        </div>
    );
};

export default DynamicDataCardTotalCard;
