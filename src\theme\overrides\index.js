//

import Card from './Card';
import Paper from './Paper';
import Input from './Input';
import Button from './Button';
import Tooltip from './Tooltip';
import Backdrop from './Backdrop';
import Typography from './Typography';
import CssBaseline from './CssBaseline';
import Autocomplete from './Autocomplete';
import Checkbox from './Checkbox';
import Select from './Select';
import FormLabel from './FormLabel';

// ----------------------------------------------------------------------

export default function ComponentsOverrides(theme) {
  return Object.assign(
    Card(theme),
    FormLabel(theme),
    Input(theme),
    Paper(theme),
    But<PERSON>(theme),
    Toolt<PERSON>(theme),
    Backdrop(theme),
    Typography(theme),
    <PERSON>ss<PERSON><PERSON><PERSON>(theme),
    Autocomplete(theme),
    Checkbox(theme),
    <PERSON>(theme),
  );
}
