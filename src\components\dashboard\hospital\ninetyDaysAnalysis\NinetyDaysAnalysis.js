import { ResponsiveRadialBar } from "@nivo/radial-bar";
import { calcProPercentsBasedOnFilterAndDays } from "../../../../utilis/common";
import useDaysCard from "../../../hooks/useDaysCard";
import ColorBox from "../../../shared/dashboard/ColorBox";
import CheckboxButton from "../../shared/checkboxButton/CheckboxButton";
import styles from "./NinetyDaysAnalysis.module.scss";
import NinetyDaysAnalysisSkeleton from "./NinetyDaysAnalysisSkeleton";
import { PAGE_TYPE } from "../../../../types/pages.type";
import useLoadingToggle from "../../../hooks/useLoadingToggle";
import CheckboxLoader from "../../../shared/checkbox-loader/CheckboxLoader";
import { Box } from "@mui/system";
import CheckboxCircle from "../../shared/checkboxButton/CheckboxCircle";
import _ from "lodash";
import { useMemo } from "react";
import TruncatedLabelWithTooltip from "./TruncatedLabelWithTooltip";
import CustomBar from "./CustomBar";

const defaultRadialDataArr = [
	{
		id: "Total",
		parentId: "total",
		color: "#92C7FF",
		data: [
			{
				x: "f",
				y: 0,
				percentage: 0,
				color: "#92C7FF",
			},
		],
	},
	{
		id: "1 - 7 Days",
		parentId: "a",
		color: "#21B1D0",
		data: [
			{
				x: "a",
				y: 0,
				percentage: 0,
				color: "#21B1D0",
			},
		],
	},
	{
		id: "8 - 14 Days",
		parentId: "b",
		color: "#5195DD",
		data: [
			{
				x: "b",
				y: 0,
				percentage: 0,
				color: "#5195DD",
			},
		],
	},
	{
		id: "15 - 30 Days",
		parentId: "c",
		color: "#4F80FF",
		data: [
			{
				x: "c",
				y: 0,
				percentage: 0,
				color: "#4F80FF",
			},
		],
	},
	{
		id: "31 - 60 Days",
		color: "#6B69DA",
		parentId: "d",
		data: [
			{
				x: "d",
				y: 0,
				percentage: 0,
				color: "#6B69DA",
			},
		],
	},
	{
		id: "61 - 90 Days",
		color: "#1D7CD3",
		parentId: "e",
		data: [
			{
				x: "e",
				y: 0,
				percentage: 0,
				color: "#1D7CD3",
			},
		],
	},
];

const defaultColors = [
	"#FF6B6B", "#FFD93D", "#6BCB77", "#4D96FF", "#FF6F91",
	"#845EC2", "#00C9A7", "#C34A36", "#F9C80E", "#FF9671",
	"#0081CF", "#2C73D2", "#FAD02E", "#00AF91", "#B76E79"
];

const NinetyDaysAnalysis = ({
	data,
	comparisonData,
	handleToggle,
	selectedItem = [],
	type,
	page,
	height,
	averageCensusComparison,
	averageCensus,
	filterListData,
	filterListDataComparison,
	totalTransfers,
	totalTransfersComparison,
	filter,
	filterComparison,
	cardTitle,
	reverseColors,
	loading,
	projectionDays,
	priorityNumber,	
	transferType,
	isComparingAgainstAvgCensus,
	lockedTotalBy,
	spacialSelectedItem = [],
	isCustom: isCustomQuestion = false,
	question,
	isCustomTab = false,
	customTab = null
}) => {

	const defaultRadialData = useMemo(() => {
		if (isCustomQuestion) {
			return [...data].map((line, index) => {
				const reversedIndex = data.length - 1 - index;
				const colorItem = defaultColors[reversedIndex % defaultColors.length]
				return {
					id: line?._id,
					parentId: line?._id,
					color: colorItem,
					data: [
						{
							x: line?._id,
							y: 0,
							percentage: 0,
							color: colorItem,
						},
					],
				};
			});
		} else {
			return defaultRadialDataArr;
		}
	}, [data, isCustomQuestion]);

	const { dataList, dataListBarChart, dataListComparisonBarChart, radialDataList, radialDataComparisonList } =
		useDaysCard({
			data,
			comparisonData,
			defaultRadialData,
			page,
			averageCensusComparison,
			averageCensus,
			filterListData,
			filterListDataComparison,
			filter,
			filterComparison,
			totalTransfers,
			totalTransfersComparison,
			reverseColors,
		});

	const { loadingItems, handleToggleWithLoader } = useLoadingToggle();

	const isCustom = useMemo(() => {
		return isCustomQuestion
	}, [isCustomQuestion]);

	const chartHeight = (() => {
		if (!dataListBarChart) return 0;
		const length = dataListBarChart.length;
		if (length > 8) return length * 52;
		if (length > 4) return length * 54;
		if (length > 3) return length * 55;
		return length * 62;
	})();

	return loading ? (
		<NinetyDaysAnalysisSkeleton style={{ height: height || "100%" }} />
	) : (
		<div className={`df aic w100 ${styles.ninetyDays}`} {...(height && { style: { height: height } })}>
			<div className={`${styles.leftSec} ${isCustomQuestion && styles.customLeftSec} ${isCustomQuestion && styles.customQuestionLeftSec}`}>
				<div className={`df aic`} style={{ height: isCustom && data?.length > 7 ? "350px" : "calc(100% - 64px)" }}>
					<div
						className={`${styles.ninetyDaysList}`}
						style={{
							...(isCustom && { height: "100%" }),
							...page === PAGE_TYPE.HOSPITAL ? { marginTop: isCustom ? "25px" : "5px" } : {},
							...page === PAGE_TYPE.DECEASED || page === PAGE_TYPE.OVERALL ? { marginTop: "-2px" } : {},
						}}
					>
						{dataList.length > 0 &&
							dataList.map((line, index) => {
								const colorItem = dataListComparisonBarChart.find((x) => x._id === line._id);

								const id = line._id;

								const isSpacialItem = line?.isSpacialItem ?? false;

								const labelId = `checkbox-analysis-list-label${id}`;

								const itemComparison = _.find(comparisonData, { _id: line._id });

								return (
									<div
										key={`${index}-nineteen`}
										className={`df aic ${styles.ninetyDaysLine} ${selectedItem.indexOf(line._id) !== -1 ? styles.ninetyDaysLineSelected : null
											}`}
									>
										{!loading && colorItem?.comparisonColor ? (<>
											<ColorBox
												type="div"
												color={colorItem?.comparisonColor}
												barWidth={colorItem?.width}
												comparingAgainst={colorItem?.value}
												comparingAgainstScaled={colorItem?.comparingAgainstScaled}
												numberOfDays={colorItem?.numberOfDays}
												sx={{ marginRight: "10px" }}
											/>
										</>
										) : (<Box sx={{ml:2.2}}>&nbsp;</Box>)}
										<Box position="relative" display="inline-flex" alignItems="center">
											{isSpacialItem ? (
												<CheckboxCircle
													labelId={labelId}
													buttonText={<TruncatedLabelWithTooltip label={line.label} />}
													handleToggle={() =>
														handleToggleWithLoader(() => handleToggle({
															item: line,
															type,
															itemComparison: itemComparison,
															isChecked: spacialSelectedItem?.indexOf(id) !== -1,
															cardTitle,
															isSpacialItem: line?.isSpacialItem ?? false,
															isCustomTab,
															customTab
														}), id)
													}
													sx={{ ...(loadingItems[id] && { opacity: 0 }) }}
													checked={spacialSelectedItem?.indexOf(id) !== -1 || selectedItem.indexOf(id) !== -1}
												/>) : (
												<CheckboxButton
													labelId={`checkbox-list-90-label-${line._id}`}
													checked={selectedItem.indexOf(line._id) !== -1}
													handleToggle={() => {
														if (!isSpacialItem) {
															handleToggleWithLoader(() => handleToggle({
																item: line,
																type,
																isChecked: selectedItem.indexOf(id) !== -1,
																cardTitle,
																question: isCustom
																	? { isCustom: true, customQuestionInputType: question?.customQuestionInputType, isCustomTab, cardTitle, customTab }
																	: null
															}), id)
														}
													}}
													buttonText={<TruncatedLabelWithTooltip label={line.label} />
													}
													disableRipple={true}
													sx={{
														mr: "-2px",
														...(loadingItems[id] && { opacity: 0 })
													}}
												/>
											)}
											<CheckboxLoader className={"small-dots-nineteen"} isLoading={loadingItems[id]} />
										</Box>
									</div>
								);
							})}
					</div>
					<div style={{ width: "calc(100% - 130px)", height: "100%" }}>
						{dataListBarChart && dataListBarChart.length > 0 && (
							isCustom ? (
								<div style={{ height: chartHeight }}>
									<CustomBar
										data={dataListBarChart}
										height={chartHeight}
										isCustom={isCustom}
										priorityNumber={priorityNumber}
										defaultColors={defaultColors}
										filter={filter}
										projectionDays={projectionDays}
										isComparingAgainstAvgCensus={isComparingAgainstAvgCensus}
										lockedTotalBy={lockedTotalBy}
										transferType={transferType}
										calcProPercentsBasedOnFilterAndDays={calcProPercentsBasedOnFilterAndDays}
									/>
								</div>
							) : (
								<CustomBar
									data={dataListBarChart}
									height={chartHeight}
									isCustom={isCustom}
									priorityNumber={priorityNumber}
									defaultColors={defaultColors}
									filter={filter}
									projectionDays={projectionDays}
									isComparingAgainstAvgCensus={isComparingAgainstAvgCensus}
									lockedTotalBy={lockedTotalBy}
									transferType={transferType}
									calcProPercentsBasedOnFilterAndDays={calcProPercentsBasedOnFilterAndDays}
								/>
							)
						)}
					</div>
				</div>
			</div>

			{!isCustomQuestion && (
				<div
					className=""
					style={{
						height: "250px",
						marginBottom: "58px",
						width: "37%",
					}}
				>
					{radialDataList.length > 0 && (
						<ResponsiveRadialBar
							data={radialDataList}
							padding={0.4}
							cornerRadius={50}
							margin={{ top: 0, right: 0, bottom: 0, left: 0 }}
							radialAxisStart={null}
							endAngle={360}
							enableTracks={false}
							enableRadialGrid={false}
							enableCircularGrid={false}
							colors={(d) => d.data.color}
							axisLeft={null}
							axisBottom={null}
							circularAxisOuter={null}
							enableArcLabels={false}
							enableArcLinkLabels={false}
						/>
					)}
					<div className={`${styles.radialLegend}`}>
						{radialDataList.length > 0 &&
							radialDataList.map((line, index) => {
								const comparisonItem = radialDataComparisonList.find((x) => line.id === x.id);
								return (
									<span className={`df aic fs10 fw600 ffint`} key={`line${index}`}>
										<div className={`${styles.icon}`} style={{ backgroundColor: line.color }}></div>
										{!loading && (
											<ColorBox
												color={comparisonItem?.data[0]?.comparisonColor || null}
												sx={{ height: "13px !important", ml: "-2px", width: "9px", mr: "1px" }}
												comparingAgainst={comparisonItem?.data[0]?.y}
												comparingAgainstScaled={comparisonItem?.data[0]?.comparingAgainstScaled}
												numberOfDays={comparisonItem?.data[0]?.numberOfDays}
											/>
										)}
										{line.id}: {line.data[0].y}
									</span>
								);
							})}
					</div>
				</div>
			)}
		</div>
	);
};

export default NinetyDaysAnalysis;
