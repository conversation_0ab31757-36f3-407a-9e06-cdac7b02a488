/**
 * Centralized Worker Manager
 * Manages all BullMQ workers and queues to prevent memory leaks
 */

const { Queue, Worker } = require('bullmq');
const configModule = require("./redis-connection");

// Global registry to track all workers and queues
const workerRegistry = new Map();
const queueRegistry = new Map();
let isManagerInitialized = false;

/**
 * Create or get existing queue
 */
const getOrCreateQueue = (name, options = {}) => {
    if (queueRegistry.has(name)) {
        return queueRegistry.get(name);
    }

    // Default queue options
    const defaultOptions = {
        connection: configModule.createRedisConnection(),
        // Default job options to prevent issues
        defaultJobOptions: {
            removeOnComplete: 10,
            removeOnFail: 50,
            attempts: 3,
            backoff: {
                type: 'exponential',
                delay: 2000,
            },
            // Job timeout to prevent hanging jobs
            timeout: 600000, // 10 minutes
        },
        ...options
    };

    const queue = new Queue(name, defaultOptions);

    // Set max listeners to prevent warnings
    queue.setMaxListeners(0); // 0 means unlimited

    // Add error handling for queue issues
    queue.on('error', (error) => {
        console.error(`❌ Queue '${name}' error:`, error);
    });

    queueRegistry.set(name, queue);
    console.log(`📬 Queue '${name}' created and registered successfully`);
    console.log(`⚙️  Default job timeout: ${defaultOptions.defaultJobOptions.timeout}ms`);
    return queue;
};

/**
 * Create or get existing worker
 */
const getOrCreateWorker = (name, processor, options = {}) => {
    if (workerRegistry.has(name)) {
        console.log(`🔄 Worker '${name}' already exists - reusing existing instance`);
        return workerRegistry.get(name);
    }

    // Default worker options to prevent lock renewal issues
    const defaultOptions = {
        connection: configModule.createRedisConnection(),
        // Job processing settings
        maxStalledCount: 1,
        stalledInterval: 30000, // 30 seconds
        // Lock settings to prevent renewal errors
        lockDuration: 300000, // 5 minutes lock duration
        lockRenewTime: 150000, // Renew lock every 2.5 minutes
        // Concurrency settings
        concurrency: 1,
        // Remove completed jobs to prevent memory issues
        removeOnComplete: 10,
        removeOnFail: 50,
        ...options
    };

    const worker = new Worker(name, processor, defaultOptions);

    // Set max listeners to prevent warnings
    worker.setMaxListeners(0); // 0 means unlimited

    // Add error handling for lock renewal issues
    worker.on('error', (error) => {
        if (error.message.includes('could not renew lock')) {
            console.warn(`⚠️  Lock renewal warning for worker '${name}': ${error.message}`);
            console.log(`🔄 This usually means a job is taking longer than expected - consider increasing lockDuration`);
        } else {
            console.error(`❌ Worker '${name}' error:`, error);
        }
    });

    worker.on('stalled', (jobId) => {
        console.warn(`⚠️  Job ${jobId} stalled in worker '${name}' - will be retried`);
    });

    workerRegistry.set(name, worker);
    console.log(`👷 Worker '${name}' created and registered - ready to process jobs!`);
    console.log(`🔒 Lock duration: ${defaultOptions.lockDuration}ms, Renewal: ${defaultOptions.lockRenewTime}ms`);
    return worker;
};

/**
 * Check if worker exists
 */
const hasWorker = (name) => {
    return workerRegistry.has(name);
};

/**
 * Check if queue exists
 */
const hasQueue = (name) => {
    return queueRegistry.has(name);
};

/**
 * Get worker by name
 */
const getWorker = (name) => {
    return workerRegistry.get(name);
};

/**
 * Get queue by name
 */
const getQueue = (name) => {
    return queueRegistry.get(name);
};

/**
 * Test Redis connection
 */
const testRedisConnection = async () => {
    try {
        const connection = configModule.createRedisConnection();
        await connection.ping();
        await connection.quit();
        console.log('✅ Redis connection test successful');
        return true;
    } catch (error) {
        console.error('❌ Redis connection test failed:', error.message);
        return false;
    }
};

/**
 * Initialize the worker manager
 */
const initializeWorkerManager = async () => {
    if (isManagerInitialized) {
        console.log('🔄 Worker manager already initialized - skipping setup');
        return;
    }

    console.log('🏗️  Initializing worker manager...');

    // Test Redis connection first
    console.log('🔍 Testing Redis connection...');
    const redisOk = await testRedisConnection();
    if (!redisOk) {
        console.warn('⚠️  Redis connection issues detected - jobs may fail');
    }

    console.log('🔧 Setting up process listeners for BullMQ workers');

    // Set process max listeners to handle BullMQ workers
    process.setMaxListeners(0); // 0 means unlimited

    isManagerInitialized = true;
    console.log('✅ Worker manager initialized successfully - ready to manage jobs!');
};

/**
 * Cleanup all workers and queues
 */
const cleanupWorkerManager = async () => {
    console.log('🧹 Cleaning up worker manager...');

    try {
        // Close all workers
        if (workerRegistry.size > 0) {
            console.log(`👷 Closing ${workerRegistry.size} active workers...`);
            for (const [name, worker] of workerRegistry) {
                console.log(`  🔄 Shutting down worker: ${name}`);
                await worker.close();
            }
            workerRegistry.clear();
            console.log('✅ All workers closed successfully');
        }

        // Close all queues
        if (queueRegistry.size > 0) {
            console.log(`📬 Closing ${queueRegistry.size} active queues...`);
            for (const [name, queue] of queueRegistry) {
                console.log(`  🔄 Shutting down queue: ${name}`);
                await queue.close();
            }
            queueRegistry.clear();
            console.log('✅ All queues closed successfully');
        }

        isManagerInitialized = false;
        console.log('🎉 Worker manager cleanup completed - all resources freed!');
    } catch (error) {
        console.error('❌ Error during worker manager cleanup:', error);
    }
};

/**
 * Get status of all workers and queues
 */
const getManagerStatus = () => {
    return {
        initialized: isManagerInitialized,
        workers: Array.from(workerRegistry.keys()),
        queues: Array.from(queueRegistry.keys()),
        workerCount: workerRegistry.size,
        queueCount: queueRegistry.size
    };
};

module.exports = {
    getOrCreateQueue,
    getOrCreateWorker,
    hasWorker,
    hasQueue,
    getWorker,
    getQueue,
    initializeWorkerManager,
    cleanupWorkerManager,
    getManagerStatus,
    testRedisConnection
};
