import React from "react";
import { connect } from "react-redux";
import { Navigate } from "react-router-dom";


const PrivateRouteComponent = (props) => {
  const { children, permission = null } = props;

  const hasPermission = () => {
    const couldShow =
      props.userPermissions.includes(permission) ||
        props.user?.role.slug === "super"
        ? true
        : false;
    return couldShow
  };

  if (!hasPermission()) {
    return <Navigate to={"/login"} replace />;
  } else {
    return children;
  }
};

const mapStateToProps = (state) => ({
  userPermissions: state.permissions,
  user: state.auth,
});

export const PrivateRoute = connect(mapStateToProps)(PrivateRouteComponent);
