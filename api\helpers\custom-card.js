const mongoose = require("mongoose");
const CustomCardFilter = mongoose.model("customCardFilter");

// Create new account
const createCustomCard = async (req) => {
    const user = req?.user;
    const { accountid } = req.headers;

    const isExistsCardFilter = await CustomCardFilter.findOne({
        userId: user._id,
        accountId: mongoose.Types.ObjectId(accountid),
        page: req.body?.page
    });

    if (isExistsCardFilter) {
        let customCardFilter = await CustomCardFilter.findById(isExistsCardFilter?._id);
        customCardFilter.cards = req.body?.cards;
        const updated = await customCardFilter.save();
        return updated;
    } else {
        const filterCustomSave = new CustomCardFilter({
            accountId: mongoose.Types.ObjectId(accountid),
            userId: user._id,
            ...req.body
        });
        const saved = await filterCustomSave.save()
        return { status: 200, data: saved }
    }
};

const getCustomCards = async (req) => {
    const { accountid } = req.headers;
    const user = req?.user;

    const filters = await CustomCardFilter.find({
        userId: user._id,
        accountId: accountid
    });

    return filters;
};

const getCustomCard = async (req) => {
    const { id } = req.query;
    const filters = await CustomCardFilter.findOne({ _id: mongoose.Types.ObjectId(id) });
    return filters;
};

const deleteCustomCard = async id => {
    let deleted = await CustomCardFilter.findByIdAndDelete(id);
    return deleted;
};

module.exports = { createCustomCard, getCustomCard, getCustomCards, deleteCustomCard };
