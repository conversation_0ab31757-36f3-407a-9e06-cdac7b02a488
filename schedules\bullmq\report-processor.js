const _ = require("lodash");
const { renderContent } = require("../../api/utilis/reports/generate-pdf");
const { PAGE_TYPE, ADMISSION_FILTER_TYPE, DECEASED_CARDS_TYPE, HOSPITAL_CARDS_TYPE, OVERALL_CARDS_TYPE, ADT_TABLE_TYPE, AUTOMATICALLY_REPORT_TYPE, REPORT_FILE_TYPE, ACCOUNT_PERCENTAGE_BY, percentageByEnum, CUSTOM_TAB_TYPE } = require("../../types/common.type");
const {
    getAllCount,
    getCardPatientChartData,
} = require("../../api/helpers/deceased");
const {
    getAllCount: getAllCountHospital,
    getCardPatientChartData: getHospitalCardPatientChartData
} = require("../../api/helpers/hospital");
const {
    getAllCount: getAllCountCommunity,
    getCardPatientChartData: getCommunityCardPatientChartData
} = require("../../api/helpers/community-transfer");
const {
    getAllCount: getAllCountOverall,
    getCardPatientChartData: getOverallCardPatientChartData
} = require("../../api/helpers/overall");
const {
    getAllCount: getAllCountAdmission,
    getADTAllData,
    getCardPatientChartData: getAdmissionCardPatientChartData,
    getADTDetailsPatientChartData
} = require("../../api/helpers/admission");
const {
    deceaseUpdateFilterListData,
    hospitalUpdateFilterListData,
    communityUpdateFilterListData,
} = require("../../api/utilis/reports/report-common");
const puppeteer = require("puppeteer");
const moment = require("moment");
const mongoose = require("mongoose");
const Patient = mongoose.model("patient");
const { sendPostmarkEmailWithAttachment } = require("../../sendEmail");
const { updateFilterListDataOverall } = require("../../api/utilis/reports/report-common-overall");
const ReportsSubscriptions = mongoose.model("reportsSubscriptions");
const { chartsData, chartGroupBy, filterOptions, hospitalFilterOptions, overAllFilterOptions, admissionFilterOptions, ADTfilterOptions, updateAdmissionChartFilter } = require('../../api/utilis/reports/graph-report-common');
const { updateFilterListDataBoth, adtCardFilterParam, getADTDataDefault } = require("../../api/utilis/reports/report-common-admission");
const { toSaveDate, toSaveEndDate, toStarDateNextDay, toStartFilterDate, toSevenDate, toMonthDate, toQuatreDate, toYearDate, toWeekDate, toDailyDate, toEndFilterDate } = require("../../api/utilis/date-format");
const { ADT_SUB_TYPES, ADT_TYPES } = require("../../types");
const { onlyHospitalTabAccess } = require("../../utils/common");
const { createReportLog } = require("../../api/helpers/report-log");
const { getEndOfDateReportSettings } = require("../../api/helpers/facilityManuallyEndOfADT");
const { getQuestionsData } = require("../../api/helpers/dynamicDataTab");
const { getCustomTabById } = require("../../api/helpers/custom-tab");
const { applyCombineFilters } = require("../../api/utilis/reports/report-common");

const jobProcessor = async (job) => {
    try {
        job.log(`Started processing job with id er er ${job.id}`);

        let reportsSubscription = job?.data;
        let page = reportsSubscription?.page;
        let forType = page;
        let forTransferType = [];
        if (page === PAGE_TYPE.HOSPITAL) {
            forType = ADT_TYPES.TRANSFER;
            forTransferType = ["hospitalTransfer"]
        } else if (page === PAGE_TYPE.COMMUNITY_TRANSFER) {
            forType = ADT_TYPES.TRANSFER;
            forTransferType = [ADT_SUB_TYPES.AMA, ADT_SUB_TYPES.SAFE_DISCHARGE, ADT_SUB_TYPES.SNF]
        } else if (page === PAGE_TYPE.DECEASED) {
            forType = ADT_TYPES.TRANSFER;
            forTransferType = [ADT_SUB_TYPES.DECEASED]
        } else if (page === PAGE_TYPE.ADMISSION) {
            forType = null;
            forTransferType = [];
        }

        let reQuestion = {
            headers: { accountid: reportsSubscription?.accountId },
            query: {
                forType,
                forTransferType,
                isCustom: true
            }
        }

        const dynamicCards = await getQuestionsData(reQuestion) ?? [];

        reportsSubscription.dynamicCards = dynamicCards ?? [];

        const hospitalTabOnly = await onlyHospitalTabAccess(reportsSubscription?.accountId);

        if (hospitalTabOnly) {
            reportsSubscription.percentageBy = hospitalTabOnly?.percentageBy;
            reportsSubscription.isOnlyHospitalTabAccess = hospitalTabOnly?.isOnlyHospitalTabAccess;
        }

        let customDataObj = {};

        const autoReport = reportsSubscription?.filtersData?.automaticallyReport;
        if (autoReport) {
            const { isCustomCard, isCustomTab, questionId } = autoReport;

            customDataObj = { isCustomCard, isCustomTab };

            if (isCustomCard && isCustomTab && questionId) {
                customDataObj.customTab = await getCustomTabById(questionId);
            }
        }
        reportsSubscription.customDataObj = customDataObj;

        const adtData = await getADTByPageWise(reportsSubscription);
        // const adtData = [{ startDate: '2025-05-02', endDate: '2025-05-02' }]

        console.log(adtData, 'adtData');

        if (adtData && adtData.length > 0) {
            let latestDateOfADT = null;

            // Process all patients concurrently
            let jobPromises = adtData.map(async (patient, i) => {
                let startDate = patient?.startDate;
                let endDate = patient?.endDate;
                latestDateOfADT = patient?.endDate;

                let isUpdateRecord = i === adtData.length - 1; // True for the last record

                let jobData = {
                    ...reportsSubscription,
                    startDate,
                    endDate,
                    jobName: `${reportsSubscription.interval}_dashboard_item_report_${reportsSubscription.id}_${i + 1}`,
                    dateOfADT: patient?.endDate,
                    isUpdateRecord,
                };

                const res = await sendMultipleReport(jobData);
                return res; // Store results
            });

            // Run all jobs in parallel
            let results = await Promise.all(jobPromises);

            // Filter out undefined results
            let attachments = results.filter(res => res !== undefined);

            console.log(attachments.length, 'attachments.length', reportsSubscription?.name);

            if (attachments.length > 0) {
                console.log(attachments.length, 'attachments.length inside', reportsSubscription?.name);
                let textBody = "Please find attached documents for daily report";
                let email = reportsSubscription.userId?.email;
                let subject = reportsSubscription?.name || `Automatic report ${reportsSubscription.interval}`;

                console.log("EMAIL SEND");
                console.log(subject, 'subject', attachments.length);
                console.log("EMAIL SEND SUCCESS");

                // Uncomment this when ready to send emails
                await sendPostmarkEmailWithAttachment(email, textBody, subject, reportsSubscription?.interval, attachments);

                // Uncomment to update the subscription
                await updateReportsSubscriptions(reportsSubscription, latestDateOfADT);

                return reportsSubscription;
            }
        }
        return reportsSubscription;
    } catch (err) {
        await createReportLog({ event: "JOB_SENT_MAIL_ERROR", description: "Report send error", data: err })
        console.log(err, "error of processor");
    }
};

const sendMultipleReport = async (reportsSubscription) => {
    let response;
    try {
        if (reportsSubscription.isGraphReport) {
            // Calculate all graph report 
            let filterDates = {
                startDate: await toSaveDate(reportsSubscription.startDate, "YYYY-MM-DD"),
                endDate: await toSaveEndDate(reportsSubscription.endDate, "YYYY-MM-DD")
            }

            if (reportsSubscription && reportsSubscription.page === PAGE_TYPE.DECEASED) {
                response = await sendDeceasedPageGraphReports({
                    ...reportsSubscription,
                    ...filterDates
                });
            }
            if (reportsSubscription && reportsSubscription.page === PAGE_TYPE.HOSPITAL) {
                response = await sendHospitalPageGraphReports({
                    ...reportsSubscription,
                    ...filterDates
                });
            }
            if (reportsSubscription && reportsSubscription.page === PAGE_TYPE.COMMUNITY_TRANSFER) {
                response = await sendCommunicationPageGraphReports({
                    ...reportsSubscription,
                    ...filterDates
                });
            }
            if (reportsSubscription && reportsSubscription.page === PAGE_TYPE.OVERALL) {
                response = await sendOverallPageGraphReports({
                    ...reportsSubscription,
                    ...filterDates
                });
            }
            if (reportsSubscription && reportsSubscription.page === PAGE_TYPE.ADMISSION) {
                if (reportsSubscription.filtersData && reportsSubscription.filtersData.admissionReportType == ADMISSION_FILTER_TYPE.ADT) {
                    response = await sendAdmissionADTPageGraphReports({
                        ...reportsSubscription,
                        ...filterDates
                    });
                } else {
                    response = await sendAdmissionPageGraphReports({
                        ...reportsSubscription,
                        ...filterDates
                    });
                }

                // console.log(response, 'response');

            }

            const res = await sendGraphReportData(reportsSubscription, response);

            if (res && res != undefined) {
                if (reportsSubscription.filtersData.graphData.selectedTab === "Table" && reportsSubscription.reportFileType === REPORT_FILE_TYPE.EXCEL) {
                    return {
                        name: `${reportsSubscription.name}_${reportsSubscription.interval}_report.xls`,
                        content: res,
                        ContentType: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                    }
                } else {
                    return {
                        name: `${reportsSubscription.name}_${reportsSubscription.interval}_report.pdf`,
                        content: res,
                        ContentType: "application/pdf",
                    }
                }
            }
        } else {
            if (
                reportsSubscription &&
                reportsSubscription.page === PAGE_TYPE.DECEASED
            ) {
                response = await sendDeceasedPageReports(reportsSubscription);
            }
            if (
                reportsSubscription &&
                reportsSubscription.page === PAGE_TYPE.ADMISSION
            ) {
                response = await sendAdmissionPageReports(reportsSubscription);
            }
            if (
                reportsSubscription &&
                reportsSubscription.page === PAGE_TYPE.HOSPITAL
            ) {
                response = await sendHospitalPageReports(reportsSubscription);
            }
            if (
                reportsSubscription &&
                reportsSubscription.page === PAGE_TYPE.COMMUNITY_TRANSFER
            ) {
                response = await sendCommunityPageReports(reportsSubscription);
            }
            if (reportsSubscription && reportsSubscription.page === PAGE_TYPE.OVERALL) {
                response = await sendOverallPageReports(reportsSubscription);
            }

            if (response && response.filterListData) {

                const res = await sendReportData(reportsSubscription, response);

                if (res && res != undefined) {
                    return {
                        name: `${reportsSubscription.name}_${reportsSubscription.interval}_report.pdf`,
                        content: res,
                        ContentType: "application/pdf",
                    }
                }
            }
        }
    } catch (err) {
        console.log(err, "Send email report error from processor");
    }
}

const sendGraphReportData = async (reportsSubscription, response) => {
    if (response && response.filterListData) {
        const { startDate, endDate } = reportsSubscription;
        let selectedADTTableData = response?.filterListData?.adtList || [];
        if (reportsSubscription.filtersData?.admissionReportType === ADMISSION_FILTER_TYPE.ADT) {
            selectedADTTableData = response.chartData.filterListData.adtList;
        }
        const questions = reportsSubscription?.filtersData?.graphData?.filters?.question ?? null;

        const reportDataHtml = await renderContent({
            questions,
            type: reportsSubscription.filtersData?.admissionReportType === ADMISSION_FILTER_TYPE.ADT ? reportsSubscription.adtType : reportsSubscription.filterCardType,
            title: reportsSubscription.filtersData?.title,
            cardFilter: reportsSubscription?.filtersData.cardFilter,
            tableFilterType: reportsSubscription?.filtersData.tableFilterType,
            filter: { ...reportsSubscription?.filtersData.filter, startDate, endDate },
            dbData: response.res.totals,
            page: reportsSubscription.page,
            reportFileType: reportsSubscription.reportFileType,
            filterListData: response.chartData.filterListData,
            isTotalCard: reportsSubscription?.filtersData?.isTotalCard || false,
            transferType: reportsSubscription.page === PAGE_TYPE.COMMUNITY_TRANSFER ? reportsSubscription?.transferTypes : reportsSubscription?.transferType,
            reportsSubscription,
            isAdt: reportsSubscription?.filtersData?.isAdt || false,
            admissionReportType: reportsSubscription?.filtersData?.admissionReportType || false,
            selectedADTTableData,
            lockedTotalBy: reportsSubscription.filtersData?.lockedTotalBy || null,
            isCensusTotalLocked: reportsSubscription.filtersData?.isCensusTotalLocked,
            isGraphReport: reportsSubscription?.isGraphReport,
            dynamicPercentageLabel: reportsSubscription.filtersData?.percentageBy ? reportsSubscription.filtersData?.percentageBy : percentageByEnum.censusAverage,
            graphData: {
                ...reportsSubscription.filtersData.graphData,
                selectedColumns: reportsSubscription?.automaticallyReport?.selectedColumns,
                title: reportsSubscription?.name,
                chartData: response.chartData,
                filterData: response.res.data,
                dbData: response.res,
            },
            activeFacilitiesCount: reportsSubscription?.facilityIds?.length,
            accountId: reportsSubscription?.accountId,
            userId: reportsSubscription?.userId,
            percentageBy: reportsSubscription?.percentageBy,
            isOnlyHospitalTabAccess: reportsSubscription?.isOnlyHospitalTabAccess,
        });

        if (reportsSubscription.filtersData.graphData.selectedTab === "Table" && reportsSubscription.reportFileType === REPORT_FILE_TYPE.EXCEL) {
            return reportDataHtml;
        } else {
            if (reportDataHtml) {
                // let fileName = `./schedules/reports/report_${reportsSubscription?._id}_${reportsSubscription?.dateOfADT}.pdf`
                // const resPdf = await generatePDFFromHtml(reportDataHtml, fileName);

                const resPdf = await generatePDFFromHtml(reportDataHtml);
                return resPdf ? resPdf : null;
            }
        }
    } else {
        return null
    }
}

const sendReportData = async (reportsSubscription, response) => {
    try {

        if (response && response.filterListData) {
            const reportDataHtml = await renderContent({
                type:
                    reportsSubscription.filtersData?.admissionReportType ===
                        ADMISSION_FILTER_TYPE.ADT
                        ? reportsSubscription.adtType
                        : reportsSubscription.filterCardType,
                title: reportsSubscription.filtersData?.title,
                cardFilter: reportsSubscription?.filtersData.cardFilter,
                filter: {
                    ...reportsSubscription?.filtersData.filter,
                    startDate: reportsSubscription?.startDate,
                    endDate: reportsSubscription?.endDate,
                },
                dbData: response.res.totals,
                tableFilterType: reportsSubscription?.filtersData.tableFilterType,
                page: reportsSubscription.page,
                filterListData: response.filterListData,
                isTotalCard: reportsSubscription?.filtersData?.isTotalCard || false,
                transferType:
                    reportsSubscription.page === PAGE_TYPE.COMMUNITY_TRANSFER
                        ? reportsSubscription?.transferTypes
                        : reportsSubscription?.transferType,
                reportsSubscription,
                isAdt: reportsSubscription?.filtersData?.isAdt || false,
                admissionReportType:
                    reportsSubscription?.filtersData?.admissionReportType || false,
                selectedADTTableData: response?.filterListData?.adtList || [],
                lockedTotalBy: reportsSubscription.filtersData?.lockedTotalBy || null,
                isCensusTotalLocked:
                    reportsSubscription.filtersData?.isCensusTotalLocked,
                percentageBy: reportsSubscription?.percentageBy,
                isOnlyHospitalTabAccess: reportsSubscription?.isOnlyHospitalTabAccess,
                dynamicPercentageLabel: reportsSubscription.filtersData?.percentageBy ? reportsSubscription.filtersData?.percentageBy : percentageByEnum.censusAverage,
                accountId: reportsSubscription?.accountId,
                userId: reportsSubscription?.userId,
            });
            if (reportDataHtml) {
                const resPdf = await generatePDFFromHtml(reportDataHtml);
                return resPdf;
            }
        } else {
            return null
        }
    } catch (error) {
        console.log(error);
    }
};

const updateReportsSubscriptions = async (reportsSubscription, date) => {
    const latestStringDate = moment.utc(date).toISOString();
    const reportsSubscriptionsSaved = await ReportsSubscriptions.findOne({
        _id: reportsSubscription._id,
    });
    if (reportsSubscriptionsSaved) {
        reportsSubscriptionsSaved.lastEmailSentADTDate = latestStringDate;
        await reportsSubscriptionsSaved.save();
    }
};

const getRequestBody = async (reportsSubscription) => {
    const req = new Object();
    const { startDate, endDate, filtersData } = reportsSubscription;
    req.headers = { facilityid: null, accountid: reportsSubscription?.accountId };
    req.user = reportsSubscription?.userId;
    req.body = {
        filter: { startDate, endDate },
        cardFilter: filtersData.cardFilter,
        facilityIds: reportsSubscription?.facilityIds,
        ...reportsSubscription.filtersData.type && { type: reportsSubscription.filtersData.type }
    };
    return req;
};

const getRequestObject = async (reportsSubscription) => {
    const req = new Object();
    const { startDate, endDate } = reportsSubscription;
    req.headers = { facilityid: null, accountid: reportsSubscription?.accountId };
    req.user = reportsSubscription?.userId;
    const facilityData = [];
    reportsSubscription?.facilityIds.map((ele) => {
        facilityData.push(ele.toString());
    });
    req.query = {
        ...{ startDate, endDate },
        facilityIds: facilityData,
    };
    return req;
};


const sendDeceasedPageReports = async (reportsSubscription) => {
    const req = await getRequestObject(reportsSubscription);
    const res = await getAllCount(req);
    if (res && res.data) {
        const filterListData = await deceaseUpdateFilterListData(
            reportsSubscription?.filtersData.cardFilter,
            res,
            reportsSubscription?.filtersData.cardFilter?.priorityData,
            false,
            reportsSubscription?.filtersData.lockedTotalBy,
            reportsSubscription?.dynamicCards,
            reportsSubscription?.isOnlyHospitalTabAccess
        );

        return { filterListData, res };
    }
    return null;
};

const sendAdmissionPageReports = async (reportsSubscription) => {
    // TO-DO :: Send ADMISSION calculation will here
    const req = await getRequestObject(reportsSubscription);
    const res = await getAllCountAdmission(req);
    const defaultListFilterData = await getADTDataDefault(
        res.data,
        reportsSubscription?.filtersData?.cardFilter?.adtData,
        false
    );
    // get adt data
    let isSelectedChildData = false;
    reportsSubscription?.filtersData?.cardFilter?.adtData.map((item) => {
        const isChildSelected = _.find(item.children, { isSelected: true });
        if (isChildSelected) {
            isSelectedChildData = true;
        }
    });
    let adtData;
    if (isSelectedChildData) {
        const adtFilters = await adtCardFilterParam(
            reportsSubscription?.filtersData?.cardFilter?.adtData
        );
        adtData = await getADTAllData({
            ...req,
            body: { ...adtFilters, ...req.query },
        });
    }
    // end of get adt data
    if (res && res.data) {
        const filterListData = await updateFilterListDataBoth(
            { ...res.data, totals: res.totals, adtList: adtData },
            reportsSubscription.filtersData.cardFilter,
            reportsSubscription?.transferType,
            reportsSubscription?.filtersData?.cardFilter?.mainPriorityData,
            defaultListFilterData,
            false,
            reportsSubscription.filtersData?.lockedTotalBy,
            reportsSubscription.filtersData?.lockeByADT,
            reportsSubscription?.dynamicCards,
            reportsSubscription?.isOnlyHospitalTabAccess
        );

        return {
            filterListData: {
                ...filterListData?.patientList,
                adtList: filterListData.adtList,
            },
            res: { ...res.data, totals: res.totals },
        };
    }
    return null;
};

const sendCommunityPageReports = async (reportsSubscription) => {
    // TO-DO :: Send COMMUNITY TRANSFER calculation will here
    const req = await getRequestObject(reportsSubscription);
    const res = await getAllCountCommunity(req);

    if (res && res.data) {
        const filterListData = await communityUpdateFilterListData(
            reportsSubscription.filtersData.cardFilter,
            { ...res.data, totals: res.totals },
            reportsSubscription?.filtersData?.cardFilter?.priorityData,
            reportsSubscription?.transferTypes,
            false,
            reportsSubscription.filtersData?.lockedTotalBy,
            reportsSubscription?.dynamicCards,
            reportsSubscription?.isOnlyHospitalTabAccess
        );
        return { filterListData, res: { ...res.data, totals: res.totals } };
    }
    return null;
};

const sendHospitalPageReports = async (reportsSubscription) => {
    // TO-DO :: Send HOSPITAL calculation will here
    const req = await getRequestObject(reportsSubscription);
    const res = await getAllCountHospital(req);

    if (res && res.list) {
        const filterListData = await hospitalUpdateFilterListData(
            reportsSubscription.filtersData.cardFilter,
            reportsSubscription?.transferType,
            res,
            reportsSubscription?.filtersData?.cardFilter?.priorityData,
            false,
            reportsSubscription.filtersData?.lockedTotalBy,
            reportsSubscription,
            reportsSubscription?.dynamicCards,
            reportsSubscription?.isOnlyHospitalTabAccess
        );
        // console.log(filterListData, 'filterListData');
        console.log(filterListData["timeRangeOnly"], 'filterListData timeRangeOnly');        
        return { filterListData, res };
    }
    return null;
};

const sendOverallPageReports = async (reportsSubscription) => {
    // TO-DO :: Send OVERALL calculation will here
    const req = await getRequestObject(reportsSubscription);
    const res = await getAllCountOverall(req);
    if (res && res.data) {
        const filterListData = await updateFilterListDataOverall(
            reportsSubscription.filtersData.cardFilter,
            { ...res.data, totals: res.totals },
            reportsSubscription?.filtersData?.cardFilter?.priorityData,
            reportsSubscription?.transferType,
            false,
            {
                mainNumPercentage: reportsSubscription.filtersData?.mainNumPercentage,
                lockedTotalBy: reportsSubscription.filtersData?.lockedTotalBy,
                isOnlyHospitalTabAccess: reportsSubscription?.isOnlyHospitalTabAccess
            }
        );
        return { filterListData, res: { ...res.data, totals: res.totals } };
    }
    return null;
};


//Graph reports 
const sendOverallPageGraphReports = async (reportsSubscription) => {
    const { filtersData } = reportsSubscription
    const req = await getRequestBody(reportsSubscription);
    let response = await getOverallCardPatientChartData({
        ...req,
        body: {
            ...req.body,
            relation: reportsSubscription?.filtersData?.graphData?.filters?.relation,
            transferType: reportsSubscription.transferType
        }
    });

    await checkAnotherDashboardData(reportsSubscription, response);

    let mainData = await sendOverallPageReports(reportsSubscription);
    let mainFilterData = [];
    if (mainData && mainData.filterListData && reportsSubscription?.filtersData?.type) {
        mainFilterData = reportsSubscription?.filtersData?.type === OVERALL_CARDS_TYPE.TOTAL ? [] : mainData.filterListData[reportsSubscription?.filtersData?.type];
    }
    if (response && response.data && response.data.length > 0) {
        const chartDataArr = await chartsData(response.data, reportsSubscription.filtersData.filter);
        const latestChartData = await chartGroupBy(
            chartDataArr,
            filtersData.graphData.selectedFilter,
            response?.censusAverage,
            response?.censusByPeriod,
            reportsSubscription.filtersData.filter
        );

        const responseData = response.data;

        let chartData = {
            mainData: responseData,
            filterPatients: responseData,
            filterData: latestChartData
        };

        let selectedFilterData = reportsSubscription?.filtersData.cardFilter[reportsSubscription.filterCardType]
        let filter = reportsSubscription.filtersData.filter;

        const { filterData, filterPatients } = await overAllFilterOptions({
            newChecked: selectedFilterData,
            chartMainDataArr: responseData,
            latestButtonFilterType: filtersData.graphData.buttonFilterType,
            filterObj: {
                ...filter,
                filterData: mainFilterData,
                censusAverage: response?.censusAverage,
                censusByPeriod: response?.censusByPeriod,
                question: reportsSubscription?.filtersData?.graphData?.filters?.question
            },
            filter,
            filters: { type: reportsSubscription?.filtersData?.type },
            mainData: responseData
        });
        chartData.filterData = filterData;
        chartData.filterPatients = filterPatients;
        chartData.mainFilterData = mainFilterData;
        chartData.filterListData = mainData.filterListData
        return {
            chartData,
            filterListData: filterData,
            res: { ...response, totals: mainData.res.totals }
        }
    }
    return null
}

const checkAnotherDashboardData = async (reportsSubscription, response) => {
    const questions = reportsSubscription?.filtersData?.graphData?.filters?.question;
    const customTab = questions?.customTab;

    const level = questions?.level;
    const isAnotherDashboard = questions?.isAnotherDashboard;
    const isCustomTab = questions?.isCustomTab;
    const questionType = customTab?.type ?? null;

    const dashboardWiseData = response?.diffDashboardPatients?.[questions?.dashboardType]?.list ?? [];

    if (questionType === CUSTOM_TAB_TYPE.combineTab) {
        const combineData = await applyCombineFilters({
            patientData: response?.data ?? [],
            ninetyDaysData: response?.ninetyDaysData ?? [],
            sixtyDaysData: response?.sixtyDaysData ?? [],
            customCombineTabData: response?.customCombineTabData ?? [],
            pageType: reportsSubscription?.page,
            customTab: customTab
        });
        response.data = combineData;
    } else {
        if (isAnotherDashboard) {
            if (level > 0) {
                response.data = dashboardWiseData.filter(item => item.level === level);
            } else {
                response.data = dashboardWiseData;
            }
        } else {
            // Case 2: From same dashboard and level > 0
            const patientData = response?.data ?? [];
            const patientIdsSet = new Set(patientData.map(item => item.id));

            response.data = dashboardWiseData.filter(item =>
                item.level === level &&
                patientIdsSet.has(item.refPatientId)
            );
        }
    }
}

const sendHospitalPageGraphReports = async (reportsSubscription) => {
    const { filtersData } = reportsSubscription;
    let mainData = await sendHospitalPageReports(reportsSubscription);
    const req = await getRequestBody(reportsSubscription);
    if (req?.body?.cardFilter?.priorityData?.length > 0) {
        req.body.cardFilter.priorityData = req.body.cardFilter.priorityData.map(ele => {
            const listData = mainData?.filterListData?.[ele.type] || [];
            if (Array.isArray(ele.selectedItems) && listData.length > 0) {
                const matchedPatientIds = listData
                    .filter(item => ele.selectedItems.includes(String(item._id)))
                    .flatMap(item => item.patientIds.map(id => String(id)));
                ele.patientIds = matchedPatientIds;
            }
            return ele;
        });
    }

    let response = await getHospitalCardPatientChartData({
        ...req,
        body: { ...req.body, relation: reportsSubscription?.filtersData?.graphData?.filters?.relation, transferType: reportsSubscription.transferType }
    });

    await checkAnotherDashboardData(reportsSubscription, response);

    let mainFilterData = [];

    if (mainData && mainData.filterListData && reportsSubscription?.filtersData?.type) {
        mainFilterData = reportsSubscription?.filtersData?.type === HOSPITAL_CARDS_TYPE.TOTAL ? [] : mainData.filterListData[reportsSubscription?.filtersData?.type];
    }
    let reportFilters = { ...reportsSubscription.filtersData.filter, startDate: reportsSubscription.startDate, endDate: reportsSubscription.endDate }
    let buttonFilterType = filtersData.graphData.buttonFilterType;
    let censusAverage = reportsSubscription?.percentageBy && reportsSubscription?.percentageBy === ACCOUNT_PERCENTAGE_BY.BED ? response?.bedCapacity : response?.censusAverage;

    if (response && response.data && response.data.length > 0) {
        const chartDataArr = await chartsData(response.data, reportFilters);
        const latestChartData = await chartGroupBy(
            chartDataArr,
            buttonFilterType,
            censusAverage,
            response?.censusByPeriod,
            reportFilters
        );

        const responseData = response.data;
        let chartData = {
            mainData: responseData,
            filterPatients: responseData,
            filterData: latestChartData
        };

        let selectedFilterData = reportsSubscription?.filtersData.cardFilter[reportsSubscription.filterCardType];

        //if (selectedFilterData && selectedFilterData.length > 0) {
        let { filterData = [], filterPatients = [] } = await hospitalFilterOptions({
            newChecked: selectedFilterData ?? [],
            chartMainDataArr: responseData,
            latestButtonFilterType: buttonFilterType,
            filterObj: {
                ...reportFilters,
                filterData: mainFilterData,
                censusAverage: censusAverage,
                censusByPeriod: response?.censusByPeriod,
                question: reportsSubscription?.filtersData?.graphData?.filters?.question
            },
            reportFilters,
            filters: { type: reportsSubscription?.filtersData?.type },
            mainData: responseData
        });
        chartData.filterData = filterData;
        chartData.filterPatients = filterPatients;
        chartData.mainFilterData = mainFilterData;
        chartData.filterListData = mainData.filterListData;

        return {
            chartData,
            filterListData: filterData,
            res: { ...response, totals: mainData.res.totals }
        }
        // } else {
        //     chartData.mainFilterData = mainFilterData;
        //     chartData.filterListData = mainData.filterListData;
        //     return {
        //         chartData,
        //         filterListData: latestChartData,
        //         res: { ...response, totals: mainData.res.totals }
        //     }
        // }
    }
    return null
}

const sendCommunicationPageGraphReports = async (reportsSubscription) => {
    const { filtersData, transferTypes = [] } = reportsSubscription;
    let mainData = await sendCommunityPageReports(reportsSubscription);

    const req = await getRequestBody(reportsSubscription);

    if (req?.body?.cardFilter?.priorityData?.length > 0) {
        req.body.cardFilter.priorityData = req.body.cardFilter.priorityData.map(ele => {
            const listData = mainData?.filterListData?.[ele.type] || [];
            if (Array.isArray(ele.selectedItems) && listData.length > 0) {
                const matchedPatientIds = listData
                    .filter(item => ele.selectedItems.includes(String(item._id)))
                    .flatMap(item => item.patientIds.map(id => String(id)));
                ele.patientIds = matchedPatientIds;
            }
            return ele;
        });
    }

    const response = await getCommunityCardPatientChartData(
        { ...req, body: { ...req.body, transferType: transferTypes, relation: reportsSubscription?.filtersData?.graphData?.filters?.relation } }
    );
    await checkAnotherDashboardData(reportsSubscription, response);

    let mainFilterData = []
    if (mainData && mainData.filterListData && reportsSubscription?.filtersData?.type) {
        mainFilterData = reportsSubscription?.filtersData?.type === DECEASED_CARDS_TYPE.TOTAL ? [] : mainData.filterListData[reportsSubscription?.filtersData?.type];
    }

    if (response && response.data && response.data.length > 0) {
        const chartDataArr = await chartsData(response.data, reportsSubscription.filtersData.filter);
        const latestChartData = await chartGroupBy(
            chartDataArr,
            filtersData.graphData.selectedFilter,
            response?.censusAverage,
            response?.censusByPeriod,
            reportsSubscription.filtersData.filter
        );

        const responseData = response.data;

        let chartData = {
            mainData: responseData,
            filterPatients: responseData,
            filterData: latestChartData
        };

        let selectedFilterData = reportsSubscription?.filtersData.cardFilter[reportsSubscription.filterCardType]
        let filter = reportsSubscription.filtersData.filter;

        const { filterData, filterPatients } = await filterOptions({
            newChecked: selectedFilterData,
            chartMainDataArr: responseData,
            latestButtonFilterType: filtersData.graphData.buttonFilterType,
            filterObj: {
                ...filter,
                filterData: mainFilterData,
                censusAverage: response?.censusAverage,
                censusByPeriod: response?.censusByPeriod,
                question: reportsSubscription?.filtersData?.graphData?.filters?.question
            },
            filter,
            filters: { type: reportsSubscription?.filtersData?.type }
        });

        chartData.filterData = filterData;
        chartData.filterPatients = filterPatients;
        chartData.mainFilterData = mainFilterData;
        chartData.filterListData = mainData.filterListData

        return {
            chartData,
            filterListData: filterData,
            res: { ...response, totals: mainData.res.totals }
        }
    }
    return null
}

const sendAdmissionADTPageGraphReports = async (reportsSubscription) => {
    const { filtersData } = reportsSubscription
    const req = await getRequestBody(reportsSubscription);
    let selectedCardItem = reportsSubscription.filtersData?.graphData?.filters?.selectedCardItem
    const response = await getADTDetailsPatientChartData({
        ...req,
        body: {
            ...req.body,
            transferType: reportsSubscription.transferType,
            relation: reportsSubscription?.filtersData?.graphData?.filters?.relation,
            selectedCardItem
        }
    });
    let mainData = await sendAdmissionPageReports(reportsSubscription);
    let mainFilterData = []
    let defaultData = []
    if (selectedCardItem) {
        defaultData = _.find(mainData.filterListData.adtList, { childId: selectedCardItem.childId, parentId: selectedCardItem.parentId })?.tableData
    }

    if (mainData && mainData.filterListData && reportsSubscription?.filtersData?.type) {
        mainFilterData = _.filter(mainData.filterListData.adtList, { childId: selectedCardItem.childId, parentId: selectedCardItem.parentId })
    }

    if (response && response.data && response.data?.list?.length > 0) {
        const chartDataArr = await chartsData(response.data.list, reportsSubscription.filtersData.filter);
        const latestChartData = await chartGroupBy(
            chartDataArr,
            filtersData.graphData.selectedFilter,
            response?.censusAverage,
            response?.censusByPeriod,
            reportsSubscription.filtersData.filter
        );
        const responseData = response.data.list;
        let chartData = {
            mainData: responseData,
            filterPatients: responseData,
            filterData: latestChartData
        };

        let selectedFilterData = [];
        let data;
        if (selectedCardItem.type === ADT_TABLE_TYPE.ALL || selectedCardItem.type === ADT_TABLE_TYPE.TOTAL) {
            data = [];
            selectedFilterData = [];
        } else {
            data = selectedCardItem?.tableData || [];
            selectedFilterData = selectedCardItem?.selectedIds;
        }

        let filter = reportsSubscription.filtersData.filter;
        let filterData = []
        let filterPatients = []
        if (selectedFilterData && selectedFilterData.length > 0) {
            const resOptions = await ADTfilterOptions({
                newChecked: selectedFilterData,
                chartMainDataArr: responseData,
                chartData: responseData,
                arrData: responseData,
                originalData: response.data,
                latestButtonFilterType: filtersData.graphData.buttonFilterType,
                filterObj: {
                    ...filter,
                    filterData: mainFilterData,
                    censusAverage: response?.censusAverage,
                    censusByPeriod: response?.censusByPeriod,
                    question: reportsSubscription?.filtersData?.graphData?.filters?.question
                },
                filter,
                filters: { type: reportsSubscription?.filtersData?.type },
                defaultPatientData: responseData,
                selectedCardItem
            });
            filterData = resOptions?.filterData
            filterPatients = resOptions?.filterPatients
        } else {
            filterData = latestChartData
            filterPatients = responseData
        }

        chartData.filterData = filterData;
        chartData.filterPatients = filterPatients;
        chartData.mainFilterData = mainFilterData || [];
        chartData.filterListData = mainData.filterListData
        chartData.defaultData = defaultData;
        chartData.selectedCardItem = selectedCardItem;

        return {
            chartData,
            filterListData: filterData,
            res: { ...response, totals: mainData.res.totals }
        }
    }
    return null
}

const sendAdmissionPageGraphReports = async (reportsSubscription) => {
    const { filtersData } = reportsSubscription
    let mainData = await sendAdmissionPageReports(reportsSubscription);

    const req = await getRequestBody(reportsSubscription);

    if (req?.body?.cardFilter?.mainPriorityData?.length > 0) {
        // console.log(req.body.cardFilter.mainPriorityData, 'req.body.cardFilter.mainPriorityData');

        req.body.cardFilter.mainPriorityData = req.body.cardFilter.mainPriorityData.map(ele => {
            const listData = mainData?.filterListData?.[ele.type] || [];
            // console.log(listData, 'listData', ele.type);            
            if (Array.isArray(ele.selectedItems) && listData.length > 0) {
                const matchedPatientIds = listData
                    .filter(item => ele.selectedItems.includes(String(item._id)))
                    .flatMap(item => item.patientIds.map(id => String(id)));
                ele.patientIds = matchedPatientIds;
            }
            return ele;
        });
        console.log(req.body.cardFilter.mainPriorityData, 'req.body.cardFilter.mainPriorityData');
    }

    let latestCardFilter = await updateAdmissionChartFilter(reportsSubscription.filtersData.cardFilter, reportsSubscription?.filtersData?.type);

    // console.log(latestCardFilter, 'latestCardFilter');

    const response = await getAdmissionCardPatientChartData({ ...req, body: { ...req.body, transferType: reportsSubscription.transferType, relation: reportsSubscription?.filtersData?.graphData?.filters?.relation, cardFilter: latestCardFilter } });

    await checkAnotherDashboardData(reportsSubscription, response);

    let mainFilterData = []
    if (mainData && mainData.filterListData && reportsSubscription?.filtersData?.type) {
        mainFilterData = reportsSubscription?.filtersData?.type === DECEASED_CARDS_TYPE.TOTAL ? [] : mainData.filterListData[reportsSubscription?.filtersData?.type];
    }
    if (response && response.data && response.data.length > 0) {
        const chartDataArr = await chartsData(response.data, reportsSubscription.filtersData.filter);
        const latestChartData = await chartGroupBy(
            chartDataArr,
            filtersData.graphData.selectedFilter,
            response?.censusAverage,
            response?.censusByPeriod,
            reportsSubscription.filtersData.filter
        );

        const responseData = response.data;

        let chartData = {
            mainData: responseData,
            filterPatients: responseData,
            filterData: latestChartData
        };

        let selectedFilterData = reportsSubscription?.filtersData.cardFilter[reportsSubscription.filterCardType]
        let filter = reportsSubscription.filtersData.filter;
        let filterData = []
        let filterPatients = []
        if (selectedFilterData && selectedFilterData.length > 0) {
            const resFilterOption = await admissionFilterOptions({
                newChecked: selectedFilterData,
                chartMainDataArr: responseData,
                latestButtonFilterType: filtersData.graphData.buttonFilterType,
                filterObj: {
                    ...filter,
                    filterData: mainFilterData,
                    censusAverage: response?.censusAverage,
                    censusByPeriod: response?.censusByPeriod,
                    question: reportsSubscription?.filtersData?.graphData?.filters?.question
                },
                filter,
                filters: { type: reportsSubscription?.filtersData?.type },
                defaultPatientData: responseData
            });
            filterData = resFilterOption?.filterData
            filterPatients = resFilterOption?.filterPatients
        } else {
            filterData = latestChartData
            filterPatients = responseData
        }

        chartData.filterData = filterData;
        chartData.filterPatients = filterPatients;
        chartData.mainFilterData = mainFilterData;
        chartData.filterListData = mainData.filterListData

        return {
            chartData,
            filterListData: filterData,
            res: { ...response, totals: mainData.res.totals }
        }
    }
    return null
}

const sendDeceasedPageGraphReports = async (reportsSubscription) => {
    const { filtersData } = reportsSubscription
    let mainData = await sendDeceasedPageReports(reportsSubscription);

    const req = await getRequestBody(reportsSubscription);
    if (req?.body?.cardFilter?.priorityData?.length > 0) {

        req.body.cardFilter.priorityData = req.body.cardFilter.priorityData.map(ele => {
            const listData = mainData?.filterListData?.[ele.type] || [];
            if (Array.isArray(ele.selectedItems) && listData.length > 0) {
                const matchedPatientIds = listData
                    .filter(item => ele.selectedItems.includes(String(item._id)))
                    .flatMap(item => item.patientIds.map(id => String(id)));
                ele.patientIds = matchedPatientIds;
            }
            return ele;
        });

    }
    const response = await getCardPatientChartData({ ...req, body: { ...req.body, transferType: reportsSubscription.transferType, relation: reportsSubscription?.filtersData?.graphData?.filters?.relation } });

    await checkAnotherDashboardData(reportsSubscription, response);

    let mainFilterData = [];
    if (mainData && mainData.filterListData && reportsSubscription?.filtersData?.type) {
        mainFilterData = reportsSubscription?.filtersData?.type === DECEASED_CARDS_TYPE.TOTAL ? [] : mainData.filterListData[reportsSubscription?.filtersData?.type];
    }
    let censusAverage = reportsSubscription?.percentageBy && reportsSubscription?.percentageBy === ACCOUNT_PERCENTAGE_BY.BED ? response?.bedCapacity : response?.censusAverage;

    if (response && response.data && response.data.length > 0) {
        const chartDataArr = await chartsData(response.data, reportsSubscription.filtersData.filter);
        const latestChartData = await chartGroupBy(
            chartDataArr,
            filtersData.graphData.selectedFilter,
            censusAverage,
            response?.censusByPeriod,
            reportsSubscription.filtersData.filter
        );

        const responseData = response.data;

        let chartData = {
            mainData: responseData,
            filterPatients: responseData,
            filterData: latestChartData
        };

        let selectedFilterData = reportsSubscription?.filtersData.cardFilter[reportsSubscription.filterCardType]
        let filter = reportsSubscription.filtersData.filter;

        const { filterData, filterPatients } = await filterOptions({
            newChecked: selectedFilterData,
            chartMainDataArr: responseData,
            latestButtonFilterType: filtersData.graphData.buttonFilterType,
            filterObj: {
                ...filter,
                filterData: mainFilterData,
                censusAverage: censusAverage,
                censusByPeriod: response?.censusByPeriod,
                question: reportsSubscription?.filtersData?.graphData?.filters?.question
            },
            filter,
            filters: { type: reportsSubscription?.filtersData?.type }
        });

        chartData.filterData = filterData;
        chartData.filterPatients = filterPatients;
        chartData.mainFilterData = mainFilterData;
        chartData.filterListData = mainData.filterListData

        return {
            chartData,
            filterListData: filterData,
            res: { ...response, totals: mainData.res.totals }
        }
    }
    return null
}

const getADTByPageWise = async (reportsSubscription) => {
    try {

        let facilityData = [];
        const facilityIds = reportsSubscription?.facilityIds;
        const page = reportsSubscription.page
        if (facilityIds && facilityIds.length > 0) {
            facilityIds.map(ele => {
                facilityData.push(ele);
            });
        }

        let lastADTdate;

        if (reportsSubscription.lastEmailSentADTDate) {
            lastADTdate = await toStarDateNextDay(reportsSubscription.lastEmailSentADTDate);
        } else {
            lastADTdate = await toStartFilterDate(reportsSubscription?.createdAt);
            // lastADTdate = await toStartFilterDate(reportsSubscription?.filtersData?.lastADTDate);
            // if (moment(reportsSubscription?.filtersData?.lastADTDate).isBefore(reportsSubscription?.createdAt)) {
            //     lastADTdate = await toStartFilterDate(reportsSubscription?.filtersData?.lastADTDate);
            // } else {
            //     lastADTdate = await toStartFilterDate(reportsSubscription?.createdAt);
            // }
        }

        let query = {
            dateOfADT: {
                $gte: lastADTdate,
                $lt: moment(lastADTdate).add(2, "years").toDate(),
            },
            facilityId: { $in: facilityData },
        };        
        
        let lastEndDateOfADT = reportsSubscription?.filtersData?.lastADTDate;

        const resEndOfDateData = await getEndOfDateReportSettings(reportsSubscription?.userId, reportsSubscription?.accountId, reportsSubscription?.facilityIds);
        const endDateOfADT = resEndOfDateData && resEndOfDateData.endDateOfADT ? resEndOfDateData?.endDateOfADT : null;
        if (endDateOfADT) {
            lastEndDateOfADT = await toStartFilterDate(resEndOfDateData.endDateOfADT);
        }

        // if (endDateOfADT && moment(lastEndDateOfADT).isBefore(endDateOfADT)) {
        //     lastEndDateOfADT = await toStartFilterDate(resEndOfDateData.endDateOfADT);
        // }
        console.log(lastEndDateOfADT, 'lastEndDateOfADT');
        console.log(query, 'query');
        
        let beforeEightHoursOneMinute = moment().utc().subtract(8, 'hours').subtract(1, 'minute').format('YYYY-MM-DD HH:mm:ss');
        console.log(beforeEightHoursOneMinute, 'beforeEightHoursOneMinute');
        
        const patient = await Patient.findOne({ ...query }) // createdAt: { $lte: beforeEightHoursOneMinute }
            .select('dateOfADT createdAt facilityId type transferType')
            .sort({ dateOfADT: -1, createdAt: -1 })
            .catch(e => {
                return e;
            });
            console.log(patient, 'patient');
            
        if (patient) {
            let latestDateOfADT = endDateOfADT ? await toDailyDate(endDateOfADT, "end") : await toDailyDate(patient.dateOfADT, "end");

            if (reportsSubscription.interval === AUTOMATICALLY_REPORT_TYPE.WEEKLY) {
                let lastDateOfWeek
                if (reportsSubscription.lastEmailSentADTDate) {
                    lastDateOfWeek = await toWeekDate(lastADTdate, "end")
                } else {
                    lastDateOfWeek = await toWeekDate(lastADTdate, "end");
                    //lastDateOfWeek = await toWeekDate("2023-12-28", "end");
                }

                const dates = [];

                if (patient) {
                    if (moment(latestDateOfADT).isSameOrAfter(lastDateOfWeek)) {
                        let date = lastDateOfWeek;

                        while (moment(date).isSameOrBefore(latestDateOfADT)) {
                            let monthStartDate = await toWeekDate(date);
                            let nextDate = await toStarDateNextDay(date);
                            dates.push({
                                startDate: moment.utc(monthStartDate).format("YYYY-MM-DD"),
                                endDate: moment.utc(date).format("YYYY-MM-DD")
                            });
                            date = await toWeekDate(nextDate, "end");
                        }
                    }
                }
                return dates //dates
            } else if (reportsSubscription.interval === AUTOMATICALLY_REPORT_TYPE.SEVEN_DAYS) {
                let lastDateOfMonth;
                if (reportsSubscription.lastEmailSentADTDate) {
                    lastDateOfMonth = await toSevenDate(lastADTdate, "end")
                } else {
                    lastDateOfMonth = await toSevenDate(lastADTdate, "end");
                }

                const dates = [];

                if (patient) {
                    if (moment(latestDateOfADT).isSameOrAfter(lastDateOfMonth)) {
                        let date = lastDateOfMonth;
                        while (moment(date).isSameOrBefore(latestDateOfADT)) {
                            let monthStartDate = await toSevenDate(date);
                            let nextDate = await toStarDateNextDay(date);
                            dates.push({
                                startDate: moment.utc(monthStartDate).format("YYYY-MM-DD"),
                                endDate: moment.utc(date).format("YYYY-MM-DD")
                            });
                            date = await toSevenDate(nextDate, "end");
                        }
                    }
                }
                return dates //dates
            } else if (reportsSubscription.interval === AUTOMATICALLY_REPORT_TYPE.MONTHLY) {
                let lastDateOfMonth
                if (reportsSubscription.lastEmailSentADTDate) {
                    lastDateOfMonth = await toMonthDate(lastADTdate, "end")
                } else {
                    lastDateOfMonth = await toMonthDate(lastADTdate, "end");
                }

                const dates = [];

                if (patient) {
                    if (moment(latestDateOfADT).isSameOrAfter(lastDateOfMonth)) {
                        let date = lastDateOfMonth;

                        while (moment(date).isSameOrBefore(latestDateOfADT)) {
                            let monthStartDate = await toMonthDate(date);
                            let nextDate = await toStarDateNextDay(date);
                            dates.push({
                                startDate: moment.utc(monthStartDate).format("YYYY-MM-DD"),
                                endDate: moment.utc(date).format("YYYY-MM-DD")
                            });
                            date = await toMonthDate(nextDate, "end");
                        }
                    }
                }
                return dates //dates
            } else if (reportsSubscription.interval === AUTOMATICALLY_REPORT_TYPE.QUARTERLY) {
                let lastDateOfMonth
                if (reportsSubscription.lastEmailSentADTDate) {
                    lastDateOfMonth = await toQuatreDate(lastADTdate, "end")
                } else {
                    lastDateOfMonth = await toQuatreDate(lastADTdate, "end");
                }

                const dates = [];

                if (patient) {
                    if (moment(latestDateOfADT).isSameOrAfter(lastDateOfMonth)) {
                        let date = lastDateOfMonth;
                        while (moment(date).isSameOrBefore(latestDateOfADT)) {
                            let monthStartDate = await toQuatreDate(date);
                            let nextDate = await toStarDateNextDay(date);
                            dates.push({
                                startDate: moment(monthStartDate).format("YYYY-MM-DD"),
                                endDate: moment.utc(date).format("YYYY-MM-DD")
                            });
                            date = await toQuatreDate(nextDate, "end");
                        }
                    }
                }
                return dates //dates
            } else if (reportsSubscription.interval === AUTOMATICALLY_REPORT_TYPE.YEARLY) {
                let lastDateOfMonth
                if (reportsSubscription.lastEmailSentADTDate) {
                    lastDateOfMonth = await toYearDate(lastADTdate, "end")
                } else {
                    lastDateOfMonth = await toYearDate(lastADTdate, "end");
                }

                const dates = [];

                if (patient) {
                    if (moment(latestDateOfADT).isSameOrAfter(lastDateOfMonth)) {
                        let date = lastDateOfMonth;
                        while (moment(date).isSameOrBefore(latestDateOfADT)) {
                            let monthStartDate = await toYearDate(date);
                            let nextDate = await toStarDateNextDay(date);
                            dates.push({
                                startDate: moment(monthStartDate).format("YYYY-MM-DD"),
                                endDate: moment.utc(date).format("YYYY-MM-DD")
                            });
                            date = await toYearDate(nextDate, "end");
                        }
                    }
                }
                return dates //dates
            } else {
                const dates = [];
                let patients = await Patient.find({ ...query }).select('dateOfADT createdAt facilityId type transferType').sort({ dateOfADT: -1, createdAt: -1 })

                const dateOfADTSDaily = patients?.map(ele => moment(ele?.dateOfADT).format("YYYY-MM-DD")) ?? [];

                if (patient && moment(latestDateOfADT).isAfter(lastADTdate)) {

                    let latestDateOfADTDaily = latestDateOfADT;

                    let date = lastADTdate;

                    while (moment(date).isSameOrBefore(latestDateOfADTDaily)) {

                        let dailyStartDate = await toDailyDate(date);

                        let nextDate = await toStarDateNextDay(date);

                        let startDate = moment(dailyStartDate).format("YYYY-MM-DD");

                        if (dateOfADTSDaily.includes(startDate)) {
                            dates.push({
                                startDate: startDate,
                                endDate: moment.utc(date).format("YYYY-MM-DD")
                            });
                        }
                        date = await toDailyDate(nextDate, "end");
                    }
                }
                return dates
            }
        }
    } catch (err) {
        console.log(err, 'getting data error');
    }
}

// End graph reports 

const generatePDFFromHtml = async (htmlContent) => {
    const browser = await puppeteer.launch(
        {
            headless: 'new',
            args: [
                "--disable-gpu",
                "--disable-dev-shm-usage",
                "--disable-setuid-sandbox",
                "--no-sandbox"
            ]
        }
    );

    const page = await browser.newPage();
    await page.setContent(htmlContent);
    //await page.pdf({ path: outputPath, format: "A4" });

    const buffer = await page.pdf({ format: "A4" });
    const base64 = buffer.toString('base64');
    await browser.close();
    return base64;
};


module.exports = {
    jobProcessor,
};