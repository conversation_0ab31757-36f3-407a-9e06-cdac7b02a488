import axios from "../../axios";
import { useSelector } from "react-redux";
import styles from "./Users.module.scss";
import { useEffect, useState } from "react";
import AddUser from "./add-user/AddUser";
import { getOnlyRoles } from "../../services/role.service";

const readableString = (stringValue) =>
  !stringValue?.length ? "" : stringValue.replace(/([A-Z]+)/g, " $1"); //.replace(/([A-Z][a-z])/g, " $1");

const Users = (props) => {
  const { auth } = useSelector(({ auth }) => ({ auth }));
  const [users, setUsers] = useState([]);
  const [showAdd, setShowAdd] = useState(false);
  const [roles, setRoles] = useState([]);
  const togglePopup = () => setShowAdd(!showAdd);

  const getUsers = async () => {
    let users = await axios.get(`/api/user/${auth.accountId}`);
    setUsers(users.data);
  };

  useEffect(() => {
    getOnlyRoles().then((res) => {
      setRoles(res || [])
    });
  }, []);

  useEffect(() => {
    if (!auth?.accountId) return;
    getUsers();
  }, [auth]);

  return (
    <div className={styles.usersPage}>
      <div className={`df aic ${styles.pageHdr}`}>
        <div className={`mla`}>
          <button
            className={`ttuc fs12 ffmm ${styles.addBtn}`}
            onClick={togglePopup}
          >
            + Add user
          </button>
        </div>
      </div>
      {users.map((user) => (
        <div className={`df aic ffmr fs14 ${styles.userLine}`} key={user._id}>
          <div className={`${styles.name} ${styles.sec}`}>
            <p className={`fs10 m-b-4 ${styles.lbl}`}>Name</p>
            <p className={styles.val}>{user.fullName}</p>
          </div>
          <div className={`${styles.email} ${styles.sec}`}>
            <p className={`fs10 m-b-4 ${styles.lbl}`}>Email</p>
            <p className={styles.val}>{user.email}</p>
          </div>
          <div className={`${styles.type} ${styles.sec}`}>
            <p className={`fs10 m-b-4 ${styles.lbl}`}>Type</p>
            <p className={styles.val}>{readableString(user.type)}</p>
          </div>
          <div className={`df ${styles.actions} ${styles.sec} p-r-0`}>
            <div className={styles.moreIcon} />
          </div>
        </div>
      ))}
      {showAdd ? (
        <AddUser close={togglePopup} refreshData={getUsers} roles={roles} />
      ) : undefined}
    </div>
  );
};

export default Users;
