import React from 'react';
import { Box, CircularProgress, Typography } from '@mui/material';

const LoadingBlock = ({ message = '', size = 30 }) => (
    <Box display="flex" flexDirection="column" alignItems="center" justifyContent="center" py={2}>
        <CircularProgress size={size} />
        {message && <Typography variant="caption" mt={1}>{message}</Typography>}
    </Box>
);

export default LoadingBlock; 