import React from 'react';
import {
    Box,
    Typography,
    Card,
    CardContent,    
    Alert,
} from '@mui/material';
import PeopleIcon from '@mui/icons-material/People';
import VisibilityIcon from '@mui/icons-material/Visibility';
import BarChartIcon from '@mui/icons-material/BarChart';

const ResidentListOnlyInfo = () => {
    return (
        <Box sx={{ mt: 2 }}>
            <Typography variant="h6" sx={{ mb: 2, display: 'flex', alignItems: 'center' }}>
                <PeopleIcon sx={{ mr: 1 }} />
                Resident List Only Configuration
            </Typography>

            <Alert severity="info" sx={{ mb: 2 }}>
                This time tab will be visible only in the resident list view and won't affect other views like graphs or reports.
            </Alert>

            <Card sx={{ bgcolor: '#f8f9ff', border: '1px solid #e3f2fd' }}>
                <CardContent>
                    <Typography variant="subtitle1" sx={{ mb: 2, fontWeight: 'bold' }}>
                        Mode Overview:
                    </Typography>
                    
                    <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                            <VisibilityIcon color="primary" />
                            <Typography variant="body2">
                                <strong>Visible in:</strong> Resident List View Only
                            </Typography>
                        </Box>
                        
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                            <BarChartIcon color="disabled" />
                            <Typography variant="body2" color="textSecondary">
                                <strong>Not visible in:</strong> Graphs, Charts, Reports, Analytics
                            </Typography>
                        </Box>
                    </Box>

                    <Box sx={{ mt: 2 }}>
                        <Typography variant="body2" color="textSecondary">
                            This mode is perfect for time-sensitive questions that are only relevant when viewing individual resident data.
                        </Typography>
                    </Box>
                </CardContent>
            </Card>
        </Box>
    );
};

export default ResidentListOnlyInfo; 