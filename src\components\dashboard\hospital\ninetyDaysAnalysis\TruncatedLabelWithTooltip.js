import React from 'react';
import { Tooltip, Typography } from '@mui/material';

const TruncatedLabelWithTooltip = ({ label, charLimit = 9 }) => {
  return (
    <Tooltip title={label} disableHoverListener={label?.length <= charLimit}>
      <Typography
        variant="subtitle4"
        sx={{
          maxWidth: '100%',
          overflow: 'hidden',
          whiteSpace: 'nowrap',
          textOverflow: 'ellipsis',
          display: 'inline-block',
        }}
      >
        {label.slice(0, charLimit)}
      </Typography>
    </Tooltip>
  );
};

export default TruncatedLabelWithTooltip;
