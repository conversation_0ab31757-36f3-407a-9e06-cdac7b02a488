const mongoose = require("mongoose");
const { Schema, Types } = mongoose;

const ReportLogSchema = new Schema(
  {
    data: { type: Object, required: false, default: null },
    description: { type: String, required: false, default: null },
    event: { type: String, required: false, default: null },
    date: { type: Date, required: false, default: null },
  },
  { timestamps: true }
);

mongoose.model("reportLogs", ReportLogSchema);
