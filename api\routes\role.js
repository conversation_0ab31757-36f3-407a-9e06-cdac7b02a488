const { authenticate } = require("../middleware/authenticate");
const { getRolesList, updateRole, getRole, getAllPermissions, getOnlyRolesList } = require("../helpers/role");
const authWithRole = require("../middleware/auth-with-role");

const route = require("express").Router();

// Create new account
route.post("/", authenticate, async (req, res) => {
  try {
    let account = await createFilter(req);
    res.send(account);
  } catch (error) {
    res.status(500).send(error);
  }
});

route.get("/all", authWithRole('manageRoles'), async (req, res) => {
  try {
    const roles = await getRolesList(req);
    res.send(roles);
  } catch (error) {
    res.status(500).send(error);
  }
});

route.get("/select/options", authWithRole('manageRoleOption'), async (req, res) => {
  try {
    const roles = await getOnlyRolesList(req);
    res.send(roles);
  } catch (error) {
    res.status(500).send(error);
  }
});

// Get accounts/account by ID
route.get("/:id?", authenticate, async (req, res) => {
  try {
    let account = await getRole(req.params.id);
    res.send(account);
  } catch (error) {
    res.status(500).send(error);
  }
});

route.put("/:id", authenticate, async (req, res) => {
  try {
    const role = await updateRole(req.params.id, req.body);
    res.send(role);
  } catch (error) {
    res.status(500).send(error);
  }
});



module.exports = route;
