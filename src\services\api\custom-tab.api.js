import api from './api';

const API_PREFIX = "api/custom-tab";

const saveCustomTab = async (body) => {
    const response = await api.post(`${API_PREFIX}`, body);
    return response;
};

// const editFacility = async (id, body) => {
//     const response = await api.put(`${API_PREFIX}/${id}`, body);
//     return response;
// };

const getCustomTabs = async (params) => {
    const response = await api.get(`${API_PREFIX}`, { params });
    return response;
};

const deleteCustomTab = async (id) => {
    const response = await api.delete(`${API_PREFIX}/${id}`);
    return response;
};

const getDynamicTabById = async (id) => {
    const response = await api.get(`${API_PREFIX}/tab-by-id/${id}`);
    return response;
};

export {
    saveCustomTab,
    getCustomTabs,
    deleteCustomTab,
    getDynamicTabById
};
