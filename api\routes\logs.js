const mongoose = require("mongoose");
const TimeLog = mongoose.model("timeLog");
const dayjs = require("dayjs");
const authWithRole = require("../middleware/auth-with-role");
const route = require("express").Router();

route.post("/timelog", authWithRole("manageLogs"), async (req, res) => {
	try {
		const dayJsObject = dayjs(new Date());
		const date = dayJsObject.format("MM/DD/YYYY");
		const time = dayJsObject.format("HH:mm");
		const user = req?.user;
		const result = await TimeLog.findOneAndUpdate(
			{
				userId: user?._id,
				accountId: req.body.accountId,
			},
			{
				$setOnInsert: {
					userId: user?._id,
					accountId: req.body.accountId,
				},
				$push: {
					dates: {
						$each: [{ date: date, times: [time] }],
						$position: 0,
					},
				},
			},
			{
				new: true,
				upsert: true,
				runValidators: true,
			}
		);
		if (result) {
			// Remove duplicate date entries and merge times
			const uniqueDates = {};
			result.dates.forEach((dateObj) => {
				if (!uniqueDates[dateObj.date]) {
					uniqueDates[dateObj.date] = new Set(dateObj.times);
				} else {
					dateObj.times.forEach((time) => uniqueDates[dateObj.date].add(time));
				}
			});

			result.dates = Object.entries(uniqueDates).map(([date, timesSet]) => ({
				date,
				times: Array.from(timesSet),
			}));

			// Sort dates array to keep the most recent date at the beginning
			result.dates.sort((a, b) => new Date(b.date) - new Date(a.date));

			await result.save();

			res.send("Time log added successfully");
		} else {
			res.status(500).send("Failed to add time log");
		}
	} catch (e) {
		res.status(500).send("Internal Server Error");
	}
});

route.get("/time", authWithRole("manageLogs"), async (req, res) => {
	try {
		const today = dayjs();

		if (req.query.userId) {
			// Calculate the start and end dates for the past month
			const startDate = today.subtract(1, "month").startOf("month");
			const endDate = today.endOf("month");

			// Convert the start and end dates to the desired format
			const startDateStr = startDate.format("MMDDYYYY");
			const endDateStr = endDate.format("MMDDYYYY");

			// Query the model to retrieve the timelogs for the past month
			const timelogs = await TimeLog.find({
				date: {
					$gte: startDateStr,
					$lte: endDateStr,
				},
				userId: req.query.userId,
			});
			res.send(timelogs);
		} else {
			res.send([]);
		}
	} catch (e) {
		res.send(500);
	}
});

// GET /api/log/timeLogs?userId=xxx&accountId=yyy&start=2025-01-01&end=2025-04-01
route.get("/timeLogs", authWithRole("manageLogs"), async (req, res) => {
	try {
		const { userId, accountId, start, end } = req.query;
		if (!userId || !start || !end) {
			return res.status(400).send("Missing required parameters (userId, start, end).");
		}

		if (!mongoose.Types.ObjectId.isValid(userId)) {
			return res.status(400).send("Invalid userId.");
		}

		const matchConditions = [{ userId: new mongoose.Types.ObjectId(userId) }];
		if (accountId && mongoose.Types.ObjectId.isValid(accountId)) {
			matchConditions.push({ accountId: new mongoose.Types.ObjectId(accountId) });
		}

		const startDate = new Date(start);
		const endDate = new Date(end);

		const results = await TimeLog.aggregate([
			{ $match: { $and: matchConditions } },
			{ $unwind: "$dates" },
			{
				$addFields: {
					dateAsDate: {
						$dateFromString: {
							dateString: "$dates.date",
							format: "%m/%d/%Y",
						},
					},
				},
			},
			{
				$match: {
					dateAsDate: { $gte: startDate, $lt: endDate },
				},
			},
			{
				$group: {
					_id: {
						docId: "$_id",
						userId: "$userId",
						accountId: "$accountId",
					},
					dates: {
						$push: {
							date: "$dates.date",
							times: "$dates.times",
						},
					},
				},
			},
			{
				$project: {
					_id: 0,
					docId: "$_id.docId",
					userId: "$_id.userId",
					accountId: "$_id.accountId",
					dates: 1,
				},
			},
		]);

		const logs = results.map(doc => ({
			_id: doc.docId,
			userId: doc.userId,
			accountId: doc.accountId,
			dates: doc.dates,
		}));

		res.send(logs);
	} catch (err) {
		console.error(err);
		res.status(500).send("Internal Server Error");
	}
});


module.exports = route;
