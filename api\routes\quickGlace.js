const { createQuickGlace, getQuickGlacesFilterData, getQuickGlaces } = require("../helpers/quick-glace");
const authWithRole = require("../middleware/auth-with-role");

const route = require("express").Router();

// Create new account
route.post("/", authWithRole("manageDashboard"), async (req, res) => {
    try {
        let account = await createQuickGlace(req);
        res.send(account);
    } catch (error) {
        res.status(500).send(error);
    }
});

route.post("/glace-filter-data", authWithRole("manageDashboard"), async (req, res) => {
    try {
        let account = await getQuickGlacesFilterData(req);
        res.send(account);
    } catch (error) {
        res.status(500).send(error);
    }
});

// Get accounts/account by ID
route.get("/:id?", authWithRole("manageDashboard"), async (req, res) => {
    try {
        let account = await getQuickGlaces(req);
        res.send(account);
    } catch (error) {
        res.status(500).send(error);
    }
});

module.exports = route;
