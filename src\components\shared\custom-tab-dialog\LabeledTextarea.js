import React from 'react';
import { TextareaAutosize } from '@mui/material';

const LabeledTextarea = ({ label, value, onChange, error = false, helperText = '', minRows = 4, maxRows = 8, ...props }) => (
    <TextareaAutosize
        fullWidth
        label={label}
        value={value}
        onChange={onChange}
        error={error}
        helperText={helperText}
        multiline
        minRows={minRows}
        maxRows={maxRows}
        {...props}
    />
);

export default LabeledTextarea; 