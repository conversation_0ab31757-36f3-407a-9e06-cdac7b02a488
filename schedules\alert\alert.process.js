const { getAllCount } = require("../../api/helpers/hospital");
const { getAllCount: getAllCommunityTransfer } = require("../../api/helpers/community-transfer");
const { getAllCount: getAllAdmissions } = require("../../api/helpers/admission");
const { getAllCount: getAllDeceased } = require("../../api/helpers/deceased");
const { getAllCount: getAllOverall } = require("../../api/helpers/overall");
const moment = require('moment-timezone');
// Set default timezone for all moment instances to "America/Atikokan"
moment.tz.setDefault("America/Atikokan");

const _ = require("lodash");
const { updateFilterListData } = require("./alert.db.data");
const { checkTransferAlerts } = require("./alert.rising.calculation");
const { checkDroppingTransferAlerts } = require("./alert.dropping");
const { sendAlertEmail } = require("../../sendEmail");
const { findHighestReasonAndSendAlert } = require("./alert.transfer.need.works");
const { getDateRangesFromGivenDate, getCurrentDateRange } = require("./alert.common.utils");
const { getEndOfDateReportSettings } = require("../../api/helpers/facilityManuallyEndOfADT");
const { ALERT_TYPE_TYPE, PAGE_TYPE } = require("../../types/common.type");
const { ADT_TYPES, ADT_SUB_TYPES } = require("../../types");
const { getQuestionsData } = require("../../api/helpers/dynamicDataTab");
const { getCustomTabsByPage } = require("../../api/helpers/custom-tab");


async function processItemSendEmail(items) {
    for (let i = 0; i < items.length; i++) {
        const emailDataArr = items[i];
        const { email, emailTemplate, subject } = emailDataArr;
        console.log(emailTemplate, 'emailTemplate');

        await sendAlertEmail(email, emailTemplate, subject);
    }
}

const processAlertReportType = async (alertReport, updateAlertReports) => {

    const alertReportType = alertReport.type // [ALERT_TYPE_TYPE.WEEKS, ALERT_TYPE_TYPE.BI_WEEKS, ALERT_TYPE_TYPE.MONTHS]; //alertReport.type
    const alertBYtypes = [];

    for (const type of alertReportType) {
        const response = await getAlertPatientData({ ...alertReport._doc, type }, updateAlertReports); // Replace with the async task you need to perform
        if (response) {
            alertBYtypes.push(response);
        }
    }

    // START uncomment this code to send email
    if (alertBYtypes && alertBYtypes.length > 0) {
        const sortedData = _.orderBy(alertBYtypes, ['order'], ['desc']);

        for (let i = 0; i < sortedData.length; i++) {
            const item = sortedData[i];

            // console.log(item?.emailData, 'item?.emailData');

            try {

                if (i === 0 && item?.emailData && item?.emailData?.length > 0) {
                    await processItemSendEmail(item?.emailData);
                }
                const { alertReport, endDate, lastUpdateDate, alertReportType } = item?.update
                // await updateAlertReports(alertReport, endDate, lastUpdateDate, alertReportType);

            } catch (error) {
                console.error(`Error processing item with order ${item.order}:`, error);
            }
        }
    }
};

const getPageData = async (pageType, currentWeekQueryParams, last4WeeksQuery, fetchFunctions) => {
    const fetchFunction = fetchFunctions[pageType];
    if (!fetchFunction) throw new Error(`Invalid page type: ${pageType}`);

    // const currentWeekData = await fetchFunction({ ...currentWeekQueryParams, query: { ...currentWeekQueryParams.query, startDate: "2025-05-01" } });
    console.log(currentWeekQueryParams, 'currentWeekQueryParams');
    
    const currentWeekData = await fetchFunction(currentWeekQueryParams);
    const last4WeeksData = await Promise.all(
        last4WeeksQuery.map(weekQuery =>
            fetchFunction({
                ...currentWeekQueryParams,
                query: weekQuery,
            })
        )
    );
    // console.log(currentWeekData, 'currentWeekData');

    return { currentWeekData, last4WeeksData };
};

const fetchFunctions = {
    [PAGE_TYPE.HOSPITAL]: getAllCount,
    [PAGE_TYPE.COMMUNITY_TRANSFER]: getAllCommunityTransfer,
    [PAGE_TYPE.ADMISSION]: getAllAdmissions,
    [PAGE_TYPE.DECEASED]: getAllDeceased,
    [PAGE_TYPE.OVERALL]: getAllOverall,
};

/**
 * Retrieves alert patient data based on the provided alert report.
 * @param {Object} alertReport - The alert report containing facility and account IDs.
 * @returns {Object} - An object containing various processed patient data.
 */
async function getAlertPatientData(alertReport) {
    try {

        const headers = {
            facilityid: alertReport?.facilityId?._id?.toString(),
            accountid: alertReport?.accountId?.toString(),
        };

        const censusAndLastADTData = await getEndOfDateReportSettings(alertReport?.userId, alertReport?.accountId, [headers.facilityid]);

        const alertReportType = alertReport.type;

        let order = 0;
        if (alertReportType === ALERT_TYPE_TYPE.WEEKS) {
            order = 1;
        } else if (alertReportType === ALERT_TYPE_TYPE.BI_WEEKS) {
            order = 2;
        } else if (alertReportType === ALERT_TYPE_TYPE.MONTHS) {
            order = 3;
        }
        const page = alertReport.page;

        const censusDate = censusAndLastADTData?.censusDate;

        const endDateOfADT = censusAndLastADTData?.endDateOfADT;

        const { isRising, isDropping, isTransferNeedWork } = alertReport;
        const lastCheckDate = alertReport[`lastCheckDate_${alertReportType}`];
        const lastEmailSendDate = alertReport[`lastEmailSendDate_${alertReportType}`];

        const sendAlertReportPrepare = async (currentWeekQuery, last4WeeksQuery, currentDateRangeEndDate) => {

            const currentWeekQueryParams = {
                headers,
                query: currentWeekQuery,
                user: alertReport?.userId,
            };

            const { currentWeekData, last4WeeksData } = await getPageData(
                page,
                currentWeekQueryParams,
                last4WeeksQuery,
                fetchFunctions
            );
            // console.log(last4WeeksQuery, 'last4WeeksQuery');
             console.log(currentWeekQuery, 'currentWeekQuery');


            let forType = page;
            let forTransferType = [];
            if (page === PAGE_TYPE.HOSPITAL) {
                forType = ADT_TYPES.TRANSFER;
                forTransferType = ["hospitalTransfer"]
            } else if (page === PAGE_TYPE.COMMUNITY_TRANSFER) {
                forType = ADT_TYPES.TRANSFER;
                forTransferType = [ADT_SUB_TYPES.AMA, ADT_SUB_TYPES.SAFE_DISCHARGE, ADT_SUB_TYPES.SNF]
            } else if (page === PAGE_TYPE.DECEASED) {
                forType = ADT_TYPES.TRANSFER;
                forTransferType = [ADT_SUB_TYPES.DECEASED]
            } else if (page === PAGE_TYPE.ADMISSION) {
                forType = ADT_TYPES.ADMISSIONS;
                //forTransferType = [];
            }

            let reQuestion = {
                headers: { accountid: alertReport?.accountId },
                query: {
                    forType,
                    forTransferType,
                    isCustom: true
                }
            }

            const dynamicCardsArr = await getQuestionsData(reQuestion) ?? [];
            const dynamicCards = dynamicCardsArr ?? [];

            let customTabs = await getCustomTabsByPage({ accountid: alertReport?.accountId, page });
            const customAlertsArr = [];

            if (customTabs.length > 0) {
                for (const ele of customTabs) {
                    let accessor = ele.accessor;
                    if (accessor) {
                        // if (alertReport.alerts && Object.prototype.hasOwnProperty.call(alertReport.alerts, accessor) && alertReport.alerts[accessor]) {
                        customAlertsArr.push({ ...ele, slug: accessor, isCustomTab: true });
                        // }
                    }
                }
                customTabs = customAlertsArr;
            }

            // console.log(dynamicCards, 'dynamicCards');
            // console.log(last4WeeksData, 'last4WeeksData here');

            const lastWeekDataArr = await Promise.all(last4WeeksData.map(data => updateFilterListData(data, page, alertReport, dynamicCards, customTabs, true)));

            const allData = {
                currentWeek: await updateFilterListData(currentWeekData, page, alertReport, dynamicCards, customTabs),
                last4Weeks: lastWeekDataArr,
            };
            const dataTypes = Object.keys(alertReport?.alerts || {}).filter(alert => alertReport.alerts[alert]);
            // console.log(allData.currentWeek, 'allData.currentWeek');

            dataTypes.map(ele => {
                console.log(allData.currentWeek?.[ele], 'allData.currentWeek' + ele);
                // allData.last4Weeks.map((ele4Week) => {
                //     console.log(ele4Week[ele], "kala");

                // });
            });
            // //console.log(allData.last4Weeks, 'allData.last4Weeks');




            if (dataTypes.length === 0) {
                console.warn('No data types selected for alert processing.');
                return;
            }

            if (dataTypes.length > 0) {
                const averages = await calculateAverageForEachField(allData.last4Weeks, page, alertReport, dynamicCards, customTabs);
                // console.log(averages, 'averages');

                const addAlertType = (data, type) => data?.map(item => ({ ...item, type })) || [];

                // "X Transfers Are Rising" Alert:
                let rising = [];
                let dropping = [];
                // let transfersNeedWork = [];

                const pageLabel = getPageLabel(page);
                if (isRising) {
                    rising = await checkTransferAlerts([allData?.currentWeek], [averages], handleEmptyData, dataTypes, alertReport, pageLabel);
                    rising = await addAlertType(rising, 'rising');
                }
                // console.log(rising, 'rising 11');

                if (isDropping) {
                    dropping = await checkDroppingTransferAlerts([allData?.currentWeek], [averages], handleEmptyData, dataTypes, alertReport, pageLabel);
                    dropping = await addAlertType(dropping, 'dropping');
                }

                // # we commented transfer need work alerts based on this task : #SNFSOL-629
                // if (isTransferNeedWork) {
                //     transfersNeedWork = await findHighestReasonAndSendAlert(allData?.currentWeek, averages, handleEmptyData, dataTypes, allData?.last4Weeks, alertReport, pageLabel);
                //     transfersNeedWork = await addAlertType(transfersNeedWork, 'transferNeedWork');
                // }

                // Call the function to merge the arrays with priority logic
                const finalAlerts = await mergeAlerts(rising, dropping);

                if (finalAlerts?.length > 0 && alertReport?.userId) {
                    const { fullName, email } = alertReport.userId;
                    const finalAlertsByType = _.groupBy(finalAlerts, 'type');
                    const alertTypesEmail = [];

                    for (const [alertType, alerts] of Object.entries(finalAlertsByType)) {
                        try {
                            // Assuming generateEmailTemplate returns an object with emailTemplate and subject
                            const { emailTemplate, subject } = await generateEmailTemplate(
                                fullName,
                                alerts,
                                alertType,
                                alertReport?.facilityId?.name,
                                currentDateRangeEndDate,
                                alertReportType,
                                page
                            );

                            if (emailTemplate && subject) {
                                alertTypesEmail.push({
                                    email,
                                    subject,
                                    emailTemplate,
                                    alertType,
                                    alertReportType,
                                })
                            } else {
                                console.log(`Missing emailTemplate or subject for alert type: ${alertType}`);
                            }
                        } catch (error) {
                            console.log(`Error generating or sending email for alert type: ${alertType}`, error);
                        }
                    }

                    return alertTypesEmail
                } else {
                    console.warn('No alerts or missing user data to process.');
                }
            }
        }

        const calculateConditions = async (today, order) => {
            console.log(censusDate, 'censusDate', today);

            const yesterdayDate = moment(today, "YYYY-MM-DD").subtract(1, 'days').format('YYYY-MM-DD');

            console.log(yesterdayDate, 'yesterdayDate');

            const currentDateRange = await getCurrentDateRange(alertReportType, yesterdayDate)[0];
            console.log(currentDateRange, 'currentDateRange');

            // let currentDateRangeEndDateNextDate = currentDateRange?.startDate;

            let currentDateRangeEndDateNextDate = moment(currentDateRange?.startDate).subtract(1, 'days').format('YYYY-MM-DD');
            // if (alertReportType === ALERT_TYPE_TYPE.BI_WEEKS) {
            // }

            const dateRanges = await getDateRangesFromGivenDate(alertReportType, 4, currentDateRangeEndDateNextDate);

            const firstDateRange = dateRanges[0];

            if (moment(firstDateRange?.startDate).isSameOrAfter(censusDate) && endDateOfADT) {
                // send email

                const endDateOfADTData = moment(moment(endDateOfADT).format("YYYY-MM-DD")); // Replace with your date input
                const currentDateRangeEndDate = moment(currentDateRange?.endDate);

                if (endDateOfADTData && moment(endDateOfADTData).isSameOrAfter(currentDateRangeEndDate)) {
                    // send email because end date of ADT is after current date range
                    console.log("Send email because end date of ADT is after current date range");

                    const res = await sendAlertReportPrepare(currentDateRange, dateRanges, currentDateRangeEndDate);

                    // await updateAlertReports(alertReport, currentDateRange?.endDate, `lastEmailSendDate_${alertReportType}`, alertReportType);
                    return {
                        emailData: res,
                        order,
                        update: {
                            alertReport,
                            endDate: currentDateRange?.endDate,
                            lastUpdateDate: `lastEmailSendDate_${alertReportType}`,
                            alertReportType
                        }
                    };
                } else {
                    const alertUpdateDate = moment(today).format("YYYY-MM-DD");
                    // if not getting matched data then saved in database for check next date
                    console.log("Not getting matched data then saved in database for check next date", today);

                    // await updateAlertReports(alertReport, alertUpdateDate, `lastCheckDate_${alertReportType}`, alertReportType);
                    return {
                        emailData: null,
                        update: {
                            alertReport,
                            order,
                            endDate: alertUpdateDate,
                            lastUpdateDate: `lastCheckDate_${alertReportType}`,
                            alertReportType
                        }
                    };
                }
            }
        }

        if (censusDate && alertReportType) {

            const today = moment("06-08-2025 07:00:00", "MM-DD-YYYY HH:mm:ss");
            //const today = moment();
            //console.log(today, 'today');

            // Check if today is the start of the week (Monday)
            // Check if today is the start of the week (Sunday)

            const isWeekStart = today.day() === 0; // '0' represents Sunday in moment.js

            // Check if today is the start of the month
            const isMonthStart = today.date() === 1; // First day of the month is day 1

            const monthStartDate = moment(today).startOf('month').format('YYYY-MM-DD');
            console.log(`Is today the start of the week? ${isWeekStart}`);
            console.log(`Is today the start of the month? ${isMonthStart}`);

            let checkTodayValidDate = false;

            if (alertReportType === ALERT_TYPE_TYPE.BI_WEEKS) {
                if (lastEmailSendDate) {
                    const lastEmailSendDateFormat = moment(lastEmailSendDate, "YYYY-MM-DD");
                    const daysDifference = today.diff(lastEmailSendDateFormat, 'days');
                    const isBiWeekCompleted = daysDifference % 14 === 0;
                    console.log(`Is today a bi-week completion? ${isBiWeekCompleted}`);
                    checkTodayValidDate = isBiWeekCompleted;
                } else {
                    checkTodayValidDate = isWeekStart;
                }
            } else if (alertReportType === ALERT_TYPE_TYPE.WEEKS) {
                checkTodayValidDate = isWeekStart;
            } else if (alertReportType === ALERT_TYPE_TYPE.MONTHS) {
                // checkTodayValidDate = isMonthStart;                
                const isWeekStart = today.day() === 0; // Sunday as the first day of the week
                // const isFirstWeekAfterMonthEnd = (today.date() <= 7 && isWeekStart) || (isWeekStart && lastEmailSendDate && moment(lastEmailSendDate, "YYYY-MM-DD").isSame(moment(monthStartDate, "YYYY-MM-DD").subtract(1, 'days').format('YYYY-MM-DD'))) // First week after month ends

                checkTodayValidDate = isWeekStart;
                console.log(`Is today the first week start after month end? ${checkTodayValidDate}`);
            }

            if (lastCheckDate && !checkTodayValidDate) {
                const lastDateSaved = moment(lastCheckDate, "YYYY-MM-DD").format('YYYY-MM-DD');
                console.log(1, lastDateSaved);
                return await calculateConditions(lastDateSaved, order);
            } else if (checkTodayValidDate) {
                console.log(today, 'today', alertReportType);
                console.log(lastEmailSendDate, 'lastEmailSendDate', alertReportType);
                let yesterdayDate = moment(today, "YYYY-MM-DD").subtract(1, 'days').format('YYYY-MM-DD');

                if (alertReportType === ALERT_TYPE_TYPE.MONTHS) {
                    yesterdayDate = moment(monthStartDate, "YYYY-MM-DD").subtract(1, 'days').format('YYYY-MM-DD');
                }
                console.log(yesterdayDate, 'yesterdayDate', alertReportType);

                if (moment(lastEmailSendDate, "YYYY-MM-DD").isSame(yesterdayDate)) {
                    console.log("Not email send because of already sent email");
                } else {
                    return await calculateConditions(today, order);
                }
            }
        }

    } catch (error) {
        console.error("Error in getAlertPatientData:", error);
        throw error;
    }
}

async function mergeAlerts(rising, dropping) {
    // Create a map to track alerts by _id
    const alertMap = new Map();

    // Helper function to add alerts while checking `name` key
    function addToMap(alerts) {
        alerts.forEach(item => {
            if (!alertMap.has(item._id)) {
                alertMap.set(item._id, { items: [], hasName: false });
            }

            // Check if the current alert has a `name` key
            if (item.name) {
                alertMap.get(item._id).hasName = true;
            }

            alertMap.get(item._id).items.push(item);
        });
    }

    // Add alerts from different categories
    addToMap(rising);
    addToMap(dropping);
    // addToMap(transfersNeedWork);

    // Prepare the final merged list
    const mergedAlerts = [];

    alertMap.forEach(({ items, hasName }) => {
        if (hasName) {
            // Keep all alerts if at least one has `name`
            mergedAlerts.push(...items);
        } else {
            // Keep only the last alert if no `name` exists
            mergedAlerts.push(items[items.length - 1]);
        }
    });

    return mergedAlerts;
}

// async function mergeAlertsOld(rising, dropping, transfersNeedWork) {
//     // Create a map to track categories with priority
//     const alertMap = new Map();

//     // First, add all the rising alerts (priority 1)
//     rising.forEach(item => {
//         alertMap.set(item._id, item); // Add rising alerts with priority
//     });

//     // Next, add all the dropping alerts (priority 1), which can replace rising alerts with same _id
//     dropping.forEach(item => {
//         alertMap.set(item._id, item); // Add dropping alerts with priority, can overwrite rising
//     });

//     // Finally, only add transfersNeedWork if the category is not already in the map
//     transfersNeedWork.forEach(item => {
//         if (!alertMap.has(item._id)) {
//             alertMap.set(item._id, item); // Add only if it doesn't already exist (lower priority)
//         }
//     });

//     // Return the combined list of alerts with the correct priority
//     return Array.from(alertMap.values());
// }

function getPageLabel(page) {
    let pageLabel;
    if (page === PAGE_TYPE.ADMISSION) {
        pageLabel = "Admissions";
    } else if (page === PAGE_TYPE.HOSPITAL) {
        pageLabel = "Hospital transfers";
    } else if (page === PAGE_TYPE.COMMUNITY_TRANSFER) {
        pageLabel = "Community transfers";
    } else if (page === PAGE_TYPE.DECEASED) {
        pageLabel = "Deceased";
    }
    return pageLabel;
}

function generateEmailTemplate(name, dataArray, key, facilityName, todayDate, alertReportType, page) {

    let pageLabel = getPageLabel(page);

    let type = "total"
    if (key === 'rising' || key === 'dropping') {
        type = "percentage"
    }
    let alertReportTypeLabel = "Weekly";
    if (alertReportType === ALERT_TYPE_TYPE.MONTHS) {
        alertReportTypeLabel = "Monthly";
    } else if (alertReportType === ALERT_TYPE_TYPE.BI_WEEKS) {
        alertReportTypeLabel = "Biweekly";
    }

    // Sort data based on total (descending order) to get most concerning areas first
    dataArray.sort((a, b) => b[type] - a[type]);

    // Extract the area with the most transfers (the first in the sorted list)
    const mostConcerning = dataArray[0];

    // Extract other areas of concern (everything after the first)
    const otherAreas = dataArray.slice(1);

    let subject = `${alertReportTypeLabel} ${pageLabel} : ${facilityName} : ${mostConcerning?.label}  ${pageLabel} are ${key} as of ${moment(todayDate).format('MM/DD')}`;
    // Build the email template
    let emailTemplate = `Hi ${name}, good morning.\n\n`;
    if (key === "transferNeedWork") {
        subject = `${alertReportTypeLabel} ${pageLabel} : ${facilityName} : ${mostConcerning?.label} ${pageLabel} still need work as of ${moment(todayDate).format('MM/DD')}.`;
        emailTemplate += `Here are the ${pageLabel} that continue to send out at a higher rate:\n\n`;
        emailTemplate += `Transfers that continue to need work:\n\n`;
    }
    if (key === "rising") {
        emailTemplate += `Here are the ${pageLabel} that have been rising the most:\n\n`;
        emailTemplate += `Biggest Riser: \n\n`;
    }
    if (key === "dropping") {
        subject = `${alertReportTypeLabel} ${pageLabel} : ${facilityName} : ${mostConcerning?.label} ${pageLabel} are dropping as of ${moment(todayDate).format('MM/DD')}.`;
        emailTemplate += `Here are the ${pageLabel} that have been dropping:\n\n`;
        emailTemplate += `Biggest Drop: \n\n`;
    }

    // Add the area with the most transfers
    if (mostConcerning?.name) {
        emailTemplate += `• ${mostConcerning?.name}\n`;
    } else {
        emailTemplate += `• ${mostConcerning?.label}\n`;
    }
    emailTemplate += `  ${mostConcerning.message}\n\n`;

    // Add other areas of concern
    if (otherAreas.length > 0) {
        if (key === "dropping") {
            emailTemplate += `Other areas of notice:\n\n`;
        } else {
            emailTemplate += `Other areas of concern:\n\n`;
        }

        otherAreas.forEach(({ name, label, message }) => {
            emailTemplate += `• ${name ?? label}\n  ${message}\n\n`;
        });
    }

    return { emailTemplate, subject };
}

async function calculateAverageForEachField(last4Weeks, page, alertReport, dynamicCards = [], customTabs = []) {

    const accumulateTotals = (accum, currentArray) => {
        currentArray?.forEach(item => {
            if (!accum[item._id]) {
                accum[item._id] = { ...item, total: 0, count: 0 };
            }
            accum[item._id].total += item.total;
            accum[item._id].count += 1;
        });
        return accum;
    };

    const calculateAverages = (data) => {
        return Object.keys(data).map(key => {
            const item = data[key];
            return {
                ...item,
                average: item.total / item.count
            };
        });
    };

    if (page == PAGE_TYPE.HOSPITAL) {
        let result = {
            hospitalizations: {},
            DCERData: {},
            insuranceData: {},
            returnsData: {},
            ninetyDaysData: {},
            floorsData: {},
            doctorData: {},
            daysData: {},
            dxData: {},
            shiftData: {},
            nurseData: {},
            hospitalData: {},
            unplannedHospitalTransfer: {},
            plannedHospitalTransfer: {},
            total: {},
        };
        const dataFields = ['hospitalizations', 'DCERData', 'insuranceData', 'returnsData', 'ninetyDaysData', 'floorsData', 'doctorData', 'daysData', 'dxData', 'shiftData', 'nurseData', 'hospitalData', 'unplannedHospitalTransfer', 'plannedHospitalTransfer', 'total'];

        last4Weeks.forEach(week => {
            dataFields.forEach(slug => {
                if (
                    alertReport?.alerts &&
                    Object.prototype.hasOwnProperty.call(alertReport.alerts, slug) &&
                    alertReport.alerts[slug]
                ) {
                    result[slug] = accumulateTotals(result[slug], week[slug], slug);
                }
            });

            // Handle dynamic custom keys conditionally
            Object.keys(week).forEach(key => {
                if (
                    key.includes('custom') &&
                    alertReport?.alerts &&
                    Object.prototype.hasOwnProperty.call(alertReport.alerts, key) &&
                    alertReport.alerts[key]
                ) {
                    result[key] = accumulateTotals(result[key] || [], week[key], key);
                }
            });

            if (dynamicCards && dynamicCards.length > 0) {
                for (const ele of dynamicCards) {
                    let accessor = ele.accessor;
                    if (accessor) {
                        result[accessor] = accumulateTotals(result[accessor] || [], week[accessor], accessor);
                    }
                }
            }
            if (customTabs && customTabs.length > 0) {
                for (const ele of customTabs) {
                    let accessor = ele.accessor;
                    if (accessor) {
                        result[accessor] = accumulateTotals(result[accessor] || [], week[accessor], accessor);
                    }
                }
            }
        });
        // Loop over each week to accumulate totals for each key

        const returnData = {};

        dataFields.forEach((slug) => {
            if (
                alertReport?.alerts &&
                Object.prototype.hasOwnProperty.call(alertReport.alerts, slug) &&
                alertReport.alerts[slug]
            ) {
                returnData[slug] = calculateAverages(result[slug]);
            }
        });

        Object.keys(result).forEach(key => {
            if (key.includes('custom')) {
                returnData[key] = calculateAverages(result[key]);
            }
        });

        if (dynamicCards && dynamicCards.length > 0) {
            for (const ele of dynamicCards) {
                let accessor = ele.accessor;
                if (accessor) {
                    returnData[accessor] = calculateAverages(result[accessor]);
                }
            }
        }
        if (customTabs && customTabs.length > 0) {
            for (const ele of customTabs) {
                let accessor = ele.accessor;
                if (accessor) {
                    returnData[accessor] = calculateAverages(result[accessor]);
                }
            }
        }
        return returnData;

    } else if (page == PAGE_TYPE.ADMISSION) {
        let result = {
            hospitalData: {},
            floorsData: {},
            insuranceData: {},
            daysData: {},
            dxData: {},
            doctorData: {},
            admission: {},
            readmission: {},
            total: {},
        };
        const dataFields = ['hospitalData', 'floorsData', 'insuranceData', 'daysData', 'dxData', 'doctorData', 'admission', 'readmission', 'total'];

        // Loop over each week to accumulate totals for each key
        last4Weeks.forEach(week => {
            dataFields.forEach(slug => {
                if (
                    alertReport?.alerts &&
                    Object.prototype.hasOwnProperty.call(alertReport.alerts, slug) &&
                    alertReport.alerts[slug]
                ) {
                    result[slug] = accumulateTotals(result[slug], week[slug], slug);
                }
            });

            // Handle dynamic custom keys conditionally
            Object.keys(week).forEach(key => {
                if (
                    key.includes('custom') &&
                    alertReport?.alerts &&
                    Object.prototype.hasOwnProperty.call(alertReport.alerts, key) &&
                    alertReport.alerts[key]
                ) {
                    result[key] = accumulateTotals(result[key] || [], week[key], key);
                }
            });

            if (dynamicCards && dynamicCards.length > 0) {
                for (const ele of dynamicCards) {
                    let accessor = ele.accessor;
                    if (accessor) {
                        result[accessor] = accumulateTotals(result[accessor] || [], week[accessor], accessor);
                    }
                }
            }
            if (customTabs && customTabs.length > 0) {
                for (const ele of customTabs) {
                    let accessor = ele.accessor;
                    if (accessor) {
                        result[accessor] = accumulateTotals(result[accessor] || [], week[accessor], accessor);
                    }
                }
            }
        });

        const returnData = {};

        dataFields.forEach((slug) => {
            if (
                alertReport?.alerts &&
                Object.prototype.hasOwnProperty.call(alertReport.alerts, slug) &&
                alertReport.alerts[slug]
            ) {
                returnData[slug] = calculateAverages(result[slug]);
            }
        });

        Object.keys(result).forEach(key => {
            if (key.includes('custom')) {
                returnData[key] = calculateAverages(result[key]);
            }
        });

        if (dynamicCards && dynamicCards.length > 0) {
            for (const ele of dynamicCards) {
                let accessor = ele.accessor;
                if (accessor) {
                    returnData[accessor] = calculateAverages(result[accessor]);
                }
            }
        }
        if (customTabs && customTabs.length > 0) {
            for (const ele of customTabs) {
                let accessor = ele.accessor;
                if (accessor) {
                    returnData[accessor] = calculateAverages(result[accessor]);
                }
            }
        }
        return returnData;
    } else if (page == PAGE_TYPE.DECEASED) {
        let result = {
            ninetyDaysData: {},
            floorsData: {},
            doctorData: {},
            insuranceData: {},
            total: {},
        };
        const keys = ['ninetyDaysData', 'floorsData', 'doctorData', 'insuranceData', 'total'];

        // Loop over each week to accumulate totals for each key
        last4Weeks.forEach(week => {
            keys.forEach(slug => {
                if (
                    alertReport?.alerts &&
                    Object.prototype.hasOwnProperty.call(alertReport.alerts, slug) &&
                    alertReport.alerts[slug]
                ) {
                    result[slug] = accumulateTotals(result[slug], week[slug], slug);
                }
            });

            Object.keys(week).forEach(key => {
                if (
                    key.includes('custom') &&
                    alertReport?.alerts &&
                    Object.prototype.hasOwnProperty.call(alertReport.alerts, key) &&
                    alertReport.alerts[key]
                ) {
                    result[key] = accumulateTotals(result[key] || [], week[key], key);
                }
            });

            if (dynamicCards && dynamicCards.length > 0) {
                for (const ele of dynamicCards) {
                    let accessor = ele.accessor;
                    if (accessor) {
                        result[accessor] = accumulateTotals(result[accessor] || [], week[accessor], accessor);
                    }
                }
            }
            if (customTabs && customTabs.length > 0) {
                for (const ele of customTabs) {
                    let accessor = ele.accessor;
                    if (accessor) {
                        result[accessor] = accumulateTotals(result[accessor] || [], week[accessor], accessor);
                    }
                }
            }
        });

        const returnData = {};

        keys.forEach(slug => {
            if (
                alertReport?.alerts &&
                Object.prototype.hasOwnProperty.call(alertReport.alerts, slug) &&
                alertReport.alerts[slug]
            ) {
                returnData[slug] = calculateAverages(result[slug]);
            }
        });

        Object.keys(result).forEach(key => {
            if (
                key.includes('custom') &&
                alertReport?.alerts &&
                Object.prototype.hasOwnProperty.call(alertReport.alerts, key) &&
                alertReport.alerts[key]
            ) {
                returnData[key] = calculateAverages(result[key]);
            }
        });

        if (dynamicCards && dynamicCards.length > 0) {
            for (const ele of dynamicCards) {
                let accessor = ele.accessor;
                if (accessor) {
                    returnData[accessor] = calculateAverages(result[accessor]);
                }
            }
        }

        if (customTabs && customTabs.length > 0) {
            for (const ele of customTabs) {
                let accessor = ele.accessor;
                if (accessor) {
                    returnData[accessor] = calculateAverages(result[accessor]);
                }
            }
        }
        return returnData
    } else if (page == PAGE_TYPE.COMMUNITY_TRANSFER) {

        let result = {
            sixtyDaysData: {},
            floorsData: {},
            doctorData: {},
            insuranceData: {},
            returnsData: {},
            SNF: {},
            AMA: {},
            safeDischarge: {},
            total: {},
        };

        const keys = [
            'sixtyDaysData',
            'floorsData',
            'doctorData',
            'insuranceData',
            'returnsData',
            'SNF',
            'AMA',
            'safeDischarge',
            'total',
        ];
        // Loop over each week to accumulate totals for each key
        last4Weeks.forEach(week => {
            keys.forEach(slug => {
                if (
                    alertReport?.alerts &&
                    Object.prototype.hasOwnProperty.call(alertReport.alerts, slug) &&
                    alertReport.alerts[slug]
                ) {
                    result[slug] = accumulateTotals(result[slug], week[slug], slug);
                }
            });

            // Handle custom keys conditionally
            Object.keys(week).forEach(key => {
                if (
                    key.includes('custom') &&
                    alertReport?.alerts &&
                    Object.prototype.hasOwnProperty.call(alertReport.alerts, key) &&
                    alertReport.alerts[key]
                ) {
                    result[key] = accumulateTotals(result[key] || [], week[key], key);
                }
            });

            if (dynamicCards && dynamicCards.length > 0) {
                for (const ele of dynamicCards) {
                    let accessor = ele.accessor;
                    if (accessor) {
                        result[accessor] = accumulateTotals(result[accessor] || [], week[accessor], accessor);
                    }
                }
            }
            if (customTabs && customTabs.length > 0) {
                for (const ele of customTabs) {
                    let accessor = ele.accessor;
                    if (accessor) {
                        result[accessor] = accumulateTotals(result[accessor] || [], week[accessor], accessor);
                    }
                }
            }
        });

        const returnData = {};

        // Only calculate averages if alertReport.alerts[slug] is truthy
        keys.forEach(slug => {
            if (
                alertReport?.alerts &&
                Object.prototype.hasOwnProperty.call(alertReport.alerts, slug) &&
                alertReport.alerts[slug]
            ) {
                returnData[slug] = calculateAverages(result[slug]);
            }
        });

        // Handle custom keys similarly
        Object.keys(result).forEach(key => {
            if (
                key.includes('custom') &&
                alertReport?.alerts &&
                Object.prototype.hasOwnProperty.call(alertReport.alerts, key) &&
                alertReport.alerts[key]
            ) {
                returnData[key] = calculateAverages(result[key]);
            }
        });

        if (dynamicCards && dynamicCards.length > 0) {
            for (const ele of dynamicCards) {
                let accessor = ele.accessor;
                if (accessor) {
                    returnData[accessor] = calculateAverages(result[accessor]);
                }
            }
        }
        if (customTabs && customTabs.length > 0) {
            for (const ele of customTabs) {
                let accessor = ele.accessor;
                if (accessor) {
                    returnData[accessor] = calculateAverages(result[accessor]);
                }
            }
        }
        return returnData;
    } else if (page == PAGE_TYPE.OVERALL) {
        const result = {
            totalIncoming: {},
            totalOutgoing: {},
            floorsData: {},
            doctorData: {},
            insuranceData: {},
            ninetyDaysData: {},
        };

        // Loop over each week to accumulate totals for each key
        last4Weeks.forEach(week => {
            result.totalIncoming = accumulateTotals(result.totalIncoming, week.totalIncoming, 'totalIncoming');
            result.totalOutgoing = accumulateTotals(result.totalOutgoing, week.totalOutgoing, 'totalOutgoing');
            result.floorsData = accumulateTotals(result.floorsData, week.floorsData, 'floorsData');
            result.doctorData = accumulateTotals(result.doctorData, week.doctorData, 'doctorData');
            result.insuranceData = accumulateTotals(result.insuranceData, week.insuranceData, 'insuranceData');
            result.ninetyDaysData = accumulateTotals(result.ninetyDaysData, week.ninetyDaysData, 'ninetyDaysData');
        });

        return {
            totalIncoming: calculateAverages(result.totalIncoming),
            totalOutgoing: calculateAverages(result.totalOutgoing),
            floorsData: calculateAverages(result.floorsData),
            doctorData: calculateAverages(result.doctorData),
            insuranceData: calculateAverages(result.insuranceData),
            ninetyDaysData: calculateAverages(result.ninetyDaysData),
        };
    }
}

async function handleEmptyData(currentData, averageData) {
    // If both data sets are null or undefined, return early.
    if (!currentData && !averageData) {
        return { currentData: [], averageData: [] };
    }

    // If both data sets are empty, return early.
    if ((currentData?.length === 0) && (averageData?.length === 0)) {
        return { currentData: [], averageData: [] };
    }

    // If the first dataset is empty, map the second dataset to default values.
    if (currentData?.length === 0) {
        currentData = averageData.map(({ _id, label, name = null }) => ({
            _id,
            label,
            total: 0,
            average: 0,
            ...name && { name }
        }));
    }

    // If the second dataset is empty, map the first dataset to default values.
    if (averageData?.length === 0) {
        averageData = currentData.map(({ _id, label, name = null }) => ({
            _id,
            label,
            total: 0,
            average: 0,
            ...name && { name }
        }));
    }


    return { currentData, averageData };
}

module.exports = {
    getAlertPatientData,
    updateFilterListData,
    handleEmptyData,
    processAlertReportType
};
