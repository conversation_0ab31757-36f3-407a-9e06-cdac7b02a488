const Account = require("../models/Account");
const Facility = require("../models/Facility");

const seedFacilities = async () => {
    const accountData = [
        {
            name: 'Ambulatory surgical centers',
        },
        {
            name: 'Birth centers',
        },
        {
            name: 'Blood banks',
        },
        {
            name: 'Hospice homes',
        },
        {
            name: 'Mental health and addiction treatment centers',
        }
    ];

    await Account.deleteMany({});

    const createdAccount = accountData.map(async (item) => {
        const role = await Account.create(item);
        return role;
    });

    await Promise.all(createdAccount);

    console.log("<-- Account Seeded -->");
};

module.exports = { seedFacilities };
