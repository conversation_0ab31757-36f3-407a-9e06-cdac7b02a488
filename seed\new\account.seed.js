const mongoose = require('mongoose');
const Account = mongoose.model("account");
const { PAGE_TYPE } = require("../../types/common.type");

const seedAccount = async (name, percentageBy, createdBy) => {
    try {
        const account = new Account({
            name,
            active: true,
            type: 'default',
            createdBy,
            percentageBy,
            dashboardAccess: [
			PAGE_TYPE.ADMISSION,
			PAGE_TYPE.HOSPITAL,
			PAGE_TYPE.COMMUNITY_TRANSFER,
			PAGE_TYPE.DECEASED,
			PAGE_TYPE.OVERALL,
		]
        });

        await account.save();
        console.log('✅ Account created successfully.');
        return account;
    } catch (err) {
        console.error('❌ Error creating account:', err.message);
        throw err;
    }
};

module.exports = {
    seedAccount,
};
