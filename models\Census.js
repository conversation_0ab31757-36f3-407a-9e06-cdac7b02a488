const mongoose = require("mongoose");
const { Schema, Types } = mongoose;

const CensusSchema = new Schema(
  {
    accountId: { type: Types.ObjectId, ref: "account" },
    facilityId: { type: Types.ObjectId, ref: "facility" },
    date: { type: Date, required: false, default : null },
    count: { type: Number, required: false, default : null },
    bedCapacity: { type: Number, required: false },
    createdBy: { type: Types.ObjectId, ref: "user" },
  },
  { timestamps: true }
);

CensusSchema.index({ facilityId: 1, created_at: -1, date: 1 });

mongoose.model("census", CensusSchema);
