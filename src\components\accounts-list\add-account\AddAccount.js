import axios from "../../../axios";
import { useEffect, useState } from "react";
import CloseIcon from "../../../assets/svgs/close.svg";
import styles from "./AddAccount.module.scss";
import { useDispatch } from "react-redux";
import { ADD_ACCOUNT, ADD_NOTIFICATION, UPDATE_ACCOUNT } from "../../../store/types";
import classNames from "classnames";
import { Checkbox, FormControlLabel } from "@mui/material";
import { ACCOUNT_PERCENTAGE_BY, PAGE_TYPE } from "../../../types/pages.type";
import { LoadingButton } from "@mui/lab";
import _ from "lodash";

const DASHBOARD_OPTIONS = [
    { label: "Hospital dashboard access", type: PAGE_TYPE.HOSPITAL },
    { label: "Community transfer dashboard access", type: PAGE_TYPE.COMMUNITY_TRANSFER },
    { label: "Deceased dashboard access", type: PAGE_TYPE.DECEASED },
    { label: "Admission dashboard access", type: PAGE_TYPE.ADMISSION },
    { label: "Overall dashboard access", type: PAGE_TYPE.OVERALL },
];

const AddAccount = props => {
    const dispatch = useDispatch();
    const [name, setName] = useState("");
    const [dashboardAccess, setDashboardAccess] = useState([]);
    const [isEdit, setIsEdit] = useState(false);
    const [isError, setIsError] = useState(false);
    const [isLoading, setIsLoading] = useState(false);

    const save = async () => {
        try {
            setIsError(false)
            if (!name) {
                setIsError(true)
                return
            }
            setIsLoading(true);
            if (isEdit) {
                let re = await axios.put("/api/account/" + props.currentEdit._id, {
                    name,
                });
                if (re && props.accountUpdated) {
                    props.accountUpdated(re.data);
                    dispatch({
                        type: UPDATE_ACCOUNT,
                        payload: re.data,
                    });
                    dispatch({
                        type: ADD_NOTIFICATION,
                        payload: {
                            type: "success",
                            label: "Account data updated successfully!",
                            id: "accountDataUpdated",
                        },
                    });
                }
                props.clearCurrentEdit && props.clearCurrentEdit();
            } else {
                const re = await axios.post("/api/account", {
                    name,
                    ...!isEdit && ({ dashboardAccess: dashboardAccess ? dashboardAccess : [] }),
                    ...!isEdit && ({ percentageBy: dashboardAccess?.length !== DASHBOARD_OPTIONS.length ? ACCOUNT_PERCENTAGE_BY.BED : null })
                });

                if (re && props.accountAdded) {
                    props.accountAdded(re.data);
                }

                dispatch({
                    type: ADD_ACCOUNT,
                    payload: re.data,
                });

                dispatch({
                    type: ADD_NOTIFICATION,
                    payload: {
                        type: "success",
                        label: "Account added successfully!",
                        id: 'accountAdded'
                    },
                });
            }
            setIsLoading(false);
            props.close();
        } catch (e) {
            setIsLoading(false);
            dispatch({
                type: ADD_NOTIFICATION,
                payload: {
                    type: "error",
                    label: e?.response?.data?.message || "Error while trying to add account.",
                    id: 'addAccountError'
                },
            });
        }
    };

    useEffect(() => {
        if (props.currentEdit && props.currentEdit._id) {
            setIsEdit(true);
            setName(props.currentEdit.name);
        }
    }, [props.currentEdit]);

    return (
        <>
            <div className={styles.overlay}></div>
            <div className={styles.addPopup}>
                <div className={classNames("df aic", styles.hdr)}>
                    <p className={`ffmm fs18`}>{isEdit ? "Edit" : "Add"} account</p>
                    <div className={`mla`}>
                        <div
                            className={classNames("df acc", styles.closeBtn)}
                            onClick={() => {
                                props.close();
                                props.clearCurrentEdit();
                            }}
                        >
                            <CloseIcon />
                        </div>
                    </div>

                </div>{" "}
                <div className={styles.popupCntnt}>
                    <div className={`m-b-15 inputWrpr`}>
                        <label className={`ffmr fs12 ${isError && !name ? "error" : null}`}>Account Name</label>
                        <input
                            style={{
                                ...(isError && !name && { borderColor: "red" }),
                            }}
                            value={name}
                            onChange={(e) => setName(e.target.value)}
                        />
                    </div>
                    {!isEdit && (
                        <>
                            {DASHBOARD_OPTIONS.map(({ label, type }) => (
                                <div key={type} className="m-b-8 inputWrpr">
                                    <FormControlLabel
                                        label={label}
                                        className="ffmr fs12"
                                        slotProps={{
                                            typography: {
                                                sx: {
                                                    mt: "10px",
                                                    fontSize: "13px",
                                                    fontFamily: "mont reg",
                                                },
                                            },
                                        }}
                                        control={
                                            <Checkbox
                                                checked={_.includes(dashboardAccess, type)}
                                                onChange={(e) =>
                                                    setDashboardAccess(
                                                        e.target.checked
                                                            ? [...dashboardAccess, type]
                                                            : _.without(dashboardAccess, type)
                                                    )
                                                }
                                            />
                                        }
                                    />
                                </div>
                            ))}
                        </>
                    )}
                </div>
                <div className={`${styles.popupFtr}`}>
                    <div className={classNames("df aic", styles.actionsWrrp)}>
                        <div className={`mla`}>
                            <button
                                className={classNames("ffmr fs14 m-r-8", styles.text)}
                                onClick={() => {
                                    props.close();
                                    props.clearCurrentEdit && props.clearCurrentEdit();
                                }}
                            >
                                Cancel
                            </button>
                            <LoadingButton
                                variant="contained"
                                className={classNames("ffmr fs14")}
                                onClick={save}
                                loading={isLoading}
                            >
                                Save
                            </LoadingButton>
                        </div>
                    </div>
                </div>
            </div>
        </>
    );
};

export default AddAccount;
