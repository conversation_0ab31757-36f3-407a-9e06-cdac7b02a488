const mongoose = require("mongoose");
const {
    toStartFilterDate,
    toEndFilterDate,
} = require("../utilis/date-format");
const { getCensusAverageInfo } = require("./census");
const { PAGE_TYPE, CUSTOM_TAB_TYPE } = require("../../types/common.type");
const { getCustomTabsByPage } = require("./custom-tab");
const { createDiffDashboardPatients, enrichDiffDashboardPatients } = require("../../utils/common");
const { findSavedReportConfig } = require("./reportsSubscription");

/**
 * Common initialization logic for getAllCount functions
 * @param {Object} req - Express request object
 * @param {string} pageType - PAGE_TYPE constant (HOSPITAL, COMMUNITY_TRANSFER, etc.)
 * @returns {Object} Common initialization data
 */
const initializeGetAllCount = async (req, pageType) => {
    const user = req.user;
    const { facilityid, accountid } = req.headers;
    const { startDate, endDate, facilityIds = [], isSkipCustomCard = false } = req.query;
    
    const startDateFilter = await toStartFilterDate(startDate);
    const endDateFilter = await toEndFilterDate(endDate);
    const customTabs = !isSkipCustomCard ? await getCustomTabsByPage({ accountid, page: pageType, userId: user?._id }) : [];
    const diffDashboardPatients = await createDiffDashboardPatients();
    const customCombineTabData = await createDiffDashboardPatients('combineTab');    
    const isCustomCombineTab = customTabs?.some(tab => tab.type === CUSTOM_TAB_TYPE.COMBINE);

    return {
        user,
        facilityid,
        accountid,
        startDate,
        endDate,
        facilityIds,
        startDateFilter,
        endDateFilter,
        customTabs,
        diffDashboardPatients,
        customCombineTabData,
        isCustomCombineTab
    };
};

/**
 * Common facility filter setup logic
 * @param {string} facilityid - Single facility ID
 * @param {Array} facilityIds - Array of facility IDs
 * @returns {Object} Facility filter and query data
 */
const setupFacilityFilter = async (facilityid, facilityIds) => {
    let facilityFilter = null;
    let query = {};
    let facilityData = [];

    if (facilityIds && facilityIds.length > 0) {
        facilityIds.map((ele) => {
            facilityData.push(mongoose.Types.ObjectId(ele));
        });
        facilityFilter = { facilityId: { $in: facilityData } };
        query.facilityId = { $in: facilityIds };
    } else {
        if (facilityid) {
            facilityData = [facilityid];
            query.facilityId = facilityid;
            facilityFilter = { facilityId: mongoose.Types.ObjectId(facilityid) };
        }
    }

    return {
        facilityFilter,
        query,
        facilityData
    };
};

/**
 * Common date filter setup logic
 * @param {Object} query - Existing query object
 * @param {Date} startDateFilter - Start date filter
 * @param {Date} endDateFilter - End date filter
 * @returns {Object} Updated query with date filters
 */
const setupDateFilter = (query, startDateFilter, endDateFilter) => {
    if (startDateFilter && endDateFilter) {
        query.dateOfADT = {
            $gte: startDateFilter,
            $lt: endDateFilter,
        };
    }
    return query;
};

/**
 * Common census info retrieval logic
 * @param {Date} startDateFilter - Start date filter
 * @param {Date} endDateFilter - End date filter
 * @param {Array} facilityData - Array of facility IDs
 * @returns {Object} Census information
 */
const getCensusInfo = async (startDateFilter, endDateFilter, facilityData) => {
    if (startDateFilter && endDateFilter) {
        return await getCensusAverageInfo(startDateFilter, endDateFilter, facilityData);
    }
    return {};
};

/**
 * Common finalization logic for getAllCount functions
 * @param {Object} diffDashboardPatients - Dashboard patients data
 * @param {Array} customTabs - Custom tabs array
 * @param {string} pageType - PAGE_TYPE constant
 * @param {string} userId - User ID
 * @returns {Object} Finalized data
 */
const finalizeGetAllCount = async (diffDashboardPatients, customTabs, pageType, userId) => {
    const enrichedDiffDashboardPatients = await enrichDiffDashboardPatients(diffDashboardPatients, customTabs);
    const isAutomaticReportSaved = await findSavedReportConfig(pageType, userId);
    
    return {
        enrichedDiffDashboardPatients,
        isAutomaticReportSaved
    };
};

module.exports = {
    initializeGetAllCount,
    setupFacilityFilter,
    setupDateFilter,
    getCensusInfo,
    finalizeGetAllCount
}; 