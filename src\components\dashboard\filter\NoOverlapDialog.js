import * as React from 'react';
import Button from '@mui/material/Button';
import Dialog from '@mui/material/Dialog';
import DialogActions from '@mui/material/DialogActions';
import DialogContent from '@mui/material/DialogContent';
import DialogContentText from '@mui/material/DialogContentText';
import DialogTitle from '@mui/material/DialogTitle';

export default function NoOverlapDialog({ handleClose, isOverLapDate, activeFacilities }) {

    return (
        <div>
            <Dialog
                open={true}
                onClose={handleClose}
                aria-labelledby="overlap-dialog-title"
                aria-describedby="alert-dialog-description"
            >
                <DialogTitle id="overlap-dialog-title">
                    {activeFacilities.length > 1 ?
                        "Overlap facilities?" :
                        "No data entered"
                    }
                </DialogTitle>
                <DialogContent>
                    <DialogContentText id="alert-dialog-description">
                        {activeFacilities.length > 1 ?
                            ("There is data that does not overlap for all facilities") :
                            ("No data entered for this facility yet")
                        }
                        {/* Data does not overlap in all facilities date range dates not available */}
                    </DialogContentText>
                </DialogContent>
                <DialogActions>
                    <Button onClick={handleClose}>Cancel</Button>
                </DialogActions>
            </Dialog>
        </div>
    );
}
