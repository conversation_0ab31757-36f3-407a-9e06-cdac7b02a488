.accounts {
  padding: 0px 30px;

  .hdr {
    padding-bottom: 15px;

    .addBtn {
      background-color: rgba(0, 56, 141, 0.15);
      border: none;
      border-radius: 4px;
      padding: 12px 22px;
      cursor: pointer;
    }
  }
  .list {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    column-gap: 20px;
    row-gap: 16px;

    .account {
      border: 1px solid #e6e6e6;
      border-radius: 2px;
      padding: 20px 26px;
      box-shadow: 0px 0px 5px 0px rgba(0, 0, 0, 0.2);
      text-decoration: none;
      color: rgb(75, 75, 75);
    }
  }
}
