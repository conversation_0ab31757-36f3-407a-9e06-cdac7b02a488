const mongoose = require("mongoose");
const { Schema, Types } = mongoose;
var slug = require("mongoose-slug-generator");
const { PAGE_TYPE, AUTOMATICALLY_REPORT_TYPE, REPORT_FILE_TYPE } = require("../types/common.type");

mongoose.plugin(slug);


const ReportsSubscriptionsSchema = new Schema(
    {
        page: {
            type: String,
            enum: [PAGE_TYPE.ADMISSION, PAGE_TYPE.HOSPITAL, PAGE_TYPE.OVERALL, PAGE_TYPE.DECEASED, PAGE_TYPE.COMMUNITY_TRANSFER],
            default: PAGE_TYPE.HOSPITAL
        },
        userId: { type: Types.ObjectId, ref: "user" },
        accountId: { type: Types.ObjectId, ref: "account" },
        facilityIds: [{ type: Types.ObjectId, ref: "facility" }],
        reportFileType: { type: String, default: null },
        interval: {
            type: String,
            enum: [AUTOMATICALLY_REPORT_TYPE.DAILY, AUTOMATICALLY_REPORT_TYPE.WEEKLY, AUTOMATICALLY_REPORT_TYPE.MONTHLY, AUTOMATICALLY_REPORT_TYPE.QUARTERLY, AUTOMATICALLY_REPORT_TYPE.YEARLY],
            default: 'daily'
        },
        name: { type: String, required: true },
        title: { type: String, required: false },
        adtType: { type: Object, required: false },
        filterCardType: { type: String, required: false },
        filter: Object,
        filtersData: Object,
        transferType: String,
        transferTypes: [String],
        lastEmailSentADTDate: { type: Date, required: false, default: null },
        isGraphReport: { type: Boolean, required: false, default: false },
        isSendReportSeparate: { type: Boolean, required: false, default: false },
    },
    { timestamps: true }
);

const ReportsSubscriptions = mongoose.model("reportsSubscriptions", ReportsSubscriptionsSchema);

module.exports = ReportsSubscriptions
