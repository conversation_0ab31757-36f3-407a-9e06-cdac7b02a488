import axios from "../../../axios";
import { useEffect, useState } from "react";
import { useSelector } from "react-redux";
import styles from "./Accounts.module.scss";
import { Link } from "react-router-dom";
import update from "immutability-helper";
import AddAccount from "../../accounts-list/add-account/AddAccount";

const Accounts = (props) => {
  const { auth } = useSelector(({ auth }) => ({ auth }));

  const [accounts, setAccounts] = useState([]);
  const [showAdd, setShowAdd] = useState(false);

  const getAccount = async () => {
    let re = await axios.get(
      `/api/account${
        auth?.type?.toLowerCase().includes("super") ? "" : `/${auth?.accountId}`
      }`
    );
    if (auth?.type?.toLowerCase().includes("super")) {
      setAccounts(re.data);
    } else {
      setAccounts([re.data]);
    }
  };

  useEffect(() => {
    if (!auth?.accountId) return;
    getAccount();
    // eslint-disable-next-line
  }, [auth]);

  const accountAdded = (account) => {
    const newData = update(accounts, {
      $push: [account],
    });

    setAccounts(newData);
  };

  const toggleShowAdd = () => setShowAdd(!showAdd);

  return (
    <div className={`p-t-15 ${styles.accounts}`}>
      <div className={`df ${styles.hdr}`}>
        <div className={`mla`}>
          <button onClick={toggleShowAdd} className={styles.addBtn}>
            + Add Account
          </button>
          {showAdd ? (
            <AddAccount close={toggleShowAdd} accountAdded={accountAdded} />
          ) : undefined}
        </div>
      </div>
      <div className={styles.list}>
        {accounts.map((account) => (
          <Link to={`${account._id}`} className={styles.account}>
            <h2 className={`ffmsb fs16 m-b-6`}>{account.name}</h2>
            <h3>{account.type}</h3>
          </Link>
        ))}
      </div>
    </div>
  );
};

export default Accounts;
