const mongoose = require("mongoose");
const Patient = mongoose.model("patient");
const _ = require("lodash");
const { momentDateFormat, toEndFilterDate, toStartFilterDate } = require("../utilis/date-format");
const { getCensusAverageInfo, getCensusAverageByPeriod } = require("./census");
const { OVERALL_CARDS_TYPE, RELATIONS, PAGE_TYPE, CUSTOM_TAB_TYPE } = require("../../types/common.type");
const { ADT_TYPES, ADT_SUB_TYPES } = require("../../types");
const { findSavedReportConfig } = require("./reportsSubscription");
const { getNinetyDaysChartCount, createDiffDashboardPatients } = require("../../utils/common");
const { ninetyDaysDataFilter, getCustomCombineTabData } = require("./hospital");
const {
    setupFacilityFilter,
    setupDateFilter,
    getCensusInfo
} = require("./common-dashboard");
const { getCustomTabsByPage } = require("./custom-tab");
const { formatPatientData } = require("../utilis/common");

const filterData = async (arr, callback) => {
    const fail = Symbol();
    return (
        await Promise.all(
            arr.map(async (item) => ((await callback(item)) ? item : fail))
        )
    ).filter((i) => i !== fail);
};

const getAllCount = async (req) => {
    try {
        const user = req.user;
        const { facilityid, accountid } = req.headers;
        const { startDate, endDate, facilityIds = [], isSkipCustomCard = false } = req.query;
        let startDateFilter = await toStartFilterDate(startDate)
        let endDateFilter = await toEndFilterDate(endDate);
        let customCombineTabData = await createDiffDashboardPatients('combineTab');
        const customTabsData = !isSkipCustomCard ? await getCustomTabsByPage({ accountid, page: PAGE_TYPE.OVERALL, userId: user._id }) : [];
        const customTabs = customTabsData?.filter(tab => tab.type === CUSTOM_TAB_TYPE.combineTab) ?? [];

        // Use common facility filter setup
        let { facilityFilter, query, facilityData } = await setupFacilityFilter(facilityid, facilityIds);

        // Use common date filter setup
        await setupDateFilter(query, startDateFilter, endDateFilter);

        // Get census info using common utility
        const censusInfo = await getCensusInfo(startDateFilter, endDateFilter, facilityData);


        let list = await Patient.find({ ...query })
            .populate([RELATIONS.INSURANCE, RELATIONS.UNIT, RELATIONS.DOCTOR, RELATIONS.PAYER_SOURCE_INSURANCE])
            .sort({ dateOfADT: 1 }).lean();

        customCombineTabData = await getCustomCombineTabData(query, PAGE_TYPE.OVERALL, facilityFilter, customCombineTabData, customTabs, list);

        const total = list.length;
        let totalIncoming = 0;
        let totalOutgoing = 0;
        const listData = [];
        let totalHospitalTransfer = 0;
        let totalCommunityTransfer = 0;
        let totalDeceasedTransfer = 0;

        let totalAdmission = 0
        let totalReAdmission = 0
        let totalHospitalReturn = 0

        if (list && list.length > 0) {
            await filterData(list, async (item) => {
                let latestObj = new Object();
                const ID = item._id.toString();
                latestObj = item
                latestObj.dateOfADTOriginal = item.dateOfADT;
                latestObj.DOBOriginal = item.DOB;
                latestObj.id = ID;
                latestObj.isOutgoing = item.type == ADT_TYPES.TRANSFER ? true : false;
                if (item.type == ADT_TYPES.TRANSFER && item.transferType) {
                    if (_.includes([ADT_SUB_TYPES.PLANNED_HOSPITAL_TRANSFER, ADT_SUB_TYPES.UNPLANNED_HOSPITAL_TRANSFER], item.transferType)) {
                        totalHospitalTransfer++
                    }
                    if (_.includes([ADT_SUB_TYPES.SAFE_DISCHARGE, ADT_SUB_TYPES.AMA, ADT_SUB_TYPES.SNF], item.transferType)) {
                        totalCommunityTransfer++
                    }
                    if (_.includes([ADT_SUB_TYPES.DECEASED], item.transferType)) {
                        totalDeceasedTransfer++
                    }
                } else {
                    if (_.includes([ADT_TYPES.ADMISSIONS], item.type)) {
                        totalAdmission++
                    }
                    if (_.includes([ADT_TYPES.READMISSIONS], item.type)) {
                        totalReAdmission++
                    }
                    if (_.includes([ADT_TYPES.RETURN], item.type)) {
                        totalHospitalReturn++
                    }
                }
                latestObj.dateOfADT = await momentDateFormat(
                    item.dateOfADT,
                    "YYYY-MM-DD"
                );
                latestObj.DOB = await momentDateFormat(item.DOB, "YYYY-MM-DD");

                latestObj.floorId = item.unit?._id?.toString() || null;
                latestObj.doctorId = item.doctor?._id?.toString() || null;
                if (item.payerSourceInsurance) {
                    latestObj.insuranceId = item.payerSourceInsurance?._id?.toString() || null;
                    latestObj.insurance = item.payerSourceInsurance || null;
                } else {
                    latestObj.insuranceId = item.insurance?._id?.toString() || null;
                    latestObj.insurance = item.insurance || null;
                }
                listData.push(latestObj);
            });

            const transferTypeTotal = _.countBy(listData, "isOutgoing");
            totalOutgoing = transferTypeTotal?.true || 0;
            totalIncoming = transferTypeTotal?.false || 0;
        }
        let outGoingList = _.filter(listData, { isOutgoing: true });
        const resAnalysis = await getNinetyDaysChartCount(outGoingList);
        ninetyDaysData = resAnalysis?.ninetyDaysDataChart ?? [];
        outGoingList = resAnalysis?.patientListRes ?? outGoingList;

        let outGoingDetailsTotal = { totalHospitalTransfer, totalCommunityTransfer, totalDeceasedTransfer }
        let incomingDetailsTotal = { totalAdmission, totalReAdmission, totalHospitalReturn }
        let isAutomaticReportSaved = await findSavedReportConfig(PAGE_TYPE.OVERALL, user?.id)
        return {
            data: {
                list: listData,
                ninetyDaysData,
                customCombineTabData,
                customTabs
            },
            totals: {
                ...censusInfo,
                total,
                totalIncoming,
                totalOutgoing,
                outGoingDetailsTotal,
                incomingDetailsTotal,
                isAutomaticReportSaved
            },
        };
    } catch (err) {
        console.log(err, 'err overall all count');
    }
};

const getCardPatientChartData = async (req) => {
    const user = req.user;
    const { body } = req;
    const {
        facilityId,
        facilityIds,
        cardFilter,
        type,
        filter: { startDate, endDate },
        relation,
        transferType,
        isSkipCustomCard = false,
        customCardType
    } = body;

    let startDateFilter = await toStartFilterDate(startDate)
    let endDateFilter = await toEndFilterDate(endDate);
    const { facilityid, accountid } = req.headers;


    let customCombineTabData = await createDiffDashboardPatients('combineTab');
    const customTabsData = !isSkipCustomCard ? await getCustomTabsByPage({ accountid, page: PAGE_TYPE.OVERALL, userId: user._id }) : [];
    const customTabs = customTabsData?.filter(tab => tab.type === CUSTOM_TAB_TYPE.combineTab) ?? [];

    let query = Object();
    let isMainData = false;
    if (
        type === OVERALL_CARDS_TYPE.TOTAL ||
        type === OVERALL_CARDS_TYPE.TOTAL_INCOMING ||
        type === OVERALL_CARDS_TYPE.TOTAL_OUTGOING
    ) {
        isMainData = true;
    }
    if (type === OVERALL_CARDS_TYPE.TOTAL) {
    } else if (type === OVERALL_CARDS_TYPE.TOTAL_INCOMING) {
        query.type = { $nin: [ADT_TYPES.TRANSFER] };
    } else if (type === OVERALL_CARDS_TYPE.TOTAL_OUTGOING) {
        query.type = { $in: [ADT_TYPES.TRANSFER] };
    } else {
        if (transferType) {
            if (transferType === OVERALL_CARDS_TYPE.TOTAL_INCOMING) {
                query.type = { $nin: [ADT_TYPES.TRANSFER] };
            }
            if (transferType === OVERALL_CARDS_TYPE.TOTAL_OUTGOING) {
                query.type = { $in: [ADT_TYPES.TRANSFER] };
            }
        }
    }
    const facilityData = [];

    if (facilityIds && facilityIds.length > 0) {
        facilityIds.map((ele) => {
            facilityData.push(ele);
        });
        facilityFilter = { facilityId: { $in: facilityData } };
        query.facilityId = { $in: facilityIds };
    } else {
        if (facilityId) {
            facilityFilter = { facilityId: mongoose.Types.ObjectId(facilityId) };
            query.facilityId = facilityId;
        }
    }

    if (startDate && endDate) {
        query.dateOfADT = {
            $gte: startDateFilter,
            $lt: endDateFilter,
        };
    }

    let relations = [
        RELATIONS.FACILITY,
        RELATIONS.DOCTOR,
        RELATIONS.UNIT,
        RELATIONS.PAYER_SOURCE_INSURANCE,
        RELATIONS.INSURANCE,
        RELATIONS.DX,
        RELATIONS.NURSE,
        RELATIONS.HOSPITAL,
        RELATIONS.SNF,
        RELATIONS.TRANSFER_TO_WHICH_ASSISTED_LIVING,
    ];
    if (relation) {
        relations.push(relation);
    }
    if (!isMainData) {
        if (cardFilter && cardFilter.doctorData.length > 0) {
            const doctorIds = cardFilter.doctorData.map((e) =>
                mongoose.Types.ObjectId(e)
            );
            query.doctor = { $in: doctorIds };
            //relations.push(RELATIONS.DOCTOR);
        }
        if (cardFilter && cardFilter.floorsData.length > 0) {
            const floorIds = cardFilter.floorsData.map((e) =>
                mongoose.Types.ObjectId(e)
            );
            query.unit = { $in: floorIds };
            //relations.push(RELATIONS.UNIT);
        }

        if (cardFilter && cardFilter.insuranceData.length > 0) {
            const insuranceIds = cardFilter.insuranceData.map((e) =>
                mongoose.Types.ObjectId(e)
            );
            if (transferType == OVERALL_CARDS_TYPE.TOTAL) {
                query.payerSourceInsurance = { $in: insuranceIds };
                //relations.push(RELATIONS.PAYER_SOURCE_INSURANCE);
                query.insurance = { $in: insuranceIds };
                //relations.push(RELATIONS.INSURANCE);
            } else {
                if (transferType === OVERALL_CARDS_TYPE.TOTAL_INCOMING) {
                    query.payerSourceInsurance = { $in: insuranceIds };
                    //relations.push(RELATIONS.PAYER_SOURCE_INSURANCE);
                } else {
                    query.insurance = { $in: insuranceIds };
                    //relations.push(RELATIONS.INSURANCE);
                }
            }
        }
    }
    let patientList = [];
    let listData = [];

    patientList = await Patient.find({ ...query })
        .populate(relations)
        .sort({ dateOfADT: -1 })
        .lean();

    if (customTabs?.length > 0) {
        customCombineTabData = await getCustomCombineTabData(query, PAGE_TYPE.OVERALL, facilityFilter, customCombineTabData, customTabs, patientList);
    }
    await filterData(patientList, async (item) => {
        let latestObj = new Object();
        latestObj = item;
        if (relation) {
            latestObj.filterId = item[relation]?._id || null;
        }
        if (item.payerSourceInsurance) {
            latestObj.insurance = item.payerSourceInsurance
        }
        latestObj.isOutgoing = item.type == ADT_TYPES.TRANSFER ? true : false;
        const formattedObj = await formatPatientData(latestObj);
        listData.push(formattedObj);
    });

    let ninetyDaysData = [];
    if (customCardType === CUSTOM_TAB_TYPE.combineTab) {
        const resAnalysis = await getNinetyDaysChartCount(listData);
        ninetyDaysData = resAnalysis?.ninetyDaysDataChart ?? [];
    }
    if (!isMainData && customCardType !== CUSTOM_TAB_TYPE.combineTab) {
        if (
            (cardFilter.ninetyDaysData &&
                cardFilter.ninetyDaysData.length > 0 &&
                transferType === OVERALL_CARDS_TYPE.TOTAL_OUTGOING) ||
            type === OVERALL_CARDS_TYPE.NINETY_DAYS_DATA
        ) {
            const resAnalysis = await getNinetyDaysChartCount(listData);
            ninetyDaysData = resAnalysis?.ninetyDaysDataChart ?? [];
            listData = resAnalysis?.patientListRes ?? listData;

            if (type === OVERALL_CARDS_TYPE.NINETY_DAYS_DATA) {
                listData = await ninetyDaysDataFilter(
                    ["a", "b", "c", "d", "e"],
                    listData,
                    ninetyDaysData
                );
            } else {
                listData = await ninetyDaysDataFilter(
                    cardFilter.ninetyDaysData,
                    listData,
                    ninetyDaysData
                );
            }
        }
    }

    let censusAverage = 0;
    let censusByPeriod = [];
    let censusByFacility = []
    let bedCapacity = 0;
    let bedByFacility = [];
    let censusAsOfNowByFacility = []
    if (startDate && endDate) {
        const facilityData = facilityIds.length > 0 ? facilityIds : [facilityId];
        if (isMainData) {
            censusByPeriod = await getCensusAverageByPeriod(
                startDateFilter,
                endDateFilter,
                facilityData
            );
        }
        const censusInfo = await getCensusAverageInfo(
            startDateFilter,
            endDateFilter,
            facilityData,
            false,
            censusByFacility,
            bedByFacility,
            censusAsOfNowByFacility
        );
        censusAverage = censusInfo.censusAverage;
        bedCapacity = censusInfo.bedCapacity;
    }
    return {
        data: listData,
        censusAverage,
        censusByPeriod,
        ninetyDaysData,
        bedByFacility,
        bedCapacity,
        censusByFacility,
        censusAsOfNowByFacility,
        customCombineTabData,
        customTabs
    };
};

module.exports = {
    getCardPatientChartData,
    getAllCount,
};
