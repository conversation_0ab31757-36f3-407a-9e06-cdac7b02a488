import axios from "../../axios";
import { useEffect, useState } from "react";
import { useSelector } from "react-redux";
import update from "immutability-helper";
import styles from "./Facilities.module.scss";
import AddFacility from "./add-facility/AddFacility";

const Facilities = (props) => {
  const { auth } = useSelector(({ auth }) => ({ auth }));
  const [facilities, setFacilities] = useState([]);
  const [showAdd, setShowAdd] = useState(false);

  const getFacilities = async () => {
    let facilities = await axios.get(`/api/facility/list/${auth.accountId}`);
    setFacilities(facilities.data);
  };

  useEffect(() => {
    if (!auth?.accountId) return;
    getFacilities();
  }, [auth]);

  const toggleShowAdd = () => setShowAdd(!showAdd);

  const facilityAdded = (facility) => {
    const newData = update(facilities, {
      $push: [facility],
    });

    setFacilities(newData);
  };

  return (
    <div className={styles.facilities}>
      <div className={`df aic ${styles.pageHdr}`}>
        <div className={`mla`}>
          <button
            className={`ttuc fs12 ffmm ${styles.addBtn}`}
            onClick={toggleShowAdd}
          >
            + Add facility
          </button>
        </div>
      </div>
      {facilities.map((facility) => (
        <div
          key={facility._id}
          className={`df aic ffmr fs14 ${styles.facilityLine}`}
        >
          <p className={styles.sec}>{facility.name}</p>
          <p className={styles.sec}>{facility.email}</p>
        </div>
      ))}

      {showAdd ? (
        <AddFacility close={toggleShowAdd} facilityAdded={facilityAdded} />
      ) : undefined}
    </div>
  );
};

export default Facilities;
