const mongoose = require("mongoose");
const { defaultInsuranceData } = require("../../data/common.data");
const { setQuestions } = require("./question");
const Facility = mongoose.model("facility");
const ReportsSubscriptions = mongoose.model("reportsSubscriptions");
const AlertReport = mongoose.model("alertReport");
const Validation = mongoose.model("validation");
const _ = require("lodash");
const { questionTypes, saveLogs } = require("../utilis/common");


// Create facility
const createFacility = async data => {
  const regEx = new RegExp(`^${data.name}$`, "i");
  const existingFacility = await Facility.findOne({ name: regEx, accountId: data.accountId });

  if (existingFacility) {
    throw new Error("Facility name already taken.");
  }

  const isFirstFacility = await Facility.findOne({ accountId: data.accountId });

  const localFacility = new Facility(data);
  const saved = await localFacility.save().catch(e => e);

  if (saved && !isFirstFacility) {
    if (defaultInsuranceData().length > 0) {
      defaultInsuranceData().map(async item => {
        await Validation.create({
          facilityId: saved._id.toString(),
          isEditable: false,
          label: item,
          active: true,
          type: "insurance",
        });
      });
    }
  }
  // questionTypes.map(async question => {
  //   setQuestions(saved._id, question.type, question.transferType, data.accountId);
  // });
  return saved;
};

const updateFacility = async (id, data, accountId) => {
  const existingFacility = await Facility.findOne({ _id: { $ne: id }, name: data.name, accountId: mongoose.Types.ObjectId(accountId) });

  if (existingFacility) {
    throw new Error("Facility name already taken.");
  }

  const updated = await Facility.findByIdAndUpdate(id, data, {
    new: true,
  });

  return updated;
};

const getFacility = async (id, user) => {
  // facilities
  let facility;
  if (id) {
    let allow = getFacilityPermission(user, id);
    if (allow) {
      facility = await Facility.findById(id);
      return facility;
    } else {
      throw new Error("Not ok");
    }
  }
  facility = await Facility.find();
  return getFacilityPermission(user, "", facility);
};

const getFacilityPermission = (user, id, facilities) => {
  if (id) {
    return user.type === "super"
      ? true
      : user.facilities.find(elem => elem.facilityId.toString() === id)?.read
        ? true
        : false;
  } else if (facilities.length) {
    return user.type === "super"
      ? facilities
      : facilities.filter(elem =>
        user.facilities.some(fa => fa.facilityId.toString() === elem._id.toString())
      );
  }
};

const filterData = async (arr, callback) => {
  const fail = Symbol();
  return (await Promise.all(arr.map(async item => ((await callback(item)) ? item : fail)))).filter(
    i => i !== fail
  );
};

const getAllAccountFacilityList = async (accountId, user, grouped) => {
  if (grouped === "true") {
    let search = {};
    if (accountId) {
      search.accountId = mongoose.Types.ObjectId(accountId);
    }
    if (user && user.role && user.role.slug != "super") {
      const filterIds = [];
      const facilities = user.facilities || [];
      facilities.map(ele => {
        if (ele.access) {
          filterIds.push(ele.facilityId);
        }
      });
      search._id = { $in: filterIds };
    }

    let a = await await Facility.aggregate([
      { $match: { ...search } },
      {
        $lookup: {
          from: "accounts",
          localField: "accountId",
          foreignField: "_id",
          as: "account",
        },
      },

      { $unwind: { path: "$account", preserveNullAndEmptyArrays: true } },
      { $group: { _id: "$account", facilities: { $push: "$$ROOT" } } },
    ]);

    return a.length > 0 ? a.filter(ele => ele._id && ele) : a;
  }
};

const getFacilityList = async (accountId, user, grouped) => {
  if (grouped === "true") {
    //call when load facility to add user modal
    let search = {};
    if (accountId) {
      search.accountId = mongoose.Types.ObjectId(accountId);
    }

    if (user && user.role && user.role.slug != "super") {
      const filterIds = [];
      const facilities = user.facilities || [];
      facilities.map(ele => {
        if (ele.access) {
          filterIds.push(ele.facilityId);
        }
      });
      search._id = { $in: filterIds };
    }

    let a = await await Facility.aggregate([
      { $match: { ...search } },
      {
        $lookup: {
          from: "accounts",
          localField: "accountId",
          foreignField: "_id",
          as: "account",
        },
      },

      { $unwind: { path: "$account", preserveNullAndEmptyArrays: true } },
      { $group: { _id: "$account", facilities: { $push: "$$ROOT" } } },
    ]);
    return a;
  }
  if (accountId) {
    //Get facility based on account id;
    let list = [];
    if (user && user.role && user.role.slug == "super") {
      list = await Facility.find({ accountId });
    } else if (user && user.role && user.role.slug == "user") {
      const filterIds = [];
      const facilities = user.facilities || [];

      facilities.map(ele => {
        if (ele.read || ele.write) {
          filterIds.push(ele.facilityId.toString());
        }
      });
      const listData = await Facility.find({
        accountId,
        _id: { $in: filterIds },
      });
      if (listData && listData.length > 0) {
        const LatestList = [];
        await filterData(listData, async item => {
          const matchedFacility = _.find(facilities, { facilityId: item._id });
          if (matchedFacility) {
            LatestList.push({
              ...item?._doc,
              read: matchedFacility.read,
              write: matchedFacility.write,
            });
          } else {
            LatestList.push(item);
          }
        });
        list = LatestList;
      } else {
        list = listData;
      }
    } else {
      const filterIds = [];
      const facilities = user.facilities || [];
      facilities.map(ele => {
        if (ele.access) {
          filterIds.push(ele.facilityId.toString());
        }
      });
      list = await Facility.find({ accountId, _id: { $in: filterIds } });
    }
    return list;
  } else {
    let list = await Facility.find();
    return list;
  }
};

const getFacilityNames = async (filterIds) => {
  const filterIdsData = [];
  filterIds.map(ele => { filterIdsData.push(ele.toString()) });
  const listData = await Facility.find({ _id: { $in: filterIdsData } });
  if (listData && listData.length > 0) {
    let names = []
    _.map(listData, (ele) => names.push(ele?.name));
    return names.join(", ");
  }
  return null;
}

const getManageFacilityList = async req => {
  const user = req.user;
  const { accountId } = req.query;
  if (user && user.role.slug !== "super") {
    const filterIds = [];
    const facilities = user.facilities || [];
    facilities.map(ele => {
      if (ele.access) {
        filterIds.push(ele.facilityId.toString());
      }
    });
    list = await Facility.find({ accountId, _id: { $in: filterIds } });
    return list;
  } else {
    const list = await Facility.find({
      accountId: mongoose.Types.ObjectId(accountId),
    });
    return list;
  }
};

const getFacilityUser = async user => {
  let facilities;
  if (user.type === "admin") {
    facilities = await Facility.find({
      accountId: user.accountId,
    });
  } else if (user.type === "superAdmin") {
    facilities = await Facility.find();
  } else {
    facilities = await Facility.find({
      _id: { $in: user.facilities.map(facility => facility.facilityId) },
    });
  }

  return facilities;
};

const deleteFacility = async (id, req) => {
  if (id) {

    const alertReportData = await AlertReport.find({
      facilityId: mongoose.Types.ObjectId(id)
    });

    if (alertReportData && alertReportData.length > 0) {
      await filterData(alertReportData, async report => {
        await AlertReport.findByIdAndDelete(report?._id);
      });
    }

    const reportData = await ReportsSubscriptions.find({
      facilityIds: {
        $elemMatch: { $in: [id.toString()] }
      }
    });

    if (reportData && reportData.length > 0) {
      await filterData(reportData, async report => {
        let facilityIds = report?.facilityIds?.map((ele) => ele.toString());
        if (facilityIds.length === 1) {
          // remove full record
          await ReportsSubscriptions.findByIdAndDelete(report?._id);
        } else {
          // remove only facility id
          const index = facilityIds.indexOf(id.toString());
          if (index > -1) {
            facilityIds.splice(index, 1);
          }
          facilityIds = facilityIds.map((e) => mongoose.Types.ObjectId(e))
          await ReportsSubscriptions.updateOne({ _id: report._id }, {
            $set: {
              facilityIds: facilityIds
            },
          });
        }
      });
    }

    let deletedFacility = await Facility.findByIdAndDelete(id);
    await saveLogs(
      deletedFacility,
      "facilityDelete",
      {
        accountId: req?.headers?.accountid,
        facilityId: req?.headers?.facilityid ? mongoose.Types.ObjectId(req?.headers?.facilityid) : null,
        userId: req?.user?._id
      },
      "Facility deleted successfully"
    );
    return deletedFacility;
  }
};

module.exports = {
  getFacilityNames,
  createFacility,
  getFacility,
  getFacilityList,
  getFacilityUser,
  updateFacility,
  deleteFacility,
  getAllAccountFacilityList,
  getManageFacilityList,
};
