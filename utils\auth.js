const config = require("../config");
const bcrypt = require("bcryptjs");
const jwt = require("jsonwebtoken");

const getTokenPayload = token => {
    const decodedToken = jwt.decode(token);

    delete decodedToken.iat;
    delete decodedToken.exp;

    return { tokenPayload: decodedToken };
};

const generateRefreshToken = (userHashedKey, payload, secretKey, options) => {
    let hashedKey = userHashedKey;

    if (!userHashedKey) {
        const salt = bcrypt.genSaltSync(10);
        hashedKey = bcrypt.hashSync(secretKey, salt);
    }

    const refreshToken = jwt.sign(payload, hashedKey, options);

    return { hashedKey, refreshToken };
};

const generateAuthTokens = async (dbRecord, accessPayload, refreshPayload = accessPayload) => {
    const { hashedKey, refreshToken } = generateRefreshToken(
        dbRecord.hashedKey,
        refreshPayload,
        config.refreshTokenSecretKey,
        { expiresIn: config.refreshTokenExpiryTimeInSeconds }
    );
    const accessToken = jwt.sign(accessPayload, config.accessTokenSecretKey, {
        expiresIn: config.accessTokenExpiryTimeInSeconds,
    });

    !dbRecord.hashedKey && (await dbRecord.updateOne({ hashedKey }));

    return { refreshToken, accessToken };
};

const renewRefreshToken = async (hashedKey, refreshToken) => {
    const { tokenPayload: refreshTokenPayload } = getTokenPayload(refreshToken);

    const { refreshToken: newRefreshToken } = generateRefreshToken(
        hashedKey,
        refreshTokenPayload,
        config.refreshTokenSecretKey,
        { expiresIn: config.refreshTokenExpiryTimeInSeconds }
    );

    return newRefreshToken;
};

const renewAccessToken = async accessToken => {
    const { tokenPayload: accessTokenPayload } = getTokenPayload(accessToken);

    const newAccessToken = jwt.sign(accessTokenPayload, config.accessTokenSecretKey, {
        expiresIn: config.accessTokenExpiryTimeInSeconds,
    });

    return newAccessToken;
};

const renewTokens = async (dbModel, dbRecordId, accessToken, refreshToken) => {
    const dbRecord = await dbModel.findOne({ _id: dbRecordId });
    const hashedKey = dbRecord.hashedKey;

    try {
        if (!hashedKey) {
            throw new Error("Authentication Error.");
        }

        jwt.verify(refreshToken, hashedKey);

        const newAccessToken = await renewAccessToken(accessToken);

        return { accessToken: newAccessToken, refreshToken, isRefreshTokenRenewed: false };
    } catch (e) {
        if (e.name === "TokenExpiredError") {
            const [newRefreshToken, newAccessToken] = await Promise.all([
                renewRefreshToken(hashedKey, refreshToken),
                renewAccessToken(accessToken),
            ]);

            return {
                accessToken: newAccessToken,
                refreshToken: newRefreshToken,
                isRefreshTokenRenewed: true,
            };
        }
        throw e;
    }
};

const attachRefreshToken = (res, refreshToken) => {
    const cookieOptions =
        process.env.NODE_ENV === "production"
            ? {
                httpOnly: true,
                secure: true,
                sameSite: "none",
            }
            : {};
    res.cookie(config.refreshCookieName, refreshToken, cookieOptions);

    return res;
};

function randomString(length) {
    let result = '';
    const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    const charactersLength = characters.length;
    let counter = 0;
    while (counter < length) {
        result += characters.charAt(Math.floor(Math.random() * charactersLength));
        counter += 1;
    }
    return result;
}

const attachTenant = (res, tenantId) => {
    const cookieOptions =
        process.env.NODE_ENV === "production"
            ? {
                httpOnly: true,
                secure: true,
                sameSite: "none",
            }
            : {};

    if (!tenantId) {
        res.clearCookie(config.tenantCookieName, cookieOptions);
    } else {
        res.cookie(config.tenantCookieName, tenantId, cookieOptions);
    }

    return res;
};

module.exports = { generateAuthTokens, attachRefreshToken, renewTokens, randomString, attachTenant };
