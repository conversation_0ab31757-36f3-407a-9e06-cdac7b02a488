.matchingCard {
    transition: all 0.3s ease;
}

.matchingCard:hover {
    transform: scale(1.03) !important;
    box-shadow: 0 0 15px rgba(0,0,0,0.3) !important;
}

.matchingTitle {
    color: #1976d2;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
}

.matchingIndicator {
    color: #1976d2;
    font-size: 12px;
    animation: pulse 2s infinite;
}

.matchingTotal {
    color: #1976d2;
    font-weight: 500;
}

@keyframes pulse {
    0% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
    100% {
        opacity: 1;
    }
} 