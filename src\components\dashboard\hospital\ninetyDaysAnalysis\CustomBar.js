import React from 'react';
import { ResponsiveBar } from '@nivo/bar';

const CustomBar = ({
    data,
    height,
    isCustom,
    priorityNumber,
    defaultColors,
    filter,
    projectionDays,
    isComparingAgainstAvgCensus,
    lockedTotalBy,
    transferType,
    calcProPercentsBasedOnFilterAndDays
}) => {
    const barComponent = ({ bar }) => {
        const isFirstItemInPriorityData = priorityNumber === 1;
        const fallbackColor = defaultColors[bar.index % defaultColors.length];
        const fillColor = bar.data.data.color || fallbackColor;

        const isZeroWidth = bar.width === 0;
        const displayWidth = isZeroWidth ? 0 : Math.max(bar.width, 80);
        const labelX = bar.x + displayWidth / 2;

        return (
            <g transform={`translate(${bar.x},${bar.y})`} className="nineteen_days_bar">
                <rect
                    style={{ borderRadius: "25% 10%" }}
                    width={displayWidth}
                    height={32}
                    fill={fillColor}
                    strokeWidth="0"
                    stroke={fillColor}
                    focusable="false"
                    ry={20}
                />
                {displayWidth > 12 && (
                    <text
                        textAnchor="middle"
                        dominantBaseline="central"
                        x={labelX}
                        y={16}
                        style={{
                            fontFamily: "manrope",
                            fontSize: "13px",
                            fill: "#fff",
                            pointerEvents: "none",
                            fontWeight: "600",
                        }}
                    >
                        {calcProPercentsBasedOnFilterAndDays(
                            bar?.data?.data?.value ?? bar?.data?.data?.total ?? 0,
                            filter,
                            projectionDays
                        )}
                        <tspan
                            fillOpacity="0.85"
                            style={{ fontWeight: "400", fontSize: "11px" }}
                        >
                            (
                            {calcProPercentsBasedOnFilterAndDays(
                                bar.data.data.percentage,
                                filter,
                                projectionDays,
                                true,
                                isComparingAgainstAvgCensus ||
                                lockedTotalBy === "census" ||
                                (isFirstItemInPriorityData && (!transferType || transferType.length === 0))
                            )}
                            %)
                        </tspan>
                    </text>
                )}
            </g>
        );
    };

    return (
        <ResponsiveBar
            data={data}
            keys={["value"]}
            indexBy="key"
            margin={{ top: 0, right: 12, bottom: 20, left: 5 }}
            padding={0.3}
            layout="horizontal"
            innerPadding={0}
            valueScale={{ type: "linear" }}
            indexScale={{ type: "band", round: true, padding: 0.9 }}
            enableArcLabels={false}
            enableArcLinkLabels={false}
            enableGridY={false}
            enableGridX={true}
            axisLeft={null}
            borderRadius={4}
            labelTextColor="#fff"
            valueFormat={(v, a, b, c) => {
                return `${v}%1531564561644657`;
            }}
            theme={{
                grid: {
                    line: {
                        strokeWidth: 1,
                        strokeDasharray: "12 12",
                    },
                },
            }}
            barComponent={barComponent}
        />
    );
};

export default CustomBar;
