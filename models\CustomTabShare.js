const mongoose = require("mongoose");
const { Schema, Types } = mongoose;

const customTabShareSchema = new Schema({
  customTabId: { type: Types.ObjectId, ref: "customTab", required: true },
  creatorId: { type: Types.ObjectId, ref: "user", required: true }, // Who shared it
  recipientId: { type: Types.ObjectId, ref: "user", required: true }, // Who received it
  accountId: { type: Types.ObjectId, ref: "account", required: true },
  
  // Share Status
  status: {
    type: String,
    enum: ["PENDING", "ACCEPTED", "KEPT", "DELETED", "UNSHARED"],
    default: "PENDING"
  },
  
  // Timestamps for different actions
  sharedAt: { type: Date, default: Date.now },
  respondedAt: { type: Date }, // When user responded to share
  viewedAt: { type: Date }, // When user first viewed the tab
  lastAccessedAt: { type: Date }, // Last time user accessed the tab
  
  // Permissions
  canEdit: { type: <PERSON>ole<PERSON>, default: false }, // Currently always false except creator
  canDelete: { type: Boolean, default: true }, // Can delete for themselves
  
  // Metadata
  shareMessage: { type: String }, // Optional message when sharing
  newTitle: { type: String }, // Used when user resolves title conflicts
  isHidden: { type: Boolean, default: false }, // For hiding shared tabs from user's view
  isActive: { type: Boolean, default: true }, // For soft delete/unshare
  isAutoIncluded: { type: Boolean, default: false }, // For superAdmins auto-included (not explicitly selected)
  
}, { timestamps: true });

// Indexes for performance
customTabShareSchema.index({ customTabId: 1, recipientId: 1 });
customTabShareSchema.index({ recipientId: 1, status: 1 });
customTabShareSchema.index({ customTabId: 1, isActive: 1 });
customTabShareSchema.index({ creatorId: 1, customTabId: 1 });

mongoose.model("customTabShare", customTabShareSchema); 