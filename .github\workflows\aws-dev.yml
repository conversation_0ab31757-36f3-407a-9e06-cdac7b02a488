name: AWS ECR Build and Push Dev

on:
    push:
        branches: develop

concurrency:
    group: ${{ github.ref }}-${{ github.workflow }}
    cancel-in-progress: true

jobs:
    build:
        runs-on: ubuntu-latest

        steps:
            - uses: actions/checkout@v4
            - name: Setup ubuntu
              run: echo fs.inotify.max_user_instances=524288 | sudo tee -a /etc/sysctl.conf && sudo sysctl -p

            - name: Free disk space
              run: |
                  df -h /
                  sudo swapoff -a
                  sudo apt clean
                  docker image ls -aq
                  if [ -n "$(docker image ls -aq)" ]; then
                      docker rmi $(docker image ls -aq)
                  else
                      echo "No Docker images to remove."
                  fi
                  find ~/work/_temp -name "cache.tgz" -exec rm -f {} \;
                  sudo rm -rf "/usr/local/share/boost"
                  sudo rm -rf "$AGENT_TOOLSDIRECTORY"
                  df -h /

            - name: Set up QEMU
              uses: docker/setup-qemu-action@v3

            - name: Set up Docker Buildx
              uses: docker/setup-buildx-action@v3

            - name: Configure AWS credentials
              uses: aws-actions/configure-aws-credentials@v4
              with:
                  aws-access-key-id: ${{ secrets.AWS_DEPLOY_ACCESS_KEY_ID }}
                  aws-secret-access-key: ${{ secrets.AWS_DEPLOY_SECRET_ACCESS_KEY }}
                  aws-region: us-east-1

            - name: Login to Amazon ECR
              id: login-ecr
              uses: aws-actions/amazon-ecr-login@v2

            - name: Build, tag, and push the image to Amazon ECR
              id: build-image
              env:
                  ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
                  ECR_REPOSITORY: "snf-dev"
                  IMAGE_TAG: latest
                  NODE_ENV: development
              run: |
                  # Build a docker container and push it to ECR
                  docker build . -t $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG -f Dockerfile
                  echo "Pushing image to ECR..."
                  docker push $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG
                  echo "::set-output name=image::$ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG"
