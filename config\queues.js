const { Queue } = require('bullmq');
const { createBullBoard } = require('bull-board');
const { BullMQAdapter } = require('bull-board/bullMQAdapter');

let jobQueue = null;
let jobQueueReportSchedular = null;

/**
 * Initialize job queues for production environment
 */
const initializeQueues = (app) => {
    const configModule = require('./redis-connection');

    // Initialize job queues
    jobQueue = new Queue('REPORTS', {
        connection: configModule.createRedisConnection(),
    });

    jobQueueReportSchedular = new Queue('reportSchedular', {
        connection: configModule.createRedisConnection(),
    });

    // Setup Bull Board for queue monitoring
    const { router: reportSchedularRouter } = createBullBoard([
        new BullMQAdapter(jobQueueReportSchedular)
    ]);
    app.use('/admin/reportSchedular', reportSchedularRouter);

    const { router } = createBullBoard([new BullMQAdapter(jobQueue)]);
    app.use('/admin/queues', router);

    console.log("Job queues initialized successfully");
};

/**
 * Setup job monitoring endpoint
 */
const setupJobMonitoring = (app) => {
    app.get('/job/:id?', async (req, res) => {
        try {
            if (!jobQueue) {
                return res.status(503).json({
                    error: 'Job queue not initialized. This endpoint is only available when reports are enabled.'
                });
            }

            if (!req.params.id) {
                // Fetch all jobs in the queue
                const [waitingJobs, activeJobs, completedJobs, failedJobs, delayedJobs] = await Promise.all([
                    jobQueue.getJobs(['waiting']),
                    jobQueue.getJobs(['active']),
                    jobQueue.getJobs(['completed']),
                    jobQueue.getJobs(['failed']),
                    jobQueue.getJobs(['delayed'])
                ]);

                return res.send({
                    waiting: waitingJobs.map(job => ({ 
                        id: job.id, 
                        data: { id: job.data?._id, name: job.data?.name } 
                    })),
                    active: activeJobs.map(job => ({ 
                        id: job.id, 
                        data: { id: job.data?._id, name: job.data?.name } 
                    })),
                    completed: completedJobs.map(job => ({ 
                        id: job.id, 
                        data: { id: job.data?._id, name: job.data?.name } 
                    })),
                    failed: failedJobs.map(job => ({ 
                        id: job.id, 
                        data: { id: job.data?._id, name: job.data?.name }, 
                        failedReason: job.failedReason 
                    })),
                    delayed: delayedJobs.map(job => ({ 
                        id: job.id, 
                        data: { id: job.data?._id, name: job.data?.name } 
                    })),
                    isProduction: true
                });
            }

            // Fetch a single job by ID
            const job = await jobQueue.getJob(req.params.id);
            if (!job) {
                return res.status(404).json({ error: 'Job not found' });
            }

            const state = await job.getState();
            const result = await job.returnvalue;

            res.send({ jobId: job.id, status: state, result });
        } catch (error) {
            res.status(500).json({ 
                error: 'Failed to fetch job status', 
                details: error.message 
            });
        }
    });
};

module.exports = { 
    initializeQueues, 
    setupJobMonitoring,
    getJobQueue: () => jobQueue,
    getReportSchedularQueue: () => jobQueueReportSchedular
};
