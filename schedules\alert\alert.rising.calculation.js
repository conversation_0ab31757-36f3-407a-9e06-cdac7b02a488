const _ = require("lodash");
const { ALERT_TYPE_TYPE } = require("../../types/common.type");

function calculatePercentageChange(lastAverage, currentTotal) {
    // If lastAverage is 0, avoid division by zero and handle accordingly
    if (lastAverage === 0) {
        return currentTotal > 0 ? 100 : 0; // If there's no previous value, treat it as 100% increase if currentTotal > 0
    }

    const change = ((currentTotal - lastAverage) / lastAverage) * 100;
    return change ? Math.round(change) : 0;
}

async function checkTransferAlerts(currentWeek, last4Weeks, handleEmptyData, dataTypes, alertReport, pageLabel) {
    const alertMessages = [];
    const { type } = alertReport;

    const checkCondition = async (lastWeekItem, currentWeekItem) => {
        const lastAverage = lastWeekItem.average;
        const currentTotal = currentWeekItem.total;
        const category = currentWeekItem.label;

        let alertMessage = '';

        const roundedLastAverage = Math.round(lastAverage);
        const roundedCurrentTotal = Math.round(currentTotal);
        const increaseByPercentage = await calculatePercentageChange(roundedLastAverage, roundedCurrentTotal);
        // console.log(`The percentage change is ${increaseByPercentage}%`);

        if (currentTotal > 0 && lastAverage > 0) {

            if (type === ALERT_TYPE_TYPE.WEEKS) {
                if (roundedLastAverage === 0 && roundedCurrentTotal === 2) {
                    alertMessage = `${category} averaged ${roundedLastAverage} ${pageLabel} a week the previous four weeks and has sent out ${roundedCurrentTotal} ${pageLabel} this past week, which is a ${increaseByPercentage}% increase`;
                } else if (roundedLastAverage === 1 && roundedCurrentTotal === 3) {
                    alertMessage = `${category} averaged ${roundedLastAverage} ${pageLabel} a week the previous four weeks and has sent out ${roundedCurrentTotal} ${pageLabel} this past week, which is a ${increaseByPercentage}% increase`;
                } else if (roundedLastAverage === 2 && roundedCurrentTotal === 4) {
                    alertMessage = `${category} averaged ${roundedLastAverage} ${pageLabel} a week the previous four weeks and has sent out ${roundedCurrentTotal} ${pageLabel} this past week, which is a ${increaseByPercentage}% increase`;
                } else if ((roundedLastAverage >= 3 && roundedLastAverage <= 5) && roundedCurrentTotal >= roundedLastAverage * 1.75) {
                    alertMessage = `${category} averaged ${roundedLastAverage} ${pageLabel} a week the previous four weeks and has sent out ${roundedCurrentTotal} ${pageLabel} this past week, which is a ${75}% increase`;
                } else if ((roundedLastAverage >= 6 && roundedLastAverage <= 9) && roundedCurrentTotal >= roundedLastAverage * 1.55) {
                    alertMessage = `${category} averaged ${roundedLastAverage} ${pageLabel} a week the previous four weeks and has sent out ${roundedCurrentTotal} ${pageLabel} this past week, which is a ${55}% increase`;
                } else if ((roundedLastAverage >= 10 && roundedLastAverage <= 13) && roundedCurrentTotal >= roundedLastAverage * 1.50) {
                    alertMessage = `${category} averaged ${roundedLastAverage} ${pageLabel} a week the previous four weeks and has sent out ${roundedCurrentTotal} ${pageLabel} this past week, which is a ${50}% increase`;
                } else if (roundedLastAverage > 13 && roundedCurrentTotal >= roundedLastAverage * 1.25) {
                    alertMessage = `${category} averaged ${roundedLastAverage} ${pageLabel} a week the previous four weeks and has sent out ${roundedCurrentTotal} ${pageLabel} this past week, which is a ${increaseByPercentage}% increase`;
                }
            }


            if (type === ALERT_TYPE_TYPE.BI_WEEKS) {
                if (roundedLastAverage === 0 && roundedCurrentTotal === 2) {
                    alertMessage = `${category} averaged ${roundedLastAverage} ${pageLabel} every two weeks over the previous two months but has sent out ${roundedCurrentTotal} ${pageLabel} in the past two weeks, marking a ${increaseByPercentage}% increase.`;
                } else if (roundedLastAverage === 1 && roundedCurrentTotal >= roundedLastAverage * 3) {
                    alertMessage = `${category} averaged ${roundedLastAverage} ${pageLabel} every two weeks over the previous two months but has sent out ${roundedCurrentTotal} ${pageLabel} in the past two weeks, marking a ${increaseByPercentage}% increase.`;
                } else if ((roundedLastAverage >= 2 && roundedLastAverage <= 3) && roundedCurrentTotal >= roundedLastAverage * 2) {
                    alertMessage = `${category} averaged ${roundedLastAverage} ${pageLabel} every two weeks over the previous two months but has sent out ${roundedCurrentTotal} ${pageLabel} in the past two weeks, marking a ${increaseByPercentage}% increase.`;
                } else if (roundedLastAverage === 4 && roundedCurrentTotal >= roundedLastAverage * 1.75) {
                    alertMessage = `${category} averaged ${roundedLastAverage} ${pageLabel} every two weeks over the previous two months but has sent out ${roundedCurrentTotal} ${pageLabel} in the past two weeks, marking a ${increaseByPercentage}% increase.`;
                } else if (roundedLastAverage > 4 && roundedCurrentTotal >= roundedLastAverage * 1.5) {
                    alertMessage = `${category} averaged ${roundedLastAverage} ${pageLabel} every two weeks over the previous two months but has sent out ${roundedCurrentTotal} ${pageLabel} in the past two weeks, marking a ${increaseByPercentage}% increase.`;
                }
            }

            if (type === ALERT_TYPE_TYPE.MONTHS) {
                if (roundedLastAverage === 0 && roundedCurrentTotal === 2) {
                    alertMessage = `${category} averaged ${roundedLastAverage} over the previous four months but has sent out ${roundedCurrentTotal} ${pageLabel} this past month, marking a ${increaseByPercentage}% increase.`;
                } else if (roundedLastAverage === 1 && roundedCurrentTotal >= roundedLastAverage * 3) {
                    alertMessage = `${category} averaged ${roundedLastAverage} over the previous four months but has sent out ${roundedCurrentTotal} ${pageLabel} this past month, marking a ${increaseByPercentage}% increase.`;
                } else if ((roundedLastAverage >= 2 && roundedLastAverage <= 3) && roundedCurrentTotal >= roundedLastAverage * 2) {
                    alertMessage = `${category} averaged ${roundedLastAverage} over the previous four months but has sent out ${roundedCurrentTotal} ${pageLabel} this past month, marking a ${increaseByPercentage}% increase.`;
                } else if (roundedLastAverage === 4 && roundedCurrentTotal >= roundedLastAverage * 1.75) {
                    alertMessage = `${category} averaged ${roundedLastAverage} over the previous four months but has sent out ${roundedCurrentTotal} ${pageLabel} this past month, marking a ${increaseByPercentage}% increase.`;
                } else if (roundedLastAverage > 4 && roundedCurrentTotal >= roundedLastAverage * 1.5) {
                    alertMessage = `${category} averaged ${roundedLastAverage} over the previous four months but has sent out ${roundedCurrentTotal} ${pageLabel} this past month, marking a ${increaseByPercentage}% increase.`;
                }
            }

        }

        if (alertMessage) {
            const name = currentWeekItem?.name || lastWeekItem?.name;
            return {
                category,
                _id: currentWeekItem._id,
                label: category,
                total: roundedCurrentTotal,
                message: alertMessage,
                percentage: increaseByPercentage,
                ...name && { name }
            }
        }
        return null;
    };


    const compareData = async (currentWeekData, last4WeeksData) => {
        // Get the processed datasets from handleEmptyData
        const { currentData, averageData } = await handleEmptyData(currentWeekData, last4WeeksData);

        const currentWeekItems = currentData ?? [];
        const last4WeeksItems = averageData ?? [];
        // Use for...of to handle async operations correctly
        for (const lastWeekItem of last4WeeksItems) {
            let currentWeekItem = currentWeekItems.find(item => item._id === lastWeekItem._id);
            currentWeekItem = !_.isEmpty(currentWeekItem) ? currentWeekItem : { ...lastWeekItem, total: 0, average: 0 };
            if (currentWeekItem) {
                // Await the async checkCondition function
                const alert = await checkCondition(lastWeekItem, currentWeekItem);
                if (alert) {
                    alertMessages.push(alert);
                }
            }
        }
    };
    for (const dataType of dataTypes) {
        await compareData(currentWeek?.[0]?.[dataType] ?? [], last4Weeks?.[0]?.[dataType] ?? []);
    };


    return alertMessages;
}

// Execute the comparison and check for alerts
module.exports = {
    checkTransferAlerts
}