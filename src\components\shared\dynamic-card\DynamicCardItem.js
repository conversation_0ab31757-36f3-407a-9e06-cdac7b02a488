import React, { useMemo } from "react";
import { Grid } from "@mui/material";
import _ from "lodash";
import CardAnimation from "../CardAnimation";
import Card from "../../dashboard/card/Card";
import TaggedList from "../../dashboard/shared/taggedList/TaggedList";
import DynamicDataCardList from "../../dashboard/hospital/DynamicDataCardList/DynamicDataCardList";
import { PAGE_TYPE } from "../notes/NotesList";
import { CUSTOM_TAB_PAGE_TYPE, OVERALL_PAGE_SUB_TYPE } from "../../../types/pages.type";

const DynamicCardItem = ({
  ele = {},
  pageType = '',
  isCustomTab = false,
  selectedCards = [],
  loading = false,
  loadingComparison = false,
  filterListData = {},
  filterListDataComparison = {},
  cardFilter = {},
  handleChartDialog = () => { },
  handleOnClickReport = () => { },
  handleToggle = () => { },
  handleToggleAll = () => { },
  filter = {},
  filterComparison = {},
  dbData = {},
  dbDataComparison = {},
  percentageAgainst = 0,
  projectionDays = 0,
  getPriorityNumber = () => 0,
  transferType = '',
  lockedTotalBy = '',
  filterTotal = 0,
}) => {
  const { accessor, isCustomTab: eleIsCustomTab, dashboardType, type, label, title, tableLabel, description, question, _id, customQuestionInputType } = ele;

  const cardSelectedItems = cardFilter?.[accessor] ?? [];
  const cardID = filterListData?.[accessor]?.[0]?.id;
  const isSelectedTotalCard = cardSelectedItems.indexOf(cardID) !== -1;

  const isAnotherDashboard = eleIsCustomTab && !dashboardType?.includes(pageType);

  const handleCardClick = async () => {
    await handleToggle({
      item: filterListData?.[accessor]?.[0],
      type: accessor,
      isChecked: isSelectedTotalCard,
      cardTitle: title || label || tableLabel,
      question: {
        isCustom: true,
        customQuestionInputType: question?.customQuestionInputType,
        isCustomTab: false,
        cardTitle: title || label || tableLabel,
        customTab: type
      },
      isCustomTab: false,
    });
  };

  const handleCardAction = (title, defaultTab) =>
    handleChartDialog(title, accessor, null, false, defaultTab, {
      isCustom: true,
      customQuestionInputType,
      isCustomTab,
      isAnotherDashboard,
      customTab: ele,
      level: filterListData?.[accessor]?.[0]?.level,
      cardTitle: title || label || tableLabel,
      ...(isCustomTab && { dashboardType }),
    });

  const handleReportClick = (title, otherData) => {
    const reportProps = {
      questionId: _id,
      isCustomCard: true,
      isCustomTab,
      cardTitle: title || label || tableLabel,
    };
    if (pageType === 'admission') {
      handleOnClickReport(accessor, title, false, false, otherData, reportProps);
    } else {
      handleOnClickReport(accessor, title, false, otherData, reportProps);
    }
  };

  const isDisabled = useMemo(() => {

    if (pageType !== PAGE_TYPE.OVERALL || ele?.page !== PAGE_TYPE.OVERALL) return false;

    const filterPageType = ele?.filters?.[0]?.filters?.[0]?.card?.dataPoint ?? null;
    
    if(transferType === OVERALL_PAGE_SUB_TYPE.TOTAL){
      return true;
    }
    if (filterPageType === CUSTOM_TAB_PAGE_TYPE.OVERALL_INCOMING && transferType === OVERALL_PAGE_SUB_TYPE.TOTAL_OUTGOING) {
      return true;
    }
    if (filterPageType === CUSTOM_TAB_PAGE_TYPE.OVERALL_OUTGOING && transferType === OVERALL_PAGE_SUB_TYPE.TOTAL_INCOMING) {
      return true;
    }
    return false;
  }, [ele?.filters, ele?.page, pageType, transferType]);

  return (
    <CardAnimation
      id={`${accessor}_scroll`}
      checked={_.includes(selectedCards, accessor)}
    >
      <Grid item xs={12} sm={6} md={6} lg={4}>
        <Card
          isDisabled={isDisabled}
          customCardData={ele}
          tooltipContent={description}
          key={accessor}
          pageType={pageType}
          loading={loading || loadingComparison}
          title={title || label || tableLabel}
          priorityNumber={getPriorityNumber(accessor)}
          handleOnClickAction={handleCardAction}
          taggedList={
            <TaggedList
              type={accessor}
              data={filterListData?.[accessor] ?? []}
              selectedItem={cardFilter?.[accessor] ?? []}
            />
          }
          handleOnClickReport={handleReportClick}
          selectAllOption={false}
          selectedItems={cardFilter?.[accessor] ?? []}
          data={filterListData?.[accessor] ?? []}
          type={accessor}
          handleToggleAll={handleToggleAll}
          canBeSearched
          cardType={accessor}
        >
          <DynamicDataCardList
            totalTransfers={dbData.total}
            totalTransfersComparison={dbDataComparison?.total}
            filterListData={filterListData}
            filterListDataComparison={filterListDataComparison}
            question={ele}
            cardTitle={title || label || tableLabel}
            data={filterListData?.[accessor] ?? []}
            dataComparison={filterListDataComparison?.[accessor] ?? []}
            handleToggle={handleToggle}
            handleToggleAll={handleToggleAll}
            selectedItem={cardFilter?.[accessor]}
            spacialSelectedItem={cardFilter?.[`${accessor}_spacial`]}
            type={accessor}
            filter={filter}
            loading={loading || loadingComparison}
            filterComparison={filterComparison}
            page={pageType}
            averageCensusComparison={dbDataComparison?.censusAverage}
            averageCensus={percentageAgainst}
            projectionDays={projectionDays}
            isComparingAgainstAvgCensus={filterListData.isComparingAgainstAvgCensus}
            priorityNumber={getPriorityNumber(accessor)}
            transferType={transferType}
            lockedTotalBy={lockedTotalBy}
            isCustomTab={isCustomTab}
            filterTotal={filterTotal}
            handleCardClick={handleCardClick}
            isSelectedTotalCard={isSelectedTotalCard}
            cardFilter={cardFilter}
            isDisabled={isDisabled}
          />
        </Card>
      </Grid>
    </CardAnimation>
  );
};

export default DynamicCardItem;
