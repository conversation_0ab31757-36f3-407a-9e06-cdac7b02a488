/**
 * CORS configuration based on environment
 */
const getCorsOptions = () => {
    let origin = "";

    if (process.env.CORS_ORIGIN) {
        origin = process.env.CORS_ORIGIN;
    } else {
        switch (process.env.NODE_ENV) {
            case "production":
                origin = "https://www.simplesnf.com";
                break;
            case "development":
                origin = "http://localhost:3007";
                break;
            case "stage":
                origin = "https://live.snfdatasolutions.com";
                break;
            default:
                origin = "http://localhost:3007";
        }
    }

    return {
        origin: origin,
        credentials: true,
    };
};

module.exports = { getCorsOptions };
