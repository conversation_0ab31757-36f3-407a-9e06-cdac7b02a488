const { questionTypes } = require("../../api/utilis/common");
const { setQuestionsByAccount } = require("../../api/helpers/question");

const seedQuestionsOrders = async (accountId) => {
    try {
        for (const question of questionTypes) {
            await setQuestionsByAccount(question.type, question?.transferType, accountId, question?.filterValue);  
        }
        console.log('✅ QuestionsOrders created successfully.');
    } catch (err) {
        console.error('❌ Error creating QuestionsOrders:', err.message);
        throw err;
    }
};

module.exports = {
    seedQuestionsOrders,
};
