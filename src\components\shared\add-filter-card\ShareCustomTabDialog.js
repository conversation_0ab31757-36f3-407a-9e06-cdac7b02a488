import React, { useState, useEffect } from 'react';
import {
    <PERSON>alog,
    DialogTitle,
    DialogContent,
    DialogActions,
    Typography,
    Button,
    Box,
    Checkbox,
    List,
    ListItem,
    ListItemButton,
    ListItemText,
    CircularProgress,
    Avatar,
    Chip,
    Fade,
    Slide,
    IconButton,
    TextField,
    InputAdornment,
    FormControlLabel,
    Switch,
} from '@mui/material';
import {
    Share as ShareIcon,
    Close as CloseIcon,
    Search as SearchIcon,
    PersonAdd as PersonAddIcon,
    Group as GroupIcon,
    CheckCircle as CheckCircleIcon,
    VerifiedUser as VerifiedUserIcon,
    SelectAll as SelectAllIcon,
    Public as PublicIcon
} from '@mui/icons-material';
import { styled } from '@mui/material/styles';
import { ADD_NOTIFICATION } from '../../../store/types';
import { useDispatch, useSelector } from 'react-redux';
import { getAllUsersByAccount } from '../../../services/user.service';
import { shareCustomTab as shareCustomTabAPI, getTabShares } from '../../../services/custom-tab-sharing.service';
import { AUTH_ROLES } from '../../../types/auth.type';

const Transition = React.forwardRef(function Transition(props, ref) {
    return <Slide direction="up" ref={ref} {...props} />;
});

const StyledDialog = styled(Dialog)(({ theme }) => ({
    '& .MuiDialog-paper': {
        borderRadius: 16,
        boxShadow: '0 24px 38px 3px rgba(0,0,0,0.14), 0 9px 46px 8px rgba(0,0,0,0.12), 0 11px 15px -7px rgba(0,0,0,0.2)',
        background: '#ffffff',
        color: '#333333',
        minHeight: '500px',
    },
}));

const StyledDialogTitle = styled(DialogTitle)(({ theme }) => ({
    background: '#ffffff',
    borderBottom: '1px solid rgba(0,0,0,0.1)',
    padding: theme.spacing(2, 3),
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'space-between',
    position: 'relative',
    '&::before': {
        content: '""',
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        height: '2px',
        background: 'linear-gradient(90deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4)',
    }
}));

const StyledDialogContent = styled(DialogContent)(({ theme }) => ({
    padding: theme.spacing(2),
    background: '#ffffff',
}));

const StyledListItem = styled(ListItem)(({ theme }) => ({
    marginBottom: theme.spacing(0.5),
    background: '#f8f9fa',
    border: '1px solid rgba(0,0,0,0.08)',
    transition: 'background 0.2s ease',
    '&:hover': {
        background: '#e3f2fd',
    },
    '&.selected': {
        background: 'rgba(33, 150, 243, 0.1)',
        border: '1px solid rgba(33, 150, 243, 0.3)',
        '&:hover': {
            background: 'rgba(33, 150, 243, 0.15)',
        }
    }
}));

const StyledAvatar = styled(Avatar)(({ theme }) => ({
    background: 'linear-gradient(45deg, #FF6B6B, #4ECDC4)',
    marginRight: theme.spacing(1.5),
    width: 36,
    height: 36,
    fontSize: '1rem',
    fontWeight: 'bold',
}));

const StyledCheckbox = styled(Checkbox)(({ theme }) => ({
    color: 'rgba(0,0,0,0.6)',
    '&.Mui-checked': {
        color: '#2196f3',
    },
    '& .MuiSvgIcon-root': {
        fontSize: 20,
    }
}));

const GlowButton = styled(Button)(({ theme }) => ({
    borderRadius: 20,
    padding: theme.spacing(1, 2.5),
    fontWeight: 'bold',
    textTransform: 'none',
    fontSize: '0.85rem',
    minHeight: '32px',
    transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
    '&.share-button': {
        background: 'linear-gradient(45deg, #2196f3, #64b5f6)',
        color: 'white',
        boxShadow: '0 2px 10px rgba(33, 150, 243, 0.3)',
        '&:hover': {
            background: 'linear-gradient(45deg, #1976d2, #42a5f5)',
            boxShadow: '0 4px 15px rgba(33, 150, 243, 0.5)',
            transform: 'translateY(-1px)',
        },
        '&:disabled': {
            background: 'rgba(0,0,0,0.1)',
            color: 'rgba(0,0,0,0.3)',
            boxShadow: 'none',
        }
    },
    '&.close-button': {
        background: '#f5f5f5',
        color: '#666666',
        border: '1px solid rgba(0,0,0,0.2)',
        '&:hover': {
            background: '#eeeeee',
            transform: 'translateY(-1px)',
        }
    }
}));

const SearchField = styled(TextField)(({ theme }) => ({
    '& .MuiOutlinedInput-root': {
        borderRadius: 25,
        background: '#f8f9fa',
        border: '1px solid rgba(0,0,0,0.1)',
        '& fieldset': {
            border: 'none',
        },
        '&:hover': {
            background: '#e9ecef',
        },
        '&.Mui-focused': {
            background: '#ffffff',
            border: '1px solid #2196f3',
        }
    },
    '& .MuiInputBase-input': {
        color: '#333333',
        '&::placeholder': {
            color: 'rgba(0,0,0,0.6)',
        }
    }
}));

const ShareCustomTabDialog = ({
    open,
    onClose,
    customTab,
    onShareSuccess
}) => {
    const [users, setUsers] = useState([]);
    const [filteredUsers, setFilteredUsers] = useState([]);
    const [selectedUsers, setSelectedUsers] = useState([]);
    const [existingShares, setExistingShares] = useState([]);
    const [loadingUsers, setLoadingUsers] = useState(false);
    const [searchTerm, setSearchTerm] = useState('');
    const [isUniversal, setIsUniversal] = useState(false);
    const [selectAll, setSelectAll] = useState(false);
    const dispatch = useDispatch();
    const authUser = useSelector((state) => state.auth);

    useEffect(() => {
        if (open && customTab?._id) {
            loadUsers();
        }
    }, [open, customTab?._id]); // eslint-disable-line react-hooks/exhaustive-deps

    useEffect(() => {
        if (searchTerm) {
            const filtered = users.filter(user => {
                const fullName = `${user.fullName || ''} ${user.lastName || ''}`.trim();
                const email = user.email || '';
                const username = user.username || '';
                return (
                    fullName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                    email.toLowerCase().includes(searchTerm.toLowerCase()) ||
                    username.toLowerCase().includes(searchTerm.toLowerCase())
                );
            });
            setFilteredUsers(filtered);
        } else {
            setFilteredUsers(users);
        }
    }, [searchTerm, users]);

    // Handle Select All logic
    useEffect(() => {
        if (selectAll && filteredUsers.length > 0) {
            const allUserIds = filteredUsers.map(user => user._id || user.id);
            setSelectedUsers(allUserIds);
        }
        // Note: We don't reset selectedUsers when selectAll becomes false
        // This allows individual user deselection to work properly
    }, [selectAll, filteredUsers]);

    // Update selectAll state when all users are manually selected
    useEffect(() => {
        const allUserIds = filteredUsers.map(user => user._id || user.id);
        const allSelected = allUserIds.length > 0 && allUserIds.every(id => selectedUsers.includes(id));

        // Only update if the state is different to avoid infinite loops
        if (selectAll !== allSelected) {
            setSelectAll(allSelected);
        }
    }, [selectedUsers, filteredUsers]); // eslint-disable-line react-hooks/exhaustive-deps

    const loadUsers = async () => {
        setLoadingUsers(true);
        try {
            if (customTab?._id) {
                // Load all users in the account
                const userList = await getAllUsersByAccount();
                setUsers(userList || []);
                setFilteredUsers(userList || []);

                // Load existing shares for this custom tab
                let customTabData = {};
                try {
                    const sharesResponse = await getTabShares(customTab._id);
                    if (sharesResponse.status === 200) {
                        const shares = sharesResponse?.data?.shares || [];
                        console.log(shares, 'shares');

                        customTabData = sharesResponse?.data?.customTab || {};
                        setExistingShares(shares);

                        // Pre-select users who already have access, excluding those with isAutoIncluded
                        const existingUserIds = shares
                            .filter(share => !share.isAutoIncluded) // Exclude auto-included shares
                            .map(share => share.recipientId._id || share.recipientId);

                        setSelectedUsers(existingUserIds);
                    }
                } catch (shareError) {
                    // If fetching shares fails, just continue without pre-selection
                    console.warn('Failed to load existing shares:', shareError);
                    setExistingShares([]);
                    setSelectedUsers([]);
                }

                // Check if the tab is already universal
                setIsUniversal(customTabData?.isUniversal || false);
            }
        } catch (error) {
            dispatch({
                type: ADD_NOTIFICATION,
                payload: {
                    type: 'error',
                    label: 'Failed to load users',
                    id: 'loadUsersError',
                },
            });
        } finally {
            setLoadingUsers(false);
        }
    };

    const handleClose = () => {
        setSelectedUsers([]);
        setExistingShares([]);
        setSearchTerm('');
        setIsUniversal(false);
        setSelectAll(false);
        onClose();
    };

    const handleUserToggle = (userId) => {
        const newSelectedUsers = selectedUsers.includes(userId)
            ? selectedUsers.filter(id => id !== userId)
            : [...selectedUsers, userId];

        setSelectedUsers(newSelectedUsers);

        // Update select all state based on current selection
        const allUserIds = filteredUsers.map(user => user._id || user.id);
        setSelectAll(allUserIds.length > 0 && allUserIds.every(id => newSelectedUsers.includes(id)));
    };

    const handleSelectAll = (checked) => {
        setSelectAll(checked);

        if (!checked) {
            // When unchecking select all, keep only existing shares (excluding auto-included)
            const existingUserIds = existingShares
                .filter(share => !share.isAutoIncluded)
                .map(share => share.recipientId._id || share.recipientId);
            setSelectedUsers(existingUserIds);
        }
        // When checking select all, the useEffect will handle selecting all users
    };

    const handleUniversalChange = (checked) => {
        setIsUniversal(checked);
        if (checked) {
            setSelectAll(true); // Automatically select all users when universal access is enabled
        } else {
            setSelectAll(false); // Deselect all users when universal access is disabled
            setSelectedUsers([]); // Clear selected users
        }
    };

    const handleShare = async () => {
        // if (selectedUsers.length === 0) {
        //     dispatch({
        //         type: ADD_NOTIFICATION,
        //         payload: {
        //             type: 'warning',
        //             label: 'Please select at least one user to share with',
        //             id: 'shareWarning',
        //         },
        //     });
        //     return;
        // }

        try {
            // Validate data before sending
            if (!customTab?._id) {
                throw new Error('Custom tab ID is missing');
            }

            // if (!selectedUsers || selectedUsers.length === 0) {
            //     throw new Error('No users selected');
            // }

            // Call the actual sharing API
            const result = await shareCustomTabAPI(
                customTab._id,
                selectedUsers,
                '', // Optional share message
                isUniversal // Universal access flag (just saved as boolean)
            );

            if (result.status === 200) {
                const { sharedWith, alreadyShared, message : messageFromAPI } = result.data;

                let message = messageFromAPI;
                if (sharedWith > 0 && alreadyShared > 0) {
                    message = `Shared with ${sharedWith} new user(s). ${alreadyShared} user(s) already had access.`;
                } else if (sharedWith > 0) {
                    message = `Custom tab shared with ${sharedWith} user(s) successfully!`;
                } else if (alreadyShared > 0) {
                    message = `All selected users already have access to this tab.`;
                }

                dispatch({
                    type: ADD_NOTIFICATION,
                    payload: {
                        type: 'success',
                        label: message,
                        id: 'shareSuccess',
                    },
                });

                if (onShareSuccess) {
                    onShareSuccess(selectedUsers, result.data);
                }

                handleClose();
            } else {
                throw new Error(result.message || 'Failed to share custom tab');
            }
        } catch (error) {
            console.error('Share error:', error);
            dispatch({
                type: ADD_NOTIFICATION,
                payload: {
                    type: 'error',
                    label: error.message || 'Failed to share custom tab',
                    id: 'shareError',
                },
            });
        }
    };

    const getInitials = (user) => {
        const fullName = user.fullName || '';
        const lastName = user.lastName || '';
        const email = user.email || '';

        if (fullName && lastName) {
            return `${fullName.charAt(0)}${lastName.charAt(0)}`.toUpperCase();
        } else if (fullName) {
            return fullName.charAt(0).toUpperCase();
        } else if (email) {
            return email.charAt(0).toUpperCase();
        }
        return '?';
    };

    const maskEmail = (email) => {
        if (!email) return '';

        const [localPart, domain] = email.split('@');
        if (!domain) return email;

        if (localPart.length <= 2) {
            return `${localPart[0]}***@${domain}`;
        } else if (localPart.length <= 4) {
            return `${localPart.substring(0, 2)}***@${domain}`;
        } else {
            return `${localPart.substring(0, 3)}***@${domain}`;
        }
    };

    const hasExistingAccess = (userId) => {
        return existingShares.some(share =>
            (share.recipientId._id || share.recipientId) === userId && !share.isAutoIncluded
        );
    };

    return (
        <StyledDialog
            open={open}
            onClose={handleClose}
            maxWidth="md"
            fullWidth
            TransitionComponent={Transition}
            keepMounted
            onClick={(e) => e.stopPropagation()}
        >
            <StyledDialogTitle>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                    <ShareIcon sx={{ fontSize: 28, color: '#2196f3' }} />
                    <Typography variant="h5" component="div" sx={{ fontWeight: 'bold', color: '#333333' }}>
                        Share Custom Tab
                    </Typography>
                </Box>
                <IconButton onClick={handleClose} sx={{ color: '#666666' }}>
                    <CloseIcon />
                </IconButton>
            </StyledDialogTitle>

            <StyledDialogContent>
                <Fade in={open} timeout={800}>
                    <Box>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                            <GroupIcon sx={{ color: '#2196f3' }} />
                            <Typography variant="body1" sx={{ color: '#333333', fontSize: '0.9rem' }}>
                                Share "{customTab?.label || 'this custom tab'}" with team members
                            </Typography>
                        </Box>

                        <SearchField
                            size="small"
                            fullWidth
                            placeholder="Search users..."
                            value={searchTerm}
                            onChange={(e) => setSearchTerm(e.target.value)}
                            InputProps={{
                                startAdornment: (
                                    <InputAdornment position="start">
                                        <SearchIcon sx={{ color: 'rgba(0,0,0,0.6)', fontSize: 18 }} />
                                    </InputAdornment>
                                ),
                            }}
                            sx={{ mb: 1 }}
                        />

                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1, flexWrap: 'wrap' }}>
                            <Chip
                                size="small"
                                icon={<PersonAddIcon sx={{ fontSize: '16px !important' }} />}
                                label={`${selectedUsers.length} selected`}
                                sx={{
                                    background: selectedUsers.length > 0 ? 'rgba(33, 150, 243, 0.1)' : '#f5f5f5',
                                    color: selectedUsers.length > 0 ? '#2196f3' : '#666666',
                                    border: `1px solid ${selectedUsers.length > 0 ? 'rgba(33, 150, 243, 0.3)' : 'rgba(0,0,0,0.1)'}`,
                                    height: '24px',
                                    fontSize: '0.75rem'
                                }}
                            />
                            {(() => {
                                // Count only explicitly selected users (exclude auto-included)
                                const explicitlySelectedCount = existingShares.filter(share => !share.isAutoIncluded).length;
                                return explicitlySelectedCount > 0 && (
                                    <Chip
                                        size="small"
                                        icon={<VerifiedUserIcon sx={{ fontSize: '16px !important' }} />}
                                        label={`${explicitlySelectedCount} have access`}
                                        sx={{
                                            background: 'rgba(76, 175, 80, 0.1)',
                                            color: '#4caf50',
                                            border: '1px solid rgba(76, 175, 80, 0.3)',
                                            height: '24px',
                                            fontSize: '0.75rem'
                                        }}
                                    />
                                );
                            })()}
                            <Typography variant="caption" sx={{ color: 'rgba(0,0,0,0.6)', fontSize: '0.75rem' }}>
                                {filteredUsers.length} users found
                            </Typography>
                        </Box>

                        {/* Universal Access Option - Right above users list */}
                        {authUser?.role?.slug !== AUTH_ROLES.USER && (
                            <Box sx={{ mb: 1, p: 1.5, background: '#f8f9fa', borderRadius: 1, border: '1px solid rgba(0,0,0,0.1)' }}>
                                <FormControlLabel
                                    control={
                                        <Switch
                                            size="small"
                                            checked={isUniversal}
                                            onChange={(e) => handleUniversalChange(e.target.checked)}
                                            sx={{
                                                '& .MuiSwitch-switchBase.Mui-checked': {
                                                    color: '#2196f3',
                                                },
                                                '& .MuiSwitch-switchBase.Mui-checked + .MuiSwitch-track': {
                                                    backgroundColor: '#2196f3',
                                                },
                                            }}
                                        />
                                    }
                                    label={
                                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                                            <PublicIcon sx={{ color: '#2196f3', fontSize: 16 }} />
                                            <Typography variant="body2" sx={{ fontWeight: 'bold', color: '#333333', fontSize: '0.875rem' }}>
                                                Universal Access
                                            </Typography>
                                            <Typography variant="caption" sx={{ color: 'rgba(0,0,0,0.6)', ml: 1 }}>
                                                - Auto-share with future users
                                            </Typography>
                                        </Box>
                                    }
                                    sx={{ mb: 0 }}
                                />
                            </Box>
                        )}

                        {/* Select All Option - Right above users list */}
                        <Box sx={{ mb: 1, p: 1.5, background: '#f0f7ff', borderRadius: 1, border: '1px solid rgba(33, 150, 243, 0.2)' }}>
                            <FormControlLabel
                                control={
                                    <Checkbox
                                        size="small"
                                        checked={selectAll}
                                        onChange={(e) => handleSelectAll(e.target.checked)}
                                        sx={{
                                            color: '#2196f3',
                                            '&.Mui-checked': {
                                                color: '#2196f3',
                                            }
                                        }}
                                    />
                                }
                                label={
                                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                                        <SelectAllIcon sx={{ color: '#2196f3', fontSize: 16 }} />
                                        <Typography variant="body2" sx={{ fontWeight: 'bold', color: '#333333', fontSize: '0.875rem' }}>
                                            Select All Users
                                        </Typography>
                                        <Typography variant="caption" sx={{ color: 'rgba(0,0,0,0.6)', ml: 1 }}>
                                            - Share with all current users
                                        </Typography>
                                    </Box>
                                }
                                sx={{ mb: 0 }}
                            />
                        </Box>

                        {loadingUsers ? (
                            <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
                                <CircularProgress
                                    size={40}
                                    sx={{ color: '#2196f3' }}
                                />
                            </Box>
                        ) : (
                            <List dense sx={{ maxHeight: 450, overflow: 'auto', pr: 1 }}>
                                {filteredUsers.length === 0 ? (
                                    <Box sx={{ textAlign: 'center', p: 4 }}>
                                        <GroupIcon sx={{ fontSize: 48, color: 'rgba(0,0,0,0.3)', mb: 2 }} />
                                        <Typography variant="body1" sx={{ color: 'rgba(0,0,0,0.6)' }}>
                                            {searchTerm ? 'No users match your search' : 'No users found in this account'}
                                        </Typography>
                                    </Box>
                                ) : (
                                    filteredUsers.map((user, index) => (
                                        <Fade in timeout={300 + index * 100} key={user._id || user.id}>
                                            <StyledListItem
                                                disablePadding
                                                className={selectedUsers.includes(user._id || user.id) ? 'selected' : ''}
                                            >
                                                <ListItemButton
                                                    onClick={() => handleUserToggle(user._id || user.id)}
                                                    sx={{ p: 1 }}
                                                >
                                                    <StyledCheckbox
                                                        checked={selectedUsers.includes(user._id || user.id)}
                                                        edge="start"
                                                        disableRipple
                                                        icon={<Box sx={{ width: 20, height: 20, border: '2px solid rgba(0,0,0,0.3)', borderRadius: '50%' }} />}
                                                        checkedIcon={<CheckCircleIcon sx={{ color: '#2196f3' }} />}
                                                    />
                                                    <StyledAvatar>
                                                        {getInitials(user)}
                                                    </StyledAvatar>
                                                    <ListItemText
                                                        primary={
                                                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                                                <Typography variant="body2" sx={{ fontWeight: 'bold', color: '#333333' }}>
                                                                    {`${user.fullName || ''} ${user.lastName || ''}`.trim() || maskEmail(user.email) || user.username}
                                                                </Typography>
                                                                {hasExistingAccess(user._id || user.id) && (
                                                                    <Chip
                                                                        size="small"
                                                                        icon={<VerifiedUserIcon sx={{ fontSize: '12px !important' }} />}
                                                                        label="Has Access"
                                                                        sx={{
                                                                            height: '20px',
                                                                            fontSize: '10px',
                                                                            background: 'rgba(76, 175, 80, 0.1)',
                                                                            color: '#4caf50',
                                                                            border: '1px solid rgba(76, 175, 80, 0.3)',
                                                                            '& .MuiChip-icon': {
                                                                                color: '#4caf50',
                                                                            }
                                                                        }}
                                                                    />
                                                                )}
                                                            </Box>
                                                        }
                                                        secondary={
                                                            user.email && (user.fullName || user.lastName) ? (
                                                                <Typography variant="caption" sx={{ color: 'rgba(0,0,0,0.6)' }}>
                                                                    {maskEmail(user.email)}
                                                                </Typography>
                                                            ) : null
                                                        }
                                                    />
                                                </ListItemButton>
                                            </StyledListItem>
                                        </Fade>
                                    ))
                                )}
                            </List>
                        )}
                    </Box>
                </Fade>
            </StyledDialogContent>

            <DialogActions sx={{ p: 2, background: '#f8f9fa', borderTop: '1px solid rgba(0,0,0,0.1)' }}>
                <GlowButton
                    onClick={handleClose}
                    className="close-button"
                    startIcon={<CloseIcon />}
                >
                    Close
                </GlowButton>
                <GlowButton
                    onClick={handleShare}
                    className="share-button"
                    disabled={loadingUsers}
                    startIcon={selectedUsers.length === 0 ? null : <ShareIcon />}
                >
                    {(() => {
                        const existingCount = selectedUsers.filter(userId => hasExistingAccess(userId)).length;
                        const newCount = selectedUsers.length - existingCount;

                        if (newCount > 0 && existingCount > 0) {
                            return `Update Sharing (${newCount} new, ${existingCount} existing)`;
                        } else if (newCount > 0) {
                            return `Share (${newCount})`;
                        } else if (existingCount > 0) {
                            return `Update Selection (${existingCount})`;
                        } else if (selectedUsers.length === 0 && existingCount === 0 && newCount === 0) {
                            return `Update`;
                        } else {
                            return `Share (${selectedUsers.length})`;
                        }
                    })()}
                </GlowButton>
            </DialogActions>
        </StyledDialog>
    );
};

export default ShareCustomTabDialog; 