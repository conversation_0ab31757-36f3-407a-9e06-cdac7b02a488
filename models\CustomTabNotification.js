const mongoose = require("mongoose");
const { Schema, Types } = mongoose;

const customTabNotificationSchema = new Schema({
  customTabId: { type: Types.ObjectId, ref: "customTab", required: true },
  customTabShareId: { type: Types.ObjectId, ref: "customTabShare" }, // Optional reference
  recipientId: { type: Types.ObjectId, ref: "user", required: true },
  senderId: { type: Types.ObjectId, ref: "user", required: true },
  accountId: { type: Types.ObjectId, ref: "account", required: true },
  
  // Notification Type
  type: {
    type: String,
    enum: [
      "TAB_SHARED", 
      "TAB_UPDATED", 
      "TAB_DELETED", 
      "TAB_UNSHARED",
      "SHARE_RESPONSE"
    ],
    required: true
  },
  
  // Content
  title: { type: String, required: true },
  message: { type: String, required: true },
  actionRequired: { type: Boolean, default: false }, // For share invitations
  
  // Status
  isRead: { type: Boolean, default: false },
  readAt: { type: Date },
  
  // Metadata
  metadata: { type: Schema.Types.Mixed }, // For additional data
  expiresAt: { type: Date }, // For auto-cleanup of old notifications
  
}, { timestamps: true });

// Indexes
customTabNotificationSchema.index({ recipientId: 1, isRead: 1, createdAt: -1 });
customTabNotificationSchema.index({ customTabId: 1 });
customTabNotificationSchema.index({ expiresAt: 1 }, { expireAfterSeconds: 0 });

mongoose.model("customTabNotification", customTabNotificationSchema); 