const mongoose = require("mongoose");
const keys = require("./index");

/**
 * Initialize MongoDB connection
 */
const connectDatabase = async () => {
    try {
        await mongoose.connect(process.env.MONGO_URI || keys.MONGO_URI, {
            useNewUrlParser: true,
            useUnifiedTopology: true,
        });
        console.log("MongoDB connected successfully");
    } catch (error) {
        console.error("MongoDB connection error:", error);
        process.exit(1);
    }
};

module.exports = { connectDatabase };
