const { ADMISSION_FILTER_TYPE, TOTAL_TYPE, ADMISSION_TYPES, ADMISSION_CARDS_TYPE, PAGE_TYPE, ADT_TABLE_TYPE } = require("../../../types/common.type");
const _ = require("lodash");
const { filterListDataItems, itemPercentage, filterAsyncData } = require("../common");
const { processDynamicCard, filterCustomPatientData, dynamicCardFilter, assignMatchedPatientIds, buildCustomTabsObject, getCustomTabsCards } = require("./report-common");
const { ADT_TYPES } = require("../../../types");

const hospitalListData = async (data, totalFilterData) => {
    //insuranceData Cal
    let hospitalDBDataGroup = [];
    if (data && data.length > 0) {
        const hospitalDBData = _.groupBy(data, "hospitalId");
        const originalData = _.groupBy(totalFilterData.originalData, "hospitalId");
        if (hospitalDBData) {
            let percentageTotal = totalFilterData?.totalForPercentage
                ? totalFilterData.totalForPercentage
                : data?.length || 0;

            hospitalDBDataGroup = await filterListDataItems(hospitalDBData, "hospital", percentageTotal, {
                ...totalFilterData,
                originalData,
            });
        }
    }
    return hospitalDBDataGroup;
}

const insuranceData = async (data, totalFilterData) => {
    //insuranceData Cal
    let insuranceDBDataGroup = [];
    if (data && data.length > 0) {
        const insuranceDBData = _.groupBy(data, "insuranceId");
        if (insuranceDBData) {
            const originalData = _.groupBy(totalFilterData.originalData, "insuranceId");
            let percentageTotal = totalFilterData?.totalForPercentage
                ? totalFilterData.totalForPercentage
                : data?.length || 0;

            insuranceDBDataGroup = await filterListDataItems(insuranceDBData, "payerSourceInsurance", percentageTotal, {
                ...totalFilterData,
                originalData,
            });
        }
    }

    return insuranceDBDataGroup;
}

const doctorData = async (data, totalFilterData) => {
    //Doctor list update Cal
    let doctorDBDataGroup = [];
    if (data && data.length > 0) {
        const doctorDBData = _.groupBy(data, "doctorId");
        if (doctorDBData) {
            const originalData = _.groupBy(totalFilterData.originalData, "doctorId");
            let percentageTotal = totalFilterData?.totalForPercentage
                ? totalFilterData.totalForPercentage
                : data?.length || 0;

            doctorDBDataGroup = await filterListDataItems(doctorDBData, "doctor", percentageTotal, {
                ...totalFilterData,
                originalData,
            });
        }
    }
    return doctorDBDataGroup;
}

const floorsData = async (data, totalFilterData) => {
    //FloorsData Cal
    let unitDBDataGroup = [];
    if (data && data.length > 0) {
        const floorDBData = _.groupBy(data, "floorId");
        if (floorDBData) {
            const originalData = _.groupBy(totalFilterData.originalData, "floorId");
            let percentageTotal = totalFilterData?.totalForPercentage
                ? totalFilterData.totalForPercentage
                : data?.length || 0;

            unitDBDataGroup = await filterListDataItems(floorDBData, "unit", percentageTotal, {
                ...totalFilterData,
                originalData,
            });
        }
    }
    return unitDBDataGroup;
}

const daysData = async (data, totalFilterData) => {
    const dayDBData = _.countBy(data, "day");
    const { originalData = [], totalType = null } = totalFilterData;
    const originalDataGroupBy = _.countBy(originalData, "day");
    let percentageTotal = totalFilterData?.totalForPercentage ? totalFilterData.totalForPercentage : data?.length || 0;

    const daysDataArr = [
        {
            _id: "Sun",
            label: "Sun",
            value: dayDBData?.Sun || 0,
            percentage: await itemPercentage(dayDBData?.Sun, percentageTotal, "number"),
            color: "#FFECA6",
            originalTotal: originalDataGroupBy?.Sun || 0,
            isTooltip: totalType && totalType === TOTAL_TYPE.MAIN ? false : true,
        },
        {
            _id: "Mon",
            label: "Mon",
            value: dayDBData?.Mon || 0,
            color: "#FFECA6",
            percentage: await itemPercentage(dayDBData?.Mon, percentageTotal, "number"),
            originalTotal: originalDataGroupBy?.Mon || 0,
            isTooltip: totalType && totalType === TOTAL_TYPE.MAIN ? false : true,
        },
        {
            _id: "Tue",
            label: "Tue",
            value: dayDBData?.Tue || 0,
            color: "#FFECA6",
            percentage: await itemPercentage(dayDBData?.Tue, percentageTotal, "number"),
            originalTotal: originalDataGroupBy?.Tue || 0,
            isTooltip: totalType && totalType === TOTAL_TYPE.MAIN ? false : true,
        },
        {
            _id: "Wed",
            label: "Wed",
            value: dayDBData?.Wed || 0,
            color: "#FFECA6",
            percentage: await itemPercentage(dayDBData?.Wed, percentageTotal, "number"),
            originalTotal: originalDataGroupBy?.Wed || 0,
            isTooltip: totalType && totalType === TOTAL_TYPE.MAIN ? false : true,
        },
        {
            _id: "Thu",
            label: "Thu",
            value: dayDBData?.Thu || 0,
            color: "#FFECA6",
            percentage: await itemPercentage(dayDBData?.Thu, percentageTotal, "number"),
            originalTotal: originalDataGroupBy?.Thu || 0,
            isTooltip: totalType && totalType === TOTAL_TYPE.MAIN ? false : true,
        },
        {
            _id: "Fri",
            label: "Fri",
            value: dayDBData?.Fri || 0,
            color: "#FFECA6",
            percentage: await itemPercentage(dayDBData?.Fri, percentageTotal, "number"),
            originalTotal: originalDataGroupBy?.Fri || 0,
            isTooltip: totalType && totalType === TOTAL_TYPE.MAIN ? false : true,
        },
        {
            _id: "Sat",
            label: "Sat",
            value: dayDBData?.Sat || 0,
            color: "#FFECA6",
            percentage: await itemPercentage(dayDBData?.Sat, percentageTotal, "number"),
            originalTotal: originalDataGroupBy?.Sat || 0,
            isTooltip: totalType && totalType === TOTAL_TYPE.MAIN ? false : true,
        },
    ];

    return daysDataArr;
}

const dxData = async (data, totalFilterData) => {
    let dxDataArr = [];
    let patientData = [];
    let patientDxIds = [];
    const { originalData = [], totalType = null, totalForPercentage = null, isOtherDashboard = false } = totalFilterData;
    let patientOriginalData = [];
    let dxOriginalDataArr = [];

    if (data && data.length > 0) {
        // eslint-disable-next-line array-callback-return
        data.filter((ele, i) => {
            if (ele.dx && ele.dx.length > 0) {
                dxDataArr = [...ele.dx, ...dxDataArr];
                patientData.push(ele);
                patientDxIds = [...ele.dxIds, ...patientDxIds];
            }
        });

        originalData.filter((ele) => {
            if (ele.dx && ele.dx.length > 0) {
                dxOriginalDataArr = [...ele.dx, ...dxOriginalDataArr];
                patientOriginalData.push(ele);
            }
        });
    }
    let percentageTotal = totalForPercentage ? totalForPercentage : data?.length || 0;

    let dxDataDB = [];
    if (dxDataArr.length > 0) {
        const dxDataGroup = _.groupBy(dxDataArr, "_id");
        const dxOriginalDataGroup = _.groupBy(dxOriginalDataArr, "_id");

        if (dxDataGroup) {
            for (const [key, value] of Object.entries(dxDataGroup)) {
                const valueArr = value[0];
                if (key && valueArr) {
                    let object = Object();
                    object._id = key;
                    object.id = key;
                    object.label = valueArr.label;
                    object.name = valueArr.label;
                    object.total = value.length;
                    let original = dxOriginalDataGroup[key] ? dxOriginalDataGroup[key]?.length : 0;
                    object.originalTotal = original;
                    object.isTooltip = totalType && totalType === TOTAL_TYPE.MAIN ? false : true;
                    object.percentage = await itemPercentage(value.length, percentageTotal, "number");

                    // Get patientIds from `value`
                    object.patientIds = value.map(item =>
                        isOtherDashboard ? item.refPatientId : String(item._id)
                    ).map(id => String(id)); // convert to string if needed

                    // Add otherDashboardIds if needed
                    if (isOtherDashboard) {
                        object.otherDashboardIds = value.map(item => String(item._id));
                    }

                    dxDataDB.push(object);
                }
            }
        }
        dxDataDB = _.orderBy(dxDataDB, "total", "desc");
    }
    return dxDataDB;
}

//All ADT TAB FUNCTIONS HERE
const getADTTotalData = async (
    element,
    patientList,
    adtTotal,
    type = "transferType",
    { totalType = null, isTooltip = false }
) => {
    let filtersArray = [];
    const eleID = element.id;
    const eleLabel = element.label;
    let findData = [];
    if (type === "transferType") {
        findData = await _.filter(patientList, { transferType: eleID });
    } else {
        findData = patientList;
    }
    const total = findData.length || 0;
    let originalTotal = total;
    if (totalType == TOTAL_TYPE.FILTER) {
        const tableData = element.tableData ? element.tableData : [];
        const findTableData = await _.find(tableData, { _id: eleID });
        originalTotal = findTableData?.originalTotal || 0;
    }
    const object = {
        id: eleID,
        _id: eleID,
        label: eleLabel,
        name: eleLabel,
        total,
        originalTotal,
        isTooltip,
        admissionIds: await _.map(findData, "admissionId"),
        transferIds: await _.map(findData, "id"),
        percentage: await itemPercentage(total, adtTotal, "number"),
        type: element.type,
        element,
    };
    filtersArray.push(object);
    return filtersArray;
}

const matchedArray = (array1, array2) => {
    return _.intersectionWith(array1, array2, _.isEqual);
}

const ninetyDaysDataList = async (listData, element, patients, adtTotal, { totalType = null, isTooltip = false }) => {
    const matchedIds = patients.length > 0 ? patients.map((item) => item.id) : [];
    const matchedAdmissionIds = patients.length > 0 ? patients.map((item) => item.admissionId) : [];

    if (listData && listData.length > 0) {
        let latestData = []
        await filterAsyncData(listData, (async (item) => {
            const intersection = await matchedArray(matchedIds, item.ids);
            const intersectionAdmission = await matchedArray(matchedAdmissionIds, item.admissionIds);
            const total = intersection.length;
            let originalTotal = total;
            if (totalType == TOTAL_TYPE.FILTER) {
                const tableData = element.tableData ? element.tableData : [];
                const findTableData = _.find(tableData, { _id: item._id });
                originalTotal = findTableData?.originalTotal || 0;
            }
            let obj = {
                id: item._id,
                name: item.label,
                _id: item._id,
                label: item.label,
                total: total || 0,
                ids: intersection,
                originalTotal,
                isTooltip,
                patientIds: intersection,
                percentage: await itemPercentage(total, adtTotal, "number", PAGE_TYPE.ADMISSION),
                type: element.type,
                element: element,
                admissionIds: intersectionAdmission,
            };
            latestData.push(Object.assign({}, item, obj));
        }));
        return latestData;
    }
}

const getADTAnalysisChartData = async (element, patientList, transferPatientList, adtTotal, totalType) => {
    let chartArray = element.arrayName ? patientList[element.arrayName] : [];
    const latest90Days = await ninetyDaysDataList(chartArray, element, transferPatientList, adtTotal, totalType);
    return latest90Days;
}

const getADTChartDaysData = async (element, patientList, adtTotal, { totalType = null, isTooltip = false }) => {
    let chartADTData = [];
    const groupByChartData = await _.groupBy(patientList, element.id);
    const chartLabels = element.chartsLabel || [];

    for await (const item of chartLabels) {
        //chartLabels.filter((item) => {
        const chartLabelItem = groupByChartData[item.filterId];
        const patientIds = await _.map(chartLabelItem, "admissionId");
        const transferIds = await _.map(chartLabelItem, "id");
        const total = chartLabelItem?.length || 0;
        let originalTotal = total;
        if (totalType == TOTAL_TYPE.FILTER) {
            const tableData = element.tableData ? element.tableData : [];
            const findTableData = _.find(tableData, { _id: item.id });
            originalTotal = findTableData?.originalTotal || 0;
        }
        let object = {
            id: item.id,
            filterId: item.filterId,
            name: item.label,
            _id: item.id,
            label: item.label,
            total,
            patientIds,
            ids: patientIds,
            admissionIds: patientIds,
            transferIds,
            originalTotal,
            isTooltip,
            percentage: await itemPercentage(total, adtTotal, "number", PAGE_TYPE.ADMISSION),
            type: element.type,
            element: element,
            chartsLabel: element.chartsLabel,
        };
        chartADTData.push(object);
    }
    return chartADTData;
}

const getADTChartData = async (element, patientList, adtTotal, { totalType = null, isTooltip = false }) => {
    let chartADTData = [];

    // check why the percentage is different
    const groupByChartData = _.groupBy(patientList, element.id);
    const chartLabels = element.chartsLabel || [];

    for await (const item of chartLabels) {
        const chartLabelItem = groupByChartData[item.filterId];
        const admissionIds = _.map(chartLabelItem, "admissionId");
        const transferIds = _.map(chartLabelItem, "id");
        const total = chartLabelItem?.length || 0;
        let originalTotal = total;
        if (totalType == TOTAL_TYPE.FILTER) {
            const tableData = element.tableData ? element.tableData : [];
            const findTableData = _.find(tableData, { _id: item.id });
            originalTotal = findTableData?.originalTotal || 0;
        }
        let object = {
            id: item.id,
            filterId: item.filterId,
            name: item.label,
            _id: item.id,
            label: item.label,
            total,
            ids: transferIds,
            originalTotal,
            isTooltip,
            patientIds: transferIds,
            admissionIds: admissionIds,
            percentage: await itemPercentage(total, adtTotal, "number", PAGE_TYPE.ADMISSION),
            type: element.type,
            element: element,
        };
        chartADTData.push(object);
    }
    return chartADTData;
}

const getADTGroupData = async (element, patientList, adtTotal, { totalType = null, isTooltip = false }) => {
    let filtersArray = [];
    const groupByData = await _.groupBy(patientList, element.id);
    if (!_.isEmpty(groupByData)) {
        for await (const [key, value] of Object.entries(groupByData)) {
            if (key != "null") {
                const arrayLabelData = value[0];
                const admissionIds = _.map(value, "admissionId") || [];
                const transferIds = _.map(value, "id") || [];
                const total = value.length || 0;
                let originalTotal = total;
                if (totalType == TOTAL_TYPE.FILTER) {
                    const tableData = element.tableData ? element.tableData : [];
                    const findTableData = _.find(tableData, { id: arrayLabelData[element.relation]["id"] });
                    originalTotal = findTableData?.originalTotal || 0;
                }
                const object = {
                    id: arrayLabelData[element.relation]["id"],
                    name: arrayLabelData[element.relation]["label"],
                    _id: arrayLabelData[element.relation]["id"],
                    label: arrayLabelData[element.relation]["label"],
                    total,
                    originalTotal,
                    isTooltip,
                    patientIds: admissionIds,
                    transferIds,
                    admissionIds: admissionIds,
                    percentage: await itemPercentage(total, adtTotal, "number", PAGE_TYPE.ADMISSION),
                    type: element.type,
                    element: element,
                };
                filtersArray.push(object);
            }
        }
        filtersArray = _.orderBy(filtersArray, "total", "desc");
    }
    return filtersArray;
}

const getADTGroupArrayData = async (element, patientList, adtTotal, { totalType = null, isTooltip = false }) => {
    let filtersArray = [];
    let dxDataArr = [];
    if (patientList && patientList.length > 0) {
        // eslint-disable-next-line array-callback-return
        patientList.filter((ele) => {
            if (ele[element.id] && ele[element.id].length > 0) {
                let objectDev = ele[element.id];
                objectDev = objectDev.map((obj) => ({ ...obj, admissionId: ele.admissionId, patientId: ele.id }));
                dxDataArr = [...objectDev, ...dxDataArr];
            }
        });
        if (dxDataArr.length > 0) {
            const dxDataGroup = _.groupBy(dxDataArr, "_id");
            if (dxDataGroup) {
                for (const [key, value] of Object.entries(dxDataGroup)) {
                    const valueArr = value[0];
                    const admissionIds = _.map(value, "admissionId") || [];
                    const transferIds = _.map(value, "patientId") || [];

                    let originalTotal = value.length;
                    if (totalType == TOTAL_TYPE.FILTER) {
                        const tableData = element.tableData ? element.tableData : [];
                        const findTableData = _.find(tableData, { _id: key });
                        originalTotal = findTableData?.originalTotal || 0;
                    }

                    if (key && valueArr) {
                        let object = {
                            id: key,
                            _id: key,
                            name: valueArr.label,
                            label: valueArr.label,
                            total: value.length,
                            type: element.type,
                            originalTotal,
                            isTooltip,
                            percentage: await itemPercentage(value.length, adtTotal, "number", PAGE_TYPE.ADMISSION),
                            element: element,
                            patientIds: transferIds,
                            admissionIds: admissionIds,
                            transferIds,
                        };
                        filtersArray.push(object);
                    }
                }
            }
            filtersArray = _.orderBy(filtersArray, "total", "desc");
        }
    }
    return filtersArray;
}

const adtCardFilterParam = async (adtData) => {
    let transferTypes = [];
    let relations = [];
    let isReturnData = false;
    let isReHospitalization = false;
    let isNinetyDays = false;
    let isSixtyDays = false;
    let isNinetyDeceasedDays = false;
    let isNinetyOverallDays = false;
    adtData.map((ele) => {
        if (_.find(ele.children, { id: "wasReturned" })?.isSelected) {
            isReturnData = true;
        }
        if (ele.id === PAGE_TYPE.HOSPITAL) {
            if (_.find(ele.children, { id: "90Days" })?.isSelected) {
                isNinetyDays = true;
            }
            if (_.find(ele.children, { id: "reHospitalization" })?.isSelected) {
                isReHospitalization = true;
            }
            transferTypes = _.concat(transferTypes, ["plannedHospitalTransfer", "unplannedHospitalTransfer"]);
        }
        if (ele.id === PAGE_TYPE.COMMUNITY_TRANSFER) {
            if (_.find(ele.children, { id: "60Days" })?.isSelected) {
                isSixtyDays = true;
            }
            transferTypes = _.concat(transferTypes, ["AMA", "SNF", "safeDischarge"]);
        }
        if (ele.id === PAGE_TYPE.DECEASED) {
            if (_.find(ele.children, { id: "90DaysDeceased" })?.isSelected) {
                isNinetyDeceasedDays = true;
            }
            transferTypes = _.concat(transferTypes, ["deceased"]);
        }
        if (ele.id === PAGE_TYPE.OVERALL) {
            if (_.find(ele.children, { id: "90DaysOverall" })?.isSelected) {
                isNinetyOverallDays = true;
            }
            transferTypes = [];
        }
        if (ele.children && ele.children.length > 0) {
            const relationsData = _.map(_.filter(ele.children, { isSelected: true }), "relation").filter((item) => item);
            relations = _.concat(relations, relationsData);
        }
        relations = _.uniq(relations);
    });

    return {
        transferTypes,
        relations,
        isReturnData,
        isReHospitalization,
        isNinetyDays,
        isSixtyDays,
        isNinetyDeceasedDays,
        isNinetyOverallDays,
    };
}

const admissionCustomAlertFilterListData = async (res, customFilter) => {
    const {
        cardFilter = {},
        transferType = null,
        cardType = null
    } = customFilter || {};

    let patientList = res?.data;
    let totals = res.totals;
    const { list, adtList } = patientList;
    // let adtTabTableData = adtList;
    let patientFilterData = list;

    const {
        mainPriorityData = null,
        type = null
    } = cardFilter || {};

    if (transferType === ADMISSION_TYPES.ADMISSION) {
        patientFilterData = _.filter(patientFilterData, {
            type: ADMISSION_TYPES.ADMISSION,
        });
    }
    if (transferType === ADMISSION_TYPES.READMISSION) {
        patientFilterData = _.filter(patientFilterData, {
            type: ADMISSION_TYPES.READMISSION,
        });
    }

    if (mainPriorityData && mainPriorityData.length > 0) {
        let filterAdmissionsIds = _.map(patientFilterData, "id");
        let filterADTAdmissionId = [];
        let updatedADTData = [];
        let isADTFilterApply = false;
        let i = 0;
        let j = 0;
        let admissionTotal = 0;

        for await (const ele of mainPriorityData) {
            j++;
            const { type, transferIds, childId, selectedIds, filterType } = ele;
            if (cardType === ele.type) {
                continue;
            }
            if (filterType === ADMISSION_FILTER_TYPE.ADMISSION) {
                if (ele?.question?.isCustom) {
                    patientFilterData = await filterCustomPatientData(patientFilterData, cardFilter, ele);
                }

                if (isADTFilterApply) {
                    patientFilterData = _.filter(patientFilterData, async ({ id }) =>
                        _.every([_.includes(filterADTAdmissionId, id)])
                    );
                }

                if (ele.type === ADMISSION_CARDS_TYPE.DOCTOR_DATA) {
                    patientFilterData = await _.filter(patientFilterData, ({ doctorId }) =>
                        _.every([_.includes(cardFilter.doctorData, doctorId)])
                    );
                }

                if (ele.type === ADMISSION_CARDS_TYPE.DAYS_DATA) {
                    patientFilterData = await _.filter(patientFilterData, ({ day }) =>
                        _.every([_.includes(cardFilter.daysData, day)])
                    );
                }

                if (ele.type === ADMISSION_CARDS_TYPE.DX_DATA) {
                    patientFilterData = await _.filter(patientFilterData, ({ dxIds }) => {
                        const matchedIds = _.intersection(cardFilter.dxData, dxIds);
                        return matchedIds.length > 0 ? true : false;
                    });
                }

                if (ele.type === ADMISSION_CARDS_TYPE.INSURANCE_DATA) {
                    patientFilterData = await _.filter(patientFilterData, ({ insuranceId }) =>
                        _.every([_.includes(cardFilter.insuranceData, insuranceId)])
                    );
                }

                if (ele.type === ADMISSION_CARDS_TYPE.FLOORS_DATA) {
                    patientFilterData = await _.filter(patientFilterData, ({ floorId }) =>
                        _.every([_.includes(cardFilter.floorsData, floorId)])
                    );
                }

                if (ele.type === ADMISSION_CARDS_TYPE.HOSPITAL_DATA) {
                    patientFilterData = await _.filter(patientFilterData, ({ hospitalId }) =>
                        _.every([_.includes(cardFilter.hospitalData, hospitalId)])
                    );
                }
                filterAdmissionsIds = _.map(patientFilterData, "id");
            } else {
                // i++;
                // isADTFilterApply = true;
                // adtFilterData = await _.filter(adtFilterData, ({ admissionId }) =>
                //     _.every([_.includes(filterAdmissionsIds, admissionId)])
                // );
                // let totalForPercentage = i == 1 ? patientFilterData?.length : adtFilterData.length;
                // admissionTotal = totalForPercentage;

                // let totalCountADTData =
                //     !transferType && j === 1 ? censusAverage : lockedTotalModified ? lockedTotalModified : totalForPercentage;

                // let tableDataNew = await getTableDataFilter(ele, adtFilterData, adtTabTableData, totalCountADTData, {
                //     totalType: TOTAL_TYPE.FILTER,
                //     isTooltip: transferType ? true : false,
                //     forComparison,
                // });

                // await updatedADTData.push({ ...ele, tableData: tableDataNew });

                // let transferIdsData = transferIds;
                // if (i == 1 && type === ADT_TABLE_TYPE.ANALYSIS_CHART) {
                //     const selectedIdData = ele.selectedIds;
                //     if (selectedIdData.length > 0) {
                //         let transferIdsDataLatest2 = [];
                //         tableDataNew.map(async (eleTable) => {
                //             if (_.includes(selectedIdData, eleTable._id)) {
                //                 transferIdsDataLatest2 = [...transferIdsDataLatest2, ...eleTable.ids];
                //             }
                //         });
                //         transferIdsData = transferIdsDataLatest2;
                //     }
                // }
                // adtFilterData = await _.filter(adtFilterData, (item) => {
                //     let filterArr = [];
                //     const idD = item[childId];
                //     const { id } = item;
                //     if (type === ADT_TABLE_TYPE.GROUP) {
                //         filterArr.push(_.includes(selectedIds, idD));
                //     } else if (type === ADT_TABLE_TYPE.GROUP_ARRAY) {
                //         filterArr.push(_.includes(transferIdsData, id));
                //     } else if (type === ADT_TABLE_TYPE.DAY_CHART) {
                //         filterArr.push(_.includes(transferIdsData, idD));
                //     } else if (type === ADT_TABLE_TYPE.ANALYSIS_CHART) {
                //         filterArr.push(_.includes(transferIdsData, id));
                //     } else if (type === ADT_TABLE_TYPE.CHART || type === ADT_TABLE_TYPE.ALL || ADT_TABLE_TYPE.TOTAL) {
                //         filterArr.push(_.includes(transferIdsData, id));
                //     }
                //     return _.every(filterArr);
                // });
                // if (ele.parentId === PAGE_TYPE.HOSPITAL) {
                //     adtFilterData = await _.filter(adtFilterData, ({ transferType }) =>
                //         _.every([_.includes(["plannedHospitalTransfer", "unplannedHospitalTransfer"], transferType)])
                //     );
                // }

                // if (ele.parentId === PAGE_TYPE.COMMUNITY_TRANSFER) {
                //     adtFilterData = await _.filter(adtFilterData, ({ transferType }) =>
                //         _.every([_.includes(["AMA", "SNF", "safeDischarge"], transferType)])
                //     );
                // }

                // if (ele.parentId === PAGE_TYPE.DECEASED) {
                //     adtFilterData = await _.filter(adtFilterData, ({ transferType }) =>
                //         _.every([_.includes(["deceased"], transferType)])
                //     );
                // }
                // filterADTAdmissionId = _.map(adtFilterData, "admissionId");
                // patientFilterData = _.filter(patientFilterData, ({ id }) => {
                //     return _.every([_.includes(filterADTAdmissionId, id)]);
                // });
                // if (lockeByADT && ele.parentId === lockeByADT.parentId && ele.childId === lockeByADT.childId) {
                //     lockedTotalModified = patientFilterData.length;
                // }
            }
        }
    }

    return patientFilterData;
}

const updateFilterListDataBoth = async (
    res,
    cardFilter,
    transferType,
    mainPriorityData,
    defaultADTData,
    forComparison,
    lockedTotalBy,
    lockeByADT,
    dynamicCards,
    isOnlyHospitalTabAccess = false
) => {
    let patientList = res;
    const customTabs = res?.customTabs ?? [];
    const diffDashboardPatients = res?.diffDashboardPatients ?? [];
    const customCombineTabData = res?.customCombineTabData ?? [];
    let totals = res.totals;
    const { list, adtList } = patientList;
    let adtTabTableData = adtList;
    let patientFilterData = list;
    let adtFilterData = adtList?.list || [];
    const { censusAverage } = totals
    let lockedTotalModified = null;
    let mainNumPercentage = isOnlyHospitalTabAccess ? totals?.bedCapacity : totals?.censusAverage;

    let totalFilterData = {
        originalData: patientFilterData,
        totalType: transferType ? TOTAL_TYPE.FILTER : TOTAL_TYPE.MAIN,
        totalForPercentage: censusAverage,
    };

    if (transferType === ADMISSION_TYPES.ADMISSION) {
        patientFilterData = _.filter(patientFilterData, {
            type: ADMISSION_TYPES.ADMISSION,
        });
    }
    if (transferType === ADMISSION_TYPES.READMISSION) {
        patientFilterData = _.filter(patientFilterData, {
            type: ADMISSION_TYPES.READMISSION,
        });
    }
    const admissionCards = dynamicCards?.length > 0
        ? dynamicCards.filter((ele) => ele.forType === ADT_TYPES.ADMISSIONS)
        : [];

    if (transferType) {
        mainNumPercentage = null;
        totalFilterData.totalForPercentage = patientFilterData?.length;
    }
    if (mainPriorityData.length > 0) {
        totalFilterData.totalForPercentage = null;
    }

    // if (!forComparison) {
    //     store.dispatch(setFacilityPercentage([]));
    //     if (!lockedTotalModified) {
    //         store.dispatch(setIsCensusTotalLocked(false));
    //         store.dispatch(setLockedTotalBy(null));
    //     }
    // }

    let objectCustom = Object();
    let customTabsObj = Object();
    let dynamicCardsObj = {};
    let newSavedFilters = [];
    let newADTData = [];
    if (lockedTotalBy && !forComparison) {
        if (
            lockedTotalBy === ADMISSION_TYPES.ALL ||
            lockedTotalBy === ADMISSION_TYPES.ADMISSION ||
            lockedTotalBy === ADMISSION_TYPES.READMISSION ||
            lockedTotalBy === "census"
        ) {
            //if (lockedTotalModified && transferType !== lockedTotalBy && lockedTotalBy !== "census") {
            lockedTotalModified = patientFilterData?.length;
            if (lockedTotalModified && !transferType && lockedTotalBy !== "census") {
                // store.dispatch(setLockedTotalBy(null));
                // store.dispatch(setLockTotal(null));
                lockedTotalModified = null;
            }
        } else {
            const lockedFilterRemoved = _.find(mainPriorityData, (ele) => {
                let typeData =
                    ele.filterType === ADMISSION_FILTER_TYPE.ADT ? ele?.type + "_" + ADMISSION_FILTER_TYPE.ADT : ele?.type;
                return typeData === lockedTotalBy ? ele : null;
            });
            lockedTotalModified = patientFilterData?.length;
            //const lockedFilterRemoved = _.find(mainPriorityData, { type: lockedTotalBy });
            if (!lockedFilterRemoved) {
                // store.dispatch(setLockedTotalBy(null));
                // store.dispatch(setLockTotal(null));
                lockedTotalModified = null;
            }
        }
    }
    if (lockedTotalModified && !forComparison) {
        //mainNumPercentage = null;
        totalFilterData.totalForPercentage = lockedTotalModified;
    }
    if (mainPriorityData.length > 0) {
        let filterAdmissionsIds = _.map(patientFilterData, "id");
        let filterADTAdmissionId = [];
        let updatedADTData = [];
        let isADTFilterApply = false;
        let i = 0;
        let j = 0;
        let admissionTotal = 0;
        for await (const ele of mainPriorityData) {
            j++;
            let totalCountData = mainNumPercentage && j === 1 ? mainNumPercentage : totalFilterData.totalForPercentage;

            const { type, transferIds, childId, selectedIds, filterType } = ele;

            if (filterType === ADMISSION_FILTER_TYPE.ADMISSION) {
                if (isADTFilterApply) {
                    patientFilterData = _.filter(patientFilterData, async ({ id }) =>
                        _.every([_.includes(filterADTAdmissionId, id)])
                    );
                }

                if (ele?.question?.isCustom) {
                    if (customTabs.length > 0) {

                        const customTabsRes = customTabs.filter((x) => x.accessor === ele.type);

                        if (customTabsRes.length > 0) {
                            customTabsObj = await getCustomTabsCards(patientFilterData, customTabsRes, totalFilterData, { dynamicCards, diffDashboardPatients, pageType: PAGE_TYPE.ADMISSION, forComparison, customCombineTabData });
                        }
                    }
                    objectCustom = { ...objectCustom, ...dynamicCardsObj, ...customTabsObj }

                    const dynamicItemData = await _.find(admissionCards, { accessor: ele.type });
                    if (dynamicItemData && !_.isEmpty(dynamicItemData)) {
                        let objectCustomResDynamic = {};
                        await processDynamicCard(dynamicItemData, patientFilterData, objectCustomResDynamic, {
                            ...totalFilterData,
                            totalForPercentage: mainNumPercentage && i === 1 ? mainNumPercentage : totalFilterData.totalForPercentage,
                        });
                        if (!_.isEmpty(objectCustomResDynamic)) {
                            objectCustom[dynamicItemData?.accessor] = objectCustomResDynamic?.[ele.type];
                        }
                    }
                    await assignMatchedPatientIds(ele, objectCustom);
                    patientFilterData = await filterCustomPatientData(patientFilterData, cardFilter, ele);
                }

                if (ele.type === ADMISSION_CARDS_TYPE.DOCTOR_DATA) {
                    objectCustom.doctorData = await doctorData(patientFilterData, {
                        ...totalFilterData,
                        totalForPercentage: totalCountData,
                    });

                    patientFilterData = await _.filter(patientFilterData, ({ doctorId }) =>
                        _.every([_.includes(cardFilter.doctorData, doctorId)])
                    );
                }

                if (ele.type === ADMISSION_CARDS_TYPE.DAYS_DATA) {
                    objectCustom.daysData = await daysData(patientFilterData, {
                        ...totalFilterData,
                        totalForPercentage: totalCountData,
                    });

                    patientFilterData = await _.filter(patientFilterData, ({ day }) =>
                        _.every([_.includes(cardFilter.daysData, day)])
                    );
                }

                if (ele.type === ADMISSION_CARDS_TYPE.DX_DATA) {
                    objectCustom.dxData = await dxData(patientFilterData, {
                        ...totalFilterData,
                        totalForPercentage: totalCountData,
                    });

                    patientFilterData = await _.filter(patientFilterData, ({ dxIds }) => {
                        const matchedIds = _.intersection(cardFilter.dxData, dxIds);
                        return matchedIds.length > 0 ? true : false;
                    });
                }

                if (ele.type === ADMISSION_CARDS_TYPE.INSURANCE_DATA) {
                    objectCustom.insuranceData = await insuranceData(patientFilterData, {
                        ...totalFilterData,
                        totalForPercentage: totalCountData,
                    });

                    patientFilterData = await _.filter(patientFilterData, ({ insuranceId }) =>
                        _.every([_.includes(cardFilter.insuranceData, insuranceId)])
                    );
                }

                if (ele.type === ADMISSION_CARDS_TYPE.FLOORS_DATA) {
                    objectCustom.floorsData = await floorsData(patientFilterData, {
                        ...totalFilterData,
                        totalForPercentage: totalCountData,
                    });

                    patientFilterData = await _.filter(patientFilterData, ({ floorId }) =>
                        _.every([_.includes(cardFilter.floorsData, floorId)])
                    );
                }

                if (ele.type === ADMISSION_CARDS_TYPE.HOSPITAL_DATA) {
                    objectCustom.hospitalData = await hospitalListData(patientFilterData, {
                        ...totalFilterData,
                        totalForPercentage: totalCountData,
                    });

                    patientFilterData = await _.filter(patientFilterData, ({ hospitalId }) =>
                        _.every([_.includes(cardFilter.hospitalData, hospitalId)])
                    );
                }
                if (ele.type === lockedTotalBy) {
                    lockedTotalModified = patientFilterData?.length
                }
                admissionTotal = patientFilterData.length;
                // store.dispatch(
                //     forComparison
                //         ? setFilterTotalComparison(lockedTotalModified ? lockedTotalModified : patientFilterData.length)
                //         : setFilterTotal(lockedTotalModified ? lockedTotalModified : patientFilterData.length)
                // );
                filterAdmissionsIds = _.map(patientFilterData, "id");
                newSavedFilters.push(ele);
                // if (j === mainPriorityData.length && !forComparison) {
                //     updateFacilityPercentageTotal(patientFilterData);
                // }
            } else {
                i++;
                isADTFilterApply = true;
                adtFilterData = await _.filter(adtFilterData, ({ admissionId }) =>
                    _.every([_.includes(filterAdmissionsIds, admissionId)])
                );
                let totalForPercentage = i == 1 ? patientFilterData?.length : adtFilterData.length;
                admissionTotal = totalForPercentage;

                let totalCountADTData =
                    !transferType && j === 1 ? censusAverage : lockedTotalModified ? lockedTotalModified : totalForPercentage;

                let tableDataNew = await getTableDataFilter(ele, adtFilterData, adtTabTableData, totalCountADTData, {
                    totalType: TOTAL_TYPE.FILTER,
                    isTooltip: transferType ? true : false,
                    forComparison,
                });

                await updatedADTData.push({ ...ele, tableData: tableDataNew });

                let transferIdsData = transferIds;
                if (i == 1 && type === ADT_TABLE_TYPE.ANALYSIS_CHART || type === ADT_TABLE_TYPE.CUSTOM) {
                    const selectedIdData = ele.selectedIds;
                    if (selectedIdData.length > 0) {
                        let transferIdsDataLatest2 = [];
                        tableDataNew.map(async (eleTable) => {
                            if (_.includes(selectedIdData, eleTable._id)) {
                                transferIdsDataLatest2 = [...transferIdsDataLatest2, ...eleTable.ids];
                            }
                        });
                        transferIdsData = transferIdsDataLatest2;
                    }
                }
                adtFilterData = await _.filter(adtFilterData, (item) => {
                    let filterArr = [];
                    const idD = item[childId];
                    const { id } = item;
                    if (type === ADT_TABLE_TYPE.GROUP) {
                        filterArr.push(_.includes(selectedIds, idD));
                    } else if (type === ADT_TABLE_TYPE.GROUP_ARRAY) {
                        filterArr.push(_.includes(transferIdsData, id));
                    } else if (type === ADT_TABLE_TYPE.DAY_CHART) {
                        filterArr.push(_.includes(transferIdsData, idD));
                    } else if (type === ADT_TABLE_TYPE.ANALYSIS_CHART) {
                        filterArr.push(_.includes(transferIdsData, id));
                    } else if (type === ADT_TABLE_TYPE.CHART || type === ADT_TABLE_TYPE.ALL || ADT_TABLE_TYPE.TOTAL) {
                        filterArr.push(_.includes(transferIdsData, id));
                    }
                    return _.every(filterArr);
                });
                if (ele.parentId === PAGE_TYPE.HOSPITAL) {
                    adtFilterData = await _.filter(adtFilterData, ({ transferType }) =>
                        _.every([_.includes(["plannedHospitalTransfer", "unplannedHospitalTransfer"], transferType)])
                    );
                }

                if (ele.parentId === PAGE_TYPE.COMMUNITY_TRANSFER) {
                    adtFilterData = await _.filter(adtFilterData, ({ transferType }) =>
                        _.every([_.includes(["AMA", "SNF", "safeDischarge"], transferType)])
                    );
                }

                if (ele.parentId === PAGE_TYPE.DECEASED) {
                    adtFilterData = await _.filter(adtFilterData, ({ transferType }) =>
                        _.every([_.includes(["deceased"], transferType)])
                    );
                }
                filterADTAdmissionId = _.map(adtFilterData, "admissionId");
                patientFilterData = await _.filter(patientFilterData, ({ id }) => {
                    return _.every([_.includes(filterADTAdmissionId, id)]);
                });
                if (lockeByADT && ele.parentId === lockeByADT.parentId && ele.childId === lockeByADT.childId) {
                    lockedTotalModified = patientFilterData.length;
                }
                // if (j === mainPriorityData.length && !forComparison) {
                //     updateFacilityPercentageTotal(adtFilterData);
                // }
                // if (i == 1) {
                //     store.dispatch(
                //         forComparison
                //             ? setFilterTotalComparison(lockedTotalModified ? lockedTotalModified : adtFilterData.length)
                //             : setFilterTotal(lockedTotalModified ? lockedTotalModified : adtFilterData.length)
                //     );
                // } else {
                //     store.dispatch(
                //         forComparison
                //             ? setFilterTotalComparison(
                //                 lockedTotalModified
                //                     ? lockedTotalModified
                //                     : mainNumPercentage && i === 1
                //                         ? mainNumPercentage
                //                         : patientFilterData.length
                //             )
                //             : setFilterTotal(
                //                 lockedTotalModified
                //                     ? lockedTotalModified
                //                     : mainNumPercentage && i === 1
                //                         ? mainNumPercentage
                //                         : patientFilterData.length
                //             )
                //     );
                // }
            }
        }
        const totalPercentageCount = lockedTotalModified ? lockedTotalModified : patientFilterData.length; //transferType ? patientFilterData?.length : censusAverage

        const selectedDoctorFilter = _.find(newSavedFilters, {
            type: ADMISSION_CARDS_TYPE.DOCTOR_DATA,
            filterType: ADMISSION_FILTER_TYPE.ADMISSION,
        });
        if (_.isEmpty(selectedDoctorFilter)) {
            objectCustom.doctorData = await doctorData(patientFilterData, {
                ...totalFilterData,
                totalType: TOTAL_TYPE.FILTER,
                totalForPercentage: totalPercentageCount,
            });
        }

        const selectedDaysFilter = _.find(newSavedFilters, {
            type: ADMISSION_CARDS_TYPE.DAYS_DATA,
            filterType: ADMISSION_FILTER_TYPE.ADMISSION,
        });
        if (_.isEmpty(selectedDaysFilter)) {
            objectCustom.daysData = await daysData(patientFilterData, {
                ...totalFilterData,
                totalType: TOTAL_TYPE.FILTER,
                totalForPercentage: totalPercentageCount,
            });
        }

        const selectedDxFilter = _.find(newSavedFilters, {
            type: ADMISSION_CARDS_TYPE.DX_DATA,
            filterType: ADMISSION_FILTER_TYPE.ADMISSION,
        });
        if (_.isEmpty(selectedDxFilter)) {
            objectCustom.dxData = await dxData(patientFilterData, {
                ...totalFilterData,
                totalType: TOTAL_TYPE.FILTER,
                totalForPercentage: totalPercentageCount,
            });
        }

        const selectedInsuranceFilter = _.find(newSavedFilters, {
            type: ADMISSION_CARDS_TYPE.INSURANCE_DATA,
            filterType: ADMISSION_FILTER_TYPE.ADMISSION,
        });
        if (_.isEmpty(selectedInsuranceFilter)) {
            objectCustom.insuranceData = await insuranceData(patientFilterData, {
                ...totalFilterData,
                totalType: TOTAL_TYPE.FILTER,
                totalForPercentage: totalPercentageCount,
            });
        }

        const selectedFloorFilter = _.find(newSavedFilters, {
            type: ADMISSION_CARDS_TYPE.FLOORS_DATA,
            filterType: ADMISSION_FILTER_TYPE.ADMISSION,
        });
        if (_.isEmpty(selectedFloorFilter)) {
            objectCustom.floorsData = await floorsData(patientFilterData, {
                ...totalFilterData,
                totalType: TOTAL_TYPE.FILTER,
                totalForPercentage: totalPercentageCount,
            });
        }

        const selectedHospitalFilter = _.find(newSavedFilters, {
            type: ADMISSION_CARDS_TYPE.HOSPITAL_DATA,
            filterType: ADMISSION_FILTER_TYPE.ADMISSION,
        });

        if (admissionCards.length > 0) {
            for (const item of admissionCards) {
                let objectCustomRes = {};
                const selectedCustomFilter = await _.find(newSavedFilters, {
                    type: item?.accessor,
                    filterType: ADMISSION_FILTER_TYPE.ADMISSION,
                });

                if (_.isEmpty(selectedCustomFilter)) {
                    await processDynamicCard(item, patientFilterData, objectCustomRes, {
                        ...totalFilterData,
                        totalType: TOTAL_TYPE.FILTER,
                        totalForPercentage: totalPercentageCount,
                    });
                    if (!_.isEmpty(objectCustomRes)) {
                        objectCustom[item?.accessor] = objectCustomRes?.[item?.accessor];
                    }
                }
            }
        }

        if (customTabs.length > 0) {
            objectCustom = await buildCustomTabsObject({
                customTabs,
                patientFilterData,
                totalFilterData: { ...totalFilterData, totalType: TOTAL_TYPE.FILTER, totalForPercentage: totalPercentageCount },
                patientList,
                dynamicCards,
                newSavedFilters,
                objectCustom,
                diffDashboardPatients,
                pageType: PAGE_TYPE.ADMISSION
            });
        }

        if (_.isEmpty(selectedHospitalFilter)) {
            objectCustom.hospitalData = await hospitalListData(patientFilterData, {
                ...totalFilterData,
                totalType: TOTAL_TYPE.FILTER,
                totalForPercentage: totalPercentageCount,
            });
        }
        objectCustom.admissionTotal = admissionTotal;

        adtFilterData = await _.filter(adtFilterData, ({ admissionId }) =>
            _.every([_.includes(filterAdmissionsIds, admissionId)])
        );

        for await (const ele of defaultADTData) {
            const selectedFilter = _.find(updatedADTData, { childId: ele.childId, cardId: ele.cardId });
            if (selectedFilter) {
                newADTData.push(selectedFilter);
            } else {
                let tableDataNew = await getTableDataFilter(
                    ele,
                    adtFilterData,
                    adtTabTableData,
                    lockedTotalModified ? lockedTotalModified : patientFilterData.length, //patientFilterData.length,
                    { totalType: TOTAL_TYPE.FILTER, isTooltip: true, totalForPercentage: totalPercentageCount }
                );
                newADTData.push({ ...ele, tableData: tableDataNew });
            }
        }

        if (!lockedTotalModified && !forComparison) {
            let lastPrioritySelected = _.last(mainPriorityData);
            let patientLockedData = [];
            if (lastPrioritySelected && lastPrioritySelected.filterType === ADMISSION_FILTER_TYPE.ADMISSION) {
                patientLockedData = patientFilterData;
            } else if (lastPrioritySelected && lastPrioritySelected.filterType === ADMISSION_FILTER_TYPE.ADT) {
                patientLockedData = adtFilterData;
            }
            //store.dispatch(setLockedByFacility(patientFilterData));
        }
    } else {
        const totalCount = lockedTotalModified
            ? lockedTotalModified
            : mainNumPercentage
                ? mainNumPercentage
                : transferType
                    ? patientFilterData?.length
                    : censusAverage;
        // store.dispatch(forComparison ? setFilterTotalComparison(totalCount) : setFilterTotal(totalCount));
        // if (!transferType && lockedTotalModified && !forComparison) {
        //     store.dispatch(setIsCensusTotalLocked(true));
        // }
        // if (lockedTotalModified && !forComparison && !lockedTotalBy) {
        //     if (transferType) {
        //         store.dispatch(setLockedTotalBy(transferType));
        //     } else {
        //         store.dispatch(setLockedTotalBy("census"));
        //     }
        // }
        // if (!lockedTotalModified && !forComparison) {
        //     store.dispatch(setLockedByFacility(patientFilterData));
        // }
        objectCustom.doctorData = await doctorData(patientFilterData, totalFilterData);
        objectCustom.daysData = await daysData(patientFilterData, totalFilterData);
        objectCustom.dxData = await dxData(patientFilterData, totalFilterData);
        objectCustom.insuranceData = await insuranceData(patientFilterData, totalFilterData);
        objectCustom.floorsData = await floorsData(patientFilterData, totalFilterData);
        objectCustom.hospitalData = await hospitalListData(patientFilterData, totalFilterData);
        objectCustom.admissionTotal = patientFilterData?.length;

        let filterADTAdmissionId = _.map(patientFilterData, "id");

        adtFilterData = await _.filter(adtFilterData, ({ admissionId }) =>
            _.every([_.includes(filterADTAdmissionId, admissionId)])
        );

        if (admissionCards.length > 0) {
            dynamicCardsObj = await dynamicCardFilter(patientFilterData, admissionCards, totalFilterData);
        }

        if (customTabs.length > 0) {
            customTabsObj = await getCustomTabsCards(patientFilterData, customTabs, totalFilterData, { dynamicCards, pageType: PAGE_TYPE.ADMISSION, diffDashboardPatients, customCombineTabData });
        }

        objectCustom = { ...objectCustom, ...dynamicCardsObj, ...customTabsObj }

        for await (const ele of defaultADTData) {
            let tableDataNew = await getTableDataFilter(ele, adtFilterData, adtTabTableData, totalCount, {
                totalType: TOTAL_TYPE.FILTER,
                isTooltip: transferType ? true : false,
            });
            newADTData.push({ ...ele, tableData: tableDataNew });
        }
    }

    return { patientList: objectCustom, adtList: newADTData };
}

const getTableDataFilter = async (
    element,
    patientList,
    defaultPatientData,
    totalForPercentage,
    typeData,
    forComparison
) => {
    let filtersArray = [];
    const type = element.type;
    if (element.parentId === PAGE_TYPE.HOSPITAL) {
        patientList = _.filter(patientList, ({ transferType }) =>
            _.every([_.includes(["plannedHospitalTransfer", "unplannedHospitalTransfer"], transferType)])
        );
    }
    if (element.parentId === PAGE_TYPE.COMMUNITY_TRANSFER) {
        patientList = await _.filter(patientList, ({ transferType }) =>
            _.every([_.includes(["AMA", "SNF", "safeDischarge"], transferType)])
        );
    }
    if (element.parentId === PAGE_TYPE.DECEASED) {
        patientList = await _.filter(patientList, ({ transferType }) => _.every([_.includes(["deceased"], transferType)]));
    }
    if (type === ADT_TABLE_TYPE.TOTAL) {
        filtersArray = await getADTTotalData(element, patientList, totalForPercentage, "transferType", typeData);
    }
    if (type === ADT_TABLE_TYPE.ALL) {
        filtersArray = await getADTTotalData(element, patientList, totalForPercentage, ADT_TABLE_TYPE.ALL, typeData);
    }
    if (type === ADT_TABLE_TYPE.ANALYSIS_CHART) {
        filtersArray = await getADTAnalysisChartData(
            element,
            defaultPatientData,
            patientList,
            totalForPercentage,
            typeData
        );
    }

    if (type === ADT_TABLE_TYPE.DAY_CHART) {
        filtersArray = await getADTChartDaysData(element, patientList, totalForPercentage, typeData);
    }

    if (type === ADT_TABLE_TYPE.CHART) {
        filtersArray = await getADTChartData(element, patientList, totalForPercentage, typeData);
    }

    if (type === ADT_TABLE_TYPE.GROUP) {
        filtersArray = await getADTGroupData(element, patientList, totalForPercentage, typeData);
    }

    if (type === ADT_TABLE_TYPE.GROUP_ARRAY) {
        filtersArray = await getADTGroupArrayData(element, patientList, totalForPercentage, typeData);
    }

    if (type === ADT_TABLE_TYPE.CUSTOM) {
        let objectCustomRes = {};
        await processDynamicCard(element, patientList, objectCustomRes, { totalForPercentage: totalForPercentage, totalType: TOTAL_TYPE.FILTER, isADT: true });
        filtersArray = objectCustomRes?.[element?.accessor];
    }

    return filtersArray;
}

const getADTDataDefault = async (res, adtData, forComparison = false) => {
    let transferData = [];
    for await (const item of adtData) {
        const childData = await _.filter(item.children, { isSelected: true });
        for await (const ele of childData) {
            // eslint-disable-next-line no-new-object
            let tableData = await getDefaultTableData(ele, res, forComparison);
            if (
                tableData &&
                tableData.length > 0 &&
                (ele.type === ADT_TABLE_TYPE.GROUP || ele.type === ADT_TABLE_TYPE.GROUP_ARRAY)
            ) {
                tableData = _.orderBy(tableData, "total", "desc");
            }
            let newItem = new Object();
            newItem.title = item.label;
            newItem.label = item.label;
            newItem.cardId = item.id;
            newItem.parentId = item.id;
            newItem.relation = ele.relation;
            newItem.helperText = ele?.helperText || "";
            newItem.childId = ele.id;
            newItem.id = ele.id;
            newItem.childId = ele.id;
            newItem.subTitle = ele.label;
            newItem.type = ele.type;
            newItem.tableData = tableData;
            newItem.chartsLabel = ele?.chartsLabel || [];
            newItem.arrayName = ele?.arrayName || [];
            newItem.validationBase = ele?.validationBase;
            newItem.customQuestionInputType = ele?.customQuestionInputType;
            newItem.customQuestionOptions = ele?.customQuestionOptions;
            newItem.validationOptions = ele?.validationOptions;
            newItem.accessor = ele?.accessor;
            transferData.push(newItem);
        }
    }
    return transferData;
}

const getDefaultTableData = async (element, adtTabTableData, forComparison) => {
    let filtersArray = [];
    let patientList = [];
    let arrayData = adtTabTableData.list || [];
    const type = element.type;
    //const { adtTotal } = forComparison ? store.getState().admissionComparison : store.getState().admission;
    let totalForPercentage;
    if (element.parentId === PAGE_TYPE.HOSPITAL) {
        patientList = arrayData
            .map((item) =>
                _.includes(["plannedHospitalTransfer", "unplannedHospitalTransfer"], item.transferType) ? item : null
            )
            .filter((item) => item);
    }
    if (element.parentId === PAGE_TYPE.COMMUNITY_TRANSFER) {
        patientList = arrayData
            .map((item) => (_.includes(["AMA", "SNF", "safeDischarge"], item.transferType) ? item : null))
            .filter((item) => item);
    }
    if (element.parentId === PAGE_TYPE.DECEASED) {
        patientList = arrayData
            .map((item) => (_.includes(["deceased"], item.transferType) ? item : null))
            .filter((item) => item);
    }
    if (element.parentId === PAGE_TYPE.OVERALL) {
        patientList = arrayData;
    }

    if (type === ADT_TABLE_TYPE.TOTAL) {
        filtersArray = await getADTTotalData(element, patientList, totalForPercentage, "transferType", TOTAL_TYPE.MAIN);
    }
    if (type === ADT_TABLE_TYPE.ALL) {
        filtersArray = await getADTTotalData(element, patientList, totalForPercentage, ADT_TABLE_TYPE.ALL, TOTAL_TYPE.MAIN);
    }
    if (type === ADT_TABLE_TYPE.ANALYSIS_CHART) {
        let patientListAnalysis = patientList;

        filtersArray = await getADTAnalysisChartData(
            element,
            adtTabTableData,
            patientListAnalysis,
            totalForPercentage,
            TOTAL_TYPE.MAIN
        );
    }

    if (type === ADT_TABLE_TYPE.DAY_CHART) {
        filtersArray = await getADTChartDaysData(element, patientList, totalForPercentage, TOTAL_TYPE.MAIN);
    }

    if (type === ADT_TABLE_TYPE.CHART) {
        filtersArray = await getADTChartData(element, patientList, totalForPercentage, TOTAL_TYPE.MAIN);
    }

    if (type === ADT_TABLE_TYPE.GROUP) {
        filtersArray = await getADTGroupData(element, patientList, totalForPercentage, TOTAL_TYPE.MAIN);
    }

    if (type === ADT_TABLE_TYPE.GROUP_ARRAY) {
        filtersArray = await getADTGroupArrayData(element, patientList, totalForPercentage, TOTAL_TYPE.MAIN);
    }
    return filtersArray;
}

module.exports = {
    updateFilterListDataBoth,
    admissionCustomAlertFilterListData,
    getADTDataDefault,
    adtCardFilterParam
}