import { useCallback, useEffect, useState } from "react";
import { useSelector } from "react-redux";
import dashboardStyles from "../components/dashboard/Dashboard.module.scss";
import styles from "../assets/styles/Quick-glace.module.scss";
import Page from "../components/Page";
import { Box, CircularProgress, Grid, Stack, Typography } from "@mui/material";
import _ from "lodash";
import QuickGlaceHeader from "../components/dashboard/quick-glace/QuickGlaceHeader";
import FilterMenuButton from "../components/dashboard/quick-glace/FilterMenuButton";
import FacilityWiseTotal from "../components/dashboard/quick-glace/facilityWiseTotal/FacilityWiseTotal";
import {
    getQuickGlaces,
    saveQuickGlace,
    getQuickGlacesFilters,
} from "../services/quick-glace.service";
import update from "immutability-helper";

const defaultVal = [0, 1, 2, 3, 4];

const QuickGlacePage = () => {
    const [filterValues, setFilterValues] = useState([]);
    const [loading, setLoading] = useState(false);
    const [facilityFilterData, setFacilityFilterData] = useState([]);
    const [defaultAddButtons, setDefaultAddButtons] = useState(defaultVal);
    const { filter } = useSelector((state) => state.quickGlace);

    const getFilterDataFromDB = async (filterValues) => {
        setDefaultAddButtons([]);
        setLoading(true);
        const res = await getQuickGlacesFilters(filterValues);
        setFacilityFilterData(res);
        setLoading(false);
        setDefaultAddButtons(defaultVal);
    };

    const groupByIndex = (data) => {
        return _.orderBy(data, "index", "asc");
    };

    useEffect(() => {
        getFilterDataFromDB({ filterValues: groupByIndex(filterValues), filter });
    }, [filterValues, filter]);

    const getQuickGlaceData = async () => {
        const res = await getQuickGlaces();
        if (res && res.length > 0) {
            const selectedData = res[0].data;
            setFilterValues(selectedData);
        }
    };

    useEffect(() => {
        getQuickGlaceData();
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    const handleOnchangeChild = useCallback(
        (value, index) => {
            let latestFacilityList;
            const selected = _.find(filterValues, { index });
            if (filterValues.indexOf(selected) === -1) {
                latestFacilityList = update(filterValues, {
                    $push: [{ ...value, index }],
                });
                setFilterValues(_.orderBy(latestFacilityList, "index", "asc"));
            } else {
                latestFacilityList = update(filterValues, {
                    [filterValues.findIndex((item) => item.index === index)]: {
                        $set: { ...value, index },
                    },
                });
                setFilterValues(_.orderBy(latestFacilityList, "index", "asc"));
            }
            saveQuickGlace({ data: latestFacilityList });
        },
        [filterValues]
    );

    const handleRemoveFilter = useCallback(
        (newItem) => {
            if (newItem && newItem.index != null) {
                const selected = _.find(filterValues, { index: newItem.index });            
                if (selected) {
                    let latestFilterList = [];
                    let isDeleted = false;
                    // eslint-disable-next-line array-callback-return
                    filterValues.map((ele, index) => {
                        if (ele.index === newItem.index) {
                            isDeleted = true;
                        } else {
                            let updatedValue = !isDeleted
                                ? { ...ele }
                                : { ...ele, index: index - 1 };
                            latestFilterList.push(updatedValue);
                        }
                    });
                    latestFilterList = _.orderBy(latestFilterList, "index", "asc");
                    setFilterValues(latestFilterList);
                    saveQuickGlace({ data: [...latestFilterList] });
                }
            }
        },
        [filterValues]
    );

    return (
        <Page title="Dashboard: Quick Glance Page">
            <div className={dashboardStyles.content}>
                <div className={`df aic ${styles.header}`}>
                    <div className={`df aic ${styles.aoWrapper}`}>
                        <QuickGlaceHeader />
                    </div>
                </div>
                <Box className={`${styles.innerContainer}`}>
                    <Grid container direction={"row"} spacing={1}>
                        <Grid item xs={12}>
                            <Typography>Filters:</Typography>
                        </Grid>
                        <Grid item xs={12}>
                            <Grid container spacing={2} direction="row">
                                {defaultAddButtons.length === 0 && (
                                    [0, 1, 2, 3, 4].map((item, index) => (
                                        <Grid item sx={{ width: "20%" }}>
                                            <FilterMenuButton
                                                key={`${item}-filter-button`}
                                                item={null}
                                                handleRemoveFilter={handleRemoveFilter}
                                                filterValues={filterValues}
                                                handleOnchangeChild={(val) =>
                                                    handleOnchangeChild(val, index)
                                                }
                                            />
                                        </Grid>
                                    ))
                                )}
                                {defaultAddButtons.map((item, index) => (
                                    <Grid item sx={{ width: { sm: "100%", md: "33.3%", lg: "20%" } }}>
                                        <FilterMenuButton
                                            key={`${item}-default-filter-button`}
                                            item={filterValues[index] ? filterValues[index] : null}
                                            handleRemoveFilter={handleRemoveFilter}
                                            filterValues={filterValues}
                                            handleOnchangeChild={(val) =>
                                                handleOnchangeChild(val, index)
                                            }
                                        />
                                    </Grid>
                                ))}
                            </Grid>
                        </Grid>
                    </Grid>
                    <Grid
                        container
                        direction={"row"}
                        className={`${styles.facilityContainer}`}
                    >
                        <Grid item xs={12} className={`${styles.facilityContainerHeader}`}>
                            <Typography>Facilities:</Typography>
                        </Grid>
                        <Grid item xs={12}>
                            {!loading &&
                                facilityFilterData &&
                                facilityFilterData.length > 0 &&
                                facilityFilterData.map((item) => (
                                    <FacilityWiseTotal item={item} loading={loading} key={item?.name} />
                                ))}
                            {loading && (
                                <Stack
                                    width={"100%"}
                                    sx={{ display: "flex", alignItems: "center", mt: 10 }}
                                >
                                    <CircularProgress />
                                </Stack>
                            )}
                        </Grid>
                    </Grid>
                </Box>
            </div>
        </Page>
    );
};

export default QuickGlacePage;
