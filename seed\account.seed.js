const Account = require("../models/Account");

const seedAccounts = async () => {
    const accountData = [
        {
            name: '<PERSON><PERSON><PERSON>',
        },
        {
            name: 'Dev Account',
        }
    ];

    await Account.deleteMany({});

    const createdAccount = accountData.map(async (item) => {
        const role = await Account.create(item);
        return role;
    });

    await Promise.all(createdAccount);

    console.log("<-- Account Seeded -->");
};

module.exports = { seedAccounts };
