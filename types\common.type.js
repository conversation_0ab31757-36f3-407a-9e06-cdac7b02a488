const ALERT_TYPE_TYPE = {
    WEEKS: 'weeks',
    BI_WEEKS: 'biWeeks',
    MONTHS: 'months',
}

const QUESTION_INPUT_TYPE = {
    UNLIMITED_ANSWERS: 'unlimitedAnswers',
    LIMITED_ANSWERS: 'limitedAnswers',
    NUMBER_RANGE: 'numberRange',
    NUMBER_RANGE_LIMITED_ANSWERS: 'numberRangeLimitedAnswers',
    RESIDENCE_LIST: 'residenceList',
    DATE: 'date',
    TIME_TAB_RESIDENT_LIST: 'timeTabResidentList',
    TIME_TAB_RANGE: 'timeTabRange',
}

const PAGE_TYPE = {
    COMMUNITY_TRANSFER: 'communityTransfer',
    ADMISSION: 'admission',
    HOSPITAL: 'hospital',
    DECEASED: 'deceased',
    OVERALL: 'overall',
    ADT: 'adt',
}

const CUSTOM_TAB_PAGE_TYPE = {
    ...Object.fromEntries(Object.entries(PAGE_TYPE).filter(([key]) => key !== 'OVERALL' && key !== 'ADT')),
    OVERALL_INCOMING: 'overallIncoming',
    OVERALL_OUTGOING: 'overallOutgoing',
}

const CUSTOM_TAB_TYPE = {
    customTab: 'customTab',
    combineTab: 'combineTab',
}

const ACCOUNT_PERCENTAGE_BY = {
    CENSUS: 'census',
    BED: 'bed',
}

const REPORT_FILE_TYPE = {
    PDF: 'pdf',
    EXCEL: 'excel',
}

const AUTOMATICALLY_REPORT_TYPE = {
    DAILY: 'daily',
    WEEKLY: 'weekly',
    MONTHLY: "monthly",
    QUARTERLY: "Quarterly",
    YEARLY: "yearly",
    SEVEN_DAYS: "sevenDays",
}

const TOTAL_TYPE = {
    MAIN: "main",
    FILTER: "filter",
}

const ADMISSION_TYPES = {
    ADMISSION: "admission",
    READMISSION: "readmission",
    ALL: "all",
};

const OVERALL_PAGE_SUB_TYPE = {
    TOTAL: 'total',
    TOTAL_INCOMING: 'totalIncoming',
    TOTAL_OUTGOING: 'totalOutgoing',
}

const DASHBOARD_FILTER_TYPE = {
    COMMUNITY_TRANSFER: 'communityTransfer',
    ADMISSION: 'admission',
    HOSPITAL: 'hospital',
    DECEASED: 'deceased',
    OVERALL: 'overall',
}

const ADT_TABLE_TYPE = {
    CHART: 'chart',
    DAY_CHART: 'dayChart',
    ANALYSIS_CHART: 'analysisChart',
    TOTAL: 'total',
    GROUP: 'group',
    GROUP_ARRAY: 'groupArray',
    ALL: 'all',
    CUSTOM: "custom"
}

const CHART_FILTER_DAY_OF = {
    DAY: "day",
    MONTH: "month",
    WEEK: "week",
    QUARTER: "quarter",
    YEAR: "year",
    THIRTY: "30",
    SEVEN: "7",
};

const FILTER_TYPES = {
    DAILY: "daily",
    WEEKLY: "weekly",
    MONTHLY: "monthly",
    QUARTERLY: "quarterly",
    YEARLY: "yearly",
    THIRTY_DAYS: "thirtyDays",
};

const ADMISSION_CARDS_TYPE = {
    DOCTOR_DATA: 'doctorData',
    DAYS_DATA: 'daysData',
    DX_DATA: 'dxData',
    INSURANCE_DATA: 'insuranceData',
    FLOORS_DATA: 'floorsData',
    HOSPITAL_DATA: 'hospitalData',
    SIXTY_DAYS_DATA: 'sixtyDaysData',
    TOTAL: "total",
    ADMISSION: "admission",
    READMISSION: "readmission",
    TOTAL_ADMISSIONS: "totalAdmissions",
    TOTAL_READMISSIONS: "totalReAdmissions"
}

const DECEASED_CARDS_TYPE = {
    INSURANCE_DATA: 'insuranceData',
    DOCTOR_DATA: 'doctorData',
    FLOORS_DATA: 'floorsData',
    NINETY_DAYS_DATA: 'ninetyDaysData',
    TOTAL: "total",
}

const CO_TRANSFER_CARDS_TYPE = {
    INSURANCE_DATA: 'insuranceData',
    SIXTY_DAYS_DATA: 'sixtyDaysData',
    RETURNS_DATA: 'returnsData',
    DOCTOR_DATA: 'doctorData',
    FLOORS_DATA: 'floorsData',
    SAFE_DISCHARGE_ASS_LIV_DATA: 'safeDischargeAssLivData',
    SNF_FACILITY_DATA: 'snfFacilityData',
    TOTAL: "total",
    SAFE_DISCHARGE: 'safeDischarge',
    SNF: 'SNF',
    AMA: 'AMA',
}

const OVERALL_CARDS_TYPE = {
    INSURANCE_DATA: 'insuranceData',
    DOCTOR_DATA: 'doctorData',
    FLOORS_DATA: 'floorsData',
    NINETY_DAYS_DATA: 'ninetyDaysData',
    TOTAL: "total",
    TOTAL_INCOMING: 'totalIncoming',
    TOTAL_OUTGOING: 'totalOutgoing',
}

const RELATIONS = {
    DOCTOR: "doctor",
    INSURANCE: "insurance",
    UNIT: "unit",
    NURSE: "nurse",
    HOSPITAL: "hospital",
    DX: "dx",
    FACILITY: "facilityId",
    PAYER_SOURCE_INSURANCE: "payerSourceInsurance",
    SNF: "snf",
    TRANSFER_TO_WHICH_ASSISTED_LIVING: "transferToWhichAssistedLiving",

}

const TYPES = {
    UNPLANNED: "unplannedHospitalTransfer",
    PLANNED: "plannedHospitalTransfer",
    ALL: "all",
}

const ADT_SUB_TYPES = {
    UNPLANNED: "Unplanned",
    PLANNED: "planned",
    SAFE_DISCHARGE: "safeDischarge",
    SNF: "SNF",
    AMA: "AMA",
    DECEASED: "deceased",
    PLANNED_HOSPITAL_TRANSFER: "plannedHospitalTransfer",
    UNPLANNED_HOSPITAL_TRANSFER: "unplannedHospitalTransfer",
    HOSPITAL_TRANSFER: "hospitalTransfer",
}

const HOSPITAL_CARDS_TYPE = {
    DOCTOR_DATA: 'doctorData',
    DCER_DATA: 'DCERData',
    INSURANCE_DATA: 'insuranceData',
    RETURNS_DATA: 'returnsData',
    NINETY_DAYS_DATA: 'ninetyDaysData',
    NURSE_DATA: 'nurseData',
    FLOORS_DATA: 'floorsData',
    DAYS_DATA: 'daysData',
    DX_DATA: 'dxData',
    SHIFT_DATA: 'shiftData',
    PERMISSION: 'permission',
    HOSPITAL_DATA: 'hospitalData',
    HOSPITALIZATIONS: 'hospitalizations',
    UNPLANNED: "unplannedHospitalTransfer",
    PLANNED: "plannedHospitalTransfer",
    TOTAL: "total",
}

const TRANSFER_TYPE = {
    SAFE_DISCHARGE: 'safeDischarge',
    SNF: 'SNF',
    AMA: 'AMA',
    ALL: 'all',
}

const ADMISSION_FILTER_TYPE = {
    ADT: "ADT",
    ADMISSION: "Admission",
    MANUAL: "Manual"
}

const percentageByEnum = {
    censusAverage: "Average Occupancy",
    bedCapacity: "Bed Capacity",
    censusAsOfNow: "Current Census",
    customNumber: "Custom Occupancy",
}

const NUMBER_RANGE_TYPE = {
    RANGE: "range",
    AVERAGE: "average",
    TOTAL: "total",
}

const CARD_LABEL_COMMON = {
    perInsurance: "Per Insurance",
    perDoctor: "Per Doctor",
    perFloor: "Per Floor",
    perDay: "Per Day",
}

const COMMUNITY_CARD_LABELS = {
    totalMain: "Total Community Transfers",
    total: "Total",
    safeDischarge: "Safe Discharges",
    SNF: "SNF Transfers",
    AMA: "AMA",
    insuranceData: CARD_LABEL_COMMON.perInsurance,
    sixtyDaysData: "60 Days Analysis",
    returnsData: "Returned / Didn't Return",
    safeDischargeAssLivData: "Safe Discharge Assisted Living",
    snfFacilityData: "SNF Transfers per Facility",
    doctorData: CARD_LABEL_COMMON.perDoctor,
    floorsData: CARD_LABEL_COMMON.perFloor,
}

const DECEASED_CARDS_LABELS = {
    totalMain: "Deceased",
    insuranceData: CARD_LABEL_COMMON.perInsurance,
    doctorData: CARD_LABEL_COMMON.perDoctor,
    floorsData: CARD_LABEL_COMMON.perFloor,
    ninetyDaysData: '90 Days Analysis',
    total: "Total Deceased"
}

const ADMISSION_CARDS_LABELS = {
    total: "Total Admissions",
    admission: "New Admissions",
    readmission: "Re-Admissions",
    doctorData: 'Per Doctor',
    daysData: 'Per Day',
    dxData: 'Per Diagnosis',
    insuranceData: 'Per Insurance',
    floorsData: 'Per Floor',
    hospitalData: 'Per Hospital',
}

module.exports = {
    ADMISSION_CARDS_LABELS,
    DECEASED_CARDS_LABELS,
    COMMUNITY_CARD_LABELS,
    percentageByEnum,
    AUTOMATICALLY_REPORT_TYPE,
    ADMISSION_FILTER_TYPE,
    TRANSFER_TYPE,
    HOSPITAL_CARDS_TYPE,
    ADT_SUB_TYPES,
    PAGE_TYPE,
    OVERALL_PAGE_SUB_TYPE,
    DASHBOARD_FILTER_TYPE,
    ADT_TABLE_TYPE,
    CHART_FILTER_DAY_OF,
    FILTER_TYPES,
    ADMISSION_CARDS_TYPE,
    DECEASED_CARDS_TYPE,
    CO_TRANSFER_CARDS_TYPE,
    OVERALL_CARDS_TYPE,
    RELATIONS,
    TOTAL_TYPE,
    ADMISSION_TYPES,
    TYPES,
    REPORT_FILE_TYPE,
    ACCOUNT_PERCENTAGE_BY,
    ALERT_TYPE_TYPE,
    QUESTION_INPUT_TYPE,
    NUMBER_RANGE_TYPE,
    CUSTOM_TAB_TYPE,
    CUSTOM_TAB_PAGE_TYPE
};