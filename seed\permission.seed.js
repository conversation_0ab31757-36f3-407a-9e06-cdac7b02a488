const Permission = require("../models/Permission");

const seedPermissions = async () => {
    const permissionsData = [
        {
            name: 'View Data Input'
        },
        {
            name: 'Add ADT'
        },
        {
            name: 'Edit ADT'
        },
        {
            name: 'Customize Questions'
        },
        {
            name: 'Manage Facility'
        },
        {
            name: 'Add Facility'
        },
        {
            name: 'Edit Facility'
        },
        {
            name: 'Delete Facility'
        },
        {
            name: 'Manage User'
        },
        {
            name: 'Add User'
        },
        {
            name: 'Edit User'
        },
        {
            name: 'Delete User'
        },
        {
            name: 'Manage Account'
        },
        {
            name: 'Add Account'
        },
        {
            name: 'Edit Account'
        },
        {
            name: 'Delete Account'
        },
        {
            name: 'View Timelog'
        }
    ];

    await Permission.deleteMany({});

    const createdPermissions = permissionsData.map(async (item) => {
        const permission = await Permission.create(item);
        return permission;
    });

    await Promise.all(createdPermissions);
    console.log("Permission Seed");
};

module.exports = { seedPermissions };
