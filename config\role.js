const { AUTH_ROLES } = require("../types/auth.type");

const roles = [AUTH_ROLES.SUPER, AUTH_ROLES.OWNER, AUTH_ROLES.TOTAL, AUTH_ROLES.ADMIN, AUTH_ROLES.REGIONAL];

const roleRights = new Map();
roleRights.set(AUTH_ROLES.SUPER, [
	"manageRoleOption",
	"manageRoles",
	"manageAccount",
	"getAccountList",
	"manageUsers",
	"manageValidation",
	"manageDashboard",
	"manageADT",
	"manageCensus",
	"manageFacility",
	"manageQuestions",
	"manageLogs",
	"manageNote",
]);
roleRights.set(AUTH_ROLES.OWNER, [
	"manageRoleOption",
	"manageRoles",
	"getAccountList",
	"manageUsers",
	"manageValidation",
	"manageDashboard",
	"manageADT",
	"manageCensus",
	"manageFacility",
	"manageQuestions",
	"manageLogs",
	"manageNote",
]);
roleRights.set(AUTH_ROLES.TOTAL, [
	"manageRoleOption",
	"manageRoles",
	"getAccountList",
	"manageUsers",
	"manageValidation",
	"manageDashboard",
	"manageADT",
	"manageCensus",
	"manageFacility",
	"manageQuestions",
	"manageLogs",
	"manageNote",
]);
roleRights.set(AUTH_ROLES.ADMIN, [
	"manageRoleOption",
	"manageRoles",
    "manageADT",
	"getAccountList",
	"manageUsers",
	"manageValidation",
	"manageDashboard",
	"manageCensus",
	"manageFacility",
	"manageQuestions",
	"manageLogs",
	"manageNote",
]);
roleRights.set(AUTH_ROLES.REGIONAL, [
	"manageRoleOption",
	"manageRoles",
	"getAccountList",
	"manageUsers",
	"manageValidation",
	"manageDashboard",
	"manageADT",
	"manageCensus",
	"manageFacility",
	"manageQuestions",
	"manageLogs",
	"manageNote",
]);
roleRights.set(AUTH_ROLES.USER, [
	"manageValidation",
	"getAccountList",
	"manageDashboard",
	"manageADT",
	"manageCensus",
	"manageFacility",
	"manageQuestions",
	"manageLogs",
	"manageNote",
]);

module.exports = {
	roles,
	roleRights,
};
