const {
  getDashboardData,  
  getCardPatientChartData,
  getAllCount
} = require("../helpers/hospital");
const authWithRole = require("../middleware/auth-with-role");

const route = require("express").Router();

route.get("/dashboard-data", authWithRole("manageDashboard"), async (req, res) => {
  try {
    let response = await getDashboardData(req);
    res.send(response);
  } catch (e) {    
    res.send(500);
  }
});

route.post("/card-patient-chart-data", authWithRole("manageDashboard"), async (req, res) => {
  try {
    let response = await getCardPatientChartData(req);
    res.send(response);
  } catch (e) {    
    console.log(e, 'e');    
    res.send(500)
  }
});


route.get("/get-all-count", authWithRole("manageDashboard"), async (req, res) => {
  try {
    let response = await getAllCount(req);
    res.send(response);
  } catch (e) {   
    console.log(e, 'e');     
    res.send(500)
  }
});


module.exports = route;
