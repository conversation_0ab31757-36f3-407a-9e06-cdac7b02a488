.cardContainer {
    position: relative;
    width: 174px;
    height: 100%;
}

.overall:before {
    content: '';
    position: absolute;
    width: 50%;
    height: 18px;
    background: linear-gradient(65deg, #FCC779 0.21%, #FC3813 112.99%);
    top: -10px;
    left: 30%;
    border-radius: 7px;
    box-shadow: -3px 3px 0 0px white, 3px 0 0 0px white, 4px 7px 0 -4px white
}


.transfer:before {
    content: '';
    position: absolute;
    width: 50%;
    height: 18px;
    background: linear-gradient(285deg, #497AF5 0.5%, #90C5FF 99.58%);
    top: -10px;
    left: 30%;
    border-radius: 7px;
    box-shadow: -3px 3px 0 0px white, 3px 0 0 0px white, 4px 7px 0 -4px white
}


.communityTransfer:before {
    content: '';
    position: absolute;
    width: 50%;
    height: 18px;
    background: linear-gradient(65deg, #4CEBEB 0.3%, #076673 99.73%);
    top: -10px;
    left: 30%;
    border-radius: 7px;
    box-shadow: -3px 3px 0 0px white, 3px 0 0 0px white, 4px 7px 0 -4px white
}

.admission:before {
    content: '';
    position: absolute;
    width: 50%;
    height: 18px;
    background: linear-gradient(285deg, #736AD1 0.21%, #00BAEB 112.99%);
    border-radius: 7px;
    top: -10px;
    left: 30%;
    box-shadow: -3px 3px 0 0px white, 3px 0 0 0px white, 4px 7px 0 -4px white
}
.deceased:before {
    content: '';
    position: absolute;
    width: 50%;
    height: 18px;
    background: linear-gradient(65deg, #FF9196 0.21%, #6B0014 112.99%);
    top: -10px;
    left: 30%;
    border-radius: 7px;
    box-shadow: -3px 3px 0 0px white, 3px 0 0 0px white, 4px 7px 0 -4px white
}

.totalCard {
    background: #EEF3F7;
    border-radius: 8px;
    overflow: hidden;

    .divider {
        background-color: #fff !important;
    }

    .mainTitle {
        margin-bottom: 16px;
        font-family: 'manrope-semi-bold';
        font-size: 14px;
        line-height: 19px;
        color: #444652;
        text-align: center;
        height: 25px;
        display: flex;
        align-items: center;
        justify-content: center;
        @media (max-width: 1200px) {
            height: 35px;
        }
        @media (max-width: 1024px) {
            height: 40px;
        }
    }

    .middleContainer {
        padding: 5px;
        display: flex;
        justify-content: center;
        align-items: center;
        color: #444652;
        font-size: 12px;
        font-family: 'manrope-semi-bold';

        .totalNumberButton {
            height: 31px;
            min-width: 40px;
            padding: 5px;
            background: #FFFFFF;
            border-radius: 4px;
            margin-right: 5px;
            color: #444652;
        }

        .percentageNumber {
            text-align: center;
        }
    }

    .bottomContainer {
        margin-top: 10px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;

        .primaryText {
            font-family: 'manrope-semi-bold';
            font-size: 8px;
            line-height: 11px;
        }

        .secondaryText {
            margin-top: 4px;
            font-family: 'manrope-semi-bold';
            font-size: 12px;
            line-height: 16px;

        }
    }
}

.colorBar {
    text-align: center;
    left: 50%;
    top: 0px;
    height: 8px;
    width: 88px;
    background: linear-gradient(65deg, #8F44DA 0.21%, #00BAEB 112.99%);
    border-radius: 7px;
    transform: rotate(90deg);
}