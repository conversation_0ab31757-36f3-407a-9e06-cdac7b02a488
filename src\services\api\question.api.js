import api from './api';

const API_PREFIX = "api/question";

const getAllQuestions = async (params) => {
    const response = await api.get(`${API_PREFIX}/set`, { params });
    return response;
};

const getAccountQuestionsAPI = async (params) => {
    const response = await api.get(`${API_PREFIX}/account/questions`, { params });
    return response;
};

const saveSelectedQuestionsAPI = async (body) => {
    const response = await api.post(`${API_PREFIX}/clone-account-question`, body);
    return response;
};

const removeArchivedQuestionsAPI = async (id) => {
    const response = await api.delete(`${API_PREFIX}/remove-archived-questions/${id}`);
    return response;
};

export {
    getAllQuestions,
    saveSelectedQuestionsAPI,
    getAccountQuestionsAPI,
    removeArchivedQuestionsAPI
};
