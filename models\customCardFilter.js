const mongoose = require("mongoose");
const { PAGE_TYPE } = require("../types/common.type");
const { Schema, Types } = mongoose;

const customCardFilterSchema = new Schema(
  {
    accountId: { type: Types.ObjectId, ref: "account" },
    userId: { type: Types.ObjectId, ref: "user" },
    cards: { type: [String], required: false },
    page: {
      type: String,
      enum: [PAGE_TYPE.ADMISSION, PAGE_TYPE.HOSPITAL, PAGE_TYPE.OVERALL, PAGE_TYPE.DECEASED, PAGE_TYPE.COMMUNITY_TRANSFER],
      default: PAGE_TYPE.HOSPITAL
    },
  },
  { timestamps: true }
);

mongoose.model("customCardFilter", customCardFilterSchema);
