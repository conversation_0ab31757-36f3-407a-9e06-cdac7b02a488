import React from 'react';
import { Popover, Typography, Box, Button } from '@mui/material';

const ErrorPopover = ({ open, onClose, errorMessages = [], sx = {} }) => (
    <Popover
        open={open}
        onClose={onClose}
        anchorReference="anchorPosition"
        anchorPosition={{ top: window.innerHeight / 2, left: window.innerWidth / 2 }}
        transformOrigin={{ vertical: "top", horizontal: "center" }}
        PaperProps={{ sx: { p: 2, maxWidth: 400, borderRadius: 2, ...sx } }}
    >
        <Typography color="error" variant="body1" mb={1}>
            Please fix the following errors:
        </Typography>
        <Box component="ul" sx={{ pl: 3, mb: 2, listStyleType: "disc" }}>
            {errorMessages.map((err, idx) => (
                <li key={idx}>
                    <Typography variant="body2" color="error">{err}</Typography>
                </li>
            ))}
        </Box>
        <Button
            size="medium"
            variant="contained"
            onClick={onClose}
        >
            Close
        </Button>
    </Popover>
);

export default ErrorPopover; 