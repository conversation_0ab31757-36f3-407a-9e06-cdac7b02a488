const { rrulestr } = require('rrule');
const { alertSubscriptionData } = require('./alert/alert.subscription.data');
const { getOrCreateQueue, getOrCreateWorker, hasWorker, initializeWorkerManager } = require('../config/worker-manager');

// Global variables to track instances and prevent duplicates
let isAlertScheduleInitialized = false;


// this job will run every day midnight and create separate job for send report based on configuration

const settings = {
    repeatStrategy: (millis, opts) => {
        const currentDate =
            opts.startDate && new Date(opts.startDate) > new Date(millis)
                ? new Date(opts.startDate)
                : new Date(millis);
        const rrule = rrulestr(opts.pattern);
        if (rrule.origOptions.count && !rrule.origOptions.dtstart) {
            throw new Error('DTSTART must be defined to use COUNT with rrule');
        }

        const next_occurrence = rrule.after(currentDate, false);
        return next_occurrence?.getTime();
    },
};

const setUpAlertSchedule = async () => {
    // Prevent duplicate initialization
    if (isAlertScheduleInitialized) {
        console.log('🔄 Alert schedule already initialized - skipping duplicate setup');
        return;
    }

    console.log('🚨 Setting up alert monitoring system...', new Date().toLocaleString());

    // Initialize worker manager
    initializeWorkerManager();

    // Check if worker already exists
    if (hasWorker('alertsReport')) {
        console.log('✅ Alert worker already exists - reusing existing monitoring instance');
        isAlertScheduleInitialized = true;
        return;
    }

    // Create queue and worker using worker manager
    const alertQueue = getOrCreateQueue('alertsReport', settings);
    const alertWorker = getOrCreateWorker(
        'alertsReport',
        async (job) => {
            console.log(`🚨 Starting alert processing for job ${job.id}...`);
            try {
                await job.updateProgress(25);
                console.log("🔍 Processing alert subscriptions...");

                await job.updateProgress(75);
                await alertSubscriptionData();

                await job.updateProgress(100);
                console.log(`✅ Alert processing completed for job ${job.id}`);
            } catch (err) {
                console.error(`❌ Alert processing failed for job ${job.id}:`, err);
                // Re-throw the error so BullMQ can handle retries
                throw new Error(`Alert processing failed: ${err.message}`);
            }
        },
        {
            concurrency: 1, // Process one alert batch at a time
            // Standard lock duration for alerts (shorter than reports)
            lockDuration: 600000, // 10 minutes
            lockRenewTime: 300000, // Renew every 5 minutes
            ...settings
        }
    );

    alertWorker.on("active", async (job) => {
        console.debug(`Processing job with alert id ${job.id}`);
    });

    alertWorker.on("completed", async (job, returnValue) => {
        // await sendTestEmail("Run cron job every day for sending report to users from local")
        console.debug(`Completed alert job with id ${job.id}`, returnValue);
    });

    alertWorker.on("error", async (failedReason) => {
        // await sendTestEmail(JSON.stringify(failedReason))
        console.log(failedReason, 'alert failedReason');
    });

    const d = new Date();
    const repeat = {
        // cron: '0 */1 * * * *', //Run in every minute for debug in local system        
        cron: '0 40 7 * * *',
        offset: d.getTimezoneOffset(),
        tz: 'America/Atikokan',
        immediately: true,
    }; // run every day midnight 12:00 

    alertQueue.add(
        'alertReportDaily',
        { type: 'Alert' },
        {
            repeat,
            attempts: 3, // retry 3 times if fails
            removeOnComplete: 10, // Keep last 10 completed jobs
            removeOnFail: 50, // Keep last 50 failed jobs for debugging
            backoff: {
                type: 'exponential',
                delay: 3000, // Start with 3 second delay
            },
            // Job timeout - alerts should be faster than reports
            timeout: 600000, // 10 minutes timeout for alert processing
        },
    );

    // Mark as initialized
    isAlertScheduleInitialized = true;
    console.log('🎉 Alert schedule initialized successfully - monitoring active at 7:40 AM EST daily!');
}

// Cleanup function for graceful shutdown
const cleanupAlertSchedule = async () => {
    // Worker manager will handle cleanup of workers and queues
    isAlertScheduleInitialized = false;
    console.log('Alert schedule cleaned up');
};

module.exports = { setUpAlertSchedule, cleanupAlertSchedule };