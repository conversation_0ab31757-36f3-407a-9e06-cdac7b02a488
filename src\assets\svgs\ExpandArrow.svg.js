const ExpandArrowSvg = () => {
    return (
        <svg width="13" height="12" viewBox="0 0 13 12" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M5.71731 11.2596C5.60984 11.2602 5.50329 11.2398 5.40379 11.1996C5.30429 11.1595 5.21379 11.1002 5.13748 11.0254L0.237481 6.17922C0.0853756 6.02788 0 5.82317 0 5.60979C0 5.39641 0.0853756 5.1917 0.237481 5.04037L5.13748 0.194215C5.29371 0.061894 5.49467 -0.00724934 5.70021 0.000602362C5.90574 0.00845406 6.10071 0.0927222 6.24616 0.236568C6.3916 0.380413 6.47681 0.573241 6.48475 0.776518C6.49268 0.979795 6.42277 1.17855 6.28898 1.33306L1.96881 5.60575L6.28898 9.87845C6.40366 9.99094 6.48203 10.1345 6.5142 10.2911C6.54636 10.4476 6.53087 10.6101 6.46968 10.7579C6.40849 10.9057 6.30436 11.0323 6.17044 11.1216C6.03652 11.2109 5.87883 11.2589 5.71731 11.2596Z" fill="#444652" />
            <path d="M11.4957 9.99999C11.4098 10.0005 11.3247 9.9842 11.2451 9.95207C11.1656 9.91994 11.0933 9.8726 11.0323 9.81276L7.1156 5.93915C6.99402 5.81819 6.92578 5.65456 6.92578 5.484C6.92578 5.31344 6.99402 5.14981 7.1156 5.02885L11.0323 1.15524C11.1571 1.04947 11.3178 0.994205 11.4821 1.00048C11.6463 1.00676 11.8022 1.07411 11.9184 1.18909C12.0347 1.30407 12.1028 1.4582 12.1091 1.62068C12.1155 1.78317 12.0596 1.94203 11.9527 2.06554L8.49949 5.48077L11.9527 8.89601C12.0443 8.98592 12.107 9.1007 12.1327 9.22583C12.1584 9.35095 12.146 9.48081 12.0971 9.59898C12.0482 9.71714 11.965 9.81831 11.8579 9.88969C11.7509 9.96107 11.6248 9.99945 11.4957 9.99999Z" fill="#D9D9D9" />
        </svg>
    )
}

export default ExpandArrowSvg;