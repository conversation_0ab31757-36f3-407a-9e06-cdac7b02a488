const mongoose = require("mongoose");
const Census = mongoose.model("census");
const {
  createFacility,
  getFacility,
  getFacilityList,
  getFacilityUser,
  updateFacility,
  deleteFacility,
  getAllAccountFacilityList,
  getManageFacilityList,
} = require("../helpers/facility");
const authWithRole = require("../middleware/auth-with-role");
const route = require("express").Router();

route.post("/", authWithRole("manageFacility"), async (req, res) => {
  try {
    const { accountid } = req.headers;
    const facility = await createFacility({...req.body, accountId : accountid});
    res.send(facility);
  } catch (error) {
    res.status(500).send({ message: error.message });
  }
});

route.put("/:facilityid", authWithRole("manageFacility"), async (req, res) => {
  try {
    const { accountid } = req.headers;

    const facility = await updateFacility(req.params.facilityid, req.body, accountid);
    res.send(facility);
  } catch (error) {
    res.status(500).send({ message: error.message });
  }
});

route.get("/list/all/account", authWithRole("manageFacility"), async (req, res) => {
  try {
    const facilityList = await getAllAccountFacilityList(
      req.params.accountId,
      req.user,
      req.query.grouped
    );
    res.send(facilityList);
  } catch (error) {
    res.status(500).send(error);
  }
});

route.get("/list/:accountId?", authWithRole("manageFacility"), async (req, res) => {
  try {
    const facilityList = await getFacilityList(req.params.accountId, req.user, req.query.grouped);
    res.send(facilityList);
  } catch (error) {
    res.status(500).send(error);
  }
});

route.get("/manage-facility-list", authWithRole("manageFacility"), async (req, res) => {
  try {
    const facilityList = await getManageFacilityList(req);
    res.send(facilityList);
  } catch (error) {
    res.status(500).send(error);
  }
});

route.get("/has-census",  authWithRole("manageFacility"), async (req, res) => {
  try {
    const census = await Census.findOne({
      facilityId: req.query.facilityId,
      isBaseline: true,
    });
    res.send(census);
  } catch (error) {
    res.status(500).send(error);
  }
});

route.get("/user", authWithRole("manageFacility"), async (req, res) => {
  try {
    const facilityList = await getFacilityUser(req.user);
    res.send(facilityList);
  } catch (error) {
    res.status(500).send(error);
  }
});

route.get("/:id?", authWithRole("manageFacility"), async (req, res) => {
  try {
    const facility = await getFacility(req.params.id, req.user);
    res.send(facility);
  } catch (error) {
    res.status(500).send(error);
  }
});

route.delete("/:id", authWithRole("manageFacility"), async (req, res) => {
  try {
    const facility = await deleteFacility(req.params.id, req);
    res.send(facility);
  } catch (error) {
    res.status(500).send(error);
  }
});

module.exports = route;
