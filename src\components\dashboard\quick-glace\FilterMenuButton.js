/* eslint-disable no-unused-vars */
import * as React from "react";
import { styled } from "@mui/material/styles";
import Button from "@mui/material/Button";
import Menu from "@mui/material/Menu";
import MenuItem from "@mui/material/MenuItem";
import KeyboardArrowDownIcon from "@mui/icons-material/KeyboardArrowDown";
import AddCircleOutlineOutlinedIcon from "@mui/icons-material/AddCircleOutlineOutlined";
import styles from "./FilterMenuButton.module.scss";
import { ListItemIcon, ListItemText, MenuList, Typography } from "@mui/material";
import { QUICK_GLACE_OPTIONS } from "../../../data/quick-glace.data";
import _ from "lodash";
import ChildMenuList from "./ChildMenuList";

const StyledMenu = styled(props => <Menu elevation={0} {...props} />)(() => ({
    "& .MuiPaper-root": {
        width: "300px",
        display: "flex",
        "justify-content": "space-between",
        borderRadius: 6,
        "& .MuiMenu-list": {
            padding: "4px 0",
        },
    },
    "& .MuiList-root": {
        width: '100%'
    }
}));

const FilterMenuButton = ({
    handleOnchangeChild,
    item,
    filterValues,
    handleRemoveFilter,
}) => {
    const [anchorEl, setAnchorEl] = React.useState(null);
    const [isParentSelected, setIsParentSelected] = React.useState(null);
    const open = Boolean(anchorEl);

    React.useEffect(() => {
        setIsParentSelected(item);
    }, [item]);

    const handleClick = event => {
        setAnchorEl(event.currentTarget);
    };

    const handleCloseMenu = () => {
        setAnchorEl(null);
    };

    const handleParentChange = value => {
        setIsParentSelected(value);
    };

    const handleChildChange = React.useCallback(
        value => {
            setAnchorEl(null);
            handleOnchangeChild(value);
        },
        [handleOnchangeChild]
    );

    const removeFilter = React.useCallback(
        value => {
            setAnchorEl(null);
            handleRemoveFilter(value);
        },
        [handleRemoveFilter]
    );

    const getChildLabel = () => {
        if (!item) {
            const selected = _.find(filterValues, { parentId: item?.id });
            let labelName = selected ? selected.label : item?.label;
            return labelName;
        } else {
            return item?.label;
        }
    };

    return (
        <div className={styles.filterMenuButton}>
            <Button
                variant="contained"
                disableElevation
                className={styles.addFilterButton}
                onClick={handleClick}
                endIcon={item ? <KeyboardArrowDownIcon /> : <AddCircleOutlineOutlinedIcon />}
            >
                {!item ? (
                    !isParentSelected ? (
                        "Add Filter"
                    ) : (
                        <>
                            <div
                                style={{
                                    background: isParentSelected?.color,
                                    width: "11px",
                                    height: "34px",
                                    borderRadius: "6.5px",
                                }}
                            />
                            {isParentSelected.label}
                        </>
                    )
                ) : (
                    <>
                        <div
                            style={{
                                background: _.find(QUICK_GLACE_OPTIONS, { id: item.parentId })?.color,
                                width: "11px",
                                height: "34px",
                                borderRadius: "6.5px",
                            }}
                        />
                        <Typography>{getChildLabel()}</Typography>
                    </>
                )}
            </Button>

            <StyledMenu
                anchorEl={anchorEl}
                open={open}
                onClose={handleCloseMenu}
                anchorOrigin={{
                    vertical: isParentSelected ? "bottom" : "top",
                    horizontal: "right",
                }}
                transformOrigin={{
                    vertical: "top",
                    horizontal: "right",
                }}
                MenuListProps={{
                    sx: { width: anchorEl && anchorEl.offsetWidth },
                }}
            >
                {!isParentSelected && (
                    <MenuList>
                        {QUICK_GLACE_OPTIONS.map((itemEle, index) => {
                            return (
                                <MenuItem key={index + "main"}>
                                    <ListItemIcon>
                                        <div
                                            style={{
                                                background: itemEle.color,
                                                width: "11px",
                                                height: "19px",
                                                borderRadius: "6.5px",
                                            }}
                                        ></div>
                                    </ListItemIcon>
                                    <ListItemText className={styles.parentTitle}>{itemEle.label}</ListItemText>
                                    <Typography variant="body2" color="text.secondary">
                                        <Button
                                            size="small"
                                            onClick={() => handleParentChange(itemEle)}
                                            className={styles.addButton}
                                        >
                                            Add
                                        </Button>
                                    </Typography>
                                </MenuItem>
                            );
                        })}
                    </MenuList>
                )}
                {isParentSelected && (
                    <ChildMenuList
                        item={item}
                        selectedId={item ? item.parentId : isParentSelected.id}
                        handleChildChange={handleChildChange}
                        removeFilter={removeFilter}
                    />
                )}
            </StyledMenu>
        </div>
    );
};

export default FilterMenuButton;
