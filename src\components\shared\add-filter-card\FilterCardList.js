import {
    Divider,
    Icon<PERSON>utton,
    InputAdornment,
    Link as MLink,
    List,
    ListItem,
    Paper,
    Stack,
    TextField,
    Box,
    Typography,
    Menu,
    MenuItem,
    Chip,
    Switch,
    FormControlLabel,
} from "@mui/material";
import _ from "lodash";
import Scrollbar from "../scrollbar";
import SearchIcon from "@mui/icons-material/Search";
import HtmlTooltip from "../HtmlTooltip";
import CardTooltipContent from "../../dashboard/card/CardTooltipContent";
import ArrowCircleRightOutlinedIcon from "@mui/icons-material/ArrowCircleRightOutlined";
import InfoOutlinedIcon from '@mui/icons-material/InfoOutlined';
import { useDashboard } from "../../../context/DashboardContext";
import { useCallback, useState } from "react";
import AddCustomTabDialog from "../custom-tab-dialog/AddCustomTabDialog";
import { PAGE_TYPE } from "../../../types/pages.type";
import AddCombineTabDialog from "../custom-tab-dialog/AddCombineTabDialog";
import { CUSTOM_TAB_TYPE } from "../../../types/common.type";
import CustomTabMenu from "./CustomTabMenu";


const FilterCardList = ({
    setSearchText,
    searchText,
    isOpenCloseAll,
    handleAddCustomCard,
    setIsOpenCloseAll,
    scrollHeight,
    listData,
    scrollToSection,
    selectedCards,
    type = null,
    transferType = null,
    pageProp = null,
}) => {
    const { dashboardData: { page, filter } } = useDashboard();
    
    const [customCard, setCustomCard] = useState(null);
    const [combineCard, setCombineCard] = useState(null);
    const [editEle, setEditEle] = useState(null);
    const [menuAnchorEl, setMenuAnchorEl] = useState(null);
    const [showHiddenTabs, setShowHiddenTabs] = useState(false);

    const handleOpenCustomCard = () => {
        setCustomCard(true);
    };

    const handleCloseCustomCard = () => {
        setCustomCard(false);
    };

    const handleEdit = useCallback((ele, { isCreator }) => {
        setEditEle({ ...ele, isCreator });
    }, []);

    const handleMenuOpen = (event) => {
        setMenuAnchorEl(event.currentTarget);
    };

    const handleMenuClose = () => {
        setMenuAnchorEl(null);
    };

    const handleCombineTabs = () => {
        handleMenuClose();
        setCombineCard(true);
    };

    const handleCloseCombineCard = () => {
        setCombineCard(null);
    };

    return (
        <>
            {!!editEle && editEle?.type !== CUSTOM_TAB_TYPE.combineTab && <AddCustomTabDialog
                page={page}
                handleClose={() => setEditEle(null)}
                filter={filter}
                id={editEle?._id}
                isCreator={editEle?.isCreator}
            />}

            {!!customCard && <AddCustomTabDialog
                page={page}
                handleClose={handleCloseCustomCard}
                filter={filter}
            />}

            {!!combineCard && <AddCombineTabDialog
                page={page}
                handleClose={handleCloseCombineCard}
                filter={filter}
            />}
            {!!editEle && editEle?.type === CUSTOM_TAB_TYPE.combineTab && <AddCombineTabDialog
                page={page}
                handleClose={() => setEditEle(null)}
                filter={filter}
                id={editEle?._id}
                isCreator={editEle?.isCreator}
            />}
            <Paper
                sx={{
                    borderRadius: "0px 0px 8px 8px",
                    boxShadow: 3,
                    ...type !== "scroll" ? { margin: "0px 4px 4px 4px" } : {}
                }}
            >
                <TextField
                    fullWidth
                    sx={{ px: 1.5, p: 1.5 }}
                    placeholder="Search tab"
                    size="small"
                    onChange={(e) => setSearchText(e.target.value)}
                    value={searchText}
                    InputProps={{
                        startAdornment: (
                            <InputAdornment position="start">
                                <SearchIcon />
                            </InputAdornment>
                        ),
                    }}
                />
                {/* Display hidden tabs toggle - only show if there are shared tabs */}
                {listData.some(tab => tab.isShared) && (
                    <Box sx={{ px: 1.5, py: 0.5 }}>
                        <FormControlLabel
                            control={
                                <Switch
                                    size="small"
                                    checked={showHiddenTabs}
                                    onChange={(e) => setShowHiddenTabs(e.target.checked)}
                                    sx={{
                                        '& .MuiSwitch-switchBase.Mui-checked': {
                                            color: '#2196f3',
                                        },
                                        '& .MuiSwitch-switchBase.Mui-checked + .MuiSwitch-track': {
                                            backgroundColor: '#2196f3',
                                        },
                                    }}
                                />
                            }
                            label={
                                <Typography variant="caption" sx={{ fontSize: '11px', color: '#666' }}>
                                    Display hidden tabs
                                </Typography>
                            }
                        />
                    </Box>
                )}

                <Box display="flex" justifyContent="space-between" alignItems="center" sx={{ px: 1, py: 0.2 }}>
                    <MLink
                        sx={{ px: 1.5, pb: 1 }}
                        component="button"
                        variant="body2"
                        onClick={() => {
                            if (!isOpenCloseAll) {
                                handleAddCustomCard("selectAll");
                            } else {
                                handleAddCustomCard("clearAll");
                            }
                            setIsOpenCloseAll((prevState) => !prevState);
                        }}
                    >
                        Open all / Close all
                    </MLink>
                    {/* Show menu for all page types */}
                    <>
                        <MLink
                            sx={{
                                px: 1.5,
                                opacity: 1,
                                cursor: 'pointer',
                            }}
                            component="button"
                            variant="body2"
                            onClick={handleMenuOpen}
                        >
                            Create Custom Tabs
                        </MLink>
                        <Menu
                            anchorEl={menuAnchorEl}
                            open={Boolean(menuAnchorEl)}
                            onClose={handleMenuClose}
                            anchorOrigin={{ vertical: 'bottom', horizontal: 'left' }}
                            transformOrigin={{ vertical: 'top', horizontal: 'left' }}
                        >
                            {page !== PAGE_TYPE.OVERALL && (
                                <MenuItem onClick={() => { handleMenuClose(); handleOpenCustomCard(); }}>
                                    Create resident data flow tab
                                </MenuItem>
                            )}
                            <MenuItem onClick={handleCombineTabs}>
                                Combine Data From Separate Dashboard tabs
                            </MenuItem>
                        </Menu>
                    </>
                </Box>
                <Divider />
                <Scrollbar
                    sx={{
                        height: scrollHeight ? scrollHeight : 200,
                        width: "100%",
                        '@media (max-width:1280px)': {
                            height: '160px'
                        }
                    }}
                    rootSx={{
                        borderRadius: "8px 8px 8px 8px",
                    }}
                >
                    <List
                        sx={{
                            bgColor: "background.paper",
                            px: 2,
                            py: 2,
                        }}
                        dense
                    >
                        {_.orderBy(listData, "label", "ASC")
                            .filter((ele) => {
                                // Filter by search text
                                const matchesSearch = ele.label?.toLowerCase().includes(searchText?.toLowerCase());

                                // Filter by hidden status
                                if (showHiddenTabs) {
                                    // Show only hidden shared tabs
                                    return matchesSearch && ele.isShared && ele.isHidden;
                                } else {
                                    // Show non-hidden tabs (default behavior)
                                    return matchesSearch && (!ele.isShared || !ele.isHidden);
                                }
                            })
                            .map((ele, index) => {
                                const isSelected = _.includes(selectedCards, ele.value);
                                return (
                                    <ListItem
                                        onClick={ele?.isHidden ? undefined : () => handleAddCustomCard(ele.value)}
                                        key={ele.value}
                                        dense
                                        disableGutters
                                        sx={{
                                            cursor: ele?.isHidden ? 'default' : 'pointer',
                                            borderRadius: '4px',
                                            background: isSelected ? '#2259c9' : '#e1e1f3',
                                            marginBottom: '10px',
                                            px: 1,
                                            py: 0.5,
                                            position: 'relative', // Add relative positioning for chip
                                            opacity: ele?.isHidden ? 0.6 : 1, // Make hidden tabs appear dimmed
                                        }}
                                    >
                                        <Box sx={{ display: 'flex', width: '100%', alignItems: 'center' }}>
                                            {/* Left Side: Tooltip + Label */}
                                            <Box sx={{ flex: 1, display: 'flex', alignItems: 'center', overflow: 'hidden' }}>
                                                <div style={{ display: 'flex', alignItems: 'center' }}>
                                                    <HtmlTooltip
                                                        content={<CardTooltipContent content={ele?.tooltipContent} />}
                                                    >
                                                        <IconButton
                                                            disableFocusRipple
                                                            disableRipple
                                                            sx={{ cursor: 'pointer' }}
                                                            size="small"
                                                        >
                                                            <InfoOutlinedIcon sx={{ fontSize: '14px', color: '#b3b3b3' }} />
                                                        </IconButton>
                                                    </HtmlTooltip>
                                                </div>
                                                <Box sx={{ flex: 1, display: 'flex', alignItems: 'center', gap: 0.5 }}>
                                                    <Typography
                                                        sx={{
                                                            ml: 1,
                                                            whiteSpace: 'normal',
                                                            wordBreak: 'break-word',
                                                            fontFamily: 'manrope',
                                                            fontWeight: 'bold',
                                                            textTransform: 'uppercase',
                                                            fontSize: '11px',
                                                            color: isSelected ? '#fff' : 'inherit',
                                                        }}
                                                    >
                                                        {_.capitalize(ele.label)}
                                                    </Typography>
                                                    {ele?.isShared && (
                                                        <Chip
                                                            label={ele?.isHidden ? "hidden" : "shared"}
                                                            size="small"
                                                            variant="outlined"
                                                            color={ele?.isHidden ? "warning" : "success"}
                                                            sx={{
                                                                height: '14px',
                                                                fontSize: '9px',
                                                                fontWeight: 'bold',
                                                                ml: -2.0,
                                                                mb:2.8,
                                                                '& .MuiChip-label': {
                                                                    padding: '0 3px',
                                                                    marginTop:"1.5px",
                                                                    fontSize: '9px',
                                                                    fontWeight: 'bold',
                                                                    textTransform: 'lowercase'
                                                                }
                                                            }}
                                                        />
                                                    )}
                                                </Box>
                                            </Box>

                                            {/* Right Side: Actions */}
                                            <Stack direction="row" spacing={0.2} alignItems="center" sx={{ ml: 0.5, flexShrink: 0 }}>
                                                {ele?.isCustomTab && ele?._id && (
                                                    <CustomTabMenu
                                                        ele={ele}
                                                        onEdit={handleEdit}
                                                        handleAddCustomCard={handleAddCustomCard}
                                                        selectedCards={selectedCards}
                                                    />
                                                )}
                                                {isSelected && (
                                                    <IconButton
                                                        size="small"
                                                        onClick={(e) => {
                                                            e.stopPropagation();
                                                            scrollToSection(ele.value);
                                                        }}
                                                    >
                                                        <ArrowCircleRightOutlinedIcon
                                                            sx={{
                                                                color: isSelected ? '#fff' : 'inherit',
                                                            }}
                                                        />
                                                    </IconButton>
                                                )}
                                            </Stack>
                                        </Box>
                                    </ListItem>
                                )
                            })}
                    </List>
                </Scrollbar>
            </Paper>
        </>
    );
};

export default FilterCardList;