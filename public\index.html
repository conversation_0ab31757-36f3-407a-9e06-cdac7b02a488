<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="utf-8" />
  <link rel="icon" href="%PUBLIC_URL%/favicon.ico" />
  <meta http-equiv='cache-control' content='no-cache'>
  <meta http-equiv='expires' content='0'>
  <meta http-equiv='pragma' content='no-cache'>
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <meta name="theme-color" content="#000000" />
  <meta name="keywords" content="SNF, simple snf, hospital, ADT">
  <meta name="description"
    content="Our program provides a comprehensive suite of tools to help you gather and analyze your skilled nursing facility's admissions, discharges, and transfers (ADT) data. With our program, you can:
Gather all of your ADT data in one place
Customize the data to your specific needs
Identify trends and patterns in your data
Generate reports on your data
Improved decision-making: Our program can help you make better decisions about your skilled nursing facility's ADTs by providing you with the information and insights you need.
Increased efficiency: Our program can help you save time and money by automating many of the tasks involved in gathering and analyzing ADT data.
Improved quality of care: Our program can help you provide better care for your residents by helping you identify trends and patterns in your data.
Our program is easy to use and navigate, and it is backed by our team of experienced professionals who are always available to help." />
  <link rel="shortcut icon" href="%PUBLIC_URL%/favicon_new.ico">
  <!--
      manifest.json provides metadata used when your web app is installed on a
      user's mobile device or desktop. See https://developers.google.com/web/fundamentals/web-app-manifest/
    -->
  <link rel="manifest" href="%PUBLIC_URL%/manifest.json" />
  <!--
      Notice the use of %PUBLIC_URL% in the tags above.
      It will be replaced with the URL of the `public` folder during the build.
      Only files inside the `public` folder can be referenced from the HTML.

      Unlike "/favicon.ico" or "favicon.ico", "%PUBLIC_URL%/favicon.ico" will
      work correctly both with client-side routing and a non-root public URL.
      Learn how to configure a non-root public URL by running `npm run build`.
    -->
  <title>SNF Data</title>
</head>

<body>
  <noscript>You need to enable JavaScript to run this app.</noscript>
  <div id="root"></div>
  <!--
      This HTML file is a template.
      If you open it directly in the browser, you will see an empty page.

      You can add webfonts, meta tags, or analytics to this file.
      The build step will place the bundled scripts into the <body> tag.

      To begin the development, run `npm start` or `yarn start`.
      To create a production bundle, use `npm run build` or `yarn build`.
    -->
</body>

</html>