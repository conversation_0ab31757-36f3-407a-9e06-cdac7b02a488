const { OVERALL_PAGE_SUB_TYPE, OVERALL_CARDS_TYPE, TOTAL_TYPE, PAGE_TYPE } = require("../../../types/common.type");
const { itemPercentage, updateListTotalValue } = require("../common");
const _ = require("lodash");
const { getCustomTabsCards } = require("./report-common");

const getTotalNumber = (data) => {
    if (data.length > 0) {
        const transferTypeTotal = _.countBy(data, "isOutgoing");
        let totalOutgoing = 0;
        let totalIncoming = 0;
        totalOutgoing = transferTypeTotal?.true || 0;
        totalIncoming = transferTypeTotal?.false || 0;
        const n = totalIncoming - totalOutgoing;
        return n;
    }
    return 0;
}

const getListData = async (dataGroupBy, type, transferType, totalPatient, totalFilter) => {
    let listGroup = [];
    const { originalData = [], totalType = null } = totalFilter;

    if (dataGroupBy) {
        for (const [key, value] of Object.entries(dataGroupBy)) {
            const valueArr = value[0];
            let totalCountValue = value.length;
            let isTotalPercentage = false;
            if (!transferType || transferType === OVERALL_PAGE_SUB_TYPE.TOTAL) {
                totalCountValue = await getTotalNumber(value);
            }
            let original = originalData[key] ? originalData[key]?.length : 0;
            if (!transferType || transferType === OVERALL_PAGE_SUB_TYPE.TOTAL) {
                original = await getTotalNumber(originalData[key] ? originalData[key] : []);
                isTotalPercentage = true;
            }

            if (key && valueArr && valueArr[type]) {
                let object = Object();
                object._id = key;
                object.id = key;
                object.isTotalPercentage = isTotalPercentage;
                object.label = valueArr[type].label;
                object.name = valueArr[type].label;
                object.total = totalCountValue;
                object.value = value.length;
                object.originalTotal = original;
                object.isTooltip = totalType && totalType === TOTAL_TYPE.MAIN ? false : true;
                object.percentage = await itemPercentage(value.length, totalPatient, "number");
                listGroup.push(object);
            }
        }
        if (listGroup.length > 0) {
            if (!transferType || transferType === OVERALL_PAGE_SUB_TYPE.TOTAL) {
                listGroup = _.orderBy(listGroup, "total", "desc").map((item) => ({
                    ...item,
                    total: new Intl.NumberFormat("en-US", { signDisplay: "exceptZero" }).format(item.total),
                }));
            } else {
                listGroup = _.orderBy(listGroup, "total", "desc");
            }
        }
    }
    return listGroup;
}

const plannedFilter = (patientData, oldFilter, type = null) => {
    patientData = _.filter(patientData, ({ floorId, doctorId, insuranceId }) => {
        let filterArr = [];

        if (oldFilter.insuranceData.length > 0 && type !== "insuranceData") {
            filterArr.push(_.includes(oldFilter.insuranceData, insuranceId));
        }
        if (oldFilter.doctorData.length > 0 && type !== "doctorData") {
            filterArr.push(_.includes(oldFilter.doctorData, doctorId));
        }
        if (oldFilter.floorsData.length > 0 && type !== "floorsData") {
            filterArr.push(_.includes(oldFilter.floorsData, floorId));
        }
        return _.every(filterArr);
    });
    return patientData;
}

const updateFacilityPercentageTotal = (data) => {
    let facilityWiseTotal = _(data)
        .groupBy("facilityId")
        .sortBy((group) => data.indexOf(group[0]))
        .map((product) => {
            return {
                id: product[0]?.facilityId,
                total: product.length || 0,
            };
        })
        .value();
}

const ninetyDaysDataFilter = (cardFilter, patientData, ninetyDaysData) => {
    let ninetyDaysDataIds = [];
    let ninetyDaysDataFilter = _.filter(ninetyDaysData, ({ _id }) => _.every([_.includes(cardFilter, _id)]));
    if (ninetyDaysDataFilter && ninetyDaysDataFilter.length > 0) {
        ninetyDaysDataFilter.map((item) => (ninetyDaysDataIds = [...ninetyDaysDataIds, ...item.ids]));
    }
    patientData = _.filter(patientData, ({ _id }) => _.every([_.includes(ninetyDaysDataIds, _id)]));
    return patientData;
}

const ninetyDaysDataList = async (data, patients, totalFilterData) => {
    const patientIds = patients && patients.length > 0 ? patients.map((item) => item.id) : [];
    let percentageTotal = totalFilterData?.totalForPercentage ? totalFilterData.totalForPercentage : patients?.length;
    const res = await updateListTotalValue(data, patientIds, "value", percentageTotal, totalFilterData);
    return res;
}

const insuranceData = async (data, totalFilter) => {
    const { transferType = null } = totalFilter;
    //insuranceData Cal
    let insuranceDBDataGroup = [];
    if (data && data.length > 0) {
        const insuranceDBData = _.groupBy(data, "insuranceId");
        const originalData = _.groupBy(totalFilter.originalData, "insuranceId");
        let percentageTotal = totalFilter?.totalForPercentage ? totalFilter.totalForPercentage : data?.length;

        if (insuranceDBData) {
            insuranceDBDataGroup = await getListData(insuranceDBData, "insurance", transferType, percentageTotal, {
                ...totalFilter,
                originalData,
            });
        }
    }

    return insuranceDBDataGroup;
}

const floorsData = async (data, totalFilter) => {
    //FloorsData Cal
    const { transferType = null } = totalFilter;

    let unitDBDataGroup = [];
    if (data && data.length > 0) {
        const floorDBData = _.groupBy(data, "floorId");
        const originalData = _.groupBy(totalFilter.originalData, "floorId");
        let percentageTotal = totalFilter?.totalForPercentage ? totalFilter.totalForPercentage : data?.length;

        if (floorDBData) {
            unitDBDataGroup = await getListData(floorDBData, "unit", transferType, percentageTotal, {
                ...totalFilter,
                originalData,
            });
        }
    }
    return unitDBDataGroup;
}

const doctorData = async (data, totalFilter) => {
    //Doctor list update Cal
    const { transferType = null } = totalFilter;

    let doctorDBDataGroup = [];
    if (data && data.length > 0) {
        const doctorDBData = _.groupBy(data, "doctorId");
        const originalData = _.groupBy(totalFilter.originalData, "doctorId");
        let percentageTotal = totalFilter?.totalForPercentage ? totalFilter.totalForPercentage : data?.length;

        if (doctorDBData) {
            doctorDBDataGroup = await getListData(doctorDBData, "doctor", transferType, percentageTotal, {
                ...totalFilter,
                originalData,
            });
        }
    }
    return doctorDBDataGroup;
}


const updateFilterListDataOverall = async (
    cardFilter,
    res,
    priorityData = [],
    transferType,
    forComparison,
    {
        mainNumPercentage = null,
        lockedTotalBy = null,
        isOnlyHospitalTabAccess = false
    }) => {
    let patientList = res;
    let totals = res.totals;
    const { list, ninetyDaysData } = patientList;
    const { lockedTotal } = totals;
    const censusAverage = isOnlyHospitalTabAccess ? totals?.bedCapacity : totals?.censusAverage;
    const customCombineTabData = patientList?.customCombineTabData ?? [];
    const customTabs = patientList?.customTabs ?? [];

    let mainNumPercentageSelected = mainNumPercentage;
    let objectCustom = Object();
    let newSavedFilters = [];
    let patientFilterData = list;
    let lockedTotalModified = lockedTotal;

    if (transferType === OVERALL_PAGE_SUB_TYPE.TOTAL_INCOMING) {
        patientFilterData = _.filter(patientFilterData, { isOutgoing: false });
    }
    if (transferType === OVERALL_PAGE_SUB_TYPE.TOTAL_OUTGOING) {
        patientFilterData = _.filter(patientFilterData, {
            isOutgoing: true,
        });
    }
    let totalFilterData = {
        originalData: patientFilterData,
        totalType: TOTAL_TYPE.FILTER,
        transferType,
        totalForPercentage: censusAverage,
    };
    if (transferType) {
        totalFilterData.totalForPercentage = patientFilterData?.length;
    }
    if (priorityData.length > 0) {
        totalFilterData.totalForPercentage = null;
    }
    if (mainNumPercentageSelected) {
        totalFilterData.totalForPercentage = mainNumPercentageSelected
    }
    if (lockedTotalBy && !forComparison) {
        if (lockedTotalBy === OVERALL_PAGE_SUB_TYPE.TOTAL_INCOMING || lockedTotalBy === OVERALL_PAGE_SUB_TYPE.TOTAL_OUTGOING || lockedTotalBy === OVERALL_PAGE_SUB_TYPE.TOTAL || lockedTotalBy === "census") {
            lockedTotalModified = patientFilterData?.length;
            if (lockedTotalModified && transferType !== lockedTotalBy && lockedTotalBy !== "census") {
                lockedTotalModified = null
            }
        } else {
            const lockedFilterRemoved = _.find(priorityData, { type: lockedTotalBy });
            lockedTotalModified = patientFilterData?.length;
            if (!lockedFilterRemoved) {
                lockedTotalModified = null
            }
        }
    }
    if (!forComparison) {
        // if (lockedTotalModified) {
        //   totalFilterData.totalForPercentage = lockedTotalModified;
        // }
        if (lockedTotalModified && priorityData.length === 1 && !transferType) {
            totalFilterData.totalForPercentage = lockedTotalModified;
        }
    }
    if (priorityData.length > 0) {
        let i = 0;
        for await (const ele of priorityData) {
            i++;
            // if (priorityData.length === i && lockedTotalModified && !lockedTotalBy && !forComparison) {
            //     store.dispatch(setLockedTotalBy(ele.type))
            // }
            if (ele?.question?.isCustom) {
                if (customTabs.length > 0) {
                    const customTabsRes = customTabs.filter((x) => x.accessor === ele.type);
                    if (customTabsRes.length > 0) {
                        customTabsObj = await getCustomTabsCards(patientFilterData, customTabsRes, totalFilterData, { ninetyDaysData, dynamicCards: [], diffDashboardPatients: [], pageType: PAGE_TYPE.OVERALL, forComparison, customCombineTabData });
                    }
                }
                objectCustom = { ...objectCustom, ...customTabsObj }

                patientFilterData = filterCustomPatientData(patientFilterData, cardFilter, ele);
            }
            if (ele.type === OVERALL_CARDS_TYPE.DOCTOR_DATA) {
                objectCustom.doctorData = await doctorData(patientFilterData, {
                    ...totalFilterData,
                    totalType: TOTAL_TYPE.MAIN,
                    totalForPercentage:
                        mainNumPercentageSelected && i === 1 ? mainNumPercentageSelected : totalFilterData.totalForPercentage,
                });

                patientFilterData = await _.filter(patientFilterData, ({ doctorId }) =>
                    _.every([_.includes(cardFilter.doctorData, doctorId)])
                );
            }

            if (ele.type === OVERALL_CARDS_TYPE.FLOORS_DATA) {
                objectCustom.floorsData = await floorsData(patientFilterData, {
                    ...totalFilterData,
                    totalType: TOTAL_TYPE.MAIN,
                    totalForPercentage:
                        mainNumPercentageSelected && i === 1 ? mainNumPercentageSelected : totalFilterData.totalForPercentage,
                });

                patientFilterData = await _.filter(patientFilterData, ({ floorId }) =>
                    _.every([_.includes(cardFilter.floorsData, floorId)])
                );
            }

            if (ele.type === OVERALL_CARDS_TYPE.INSURANCE_DATA) {
                objectCustom.insuranceData = await insuranceData(patientFilterData, {
                    ...totalFilterData,
                    totalType: TOTAL_TYPE.MAIN,
                    totalForPercentage:
                        mainNumPercentageSelected && i === 1 ? mainNumPercentageSelected : totalFilterData.totalForPercentage,
                });

                patientFilterData = await _.filter(patientFilterData, ({ insuranceId }) =>
                    _.every([_.includes(cardFilter.insuranceData, insuranceId)])
                );
            }

            if (ele.type === OVERALL_CARDS_TYPE.NINETY_DAYS_DATA) {
                objectCustom.ninetyDaysData = await ninetyDaysDataList(ninetyDaysData, patientFilterData, {
                    ...totalFilterData,
                    totalType: TOTAL_TYPE.MAIN,
                    totalForPercentage:
                        mainNumPercentageSelected && i === 1 ? mainNumPercentageSelected : totalFilterData.totalForPercentage,
                });

                patientFilterData = await ninetyDaysDataFilter(cardFilter.ninetyDaysData, patientFilterData, ninetyDaysData);
            }
            if (ele.type === lockedTotalBy) {
                lockedTotalModified = patientFilterData.length;
            }
            newSavedFilters = [...newSavedFilters, ele.type];
        }
        const totalPercentageCount = lockedTotalModified ? lockedTotalModified : patientFilterData.length; //transferType ? patientFilterData?.length : censusAverage

        if (!_.includes(newSavedFilters, OVERALL_CARDS_TYPE.INSURANCE_DATA)) {
            objectCustom.insuranceData = await insuranceData(patientFilterData, {
                ...totalFilterData,
                totalForPercentage: totalPercentageCount,
            });
        }
        if (!_.includes(newSavedFilters, OVERALL_CARDS_TYPE.FLOORS_DATA)) {
            objectCustom.floorsData = await floorsData(patientFilterData, {
                ...totalFilterData,
                totalForPercentage: totalPercentageCount,
            });
        }
        if (!_.includes(newSavedFilters, OVERALL_CARDS_TYPE.DOCTOR_DATA)) {
            objectCustom.doctorData = await doctorData(patientFilterData, {
                ...totalFilterData,
                totalForPercentage: totalPercentageCount,
            });
        }
        if (!_.includes(newSavedFilters, OVERALL_CARDS_TYPE.NINETY_DAYS_DATA)) {
            objectCustom.ninetyDaysData = await ninetyDaysDataList(ninetyDaysData, patientFilterData, {
                ...totalFilterData,
                totalForPercentage: totalPercentageCount,
            });
        }
    } else {
        totalFilterData.totalType = TOTAL_TYPE.MAIN;
        const totalCount = mainNumPercentageSelected
            ? mainNumPercentageSelected
            : transferType && transferType !== "total"
                ? patientFilterData.length
                : null;

        let totalCountPer = lockedTotalModified ? lockedTotalModified : transferType ? totalCount : censusAverage;
        totalFilterData.totalForPercentage = totalCountPer;
        objectCustom.insuranceData = await insuranceData(patientFilterData, totalFilterData);
        objectCustom.floorsData = await floorsData(patientFilterData, totalFilterData);
        objectCustom.doctorData = await doctorData(patientFilterData, totalFilterData);
        objectCustom.ninetyDaysData = await ninetyDaysDataList(ninetyDaysData, patientFilterData, totalFilterData);
        if (customTabs.length > 0) {
            customTabsObj = await getCustomTabsCards(patientFilterData, customTabs, totalFilterData, { ninetyDaysData, dynamicCards: [], pageType: PAGE_TYPE.OVERALL, diffDashboardPatients: [], forComparison, customCombineTabData });
        }

        objectCustom = { ...objectCustom, ...customTabsObj }
    }
    return objectCustom;
}

module.exports = {
    updateFilterListDataOverall
}