const mongoose = require('mongoose');
const Question = mongoose.model("question");

const seedQuestionsData = [{
    "label": "Hospital transfer type",
    "tableLabel": "Hospital transfer type",
    "accessor": "transferType",
    "type": "toggle",
    "options": [
        {
            "label": "Unplanned Hospital Transfer",
            "value": "unplannedHospitalTransfer"
        },
        {
            "label": "Planned Hospital Transfer",
            "value": "plannedHospitalTransfer"
        }
    ],
    "forType": "transfer",
    "forTransferType": "hospitalTransfer",
    "allowAfter": true,
    "active": true,
    "isEditable": false,
    "defaultOrder": -1,
    "isRequired": true
},
{
    "label": "Date of admission",
    "tableLabel": "Date",
    "accessor": "dateOfADT",
    "type": "date",
    "forType": "admission",
    "allowAfter": true,
    "active": true,
    "isEditable": false,
    "defaultOrder": 0,
    "isRequired": true
},
{
    "label": "Date of return",
    "tableLabel": "Date",
    "accessor": "dateOfADT",
    "type": "date",
    "forType": "return",
    "allowAfter": true,
    "active": true,
    "isEditable": false,
    "defaultOrder": 0,
    "isRequired": true
},
{
    "label": "Date of readmission",
    "tableLabel": "Date",
    "accessor": "dateOfADT",
    "type": "date",
    "forType": "readmission",
    "allowAfter": true,
    "active": true,
    "isEditable": false,
    "defaultOrder": 0,
    "isRequired": true
},
{
    "label": "Date of latest Admission / Readmission (Prior to transfer)",
    "tableLabel": "Date of latest Admission / Readmission (Prior to transfer)",
    "accessor": "dateOfLatestAdmission",
    "type": "date",
    "forType": "transfer",
    "forTransferType": "safeDischarge",
    "allowAfter": true,
    "active": true,
    "isEditable": false,
    "isRequired": true,
    "defaultOrder": 0,
    "dependsOn": "server"
},
{
    "label": "Date of latest Admission / Readmission (Prior to transfer)",
    "tableLabel": "Date of latest Admission / Readmission (Prior to transfer)",
    "accessor": "dateOfLatestAdmission",
    "type": "date",
    "forType": "transfer",
    "forTransferType": "SNF",
    "allowAfter": true,
    "active": true,
    "isEditable": false,
    "isRequired": true,
    "defaultOrder": 0,
    "dependsOn": "server"
},
{
    "label": "Date of latest Admission / Readmission (Prior to transfer)",
    "tableLabel": "Date of latest Admission / Readmission (Prior to transfer)",
    "accessor": "dateOfLatestAdmission",
    "type": "date",
    "forType": "transfer",
    "forTransferType": "AMA",
    "allowAfter": true,
    "active": true,
    "isEditable": false,
    "isRequired": true,
    "defaultOrder": 0,
    "dependsOn": "server"
},
{
    "label": "Date of latest Admission / Readmission (Prior to transfer)",
    "tableLabel": "Date of latest Admission / Readmission (Prior to transfer)",
    "accessor": "dateOfLatestAdmission",
    "type": "date",
    "forType": "transfer",
    "forTransferType": "deceased",
    "allowAfter": true,
    "active": true,
    "isEditable": false,
    "isRequired": true,
    "defaultOrder": 0,
    "dependsOn": "server"
},
{
    "label": "Date of latest Admission / Readmission (Prior to transfer)",
    "tableLabel": "Date of latest Admission / Readmission (Prior to transfer)",
    "accessor": "dateOfLatestAdmission",
    "type": "date",
    "forType": "transfer",
    "forTransferType": "hospitalTransfer",
    "allowAfter": true,
    "active": true,
    "isEditable": false,
    "isRequired": true,
    "defaultOrder": 0,
    "dependsOn": "server"
},
{
    "label": "Date of transfer",
    "tableLabel": "Date",
    "accessor": "dateOfADT",
    "type": "date",
    "forType": "transfer",
    "forTransferType": "deceased",
    "allowAfter": true,
    "active": true,
    "isEditable": false,
    "defaultOrder": 1,
    "isRequired": true
},
{
    "label": "What Unit/Floor was resident upon death?",
    "tableLabel": "What Unit/Floor was resident upon death?",
    "accessor": "unit",
    "type": "validation",
    "forType": "transfer",
    "forTransferType": "deceased",
    "allowAfter": true,
    "active": true,
    "validationType": "unit",
    "multiple": false,
    "isEditable": false,
    "defaultOrder": 1,
    "isRequired": true
},
{
    "label": "Date of transfer",
    "tableLabel": "Date",
    "accessor": "dateOfADT",
    "type": "date",
    "forType": "transfer",
    "forTransferType": "safeDischarge",
    "allowAfter": true,
    "active": true,
    "isEditable": false,
    "defaultOrder": 1,
    "isRequired": true
},
{
    "label": "Unit upon admission",
    "tableLabel": "Unit",
    "accessor": "unit",
    "type": "validation",
    "forType": "readmission",
    "allowAfter": true,
    "active": true,
    "validationType": "unit",
    "isEditable": false,
    "defaultOrder": 1,
    "isRequired": true
},
{
    "label": "Date of transfer",
    "tableLabel": "Date",
    "accessor": "dateOfADT",
    "type": "date",
    "forType": "transfer",
    "forTransferType": "SNF",
    "allowAfter": true,
    "active": true,
    "isEditable": false,
    "defaultOrder": 1,
    "isRequired": true
},
{
    "label": "Unit upon admission",
    "tableLabel": "Unit",
    "accessor": "unit",
    "type": "validation",
    "forType": "admission",
    "allowAfter": true,
    "active": true,
    "validationType": "unit",
    "isEditable": false,
    "defaultOrder": 1,
    "isRequired": true
},
{
    "label": "Date of transfer",
    "tableLabel": "Date",
    "accessor": "dateOfADT",
    "type": "date",
    "forType": "transfer",
    "forTransferType": "AMA",
    "allowAfter": true,
    "active": true,
    "isEditable": false,
    "defaultOrder": 1,
    "isRequired": true
},
{
    "label": "What Unit/Floor was resident return from?",
    "tableLabel": "What Unit/Floor was resident return from?",
    "accessor": "unit",
    "type": "validation",
    "forType": "return",
    "allowAfter": true,
    "active": true,
    "validationType": "unit",
    "multiple": false,
    "isEditable": false,
    "defaultOrder": 1,
    "isRequired": true
},
{
    "label": "Date of transfer",
    "tableLabel": "Date",
    "accessor": "dateOfADT",
    "type": "date",
    "forType": "transfer",
    "forTransferType": "hospitalTransfer",
    "allowAfter": true,
    "active": true,
    "isEditable": false,
    "defaultOrder": 1,
    "isRequired": true
},
{
    "label": "What Unit/Floor was resident transferred from?",
    "tableLabel": "What Unit/Floor was resident transferred from?",
    "accessor": "unit",
    "type": "validation",
    "forType": "transfer",
    "forTransferType": "SNF",
    "allowAfter": true,
    "active": true,
    "validationType": "unit",
    "multiple": false,
    "isEditable": false,
    "defaultOrder": 2,
    "isRequired": true
},
{
    "label": "Hospital admitted from",
    "tableLabel": "Hospital",
    "accessor": "hospital",
    "type": "validation",
    "forType": "admission",
    "allowAfter": true,
    "active": true,
    "validationType": "hospital",
    "isEditable": false,
    "defaultOrder": 2,
    "isRequired": true,
    "validationBase": "account"
},
{
    "label": "Transferred to (Home / Assisted Living / Other)",
    "tableLabel": "Transferred to",
    "accessor": "transferTo",
    "type": "toggle",
    "options": [
        {
            "label": "Home",
            "value": "home"
        },
        {
            "label": "Assisted Living",
            "value": "assistedLiving"
        },
        {
            "label": "Other",
            "value": "other"
        }
    ],
    "forType": "transfer",
    "forTransferType": "safeDischarge",
    "allowAfter": true,
    "active": true,
    "isEditable": false,
    "defaultOrder": 2,
    "isRequired": true
},
{
    "label": "Resident doctor upon death",
    "tableLabel": "Resident doctor upon death",
    "accessor": "doctor",
    "type": "validation",
    "forType": "transfer",
    "forTransferType": "deceased",
    "allowAfter": true,
    "active": true,
    "validationType": "doctor",
    "multiple": false,
    "isEditable": false,
    "defaultOrder": 2,
    "isRequired": true,
    "validationBase": "account"
},
{
    "label": "Resident doctor upon transfer",
    "tableLabel": "Resident doctor upon transfer",
    "accessor": "doctor",
    "type": "validation",
    "forType": "transfer",
    "forTransferType": "SNF",
    "allowAfter": true,
    "active": true,
    "validationType": "doctor",
    "multiple": false,
    "isEditable": false,
    "defaultOrder": 2,
    "isRequired": true,
    "validationBase": "account"
},
{
    "label": "Transfer To which assisted living?",
    "tableLabel": "Transfer To which assisted living?",
    "accessor": "transferToWhichAssistedLiving",
    "type": "validation",
    "forType": "transfer",
    "forTransferType": "safeDischarge",
    "allowAfter": true,
    "active": true,
    "validationType": "alf",
    "multiple": false,
    "isEditable": false,
    "dependsOn": "transferTo",
    "dependsOnValues": [
        "assistedLiving"
    ],
    "defaultOrder": 2,
    "isRequired": true,
    "validationBase": "account"
},
{
    "label": "Hospital admitted from",
    "tableLabel": "Hospital",
    "accessor": "hospital",
    "type": "validation",
    "forType": "readmission",
    "allowAfter": true,
    "active": true,
    "validationType": "hospital",
    "isEditable": false,
    "defaultOrder": 2,
    "isRequired": true,
    "validationBase": "account"
},
{
    "label": "What Unit/Floor was resident transferred from?",
    "tableLabel": "What Unit/Floor was resident transferred from?",
    "accessor": "unit",
    "type": "validation",
    "forType": "transfer",
    "forTransferType": "AMA",
    "allowAfter": true,
    "active": true,
    "validationType": "unit",
    "multiple": false,
    "isEditable": false,
    "defaultOrder": 2,
    "isRequired": true
},
{
    "label": "Resident doctor upon transfer",
    "tableLabel": "Resident doctor upon transfer",
    "accessor": "doctor",
    "type": "validation",
    "forType": "transfer",
    "forTransferType": "AMA",
    "allowAfter": true,
    "active": true,
    "validationType": "doctor",
    "multiple": false,
    "isEditable": false,
    "defaultOrder": 2,
    "isRequired": true,
    "validationBase": "account"
},
{
    "label": "Resident doctor upon return",
    "tableLabel": "Resident doctor upon return",
    "accessor": "doctor",
    "type": "validation",
    "forType": "return",
    "allowAfter": true,
    "active": true,
    "validationType": "doctor",
    "multiple": false,
    "isEditable": false,
    "defaultOrder": 2,
    "isRequired": true,
    "validationBase": "account"
},
{
    "label": "What Time was resident transferred",
    "tableLabel": "What Time was resident transferred",
    "accessor": "transferTime",
    "type": "time",
    "forType": "transfer",
    "forTransferType": "hospitalTransfer",
    "allowAfter": true,
    "active": true,
    "isEditable": false,
    "defaultOrder": 2,
    "isRequired": true
},
{
    "label": "What Unit/Floor was resident transferred from?",
    "tableLabel": "Floor",
    "accessor": "unit",
    "type": "validation",
    "forType": "transfer",
    "forTransferType": "hospitalTransfer",
    "allowAfter": true,
    "active": true,
    "validationType": "unit",
    "multiple": false,
    "isEditable": false,
    "defaultOrder": 2,
    "isRequired": true
},
{
    "label": "Which hospital is the resident being transferred to?",
    "tableLabel": "Which hospital is the resident being transferred to?",
    "accessor": "hospital",
    "type": "validation",
    "forType": "transfer",
    "forTransferType": "hospitalTransfer",
    "allowAfter": true,
    "active": true,
    "validationType": "hospital",
    "multiple": false,
    "isEditable": false,
    "defaultOrder": 2,
    "isRequired": true,
    "validationBase": "account"
},
{
    "label": "Resident doctor upon admission",
    "tableLabel": "Resident doctor",
    "accessor": "doctor",
    "type": "validation",
    "forType": "admission",
    "active": true,
    "validationType": "doctor",
    "isEditable": false,
    "defaultOrder": 3,
    "isRequired": true,
    "validationBase": "account"
},
{
    "label": "Payer source/Insurance Upon Return",
    "tableLabel": "Payer source/Insurance",
    "accessor": "payerSourceInsurance",
    "type": "validation",
    "forType": "return",
    "allowAfter": true,
    "active": true,
    "validationType": "insurance",
    "multiple": false,
    "isEditable": false,
    "defaultOrder": 3,
    "isRequired": true,
    "validationBase": "account"
},
{
    "label": "Resident doctor upon admission",
    "tableLabel": "Resident doctor",
    "accessor": "doctor",
    "type": "validation",
    "forType": "readmission",
    "active": true,
    "validationType": "doctor",
    "isEditable": false,
    "defaultOrder": 3,
    "isRequired": true,
    "validationBase": "account"
},
{
    "label": "Payer source / Insurance",
    "tableLabel": "Payer source / Insurance",
    "accessor": "insurance",
    "type": "validation",
    "forType": "transfer",
    "forTransferType": "AMA",
    "allowAfter": true,
    "active": true,
    "validationType": "insurance",
    "multiple": false,
    "isEditable": false,
    "defaultOrder": 3,
    "isRequired": true,
    "validationBase": "account"
},
{
    "label": "Payer source / Insurance",
    "tableLabel": "Payer source / Insurance",
    "accessor": "insurance",
    "type": "validation",
    "forType": "transfer",
    "forTransferType": "deceased",
    "allowAfter": true,
    "active": true,
    "validationType": "insurance",
    "multiple": false,
    "isEditable": false,
    "defaultOrder": 3,
    "isRequired": true,
    "validationBase": "account"
},
{
    "label": "Transferred to which Facility",
    "tableLabel": "Transferred to which Facility",
    "accessor": "snf",
    "type": "validation",
    "forType": "transfer",
    "forTransferType": "SNF",
    "allowAfter": true,
    "active": true,
    "validationType": "snf",
    "multiple": false,
    "isEditable": false,
    "defaultOrder": 3,
    "isRequired": true,
    "validationBase": "account"
},
{
    "label": "What Unit/Floor was resident discharged from?",
    "tableLabel": "What Unit/Floor was resident discharged from?",
    "accessor": "unit",
    "type": "validation",
    "forType": "transfer",
    "forTransferType": "safeDischarge",
    "allowAfter": true,
    "active": true,
    "validationType": "unit",
    "multiple": false,
    "isEditable": false,
    "defaultOrder": 3,
    "isRequired": true
},
{
    "label": "Notes",
    "tableLabel": "Notes",
    "accessor": "notes",
    "type": "text",
    "forType": "return",
    "allowAfter": true,
    "active": true,
    "isEditable": false,
    "defaultOrder": 4,
    "isRequired": false
},
{
    "label": "Payer source / Insurance",
    "tableLabel": "Payer source / Insurance",
    "accessor": "insurance",
    "type": "validation",
    "forType": "transfer",
    "forTransferType": "SNF",
    "allowAfter": true,
    "active": true,
    "validationType": "insurance",
    "multiple": false,
    "isEditable": false,
    "defaultOrder": 4,
    "isRequired": true,
    "validationBase": "account"
},
{
    "label": "Resident doctor upon discharge",
    "tableLabel": "Resident doctor upon discharge",
    "accessor": "doctor",
    "type": "validation",
    "forType": "transfer",
    "forTransferType": "safeDischarge",
    "allowAfter": true,
    "active": true,
    "validationType": "doctor",
    "multiple": false,
    "isEditable": false,
    "defaultOrder": 4,
    "isRequired": true,
    "validationBase": "account"
},
{
    "label": "Notes",
    "tableLabel": "Notes",
    "accessor": "notes",
    "type": "text",
    "forType": "transfer",
    "forTransferType": "AMA",
    "allowAfter": true,
    "active": true,
    "isEditable": false,
    "defaultOrder": 4,
    "isRequired": false
},
{
    "label": "Notes",
    "tableLabel": "Notes",
    "accessor": "notes",
    "type": "text",
    "forType": "transfer",
    "forTransferType": "deceased",
    "allowAfter": true,
    "active": true,
    "isEditable": false,
    "defaultOrder": 4,
    "isRequired": false
},
{
    "label": "Dx Upon Admission",
    "tableLabel": "Dx",
    "accessor": "dx",
    "type": "validation",
    "forType": "admission",
    "allowAfter": true,
    "active": true,
    "validationType": "dx",
    "multiple": true,
    "isEditable": false,
    "defaultOrder": 4,
    "isRequired": true,
    "validationBase": "account"
},
{
    "label": "Dx Upon Admission",
    "tableLabel": "Dx",
    "accessor": "dx",
    "type": "validation",
    "forType": "readmission",
    "allowAfter": true,
    "active": true,
    "validationType": "dx",
    "multiple": true,
    "isEditable": false,
    "defaultOrder": 4,
    "isRequired": true,
    "validationBase": "account"
},
{
    "label": "Reason for transfer per dx",
    "tableLabel": "Dx",
    "accessor": "dx",
    "type": "validation",
    "forType": "transfer",
    "forTransferType": "hospitalTransfer",
    "allowAfter": true,
    "active": true,
    "validationType": "dx",
    "multiple": true,
    "isEditable": false,
    "defaultOrder": 4,
    "isRequired": true,
    "validationBase": "account"
},
{
    "label": "Notes",
    "tableLabel": "Notes",
    "accessor": "notes",
    "type": "text",
    "forType": "transfer",
    "forTransferType": "SNF",
    "allowAfter": true,
    "active": true,
    "isEditable": false,
    "defaultOrder": 5,
    "isRequired": false
},
{
    "label": "Payer source / Insurance",
    "tableLabel": "Payer source / Insurance",
    "accessor": "insurance",
    "type": "validation",
    "forType": "transfer",
    "forTransferType": "safeDischarge",
    "allowAfter": true,
    "active": true,
    "validationType": "insurance",
    "multiple": false,
    "isEditable": false,
    "defaultOrder": 5,
    "isRequired": true,
    "validationBase": "account"
},
{
    "label": "Payer source / Insurance Upon Admission",
    "tableLabel": "Payer source/Insurance",
    "accessor": "payerSourceInsurance",
    "type": "validation",
    "forType": "readmission",
    "allowAfter": true,
    "active": true,
    "validationType": "insurance",
    "multiple": false,
    "isEditable": false,
    "defaultOrder": 5,
    "isRequired": true,
    "validationBase": "account"
},
{
    "label": "Payer source / Insurance Upon Admission",
    "tableLabel": "Payer source/Insurance",
    "accessor": "payerSourceInsurance",
    "type": "validation",
    "forType": "admission",
    "allowAfter": true,
    "active": true,
    "validationType": "insurance",
    "multiple": false,
    "isEditable": false,
    "defaultOrder": 5,
    "isRequired": true,
    "validationBase": "account"
},
{
    "label": "Resident doctor upon transfer",
    "tableLabel": "Resident doctor upon transfer",
    "accessor": "doctor",
    "type": "validation",
    "forType": "transfer",
    "forTransferType": "hospitalTransfer",
    "allowAfter": true,
    "active": true,
    "validationType": "doctor",
    "multiple": false,
    "isEditable": false,
    "defaultOrder": 5,
    "isRequired": true,
    "validationBase": "account"
},
{
    "label": "Notes",
    "tableLabel": "Notes",
    "accessor": "notes",
    "type": "text",
    "forType": "admission",
    "allowAfter": true,
    "active": true,
    "isEditable": false,
    "defaultOrder": 6,
    "isRequired": false
},
{
    "label": "Notes",
    "tableLabel": "Notes",
    "accessor": "notes",
    "type": "text",
    "forType": "readmission",
    "allowAfter": true,
    "active": true,
    "isEditable": false,
    "defaultOrder": 6,
    "isRequired": false
},
{
    "label": "Notes",
    "tableLabel": "Notes",
    "accessor": "notes",
    "type": "text",
    "forType": "transfer",
    "forTransferType": "safeDischarge",
    "allowAfter": true,
    "active": true,
    "isEditable": false,
    "defaultOrder": 6,
    "isRequired": false
},
{
    "label": "Resident Nurse (that initiated resident hospital transfer)",
    "tableLabel": "Resident Nurse (that initiated resident hospital transfer)",
    "accessor": "nurse",
    "type": "validation",
    "forType": "transfer",
    "forTransferType": "hospitalTransfer",
    "allowAfter": true,
    "active": true,
    "validationType": "nurse",
    "multiple": false,
    "isEditable": false,
    "defaultOrder": 6,
    "isRequired": true
},
{
    "label": "Payer source / Insurance",
    "tableLabel": "Payer source / Insurance",
    "accessor": "insurance",
    "type": "validation",
    "forType": "transfer",
    "forTransferType": "hospitalTransfer",
    "allowAfter": true,
    "active": true,
    "validationType": "insurance",
    "multiple": false,
    "isEditable": false,
    "defaultOrder": 7,
    "isRequired": true,
    "validationBase": "account"
},
{
    "label": "Notes",
    "tableLabel": "Notes",
    "accessor": "notes",
    "type": "text",
    "forType": "transfer",
    "forTransferType": "hospitalTransfer",
    "allowAfter": true,
    "active": true,
    "isEditable": false,
    "defaultOrder": 8,
    "isRequired": false
},
{
    "label": "Resident admitted to hospital",
    "tableLabel": "Resident admitted to hospital",
    "accessor": "wasAdmitted",
    "type": "toggle",
    "options": [
        {
            "label": "Yes",
            "value": true
        },
        {
            "label": "No",
            "value": false
        }
    ],
    "forType": "transfer",
    "forTransferType": "hospitalTransfer",
    "allowAfter": true,
    "active": true,
    "isEditable": false,
    "defaultOrder": 9,
    "isRequired": true
}];

const seedQuestions = async (facilityId) => {
    try {
        if (!facilityId) {
            throw new Error("Facility ID is required to seed questions.");
        }

        const questionsWithFacilityId = seedQuestionsData.map(question => ({
            ...question,
            facilityId
        }));

        const questionCollection = Question.collection;

        const questions = await questionCollection.insertMany(questionsWithFacilityId);
        console.log('✅ Questions seeded successfully.');
        return questions;
    } catch (err) {
        console.error('❌ Error seeding questions:', err.message);
        throw err;
    }
};

module.exports = {
    seedQuestionsData,
    seedQuestions,
};
