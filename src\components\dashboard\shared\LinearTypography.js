import * as React from "react";
import { Box, Typography } from "@mui/material";

const LinearTypography = ({ total, background = "-webkit-linear-gradient(45deg, #14AAE9 0%, #8B47DA 100%)" }) => {
    return (
        <Box sx={{ background: "#fff", padding: "0px 10px", borderRadius:"4px" }}>
            <Typography style={{
                fontSize: 20,
                fontWeight: 900,
                background: background,
                WebkitBackgroundClip: "text",
                WebkitTextFillColor: "transparent"
            }}>
                {total}
            </Typography>
        </Box>
    );
};

export default LinearTypography;
