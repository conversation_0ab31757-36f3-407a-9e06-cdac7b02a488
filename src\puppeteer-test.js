// Testing Puppeteer

const puppeteer = require("puppeteer");

const test = async () => {
  try {

    const browser = await puppeteer.launch({
      headless: "new",
      args: [
        "--disable-gpu",
        "--disable-dev-shm-usage",
        "--disable-setuid-sandbox",
        "--no-sandbox",
      ],
    });

    const page = await browser.newPage();
    await page.setContent(
      "<html><body><h1>Testing</h1><p>Something to test</p></body></html>"
    );
    const buffer = await page.pdf({ format: "A4" });
    const base64 = buffer.toString("base64");
    await page.close();
    await browser.close();

  } catch (error) {
    console.error(error);
  }
};

module.exports = test;
