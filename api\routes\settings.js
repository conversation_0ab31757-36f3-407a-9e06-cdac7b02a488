const { createSetting, getSettings, deleteSetting, createEndDateOfADT, getEndDateOfADT, createShortcutSetting, getShortcutSettings } = require("../helpers/settings");
const authWithRole = require("../middleware/auth-with-role");

const route = require("express").Router();

// Create new account
route.post("/", authWithRole("manageDashboard"), async (req, res) => {
  try {
    let account = await createSetting(req);
    res.send(account);
  } catch (error) {
    res.status(500).send({ message: error.message });
  }
});



// Get accounts/account by ID
route.get("/:id?", authWithRole("manageDashboard"), async (req, res) => {
  try {
    let account = await getSettings(req);
    res.send(account);
  } catch (error) {
    res.status(500).send(error);
  }
});

// Create new shortcut settings by user
route.post("/shortcut", authWithRole("manageDashboard"), async (req, res) => {
  try {
    let sortCutSettings = await createShortcutSetting(req);
    res.send(sortCutSettings);
  } catch (error) {
    res.status(500).send({ message: error.message });
  }
});

// get shortcut settings by user
route.get("/shortcut/data", authWithRole("manageDashboard"), async (req, res) => {
  try {
    let sortCutSettings = await getShortcutSettings(req);
    res.send(sortCutSettings);
  } catch (error) {
    res.status(500).send(error);
  }
});


route.delete("/:id", authWithRole("manageDashboard"), async (req, res) => {
  try {
    const filter = await deleteSetting(req.params.id);
    res.send(filter);
  } catch (error) {
    res.status(500).send(error);
  }
});

route.post("/adt/end-date-of-adt", authWithRole("manageDashboard"), async (req, res) => {
  try {
    let account = await createEndDateOfADT(req);
    res.send(account);
  } catch (error) {
    res.status(500).send({ message: error.message });
  }
});

route.get("/adt/end-date-of-adt", authWithRole("manageDashboard"), async (req, res) => {
  try {    
    let endDateOfADT = await getEndDateOfADT(req);
    res.send(endDateOfADT);
  } catch (error) {
    res.status(500).send({ message: error.message });
  }
});


module.exports = route;
