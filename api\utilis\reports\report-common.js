const {
    TOTAL_TYPE,
    DECEASED_CARDS_TYPE,
    TYPES,
    HOSPITAL_CARDS_TYPE,
    CO_TRANSFER_CARDS_TYPE,
    TRANSFER_TYPE,
    ADMISSION_CARDS_TYPE,
    ADT_TABLE_TYPE,
    OVERALL_CARDS_TYPE,
    ACCOUNT_PERCENTAGE_BY,
    QUESTION_INPUT_TYPE,
    NUMBER_RANGE_TYPE,
    PAGE_TYPE,
    COMMUNITY_CARD_LABELS,
    DECEASED_CARDS_LABELS,
    ADMISSION_CARDS_LABELS,
    CUSTOM_TAB_TYPE,
    CUSTOM_TAB_PAGE_TYPE
} = require("../../../types/common.type");
const _ = require("lodash");
const { filterListDataItems, updateListTotalValue, itemPercentage } = require("../common");
const { ninetyDaysDataFilter } = require("../../helpers/hospital");
const { ADT_TYPES } = require("../../../types");
const { HOSPITAL_CARDS_LABELS } = require("../../../types/hospital.type");
const { HOURLY_TIME_RANGES, TIME_RANGE_TYPE } = require("../../../types/question.type");

async function processFilterForCombineTab({
    filter,
    patientData,
    ninetyDaysData,
    result,
    customTab,
    sixtyDaysData,
    pageType,
    customCombineTabData = [],
    forComparison,
    totalFilterData,
}) {
    const filters = customTab?.filters ?? [];
    const customKey = customTab?.accessor;
    let totalForPercentage = totalFilterData?.totalForPercentage;

    let resultData = [{
        _id: customKey,
        id: customKey,
        label: customTab?.label,
        name: customTab?.label,
        total: 0,
        value: 0,
        totalByPage: {},
        parentCard: customTab?.page,
        patientIds: [],
        percentage: 0
    }];



    for (const filter of filters) {
        let filterDashboard = filter?.dashboard;

        if (filterDashboard === pageType || (pageType === PAGE_TYPE.OVERALL && (filterDashboard === CUSTOM_TAB_PAGE_TYPE.OVERALL_INCOMING || filterDashboard === CUSTOM_TAB_PAGE_TYPE.OVERALL_OUTGOING))) {
            for (const filterItem of filter?.filters) {
                const { card, items = [] } = filterItem;
                if (!card?.value) continue;

                if (card?.questionId) {
                    let objectCustomRes = {};

                    const question = filterItem?.question;
                    const key = card?.value;

                    await processDynamicCard(question, patientData, objectCustomRes, { ...totalFilterData });

                    const resultItems = objectCustomRes?.[key] || [];

                    const patientIds = resultItems.reduce((acc, ele) => {
                        if (_.includes(items, ele?._id)) {
                            acc.push(...(ele?.patientIds || []));
                        }
                        return acc;
                    }, []);

                    patientData = await filterCustomPatientData(
                        patientData,
                        { [key]: items },
                        {
                            type: key,
                            patientIds,
                            question: { customQuestionInputType: question.customQuestionInputType },
                        },
                        true
                    );
                } else {
                    patientData = await applyFilter(card?.value, items, patientData, ninetyDaysData, sixtyDaysData, forComparison);
                }
            }

            const total = patientData?.length || 0;
            const percentageTotal = totalForPercentage || total;
            resultData[0].total += total;
            resultData[0].value += total;
            resultData[0].patientIds = patientData?.map(item => String(item._id));
            resultData[0].totalByPage = {
                ...resultData[0].totalByPage,
                [filterDashboard]: {
                    total,
                    percentage: await itemPercentage(total, percentageTotal, "number")
                }
            };
        }
        if (filterDashboard !== pageType && !(pageType === PAGE_TYPE.OVERALL && (filterDashboard === CUSTOM_TAB_PAGE_TYPE.OVERALL_INCOMING || filterDashboard === CUSTOM_TAB_PAGE_TYPE.OVERALL_OUTGOING))) {
            let dashboardData = customCombineTabData[filterDashboard]?.list ?? [];
            const dashboardNinetyDays = customCombineTabData[filterDashboard]?.ninetyDaysData ?? [];
            const dashboardSixtyDays = customCombineTabData[filterDashboard]?.sixtyDaysData ?? [];

            let objectCustomRes = {};

            for (const filterItem of filter?.filters) {
                const { card, items = [] } = filterItem;

                if (!card?.value) continue;



                if (card?.questionId) {
                    const question = filterItem?.question;
                    const key = card?.value;

                    if (!question) continue;

                    await processDynamicCard(question, dashboardData, objectCustomRes, { ...totalFilterData });

                    const resultItems = objectCustomRes?.[key] || [];

                    const patientIds = resultItems.reduce((acc, ele) => {
                        if (_.includes(items, ele?._id)) {
                            acc.push(...(ele?.patientIds || []));
                        }
                        return acc;
                    }, []);
                    dashboardData = filterCustomPatientData(
                        dashboardData,
                        { [key]: items },
                        {
                            type: key,
                            patientIds,
                            question: { customQuestionInputType: question.customQuestionInputType },
                        },
                        true
                    );
                } else {
                    dashboardData = await applyFilter(card?.value, items, dashboardData, dashboardNinetyDays, dashboardSixtyDays, forComparison);
                }
            }

            const total = dashboardData?.length || 0;

            const percentageTotal = totalForPercentage || total;
            resultData[0].total += total;
            resultData[0].value += total;
            resultData[0].totalByPage = {
                ...resultData[0].totalByPage,
                [filterDashboard]: {
                    total,
                    percentage: await itemPercentage(total, percentageTotal, "number")
                }
            };
        }

    }
    if (resultData.length > 0) {
        resultData[0].percentage = await itemPercentage(resultData[0].value, totalForPercentage || resultData[0].total, "number");
    }
    result[customKey] = resultData;

    return patientData;
}

/**
 * Helper function to validate time range configuration for alerts
 *
 * @param {string} timeRangeType - Either 'hourly' or 'custom'
 * @param {Array} customQuestionOptions - Array of custom time range options
 * @param {string} accessor - Field name being processed
 * @returns {boolean} - True if configuration is valid
 *
 * Example usage:
 *
 * For hourly ranges:
 * validateTimeRangeConfig('hourly', [], 'transferTime')
 *
 * For custom ranges:
 * validateTimeRangeConfig('custom', [
 *   { label: 'Morning', startTime: '06:00', endTime: '12:00' },
 *   { label: 'Afternoon', startTime: '12:00', endTime: '18:00' },
 *   { label: 'Evening', startTime: '18:00', endTime: '06:00' }
 * ], 'transferTime')
 */
const validateTimeRangeConfig = (timeRangeType, customQuestionOptions, accessor) => {
    if (timeRangeType === TIME_RANGE_TYPE.HOURLY) {
        console.log(`📊 Using hourly time ranges (${HOURLY_TIME_RANGES.length} ranges) for ${accessor}`);
        return true;
    } else if (timeRangeType === 'custom' && Array.isArray(customQuestionOptions) && customQuestionOptions.length > 0) {
        console.log(`📊 Using custom time ranges (${customQuestionOptions.length} ranges) for ${accessor}`);
        // Validate each custom range has required fields
        const validRanges = customQuestionOptions.every(range =>
            range.label && range.startTime && range.endTime
        );
        if (!validRanges) {
            console.warn(`⚠️  Some custom time ranges missing required fields (label, startTime, endTime) for ${accessor}`);
            return false;
        }
        return true;
    } else {
        console.warn(`⚠️  Invalid time range configuration for ${accessor}: timeRangeType=${timeRangeType}, customOptions=${customQuestionOptions?.length || 0}`);
        return false;
    }
};

const processDynamicCardAlerts = async (item, patientFilterData, objectCustomRes) => {
    const { customQuestionInputType, accessor, validationOptions, customQuestionOptions = [], timeRangeType } = item;

    switch (customQuestionInputType) {
        case QUESTION_INPUT_TYPE.UNLIMITED_ANSWERS:
            objectCustomRes[accessor] = await calculateTotalResponses(patientFilterData, accessor, validationOptions, { isFilterType: "alert" });
            break;

        case QUESTION_INPUT_TYPE.LIMITED_ANSWERS:
            objectCustomRes[accessor] = await calculateTotalResponses(patientFilterData, accessor, customQuestionOptions, { isFilterType: "alert" });
            break;

        case QUESTION_INPUT_TYPE.NUMBER_RANGE:
            objectCustomRes[accessor] = await calculateTotalResponsesNumberRange(patientFilterData, accessor, customQuestionOptions, { isFilterType: "alert" });
            break;

        case QUESTION_INPUT_TYPE.TIME_TAB_RANGE:
            console.log(`🕐 Processing time range filter for alerts - accessor: ${accessor}, timeRangeType: ${timeRangeType}`);

            // Validate configuration before processing
            if (!validateTimeRangeConfig(timeRangeType, customQuestionOptions, accessor)) {
                console.error(`❌ Skipping time range processing for ${accessor} due to invalid configuration`);
                objectCustomRes[accessor] = [];
                break;
            }

            try {
                objectCustomRes[accessor] = await calculateTotalResponsesTimeRange(
                    patientFilterData,
                    accessor,
                    customQuestionOptions,
                    { isFilterType: "alert" },
                    timeRangeType
                );
                console.log(`✅ Time range filter completed for ${accessor} - found ${objectCustomRes[accessor]?.length || 0} time ranges`);

                // Log summary of results for debugging
                if (objectCustomRes[accessor]?.length > 0) {
                    const totalPatients = objectCustomRes[accessor].reduce((sum, range) => sum + (range.total || 0), 0);
                    console.log(`📈 Alert time range summary for ${accessor}: ${totalPatients} total patients across ${objectCustomRes[accessor].length} time ranges`);
                }
            } catch (error) {
                console.error(`❌ Error processing time range filter for alerts (${accessor}):`, error);
                objectCustomRes[accessor] = [];
            }
            break;

        case QUESTION_INPUT_TYPE.NUMBER_RANGE_LIMITED_ANSWERS:
            objectCustomRes[accessor] = await calculateTotalRangeLimitedResponses(patientFilterData, accessor, customQuestionOptions, { isFilterType: "alert" });
            break;

        default:
            console.log(`⚠️  Invalid customQuestionInputType for alerts: ${customQuestionInputType}`);
            break;
    }
}

const dynamicCardFilterAlerts = async (patientFilterData, dynamicCards, res, page) => {
    let objectCustomRes = {};
    for (const item of dynamicCards) {

        if (item.isCustomTab) {
            let resArr = res;
            if (page !== PAGE_TYPE.HOSPITAL) {
                resArr = res?.data ?? [];
            }
            const { ninetyDaysData = [], diffDashboardPatients = [], sixtyDaysData = [], customCombineTabData = [] } = resArr;

            const result = await getCustomTabsCards(
                patientFilterData,
                [item],
                {},
                { ninetyDaysData, dynamicCards, diffDashboardPatients, pageType: page, sixtyDaysData, customCombineTabData }
            );

            objectCustomRes = { ...objectCustomRes, ...result };
        } else {
            await processDynamicCardAlerts(item, patientFilterData, objectCustomRes);
        }
    }

    return objectCustomRes;
}


const floorsData = async (data, totalFilter) => {
    //FloorsData Cal
    let unitDBDataGroup = [];
    if (data && data.length > 0) {
        const floorDBData = await _.groupBy(data, "floorId");
        const originalData = _.groupBy(totalFilter.originalData, "floorId");
        let percentageTotal = totalFilter?.totalForPercentage ? totalFilter.totalForPercentage : data?.length;

        if (floorDBData) {
            unitDBDataGroup = await filterListDataItems(floorDBData, "unit", percentageTotal, {
                ...totalFilter,
                originalData,
            });
        }
    }
    return unitDBDataGroup;
}

const insuranceData = async (data, totalFilter) => {
    //insuranceData Cal
    let insuranceDBDataGroup = [];
    if (data && data.length > 0) {
        const insuranceDBData = _.groupBy(data, "insuranceId");
        const originalData = _.groupBy(totalFilter.originalData, "insuranceId");
        let percentageTotal = totalFilter?.totalForPercentage ? totalFilter.totalForPercentage : data?.length;

        if (insuranceDBData) {
            insuranceDBDataGroup = await filterListDataItems(insuranceDBData, "insurance", percentageTotal, {
                ...totalFilter,
                originalData,
            });
        }
    }

    return insuranceDBDataGroup;
}

const doctorData = async (data, totalFilter) => {
    if (!data?.length) return [];

    const doctorDBData = _.groupBy(data, "doctorId");
    const originalData = _.groupBy(totalFilter?.originalData || [], "doctorId");
    const percentageTotal = totalFilter?.totalForPercentage || data.length;

    return doctorDBData
        ? await filterListDataItems(doctorDBData, "doctor", percentageTotal, {
            ...totalFilter,
            originalData
        })
        : [];
};

const ninetyDaysDataList = async (data, patients, totalFilter) => {
    if (!patients?.length) return [];

    const patientIds = patients.map((item) => item.id);
    const percentageTotal = totalFilter?.totalForPercentage || patients.length;

    return await updateListTotalValue(data, patientIds, "value", percentageTotal, totalFilter);
};

const filterData = async (arr, callback) => {
    const fail = Symbol();
    return (await Promise.all(arr.map(async (item) => ((await callback(item)) ? item : fail)))).filter((i) => i !== fail);
};

const hospitalizationData = async (data, totalFilter) => {
    let newHospitalizationsTotal = 0;
    let reHospitalizationsTotal = 0;
    let newHospitalizationsIds = [];
    let reHospitalizationsIds = [];

    let newHospitalizationsRefIds = [];
    let reHospitalizationsRefIds = [];

    const { originalData = [], totalType = null, totalForPercentage, isOtherDashboard = false } = totalFilter;

    const originalDataGroupBy = _.countBy(originalData, "reHospitalization");
    if (data && data.length > 0) {
        await filterData(data, async (ele) => {
            if (ele.reHospitalization) {
                reHospitalizationsTotal++;
                reHospitalizationsIds.push(ele.id);
                if (isOtherDashboard) {
                    reHospitalizationsRefIds.push(ele.refPatientId);
                }
            } else {
                newHospitalizationsTotal++;
                newHospitalizationsIds.push(ele.id);
                if (isOtherDashboard) {
                    newHospitalizationsRefIds.push(ele.refPatientId);
                }
            }
        });
    }

    let percentageTotal = totalForPercentage ? totalForPercentage : data?.length || 0;
    const hospitalizationsDBTTotal = [
        {
            _id: "newHospitalizations",
            label: "New Hospitalizations",
            color: "#00BAEB",
            total: newHospitalizationsTotal,
            originalTotal: originalDataGroupBy?.[false],
            isTooltip: totalType && totalType === TOTAL_TYPE.MAIN ? false : true,
            ids: newHospitalizationsIds,
            percentage: await itemPercentage(newHospitalizationsTotal, percentageTotal, "number"),
            patientIds: isOtherDashboard ? newHospitalizationsRefIds : newHospitalizationsIds,
            ...(isOtherDashboard && { otherDashboardIds: newHospitalizationsIds }),
        },
        {
            _id: "reHospitalizations",
            label: "Re-Hospitalizations",
            color: "#5195DD",
            total: reHospitalizationsTotal,
            ids: reHospitalizationsIds,
            originalTotal: originalDataGroupBy?.[true],
            isTooltip: totalType && totalType === TOTAL_TYPE.MAIN ? false : true,
            percentage: await itemPercentage(reHospitalizationsTotal, percentageTotal, "number"),
            patientIds: isOtherDashboard ? reHospitalizationsRefIds : reHospitalizationsIds,
            ...(isOtherDashboard && { otherDashboardIds: reHospitalizationsIds }),
        },
    ];
    return hospitalizationsDBTTotal;
};

const dcErData = async (data, totalFilter) => {
    if (!Array.isArray(data) || data.length === 0) {
        // console.warn("Invalid or empty data array.");
        return [];
    }

    const { originalData = [], totalType = null, totalForPercentage, isOtherDashboard = false } = totalFilter ?? {};

    // Ensure totalForPercentage is valid
    const percentageTotal = totalForPercentage || data.length;

    // Use countBy to group by "wasAdmitted" values
    const DCERResult = _.countBy(data, "wasAdmitted");
    const originalDataGroupBy = _.countBy(originalData, "wasAdmitted");
    const patientIdsByAdmissionStatus = _.groupBy(data, "wasAdmitted");

    // Common function to generate chart details
    const createChartDetail = async (id, label, color, admitted) => ({
        id,
        label,
        _id: id,
        total: DCERResult[admitted] || 0,
        originalTotal: originalDataGroupBy[admitted] || 0,
        isTooltip: totalType !== TOTAL_TYPE.MAIN,
        color,
        percentage: await itemPercentage(DCERResult[admitted], percentageTotal, "number"),
        isDefaultData: !DCERResult[admitted],
        patientIds: (patientIdsByAdmissionStatus[admitted] || []).map(item => isOtherDashboard ? item.refPatientId : String(item._id)),
        ...isOtherDashboard && { otherDashboardIds: patientIdsByAdmissionStatus[admitted]?.map(item => String(item._id)) }
    });

    // Generate chart details
    return [
        await createChartDetail("DC", "DC", "#606BDF", true),
        await createChartDetail("ER", "ER", "#87BCFE", false)
    ];
}

const returnsDataFilter = (oldFilter) => oldFilter?.returnsData?.[0] === "Returned";

const hospitalizationsFilter = (oldFilter) => oldFilter?.hospitalizations?.[0] === "reHospitalizations";

const DCERDataFilter = (oldFilter) => oldFilter?.DCERData?.[0] === "DC";

const returnsData = async (data, totalFilter) => {
    if (!data?.length) return [];

    const { originalData = [], totalType = null, totalForPercentage, isOtherDashboard = false } = totalFilter;
    const percentageTotal = totalForPercentage || data.length;

    const dintReturnResult = _.countBy(data, "wasReturned");

    const patientIdsByReturnStatus = _.groupBy(data, "wasReturned");

    const originalDataGroupBy = _.countBy(originalData, "wasReturned");

    const createDetail = async (id, label, key) => ({
        id,
        label,
        _id: id,
        total: dintReturnResult[key] || 0,
        patientIds: (patientIdsByReturnStatus[key] || []).map(item => isOtherDashboard ? item.refPatientId : String(item._id)),
        ...isOtherDashboard && { otherDashboardIds: patientIdsByReturnStatus[key]?.map(item => String(item._id)) },
        originalTotal: originalDataGroupBy[key] || 0,
        isTooltip: totalType !== TOTAL_TYPE.MAIN,
        percentage: await itemPercentage(dintReturnResult[key] || 0, percentageTotal, "number"),
    });

    const returnDidNotDetails = await Promise.all([
        createDetail("Returned", "Returned", "true"),
        createDetail("Didn't Return", "Didn't Return", "false")
    ]);

    return returnDidNotDetails;
};

const daysData = async (data, totalFilter) => {
    let totalData = data;
    const { originalData = [], totalType = null, totalForPercentage, isOtherDashboard = false } = totalFilter;
    const originalDataGroupBy = _.countBy(originalData, "day");
    const dayDBData = _.countBy(totalData, "day");
    let percentageTotal = totalForPercentage ? totalForPercentage : data?.length;
    const patientIdsByDayStatus = _.groupBy(totalData, "day");

    const daysDataArr = [
        {
            _id: "Sun",
            label: "Sun",
            value: dayDBData?.Sun || 0,
            originalTotal: originalDataGroupBy?.Sun || 0,
            isTooltip: totalType && totalType === TOTAL_TYPE.MAIN ? false : true,
            color: "#FFECA6",
            percentage: await itemPercentage(dayDBData?.Sun || 0, percentageTotal, "number"),
            patientIds: (patientIdsByDayStatus?.Sun?.map(item => isOtherDashboard ? item.refPatientId : String(item._id)) || []),
            ...isOtherDashboard && { otherDashboardIds: patientIdsByDayStatus?.Sun?.map(item => String(item._id)) }
        },
        {
            _id: "Mon",
            label: "Mon",
            value: dayDBData?.Mon || 0,
            originalTotal: originalDataGroupBy?.Mon || 0,
            isTooltip: totalType && totalType === TOTAL_TYPE.MAIN ? false : true,
            color: "#FFECA6",
            percentage: await itemPercentage(dayDBData?.Mon || 0, percentageTotal, "number"),
            patientIds: (patientIdsByDayStatus?.Mon?.map(item => isOtherDashboard ? item.refPatientId : String(item._id)) || []),
            ...isOtherDashboard && { otherDashboardIds: patientIdsByDayStatus?.Mon?.map(item => String(item._id)) }
        },
        {
            _id: "Tue",
            label: "Tue",
            value: dayDBData?.Tue || 0,
            originalTotal: originalDataGroupBy?.Tue || 0,
            isTooltip: totalType && totalType === TOTAL_TYPE.MAIN ? false : true,
            color: "#FFECA6",
            percentage: await itemPercentage(dayDBData?.Tue || 0, percentageTotal, "number"),
            patientIds: (patientIdsByDayStatus?.Tue?.map(item => isOtherDashboard ? item.refPatientId : String(item._id)) || []),
            ...isOtherDashboard && { otherDashboardIds: patientIdsByDayStatus?.Tue?.map(item => String(item._id)) }
        },
        {
            _id: "Wed",
            label: "Wed",
            value: dayDBData?.Wed || 0,
            originalTotal: originalDataGroupBy?.Wed || 0,
            isTooltip: totalType && totalType === TOTAL_TYPE.MAIN ? false : true,
            color: "#FFECA6",
            percentage: await itemPercentage(dayDBData?.Wed || 0, percentageTotal, "number"),
            patientIds: (patientIdsByDayStatus?.Wed?.map(item => isOtherDashboard ? item.refPatientId : String(item._id)) || []),
            ...isOtherDashboard && { otherDashboardIds: patientIdsByDayStatus?.Wed?.map(item => String(item._id)) }
        },
        {
            _id: "Thu",
            label: "Thu",
            value: dayDBData?.Thu || 0,
            originalTotal: originalDataGroupBy?.Thu || 0,
            isTooltip: totalType && totalType === TOTAL_TYPE.MAIN ? false : true,
            color: "#FFECA6",
            percentage: await itemPercentage(dayDBData?.Thu || 0, percentageTotal, "number"),
            patientIds: (patientIdsByDayStatus?.Thu?.map(item => isOtherDashboard ? item.refPatientId : String(item._id)) || []),
            ...isOtherDashboard && { otherDashboardIds: patientIdsByDayStatus?.Thu?.map(item => String(item._id)) }
        },
        {
            _id: "Fri",
            label: "Fri",
            value: dayDBData?.Fri || 0,
            originalTotal: originalDataGroupBy?.Fri || 0,
            isTooltip: totalType && totalType === TOTAL_TYPE.MAIN ? false : true,
            color: "#FFECA6",
            percentage: await itemPercentage(dayDBData?.Fri || 0, percentageTotal, "number"),
            patientIds: (patientIdsByDayStatus?.Fri?.map(item => isOtherDashboard ? item.refPatientId : String(item._id)) || []),
            ...isOtherDashboard && { otherDashboardIds: patientIdsByDayStatus?.Fri?.map(item => String(item._id)) }
        },
        {
            _id: "Sat",
            label: "Sat",
            value: dayDBData?.Sat || 0,
            originalTotal: originalDataGroupBy?.Sat || 0,
            isTooltip: totalType && totalType === TOTAL_TYPE.MAIN ? false : true,
            color: "#FFECA6",
            percentage: await itemPercentage(dayDBData?.Sat || 0, percentageTotal, "number"),
            patientIds: (patientIdsByDayStatus?.Sat?.map(item => isOtherDashboard ? item.refPatientId : String(item._id)) || []),
            ...isOtherDashboard && { otherDashboardIds: patientIdsByDayStatus?.Sat?.map(item => String(item._id)) }
        },
    ];

    return daysDataArr;
}

const dxData = async (data, totalFilter) => {
    let totalData = data;
    const { originalData = [], totalType = null, totalForPercentage } = totalFilter;

    let dxDataArr = [];
    let patientData = [];
    let patientOriginalData = [];
    let dxOriginalDataArr = [];
    let patientDxIds = [];
    if (totalData && totalData.length > 0) {
        // eslint-disable-next-line array-callback-return
        totalData.filter((ele) => {
            if (ele.dx && ele.dx.length > 0) {
                dxDataArr = [...ele.dx, ...dxDataArr];
                patientData.push(ele);
                patientDxIds = [...ele.dxIds, ...patientDxIds];
            }
        });

        originalData.filter((ele) => {
            if (ele.dx && ele.dx.length > 0) {
                dxOriginalDataArr = [...ele.dx, ...dxOriginalDataArr];
                patientOriginalData.push(ele);
                // patientDxIds = [...ele.dxIds, ...patientDxIds];
            }
        });
    }
    let dxDataDB = [];
    let percentageTotal = totalForPercentage ? totalForPercentage : totalData?.length;

    if (dxDataArr.length > 0) {
        const dxDataGroup = _.groupBy(dxDataArr, "_id");
        const dxOriginalDataGroup = _.groupBy(dxOriginalDataArr, "_id");
        if (dxDataGroup) {
            for (const [key, value] of Object.entries(dxDataGroup)) {
                let patientRes = patientData.filter((item) => {
                    if (_.includes(item.dxIds, key)) {
                        return item;
                    }
                });
                let patientResOriginal = patientOriginalData.filter((item) => {
                    if (_.includes(item.dxIds, key)) {
                        return item;
                    }
                });
                const valueArr = value[0];
                if (key && valueArr) {
                    let object = Object();
                    object._id = key;
                    object.id = key;
                    object.label = valueArr.label;
                    object.name = valueArr.label;
                    object.total = value.length;
                    let original = dxOriginalDataGroup[key] ? dxOriginalDataGroup[key]?.length : 0;
                    object.originalTotal = original;
                    object.isTooltip = totalType && totalType === TOTAL_TYPE.MAIN ? false : true;
                    object.chartData = await dcErData(patientRes, { ...totalFilter, originalData: patientResOriginal });
                    object.percentage = await itemPercentage(value.length || 0, percentageTotal || 0, "number");
                    dxDataDB.push(object);
                }
            }
        }
        dxDataDB = _.orderBy(dxDataDB, "total", "desc");
    }
    return dxDataDB;
}

const shiftData = async (data, totalFilter) => {
    let totalData = data;
    const { originalData = [], totalType = null, totalForPercentage, isOtherDashboard = false } = totalFilter;

    let percentageTotal = totalForPercentage ? totalForPercentage : data?.length;

    const shiftDBData = _.countBy(totalData, "shiftName");
    const originalDataGroupBy = _.countBy(originalData, "shiftName");
    const patientIdsByShiftStatus = _.groupBy(totalData, "shiftName");

    const shiftDataDefault = [
        {
            _id: "Morning",
            label: "Morning",
            value: shiftDBData?.Morning || 0,
            originalTotal: originalDataGroupBy?.Morning || 0,
            isTooltip: totalType && totalType === TOTAL_TYPE.MAIN ? false : true,
            color: "#636578",
            percentage: await itemPercentage(shiftDBData?.Morning || 0, percentageTotal, "number"),
            patientIds: (patientIdsByShiftStatus?.Morning?.map(item => isOtherDashboard ? item.refPatientId : String(item._id)) || []),
            ...isOtherDashboard && { otherDashboardIds: patientIdsByShiftStatus?.Morning?.map(item => String(item._id)) }
        },
        {
            _id: "Evening",
            label: "Evening",
            value: shiftDBData?.Evening || 0,
            originalTotal: originalDataGroupBy?.Evening || 0,
            isTooltip: totalType && totalType === TOTAL_TYPE.MAIN ? false : true,
            color: "#4879f5",
            percentage: await itemPercentage(shiftDBData?.Evening || 0, percentageTotal, "number"),
            patientIds: (patientIdsByShiftStatus?.Evening?.map(item => isOtherDashboard ? item.refPatientId : String(item._id)) || []),
            ...isOtherDashboard && { otherDashboardIds: patientIdsByShiftStatus?.Evening?.map(item => String(item._id)) }
        },
        {
            _id: "Night",
            label: "Night",
            value: shiftDBData?.Night || 0,
            originalTotal: originalDataGroupBy?.Night || 0,
            isTooltip: totalType && totalType === TOTAL_TYPE.MAIN ? false : true,
            color: "#636578",
            percentage: await itemPercentage(shiftDBData?.Night || 0, percentageTotal, "number"),
            patientIds: (patientIdsByShiftStatus?.Night?.map(item => isOtherDashboard ? item.refPatientId : String(item._id)) || []),
            ...isOtherDashboard && { otherDashboardIds: patientIdsByShiftStatus?.Night?.map(item => String(item._id)) }
        },
    ];
    return shiftDataDefault;
}

const nurseData = async (data, totalFilter) => {
    //nurseData list update Cal

    let nurseDataGroup = [];
    if (data && data.length > 0) {
        let totalData = data;
        // totalData = _.filter(totalData, { transferType: ADT_SUB_TYPES.UNPLANNED_HOSPITAL_TRANSFER });
        const floorDBData = _.groupBy(totalData, "nurseId");
        const originalData = _.groupBy(totalFilter.originalData, "nurseId");
        let percentageTotal = totalFilter?.totalForPercentage ? totalFilter.totalForPercentage : data?.length;

        if (floorDBData) {
            nurseDataGroup = await filterListDataItems(floorDBData, "nurse", percentageTotal, {
                ...totalFilter,
                originalData,
            });
        }
    }
    return nurseDataGroup;
}

const customHospitalData = async (data, totalFilter) => {
    //nurseData list update Cal

    let nurseDataGroup = [];
    if (data && data.length > 0) {
        let totalData = data;
        const floorDBData = _.groupBy(totalData, "hospitalId");
        const originalData = _.groupBy(totalFilter.originalData, "hospitalId");
        let percentageTotal = totalFilter?.totalForPercentage ? totalFilter.totalForPercentage : data?.length;

        if (floorDBData) {
            nurseDataGroup = await filterListDataItems(floorDBData, "hospital", percentageTotal, {
                ...totalFilter,
                originalData,
            });
        }
    }
    return nurseDataGroup;
}

const hospitalDataList = (data, patients, totalFilter) => {
    const { originalData = [], totalType = null, totalForPercentage } = totalFilter;

    const hospitalIdStrIds = patients?.length > 0 ? patients.map((item) => item.hospitalIdStr) : [];
    const patientIds = patients?.length > 0 ? patients.map((item) => item.id) : [];
    const patientIdsMain = originalData.length > 0 ? originalData.map((item) => item.id) : [];
    let patientData = _.filter(data, ({ _id }) => _.every([_.includes(hospitalIdStrIds, _id)]));

    let totalLength = totalForPercentage ? totalForPercentage : patients?.length || 0;

    let newHospitalData = [];
    patientData.filter(async (ele) => {
        const intersection = matchedArray(patientIds, ele.ids);
        const intersectionMain = matchedArray(patientIdsMain, ele.ids);
        let patientDataDidNot = _.filter(patients, ({ id }) => _.every([_.includes(intersection, id)]));
        const wasReturnedCount = _.countBy(patientDataDidNot, "wasReturned");
        const totalEle = _.find(ele.graphData, { id: "Total Transfer" });
        const chartArrData = [];
        chartArrData.push(
            {
                id: totalEle.id,
                data: [
                    {
                        x: totalEle?.id,
                        y: intersection?.length || 0,
                        color: "#15BDB2",
                    },
                ],
            },
            {
                id: "Total DidNot Return",
                data: [
                    {
                        x: "Total DidNot Return",
                        y: wasReturnedCount.false || 0,
                        color: "#076673",
                    },
                ],
            }
        );
        let original = intersectionMain.length || 0;
        let obj = {
            ...ele,
            originalTotal: original,
            isTooltip: totalType && totalType === TOTAL_TYPE.MAIN ? false : true,
            totalTransfer: intersection.length || 0,
            total: intersection.length || 0,
            totalDidNotReturn: wasReturnedCount.false || 0,
            graphData: chartArrData,
            ids: intersection || [],
            percentage: await itemPercentage(intersection.length || 0, totalLength, "number"),
        };
        newHospitalData.push(obj);
        return Object.assign({}, ele, obj);
    });
    newHospitalData = _.orderBy(newHospitalData, "totalTransfer", "desc");
    return newHospitalData;
}

const sixtyDaysDataList = async (data, patients, totalFilter) => {
    const patientIds = patients?.length > 0 ? await patients.map((item) => item.id) : [];
    let percentageTotal = totalFilter?.totalForPercentage ? totalFilter.totalForPercentage : patients?.length;

    const res = await updateListTotalValue(data, patientIds, "value", percentageTotal, totalFilter);
    return res;
}

const sixtyDaysDataFilter = async (cardFilter, patientData, ninetyDaysData) => {
    let sixtyDaysDataIds = [];
    let sixtyDaysDataFilters = await _.filter(ninetyDaysData, ({ _id }) => _.every([_.includes(cardFilter, _id)]));
    if (sixtyDaysDataFilters && sixtyDaysDataFilters?.length > 0) {
        await sixtyDaysDataFilters.map((item) => (sixtyDaysDataIds = [...sixtyDaysDataIds, ...item.ids]));
    }
    patientData = await _.filter(patientData, async ({ _id }) => await _.every([_.includes(sixtyDaysDataIds, _id)]));
    return patientData;
}

const safeDischargeAssLivData = async (data, totalFilter) => {
    //SNF Facility Data Cal
    let safeDischargeAssLivDBDataGroup = [];
    if (data && data.length > 0) {
        const safeDischargeAssLivDBData = _.groupBy(data, "assistantLivId");
        const originalData = _.groupBy(totalFilter.originalData, "assistantLivId");
        let percentageTotal = totalFilter?.totalForPercentage ? totalFilter.totalForPercentage : data?.length;

        if (safeDischargeAssLivDBData) {
            safeDischargeAssLivDBDataGroup = await filterListDataItems(
                safeDischargeAssLivDBData,
                "transferToWhichAssistedLiving",
                percentageTotal,
                { ...totalFilter, originalData }
            );
        }
    }
    return safeDischargeAssLivDBDataGroup;
}

const snfFacilityData = async (data, totalFilter) => {
    //SNF Facility Data Cal
    let snfFacilityDBDataGroup = [];
    if (data && data.length > 0) {
        const totalData = _.filter(data, (item) => item.snfFacilityId);
        const snfFacilityDBData = _.groupBy(data, "snfFacilityId");
        const originalData = _.groupBy(totalFilter.originalData, "snfFacilityId");
        let percentageTotal = totalFilter?.totalForPercentage ? totalFilter.totalForPercentage : totalData?.length;

        if (snfFacilityDBData) {
            snfFacilityDBDataGroup = await filterListDataItems(snfFacilityDBData, "snf", percentageTotal, {
                ...totalFilter,
                originalData,
            });
        }
    }
    return snfFacilityDBDataGroup;
}

const communityTransferCustomAlertFilterListData = async (res, customFilter) => {
    const {
        cardFilter = {},
        transferType = null,
        cardType = null
    } = customFilter || {};

    const {
        priorityData = [],
        type = null
    } = cardFilter || {};

    let patientFilterData = res?.data?.list;

    let sixtyDaysData = res?.data?.sixtyDaysData;

    let diffDashboardPatients = res?.data?.diffDashboardPatients;

    let customCombineTabData = res?.data?.customCombineTabData ?? [];

    let customTabs = res?.data?.customTabs ?? [];

    let dynamicCards = res?.data?.dynamicCards ?? [];

    if (transferType && transferType.length > 0) {
        if (!_.includes(transferType, TRANSFER_TYPE.ALL)) {
            patientFilterData = patientFilterData
                .map((item) => {
                    if (_.includes(transferType, item.transferType)) {
                        return item;
                    }
                })
                .filter((item) => item);
        }
    }
    let objectCustom = Object();
    let dynamicCardsObj = Object();
    let customTabsObj = Object();

    if (priorityData.length > 0) {
        let i = 0;
        for await (const ele of priorityData) {
            i++;
            if (cardType === ele.type) {
                continue;
            }

            if (ele?.question?.isCustom) {
                if (customTabs.length > 0) {
                    const customTabsRes = customTabs.filter((x) => x.accessor === ele.type);
                    if (customTabsRes.length > 0) {
                        customTabsObj = await getCustomTabsCards(
                            patientFilterData,
                            customTabsRes,
                            null,
                            { sixtyDaysData, dynamicCards, diffDashboardPatients, pageType: PAGE_TYPE.COMMUNITY_TRANSFER, customCombineTabData });
                    }
                }
                objectCustom = { ...objectCustom, ...dynamicCardsObj, ...customTabsObj };

                patientFilterData = await filterCustomPatientData(patientFilterData, cardFilter, ele);
            }

            if (ele.type === CO_TRANSFER_CARDS_TYPE.INSURANCE_DATA) {
                patientFilterData = await _.filter(patientFilterData, ({ insuranceId }) =>
                    _.every([_.includes(cardFilter.insuranceData, insuranceId)])
                );
            }

            if (ele.type === CO_TRANSFER_CARDS_TYPE.SIXTY_DAYS_DATA) {
                if (i === priorityData.length) {
                    //updateFacilityPercentageTotal(patientFilterData)
                }
                patientFilterData = await sixtyDaysDataFilter(cardFilter.sixtyDaysData, patientFilterData, sixtyDaysData);
            }

            if (ele.type === CO_TRANSFER_CARDS_TYPE.RETURNS_DATA) {

                if (cardFilter.returnsData.length === 1) {
                    filter1.wasReturned = await returnsDataFilter(cardFilter);
                    if (!_.isEmpty(filter1)) {
                        patientFilterData = await _.filter(patientFilterData, filter1);
                    }
                }
            }

            if (ele.type === CO_TRANSFER_CARDS_TYPE.DOCTOR_DATA) {
                patientFilterData = await _.filter(patientFilterData, ({ doctorId }) =>
                    _.every([_.includes(cardFilter.doctorData, doctorId)])
                );
            }

            if (ele.type === CO_TRANSFER_CARDS_TYPE.FLOORS_DATA) {
                patientFilterData = await _.filter(patientFilterData, ({ floorId }) =>
                    _.every([_.includes(cardFilter.floorsData, floorId)])
                );
            }

            if (ele.type === CO_TRANSFER_CARDS_TYPE.SAFE_DISCHARGE_ASS_LIV_DATA) {
                patientFilterData = await _.filter(patientFilterData, ({ assistantLivId }) =>
                    _.every([_.includes(cardFilter.safeDischargeAssLivData, assistantLivId)])
                );
            }

            if (ele.type === CO_TRANSFER_CARDS_TYPE.SNF_FACILITY_DATA) {
                patientFilterData = await _.filter(patientFilterData, ({ snfFacilityId }) =>
                    _.every([_.includes(cardFilter.snfFacilityData, snfFacilityId)])
                );
            }
        }
    }
    return patientFilterData;
}

const communityUpdateFilterListData = async (cardFilter, res, priorityData = [], transferType, forComparison, lockedTotalBy, dynamicCards = [], isOnlyHospitalTabAccess = false) => {
    let patientList = res;
    let totals = res.totals;
    const { list, sixtyDaysData } = patientList;
    const {
        lockedTotal,
    } = totals;

    const censusAverage = isOnlyHospitalTabAccess ? totals?.bedCapacity : totals?.censusAverage;
    const customTabs = patientList?.customTabs ?? [];
    const diffDashboardPatients = patientList?.diffDashboardPatients ?? [];
    const customCombineTabData = patientList?.customCombineTabData ?? [];

    let objectCustom = Object();
    let customTabsObj = Object();
    let dynamicCardsObj = {};
    let newSavedFilters = [];
    let patientFilterData = list;
    let filter1 = Object();
    let mainNumPercentage = censusAverage;

    let totalFilterData = {
        originalData: patientFilterData,
        totalType: transferType.length > 0 ? TOTAL_TYPE.FILTER : TOTAL_TYPE.MAIN,
        totalForPercentage: censusAverage,
    };
    let lockedTotalModified = lockedTotal
    if (transferType.length > 0) {
        mainNumPercentage = null;
        if (!_.includes(transferType, TRANSFER_TYPE.ALL)) {
            patientFilterData = patientFilterData
                .map((item) => {
                    if (_.includes(transferType, item.transferType)) {
                        return item;
                    }
                })
                .filter((item) => item);
        }
        totalFilterData.totalForPercentage = patientFilterData.length;
    }
    if (priorityData.length > 0) {
        totalFilterData.totalForPercentage = null;
    }
    if (lockedTotalModified) {
        // if (priorityData.length > 1) {
        //   mainNumPercentage = null;
        // }
        totalFilterData.totalForPercentage = lockedTotalModified;
    }
    if (!forComparison) {
        if (lockedTotalBy) {
            if (lockedTotalBy === "transferType" || lockedTotalBy === "census") {
                lockedTotalModified = patientFilterData.length
                if (lockedTotalModified && lockedTotalBy !== "census") {
                    lockedTotalModified = null
                }
            } else {
                const lockedFilterRemoved = _.find(priorityData, { type: lockedTotalBy });
                lockedTotalModified = patientFilterData.length;
                if (!lockedFilterRemoved) {
                    lockedTotalModified = null
                }
            }
        }
    }


    if (priorityData.length > 0) {
        let i = 0;
        for await (const ele of priorityData) {
            i++;
            const totalCountData = mainNumPercentage && i === 1 ? mainNumPercentage : totalFilterData.totalForPercentage;

            if (ele?.question?.isCustom) {
                if (customTabs.length > 0) {

                    const customTabsRes = customTabs.filter((x) => x.accessor === ele.type);

                    if (customTabsRes.length > 0) {
                        customTabsObj = await getCustomTabsCards(patientFilterData, customTabsRes, totalFilterData, { sixtyDaysData, dynamicCards, diffDashboardPatients, pageType: PAGE_TYPE.COMMUNITY_TRANSFER, forComparison, customCombineTabData });
                    }
                }
                objectCustom = { ...objectCustom, ...dynamicCardsObj, ...customTabsObj }


                const dynamicItemData = await _.find(dynamicCards, { accessor: ele.type });

                if (dynamicItemData && !_.isEmpty(dynamicItemData)) {
                    let objectCustomResDynamic = {};
                    await processDynamicCard(dynamicItemData, patientFilterData, objectCustomResDynamic, {
                        ...totalFilterData,
                        totalForPercentage: totalCountData,
                    });
                    if (!_.isEmpty(objectCustomResDynamic)) {
                        objectCustom[dynamicItemData?.accessor] = objectCustomResDynamic?.[ele.type];
                    }
                }

                await assignMatchedPatientIds(ele, objectCustom);

                patientFilterData = await filterCustomPatientData(patientFilterData, cardFilter, ele);
            }
            if (ele.type === CO_TRANSFER_CARDS_TYPE.INSURANCE_DATA) {
                objectCustom.insuranceData = await insuranceData(patientFilterData, {
                    ...totalFilterData,
                    totalForPercentage: totalCountData,
                });

                patientFilterData = await _.filter(patientFilterData, ({ insuranceId }) =>
                    _.every([_.includes(cardFilter.insuranceData, insuranceId)])
                );
            }

            if (ele.type === CO_TRANSFER_CARDS_TYPE.SIXTY_DAYS_DATA) {
                objectCustom.sixtyDaysData = await sixtyDaysDataList(sixtyDaysData, patientFilterData, {
                    ...totalFilterData,
                    totalForPercentage: totalCountData,
                });
                if (i === priorityData.length) {
                    //updateFacilityPercentageTotal(patientFilterData)
                }
                patientFilterData = await sixtyDaysDataFilter(cardFilter.sixtyDaysData, patientFilterData, sixtyDaysData);
            }

            if (ele.type === CO_TRANSFER_CARDS_TYPE.RETURNS_DATA) {
                objectCustom.returnsData = await returnsData(patientFilterData, {
                    ...totalFilterData,
                    totalForPercentage: totalCountData,
                });

                if (cardFilter.returnsData.length === 1) {
                    filter1.wasReturned = await returnsDataFilter(cardFilter);
                    if (!_.isEmpty(filter1)) {
                        patientFilterData = await _.filter(patientFilterData, filter1);
                    }
                }
            }

            if (ele.type === CO_TRANSFER_CARDS_TYPE.DOCTOR_DATA) {
                objectCustom.doctorData = await doctorData(patientFilterData, {
                    ...totalFilterData,
                    totalForPercentage: totalCountData,
                });
                patientFilterData = await _.filter(patientFilterData, ({ doctorId }) =>
                    _.every([_.includes(cardFilter.doctorData, doctorId)])
                );
            }

            if (ele.type === CO_TRANSFER_CARDS_TYPE.FLOORS_DATA) {
                objectCustom.floorsData = await floorsData(patientFilterData, {
                    ...totalFilterData,
                    totalForPercentage: totalCountData,
                });
                patientFilterData = await _.filter(patientFilterData, ({ floorId }) =>
                    _.every([_.includes(cardFilter.floorsData, floorId)])
                );
            }

            if (ele.type === CO_TRANSFER_CARDS_TYPE.SAFE_DISCHARGE_ASS_LIV_DATA) {
                objectCustom.safeDischargeAssLivData = await safeDischargeAssLivData(patientFilterData, {
                    ...totalFilterData,
                    totalForPercentage: totalCountData,
                });

                patientFilterData = await _.filter(patientFilterData, ({ assistantLivId }) =>
                    _.every([_.includes(cardFilter.safeDischargeAssLivData, assistantLivId)])
                );
            }

            if (ele.type === CO_TRANSFER_CARDS_TYPE.SNF_FACILITY_DATA) {
                objectCustom.snfFacilityData = await snfFacilityData(patientFilterData, {
                    ...totalFilterData,
                    totalForPercentage: totalCountData,
                });

                patientFilterData = await _.filter(patientFilterData, ({ snfFacilityId }) =>
                    _.every([_.includes(cardFilter.snfFacilityData, snfFacilityId)])
                );
            }
            if (ele.type === lockedTotalBy) {
                lockedTotalModified = patientFilterData.length;
            }
            newSavedFilters = [...newSavedFilters, ele.type];

        }
        //store.dispatch(setFilterTotal(patientFilterData.length));
        const totalPercentageCount = lockedTotalModified ? lockedTotalModified : patientFilterData.length; //transferType ? patientFilterData?.length : censusAverage
        // store.dispatch(
        //     forComparison ? setFilterTotalComparison(totalPercentageCount) : setFilterTotal(totalPercentageCount)
        // );
        // Update array data

        if (!_.includes(newSavedFilters, CO_TRANSFER_CARDS_TYPE.INSURANCE_DATA)) {
            objectCustom.insuranceData = await insuranceData(patientFilterData, {
                ...totalFilterData,
                totalType: TOTAL_TYPE.FILTER,
                totalForPercentage: totalPercentageCount,
            });
        }
        if (!_.includes(newSavedFilters, CO_TRANSFER_CARDS_TYPE.SIXTY_DAYS_DATA)) {
            objectCustom.sixtyDaysData = await sixtyDaysDataList(sixtyDaysData, patientFilterData, {
                ...totalFilterData,
                totalType: TOTAL_TYPE.FILTER,
                totalForPercentage: totalPercentageCount,
            });
        }
        if (!_.includes(newSavedFilters, CO_TRANSFER_CARDS_TYPE.RETURNS_DATA)) {
            objectCustom.returnsData = await returnsData(patientFilterData, {
                ...totalFilterData,
                totalType: TOTAL_TYPE.FILTER,
                totalForPercentage: totalPercentageCount,
            });
        }
        if (!_.includes(newSavedFilters, CO_TRANSFER_CARDS_TYPE.DOCTOR_DATA)) {
            objectCustom.doctorData = await doctorData(patientFilterData, {
                ...totalFilterData,
                totalType: TOTAL_TYPE.FILTER,
                totalForPercentage: totalPercentageCount,
            });
        }
        if (!_.includes(newSavedFilters, CO_TRANSFER_CARDS_TYPE.FLOORS_DATA)) {
            objectCustom.floorsData = await floorsData(patientFilterData, {
                ...totalFilterData,
                totalType: TOTAL_TYPE.FILTER,
                totalForPercentage: totalPercentageCount,
            });
        }
        if (!_.includes(newSavedFilters, CO_TRANSFER_CARDS_TYPE.SAFE_DISCHARGE_ASS_LIV_DATA)) {
            objectCustom.safeDischargeAssLivData = await safeDischargeAssLivData(patientFilterData, {
                ...totalFilterData,
                totalType: TOTAL_TYPE.FILTER,
                totalForPercentage: totalPercentageCount,
            });
        }
        if (!_.includes(newSavedFilters, CO_TRANSFER_CARDS_TYPE.SNF_FACILITY_DATA)) {
            objectCustom.snfFacilityData = await snfFacilityData(patientFilterData, {
                ...totalFilterData,
                totalType: TOTAL_TYPE.FILTER,
                totalForPercentage: totalPercentageCount,
            });
        }

        if (dynamicCards.length > 0) {
            for (const item of dynamicCards) {
                let objectCustomRes = {};
                if (!_.includes(newSavedFilters, item?.accessor)) {
                    await processDynamicCard(item, patientFilterData, objectCustomRes, {
                        ...totalFilterData,
                        totalType: TOTAL_TYPE.FILTER,
                        totalForPercentage: totalPercentageCount,
                    });
                    if (!_.isEmpty(objectCustomRes)) {
                        objectCustom[item?.accessor] = objectCustomRes?.[item?.accessor];
                    }
                }
            }
        }
        if (customTabs.length > 0) {
            objectCustom = await buildCustomTabsObject({
                customTabs,
                patientFilterData,
                totalFilterData: { ...totalFilterData, totalType: TOTAL_TYPE.FILTER, totalForPercentage: totalPercentageCount },
                patientList,
                dynamicCards,
                newSavedFilters,
                objectCustom,
                diffDashboardPatients,
                pageType: PAGE_TYPE.COMMUNITY_TRANSFER
            });
        }
    } else {
        objectCustom.insuranceData = await insuranceData(patientFilterData, totalFilterData);
        objectCustom.sixtyDaysData = await sixtyDaysDataList(sixtyDaysData, patientFilterData, totalFilterData);
        objectCustom.returnsData = await returnsData(patientFilterData, totalFilterData);
        objectCustom.doctorData = await doctorData(patientFilterData, totalFilterData);
        objectCustom.floorsData = await floorsData(patientFilterData, totalFilterData);
        objectCustom.safeDischargeAssLivData = await safeDischargeAssLivData(patientFilterData, totalFilterData);
        objectCustom.snfFacilityData = await snfFacilityData(patientFilterData, totalFilterData);
        if (dynamicCards.length > 0) {
            dynamicCardsObj = await dynamicCardFilter(patientFilterData, dynamicCards, totalFilterData);
        }

        if (customTabs.length > 0) {
            customTabsObj = await getCustomTabsCards(patientFilterData, customTabs, totalFilterData, { sixtyDaysData, dynamicCards, pageType: PAGE_TYPE.COMMUNITY_TRANSFER, diffDashboardPatients, forComparison, customCombineTabData });
        }
        objectCustom = { ...objectCustom, ...dynamicCardsObj, ...customTabsObj }
    }
    return objectCustom;
}

const hospitalCustomAlertFilterListData = async (res, customFilter) => {

    const {
        cardFilter = {},
        transferType = null,
        cardType = null
    } = customFilter || {};

    const {
        priorityData = [],
        type = null
    } = cardFilter || {};

    const { list, ninetyDaysData, hospitalData: hospitalDBData, dynamicCards = [] } = res;
    let patientData = list;

    const customTabs = res?.customTabs ?? [];
    const diffDashboardPatients = res?.diffDashboardPatients ?? [];
    const customCombineTabData = res?.customCombineTabData ?? [];

    if (transferType === TYPES.PLANNED) {
        patientData = _.filter(patientData, {
            transferType: "plannedHospitalTransfer",
        });
    }
    if (transferType === TYPES.UNPLANNED) {
        patientData = _.filter(patientData, {
            transferType: "unplannedHospitalTransfer",
        });
    }

    let filter1 = Object();
    let patientFilterData = patientData;
    let objectCustom = Object();
    let dynamicCardsObj = {};
    let customTabsObj = {};

    if (priorityData?.length > 0) {
        for await (const ele of priorityData) {
            if (cardType === ele.type) {
                continue;
            }

            if (ele?.question?.isCustom) {
                if (customTabs.length > 0) {
                    const customTabsRes = customTabs.filter((x) => x.accessor === ele.type);
                    if (customTabsRes.length > 0) {
                        customTabsObj = await getCustomTabsCards(
                            patientFilterData,
                            customTabsRes,
                            null,
                            { ninetyDaysData, dynamicCards, diffDashboardPatients, pageType: PAGE_TYPE.HOSPITAL, customCombineTabData });
                    }
                }
                objectCustom = { ...objectCustom, ...dynamicCardsObj, ...customTabsObj }

                patientFilterData = await filterCustomPatientData(patientFilterData, cardFilter, ele);
            }

            if (ele.type === HOSPITAL_CARDS_TYPE.HOSPITALIZATIONS) {

                if (cardFilter.hospitalizations.length === 1) {
                    filter1.reHospitalization = await hospitalizationsFilter(cardFilter);
                    if (!_.isEmpty(filter1)) {
                        patientFilterData = await _.filter(patientFilterData, filter1);
                    }
                }
            }

            if (ele.type === HOSPITAL_CARDS_TYPE.DCER_DATA) {

                if (cardFilter.DCERData.length === 1) {
                    filter1.wasAdmitted = DCERDataFilter(cardFilter);
                    if (!_.isEmpty(filter1)) {
                        patientFilterData = _.filter(patientFilterData, filter1);
                    }
                }
            }

            if (ele.type === HOSPITAL_CARDS_TYPE.INSURANCE_DATA) {
                patientFilterData = _.filter(patientFilterData, ({ insuranceId }) => {
                    return _.every([_.includes(cardFilter.insuranceData, insuranceId)]);
                });
            }

            if (ele.type === HOSPITAL_CARDS_TYPE.RETURNS_DATA) {
                if (cardFilter.returnsData.length === 1) {
                    filter1.wasReturned = await returnsDataFilter(cardFilter);
                    if (!_.isEmpty(filter1)) {
                        patientFilterData = await _.filter(patientFilterData, filter1);
                    }
                }
            }

            if (ele.type === HOSPITAL_CARDS_TYPE.NINETY_DAYS_DATA) {
                patientFilterData = await ninetyDaysDataFilter(cardFilter.ninetyDaysData, patientFilterData, ninetyDaysData);
            }

            if (ele.type === HOSPITAL_CARDS_TYPE.FLOORS_DATA) {
                patientFilterData = await filterData(patientFilterData, ({ floorId }) =>
                    _.every([_.includes(cardFilter.floorsData, floorId)])
                );
            }

            if (ele.type === HOSPITAL_CARDS_TYPE.DOCTOR_DATA) {
                patientFilterData = await filterData(patientFilterData, ({ doctorId }) =>
                    _.every([_.includes(cardFilter.doctorData, doctorId)])
                );
            }

            if (ele.type === HOSPITAL_CARDS_TYPE.DAYS_DATA) {
                patientFilterData = await _.filter(patientFilterData, ({ day }) =>
                    _.every([_.includes(cardFilter.daysData, day)])
                );
            }

            if (ele.type === HOSPITAL_CARDS_TYPE.DX_DATA) {
                patientFilterData = await _.filter(patientFilterData, ({ dxIds }) => {
                    const matchedIds = _.intersection(cardFilter.dxData, dxIds);
                    return matchedIds.length > 0 ? true : false;
                });
            }

            if (ele.type === HOSPITAL_CARDS_TYPE.SHIFT_DATA) {
                patientFilterData = await _.filter(patientFilterData, ({ shiftName }) =>
                    _.every([_.includes(cardFilter.shiftData, shiftName)])
                );
            }

            if (ele.type === HOSPITAL_CARDS_TYPE.NURSE_DATA) {
                patientFilterData = await _.filter(patientFilterData, ({ nurseId }) =>
                    _.every([_.includes(cardFilter.nurseData, nurseId)])
                );
            }

            if (ele.type === HOSPITAL_CARDS_TYPE.HOSPITAL_DATA) {
                patientFilterData = await hospitalDataFilter(cardFilter.hospitalData, patientFilterData, hospitalDBData);
            }
        }
    }
    return patientFilterData;
}

async function assignMatchedPatientIds(ele, objectCustom) {
    if (!ele || !ele.selectedItems || !objectCustom) return;

    const selectedItemsData = objectCustom?.[ele.type];
    if (!Array.isArray(selectedItemsData) || selectedItemsData.length === 0) return;

    const matchedPatientIds = selectedItemsData
        .filter(item => ele.selectedItems.includes(String(item._id)))
        .flatMap(item => item.patientIds.map(id => String(id)));

    ele.patientIds = matchedPatientIds;
}

const hospitalUpdateFilterListData = async (
    cardFilter,
    transferType,
    res,
    priorityData = [],
    forComparison,
    lockedTotalBy,
    reportSubscription,
    dynamicCards,
    isOnlyHospitalTabAccess = false
) => {
    let totals = res.totals;
    let censusAverage = reportSubscription?.percentageBy && reportSubscription?.percentageBy === ACCOUNT_PERCENTAGE_BY.BED ? totals?.bedCapacity : totals?.censusAverage;

    if (isOnlyHospitalTabAccess) {
        censusAverage = isOnlyHospitalTabAccess ? totals?.bedCapacity : censusAverage;
    }
    const {
        lockedTotal,
    } = totals;
    const { list, ninetyDaysData, hospitalData: hospitalDBData } = res;
    const customTabs = res?.customTabs ?? [];
    const diffDashboardPatients = res?.diffDashboardPatients ?? [];
    const customCombineTabData = res?.customCombineTabData ?? [];

    let objectCustom = Object();
    let patientData = list;
    let lockedTotalModified = lockedTotal;
    let totalFilterData = {
        originalData: patientData,
        totalType: transferType ? TOTAL_TYPE.FILTER : TOTAL_TYPE.MAIN,
        totalForPercentage: censusAverage,
    };
    if (transferType === TYPES.PLANNED) {
        patientData = _.filter(patientData, {
            transferType: "plannedHospitalTransfer",
        });
    }
    if (transferType === TYPES.UNPLANNED) {
        patientData = _.filter(patientData, {
            transferType: "unplannedHospitalTransfer",
        });
    }
    // if (!lockedTotalModified && !forComparison) {
    //     store.dispatch(setLockedTotalBy(null));
    // }
    let filter1 = Object();
    let dynamicCardsObj = Object();
    let customTabsObj = Object();
    let newSavedFilters = [];
    let patientFilterData = patientData;
    let mainNumPercentage = censusAverage;
    if (transferType) {
        mainNumPercentage = null;
        totalFilterData.totalForPercentage = patientData.length;
    }
    if (priorityData.length > 0) {
        totalFilterData.totalForPercentage = null;
    }
    if (lockedTotalBy && !forComparison) {
        if (
            lockedTotalBy === TYPES.PLANNED ||
            lockedTotalBy === TYPES.UNPLANNED ||
            lockedTotalBy === TYPES.ALL ||
            lockedTotalBy === "census"
        ) {
            lockedTotalModified = patientFilterData.length;
            if (lockedTotalModified && !transferType && lockedTotalBy !== "census") {
                lockedTotalModified = null;
            }
        } else {
            const lockedFilterRemoved = _.find(priorityData, { type: lockedTotalBy });
            lockedTotalModified = patientFilterData.length;
            if (!lockedFilterRemoved) {
                lockedTotalModified = null;
            }
        }
    }
    if (lockedTotalModified && !forComparison) {
        totalFilterData.totalForPercentage = lockedTotalModified;
    }
    if (lockedTotalModified && priorityData.length === 1 && !transferType && !forComparison) {
        totalFilterData.totalForPercentage = lockedTotalModified;
    }

    if (priorityData.length > 0) {
        let i = 0;
        for await (const ele of priorityData) {
            i++;

            if (ele?.question?.isCustom) {

                if (customTabs.length > 0) {

                    const customTabsRes = customTabs.filter((x) => x.accessor === ele.type);

                    if (customTabsRes.length > 0) {
                        customTabsObj = await getCustomTabsCards(patientFilterData, customTabsRes, totalFilterData, { ninetyDaysData, dynamicCards, diffDashboardPatients, pageType: PAGE_TYPE.HOSPITAL, forComparison, customCombineTabData });
                    }
                }
                objectCustom = { ...objectCustom, ...dynamicCardsObj, ...customTabsObj }

                const dynamicItemData = await _.find(dynamicCards, { accessor: ele.type });

                if (dynamicItemData && !_.isEmpty(dynamicItemData)) {
                    let objectCustomResDynamic = {};
                    await processDynamicCard(dynamicItemData, patientFilterData, objectCustomResDynamic, {
                        ...totalFilterData,
                        totalForPercentage: mainNumPercentage && i === 1 ? mainNumPercentage : totalFilterData.totalForPercentage,
                    });
                    if (!_.isEmpty(objectCustomResDynamic)) {
                        objectCustom[dynamicItemData?.accessor] = objectCustomResDynamic?.[ele.type];
                    }
                }
                await assignMatchedPatientIds(ele, objectCustom);
                patientFilterData = await filterCustomPatientData(patientFilterData, cardFilter, ele);
            }

            if (ele.type === HOSPITAL_CARDS_TYPE.HOSPITALIZATIONS) {
                objectCustom.hospitalizations = await hospitalizationData(patientFilterData, {
                    ...totalFilterData,
                    totalForPercentage: mainNumPercentage && i === 1 ? mainNumPercentage : totalFilterData.totalForPercentage,
                });

                if (cardFilter.hospitalizations.length === 1) {
                    filter1.reHospitalization = await hospitalizationsFilter(cardFilter);
                    if (!_.isEmpty(filter1)) {
                        patientFilterData = await _.filter(patientFilterData, filter1);
                    }
                }
            }

            if (ele.type === HOSPITAL_CARDS_TYPE.DCER_DATA) {
                objectCustom.DCERData = await dcErData(patientFilterData, {
                    ...totalFilterData,
                    totalForPercentage: mainNumPercentage && i === 1 ? mainNumPercentage : totalFilterData.totalForPercentage,
                });

                if (cardFilter.DCERData.length === 1) {
                    filter1.wasAdmitted = DCERDataFilter(cardFilter);
                    if (!_.isEmpty(filter1)) {
                        patientFilterData = _.filter(patientFilterData, filter1);
                    }
                }
            }

            if (ele.type === HOSPITAL_CARDS_TYPE.INSURANCE_DATA) {
                objectCustom.insuranceData = await insuranceData(patientFilterData, {
                    ...totalFilterData,
                    totalForPercentage: mainNumPercentage && i === 1 ? mainNumPercentage : totalFilterData.totalForPercentage,
                });

                patientFilterData = _.filter(patientFilterData, ({ insuranceId }) => {
                    return _.every([_.includes(cardFilter.insuranceData, insuranceId)]);
                });
            }

            if (ele.type === HOSPITAL_CARDS_TYPE.RETURNS_DATA) {
                objectCustom.returnsData = await returnsData(patientFilterData, {
                    ...totalFilterData,
                    totalForPercentage: mainNumPercentage && i === 1 ? mainNumPercentage : totalFilterData.totalForPercentage,
                });

                if (cardFilter.returnsData.length === 1) {
                    filter1.wasReturned = await returnsDataFilter(cardFilter);
                    if (!_.isEmpty(filter1)) {
                        patientFilterData = await _.filter(patientFilterData, filter1);
                    }
                }
            }

            if (ele.type === HOSPITAL_CARDS_TYPE.NINETY_DAYS_DATA) {
                objectCustom.ninetyDaysData = await ninetyDaysDataList(ninetyDaysData, patientFilterData, {
                    ...totalFilterData,
                    totalForPercentage: mainNumPercentage && i === 1 ? mainNumPercentage : totalFilterData.totalForPercentage,
                });
                if (i === priorityData.length && !forComparison) {
                    //updateFacilityPercentageTotal(patientFilterData);
                }
                patientFilterData = await ninetyDaysDataFilter(cardFilter.ninetyDaysData, patientFilterData, ninetyDaysData);
            }

            if (ele.type === HOSPITAL_CARDS_TYPE.FLOORS_DATA) {
                objectCustom.floorsData = await floorsData(patientFilterData, {
                    ...totalFilterData,
                    totalForPercentage: mainNumPercentage && i === 1 ? mainNumPercentage : totalFilterData.totalForPercentage,
                });

                patientFilterData = await filterData(patientFilterData, ({ floorId }) =>
                    _.every([_.includes(cardFilter.floorsData, floorId)])
                );
            }

            if (ele.type === HOSPITAL_CARDS_TYPE.DOCTOR_DATA) {
                objectCustom.doctorData = await doctorData(patientFilterData, {
                    ...totalFilterData,
                    totalForPercentage: mainNumPercentage && i === 1 ? mainNumPercentage : totalFilterData.totalForPercentage,
                });
                patientFilterData = await filterData(patientFilterData, ({ doctorId }) =>
                    _.every([_.includes(cardFilter.doctorData, doctorId)])
                );
            }

            if (ele.type === HOSPITAL_CARDS_TYPE.DAYS_DATA) {
                objectCustom.daysData = await daysData(patientFilterData, {
                    ...totalFilterData,
                    totalForPercentage: mainNumPercentage && i === 1 ? mainNumPercentage : totalFilterData.totalForPercentage,
                });
                patientFilterData = await _.filter(patientFilterData, ({ day }) =>
                    _.every([_.includes(cardFilter.daysData, day)])
                );
            }

            if (ele.type === HOSPITAL_CARDS_TYPE.DX_DATA) {
                objectCustom.dxData = await dxData(patientFilterData, {
                    ...totalFilterData,
                    totalForPercentage: mainNumPercentage && i === 1 ? mainNumPercentage : totalFilterData.totalForPercentage,
                });
                patientFilterData = await _.filter(patientFilterData, ({ dxIds }) => {
                    const matchedIds = _.intersection(cardFilter.dxData, dxIds);
                    return matchedIds.length > 0 ? true : false;
                });
            }

            if (ele.type === HOSPITAL_CARDS_TYPE.SHIFT_DATA) {
                objectCustom.shiftData = await shiftData(patientFilterData, {
                    ...totalFilterData,
                    totalForPercentage: mainNumPercentage && i === 1 ? mainNumPercentage : totalFilterData.totalForPercentage,
                });
                patientFilterData = await _.filter(patientFilterData, ({ shiftName }) =>
                    _.every([_.includes(cardFilter.shiftData, shiftName)])
                );
            }

            if (ele.type === HOSPITAL_CARDS_TYPE.NURSE_DATA) {
                objectCustom.nurseData = await nurseData(patientFilterData, {
                    ...totalFilterData,
                    totalForPercentage: mainNumPercentage && i === 1 ? mainNumPercentage : totalFilterData.totalForPercentage,
                });
                patientFilterData = await _.filter(patientFilterData, ({ nurseId }) =>
                    _.every([_.includes(cardFilter.nurseData, nurseId)])
                );
            }

            if (ele.type === HOSPITAL_CARDS_TYPE.HOSPITAL_DATA) {
                objectCustom.hospitalData = await hospitalDataList(hospitalDBData, patientFilterData, {
                    ...totalFilterData,
                    totalForPercentage: mainNumPercentage && i === 1 ? mainNumPercentage : totalFilterData.totalForPercentage,
                });
                patientFilterData = await hospitalDataFilter(cardFilter.hospitalData, patientFilterData, hospitalDBData);
            }
            if (ele.type === lockedTotalBy) {
                lockedTotalModified = patientFilterData.length;
            }
            newSavedFilters = [...newSavedFilters, ele.type];
        }

        //UPdate remaining cards data
        const totalPercentageCount = lockedTotalModified ? lockedTotalModified : patientFilterData.length; //transferType ? patientFilterData?.length : censusAverage
        // store.dispatch(
        //     forComparison ? setFilterTotalComparison(totalPercentageCount) : setFilterTotal(totalPercentageCount)
        // );
        if (!_.includes(newSavedFilters, HOSPITAL_CARDS_TYPE.HOSPITALIZATIONS)) {
            objectCustom.hospitalizations = await hospitalizationData(patientFilterData, {
                ...totalFilterData,
                totalType: TOTAL_TYPE.FILTER,
                totalForPercentage: totalPercentageCount,
            });
        }
        if (!_.includes(newSavedFilters, HOSPITAL_CARDS_TYPE.DCER_DATA)) {
            objectCustom.DCERData = await dcErData(patientFilterData, {
                ...totalFilterData,
                totalType: TOTAL_TYPE.FILTER,
                totalForPercentage: totalPercentageCount,
            });
        }
        if (!_.includes(newSavedFilters, HOSPITAL_CARDS_TYPE.INSURANCE_DATA)) {
            objectCustom.insuranceData = await insuranceData(patientFilterData, {
                ...totalFilterData,
                totalType: TOTAL_TYPE.FILTER,
                totalForPercentage: totalPercentageCount,
            });
        }

        if (!_.includes(newSavedFilters, HOSPITAL_CARDS_TYPE.RETURNS_DATA)) {
            objectCustom.returnsData = await returnsData(patientFilterData, {
                ...totalFilterData,
                totalType: TOTAL_TYPE.FILTER,
                totalForPercentage: totalPercentageCount,
            });
        }
        if (!_.includes(newSavedFilters, HOSPITAL_CARDS_TYPE.NINETY_DAYS_DATA)) {
            objectCustom.ninetyDaysData = await ninetyDaysDataList(ninetyDaysData, patientFilterData, {
                ...totalFilterData,
                totalType: TOTAL_TYPE.FILTER,
                totalForPercentage: totalPercentageCount,
            });
        }
        if (!_.includes(newSavedFilters, HOSPITAL_CARDS_TYPE.FLOORS_DATA)) {
            objectCustom.floorsData = await floorsData(patientFilterData, {
                ...totalFilterData,
                totalType: TOTAL_TYPE.FILTER,
                totalForPercentage: totalPercentageCount,
            });
        }
        if (!_.includes(newSavedFilters, HOSPITAL_CARDS_TYPE.DOCTOR_DATA)) {
            objectCustom.doctorData = await doctorData(patientFilterData, {
                ...totalFilterData,
                totalType: TOTAL_TYPE.FILTER,
                totalForPercentage: totalPercentageCount,
            });
        }
        if (!_.includes(newSavedFilters, HOSPITAL_CARDS_TYPE.DAYS_DATA)) {
            objectCustom.daysData = await daysData(patientFilterData, {
                ...totalFilterData,
                totalType: TOTAL_TYPE.FILTER,
                totalForPercentage: totalPercentageCount,
            });
        }
        if (!_.includes(newSavedFilters, HOSPITAL_CARDS_TYPE.DX_DATA)) {
            objectCustom.dxData = await dxData(patientFilterData, {
                ...totalFilterData,
                totalType: TOTAL_TYPE.FILTER,
                totalForPercentage: totalPercentageCount,
            });
        }
        if (!_.includes(newSavedFilters, HOSPITAL_CARDS_TYPE.SHIFT_DATA)) {
            objectCustom.shiftData = await shiftData(patientFilterData, {
                ...totalFilterData,
                totalType: TOTAL_TYPE.FILTER,
                totalForPercentage: totalPercentageCount,
            });
        }
        if (!_.includes(newSavedFilters, HOSPITAL_CARDS_TYPE.NURSE_DATA)) {
            objectCustom.nurseData = await nurseData(patientFilterData, {
                ...totalFilterData,
                totalType: TOTAL_TYPE.FILTER,
                totalForPercentage: totalPercentageCount,
            });
        }
        if (!_.includes(newSavedFilters, HOSPITAL_CARDS_TYPE.HOSPITAL_DATA)) {
            objectCustom.hospitalData = await hospitalDataList(hospitalDBData, patientFilterData, {
                ...totalFilterData,
                totalType: TOTAL_TYPE.FILTER,
                totalForPercentage: totalPercentageCount,
            });
        }
        if (dynamicCards.length > 0) {
            for (const item of dynamicCards) {
                let objectCustomRes = {};
                if (!_.includes(newSavedFilters, item?.accessor)) {
                    await processDynamicCard(item, patientFilterData, objectCustomRes, {
                        ...totalFilterData,
                        totalType: TOTAL_TYPE.FILTER,
                        totalForPercentage: totalPercentageCount,
                    });
                    if (!_.isEmpty(objectCustomRes)) {
                        objectCustom[item?.accessor] = objectCustomRes?.[item?.accessor];
                    }
                }
            }
        }

        if (customTabs.length > 0) {
            let customTabsObjRes = {};
            const isCheckNewFilters = true;
            customTabsObjRes = await getCustomTabsCards(
                patientFilterData,
                customTabs,
                { ...totalFilterData, totalType: TOTAL_TYPE.FILTER, totalForPercentage: totalPercentageCount },
                { ninetyDaysData, dynamicCards, newSavedFilters, isCheckNewFilters, objectCustom, pageType: PAGE_TYPE.HOSPITAL, diffDashboardPatients, forComparison, customCombineTabData }
            );

            objectCustom = { ...objectCustom, ...customTabsObjRes };
        }

    } else {
        objectCustom.hospitalizations = await hospitalizationData(patientFilterData, totalFilterData);
        objectCustom.DCERData = await dcErData(patientFilterData, totalFilterData);
        objectCustom.insuranceData = await insuranceData(patientFilterData, totalFilterData);
        objectCustom.returnsData = await returnsData(patientFilterData, totalFilterData);
        objectCustom.ninetyDaysData = await ninetyDaysDataList(ninetyDaysData, patientFilterData, totalFilterData);
        objectCustom.floorsData = await floorsData(patientFilterData, totalFilterData);
        objectCustom.doctorData = await doctorData(patientFilterData, totalFilterData);
        objectCustom.daysData = await daysData(patientFilterData, totalFilterData);
        objectCustom.dxData = await dxData(patientFilterData, totalFilterData);
        objectCustom.shiftData = await shiftData(patientFilterData, totalFilterData);
        objectCustom.nurseData = await nurseData(patientFilterData, totalFilterData);
        objectCustom.hospitalData = await hospitalDataList(hospitalDBData, patientFilterData, totalFilterData);
        if (dynamicCards.length > 0) {
            dynamicCardsObj = await dynamicCardFilter(patientFilterData, dynamicCards, totalFilterData);
        }
        if (customTabs.length > 0) {
            customTabsObj = await getCustomTabsCards(patientFilterData, customTabs, totalFilterData, { ninetyDaysData, dynamicCards, pageType: PAGE_TYPE.HOSPITAL, diffDashboardPatients, forComparison, customCombineTabData });
        }
        objectCustom = { ...objectCustom, ...dynamicCardsObj, ...customTabsObj }
    }
    return objectCustom;
}

const deceasedCustomAlertFilterListData = async (res, customFilter) => {
    const {
        cardFilter = {},
        transferType = null,
        cardType = null
    } = customFilter || {};

    const {
        priorityData = [],
        type = null
    } = cardFilter || {};

    let patientFilterData = res?.data?.list;

    let ninetyDaysData = res?.data?.ninetyDaysData;

    let diffDashboardPatients = res?.data?.diffDashboardPatients;
    let customCombineTabData = res?.data?.customCombineTabData;
    const dynamicCards = res?.data?.dynamicCards ?? [];

    let objectCustom = Object();
    let dynamicCardsObj = {};
    let customTabsObj = Object();

    if (priorityData?.length > 0) {
        let i = 0;
        for await (const ele of priorityData) {
            i++;
            if (cardType === ele.type) {
                continue;
            }

            if (ele?.question?.isCustom) {
                if (customTabs.length > 0) {
                    const customTabsRes = customTabs.filter((x) => x.accessor === ele.type);
                    if (customTabsRes.length > 0) {
                        customTabsObj = await getCustomTabsCards(
                            patientFilterData,
                            customTabsRes,
                            null,
                            { ninetyDaysData, dynamicCards, diffDashboardPatients, pageType: PAGE_TYPE.DECEASED, customCombineTabData });
                    }
                }
                objectCustom = { ...objectCustom, ...dynamicCardsObj, ...customTabsObj };

                patientFilterData = await filterCustomPatientData(patientFilterData, cardFilter, ele);
            }

            if (ele.type === DECEASED_CARDS_TYPE.INSURANCE_DATA) {

                patientFilterData = await _.filter(patientFilterData, ({ insuranceId }) =>
                    _.every([_.includes(cardFilter.insuranceData, insuranceId)])
                );
            }

            if (ele.type === DECEASED_CARDS_TYPE.FLOORS_DATA) {
                patientFilterData = await _.filter(patientFilterData, ({ floorId }) =>
                    _.every([_.includes(cardFilter.floorsData, floorId)])
                );
            }

            if (ele.type === DECEASED_CARDS_TYPE.DOCTOR_DATA) {
                patientFilterData = await _.filter(patientFilterData, ({ doctorId }) =>
                    _.every([_.includes(cardFilter.doctorData, doctorId)])
                );
            }

            if (ele.type === DECEASED_CARDS_TYPE.NINETY_DAYS_DATA) {
                patientFilterData = await ninetyDaysDataFilter(
                    cardFilter.ninetyDaysData,
                    patientFilterData,
                    ninetyDaysData
                );
            }
        }
    }
    return patientFilterData;
}

const calculateTotalOrAverage = async (patients, accessor, type, title, patientIds, isADT = false) => {
    const patientsFilterArr = await patients?.filter(patient => patient?.[accessor] != null) || [];

    const total = await patientsFilterArr.reduce((sum, patient) => {
        const value = Number(patient[accessor]);
        return !isNaN(value) ? sum + value : sum;  // Only add numeric values
    }, 0);

    const average = patientsFilterArr.length > 0 ? total / patientsFilterArr.length : 0;

    const formatValue = async (value) => value < 1 ? value.toFixed(2) : Math.round(value);

    const result = {
        _id: title,
        id: title,
        label: title,
        name: title,
        total: type === NUMBER_RANGE_TYPE.AVERAGE
            ? await formatValue(average)
            : await formatValue(total),
        isSpacialItem: true,
        percentage: '',
        patientIds,
        ...(isADT && { type: ADT_TABLE_TYPE.CUSTOM }),
    };

    return result;
};

const calculateTotalResponsesNumberRange = async (patients, accessor, options, totalFilterData) => {
    let isADT = totalFilterData?.isADT ?? false;
    let isFilterType = totalFilterData?.isFilterType ?? null;
    const isOtherDashboard = totalFilterData?.isOtherDashboard ?? false;

    const results = await Promise.all(
        options.map(async ({ min, max, type, title }) => {
            let patientsFilter;

            if (min === ">") {
                patientsFilter = _.filter(patients, (patient) => {
                    const age = Number(patient[accessor]);
                    return age > Number(max);
                });
            } else if (max === ">") {
                patientsFilter = _.filter(patients, (patient) => {
                    const age = Number(patient[accessor]);
                    return age > Number(min);
                });
            } else {
                let minValue = Number(min);
                let maxValue = Number(max);

                // Swap values if min is greater than max
                if (minValue > maxValue) {
                    [minValue, maxValue] = [maxValue, minValue];
                }

                patientsFilter = _.filter(patients, (patient) => {
                    const age = Number(patient[accessor]);
                    return age >= minValue && age <= maxValue;
                });
            }

            const total = patientsFilter?.length || 0;
            const label = `${min} - ${max}`;
            const patientIds = patientsFilter.map((item) => String(item._id));
            const patientRefIds = patientsFilter?.length > 0 ? patientsFilter.map((item) => String(item.refPatientId)) : [];
            const admissionIds = patientsFilter.map((item) => String(item.admissionId));

            const percentageTotal = totalFilterData?.totalForPercentage || patients?.length;

            if (isFilterType === "alert") {
                if (type === NUMBER_RANGE_TYPE.TOTAL || type === NUMBER_RANGE_TYPE.AVERAGE) {
                    const res = await calculateTotalOrAverage(patients, accessor, type, title, patientIds, isADT);
                    return res;
                }
                return {
                    _id: label,
                    label,
                    total: total
                }
            } else {
                if (type === NUMBER_RANGE_TYPE.TOTAL || type === NUMBER_RANGE_TYPE.AVERAGE) {
                    const res = await calculateTotalOrAverage(patients, accessor, type, title, patientIds, isADT);
                    return res;
                }
                return {
                    _id: label,
                    id: label,
                    label,
                    name: label,
                    total,
                    value: total,
                    originalTotal: total,
                    percentage: await itemPercentage(total, percentageTotal, "number"),
                    patientIds: isOtherDashboard ? patientRefIds : patientIds,
                    ...isOtherDashboard && { otherDashboardIds: patientIds },
                    ...(isADT && { transferIds: patientIds }),
                    ...(isADT && { admissionIds }),
                    ...(isADT && { ids: patientIds }),
                    ...(isADT && { type: ADT_TABLE_TYPE.CUSTOM }),
                };
            }
        })
    );

    // Filter out any null or undefined results
    const filteredResults = results.filter(Boolean);
    return filteredResults;
};

// Helper function to handle NUMBER_RANGE_LIMITED_ANSWERS
const calculateTotalRangeLimitedResponses = async (patients, accessor, options, totalFilter) => {
    const numberRangeOptions = options.filter((opt) => opt.type === 'numberRange');
    const limitedAnswerOptions = options.filter((opt) => opt.type === 'limitedAnswers');
    const totalAndAverageOptions = options.filter((opt) => [NUMBER_RANGE_TYPE.TOTAL, NUMBER_RANGE_TYPE.AVERAGE].includes(opt.type));

    const totalAndAverageResults = await calculateTotalResponsesNumberRange(patients, accessor, totalAndAverageOptions, totalFilter);

    const numberRangeResults = await calculateTotalResponsesNumberRange(patients, accessor, numberRangeOptions, totalFilter);
    const limitedAnswerResults = await calculateTotalResponses(
        patients,
        accessor,
        limitedAnswerOptions.map((opt) => opt.option),
        totalFilter
    );

    return [...numberRangeResults, ...limitedAnswerResults, ...totalAndAverageResults];
};

const calculateTotalResponses = async (patients, accessor, options, totalFilter) => {
    let percentageTotal = totalFilter?.totalForPercentage ? totalFilter.totalForPercentage : patients?.length;
    const totalType = totalFilter?.totalType;
    let isADT = totalFilter?.isADT ?? false;
    let isFilterType = totalFilter?.isFilterType ?? null;
    const isOtherDashboard = totalFilter?.isOtherDashboard ?? false;

    const responseCounts = Array.isArray(patients)
        ? patients.reduce((acc, patient) => {
            const value = patient?.[accessor];
            if (value !== undefined) {
                if (!acc[value]) {
                    acc[value] = { count: 0, patientIds: [], admissionIds: [], refPatientIds: [] };
                }
                acc[value].count++;
                acc[value].patientIds.push(String(patient._id));
                acc[value].refPatientIds.push(String(patient.refPatientId)); // Store patient IDs
                acc[value].admissionIds.push(String(patient.admissionId));
            }
            return acc;
        }, {})
        : {};

    const results = await Promise.all(
        options.map(async (option) => {
            const optionId = option._id ?? option;
            const label = option.label ?? option;
            const totalRes = responseCounts?.[optionId]?.count || 0;
            const patientIds = responseCounts?.[optionId]?.patientIds || [];
            const patientRefIds = responseCounts?.[optionId]?.refPatientIds || [];
            const admissionIds = responseCounts?.[optionId]?.admissionIds || [];
            const uniquePatientIds = [...new Set(patientIds || [])];

            if (isFilterType === "alert") {
                return {
                    _id: optionId,
                    label,
                    total: totalRes
                }
            } else {
                const result = {
                    _id: optionId,
                    id: optionId,
                    label,
                    name: label,
                    type: option.type || undefined,
                    total: totalRes,
                    value: totalRes,
                    originalTotal: totalRes,
                    percentage: await itemPercentage(totalRes, percentageTotal, "number"),
                    patientIds: isOtherDashboard ? patientRefIds : patientIds,
                    ...isOtherDashboard && { otherDashboardIds: uniquePatientIds },
                    ...(isADT && { transferIds: patientIds }),
                    ...(isADT && { admissionIds: admissionIds }),
                    ...(isADT && { ids: patientIds }),
                    ...(isADT && { type: ADT_TABLE_TYPE.CUSTOM }),
                };

                if (totalType === QUESTION_INPUT_TYPE.UNLIMITED_ANSWERS && totalRes <= 0) {
                    return null;
                }

                return result;
            }
        })
    );

    // Filter out null values
    const filteredResults = results.filter(Boolean);
    return filteredResults;
};

const calculateTotalResponsesTimeRange = async (patients, accessor, options, totalFilterData, timeRangeType) => {
    let isADT = totalFilterData?.isADT ?? false;
    const isOtherDashboard = totalFilterData?.isOtherDashboard ?? false;

    // Import time range constants

    let timeRanges = [];

    // Determine time ranges based on type
    if (timeRangeType === TIME_RANGE_TYPE.HOURLY) {
        timeRanges = HOURLY_TIME_RANGES.map(hour => ({
            label: hour.label,
            startTime: hour.value,
            endTime: hour.value === '23:00' ? '23:59' : HOURLY_TIME_RANGES.find(h => h.value === `${String(parseInt(hour.value.split(':')[0]) + 1).padStart(2, '0')}:00`)?.value || '00:00'
        }));
    } else {
        timeRanges = options || [];
    }

    // Use Promise.all to await all async operations
    const results = await Promise.all(timeRanges.map(async (timeRange) => {
        const { label, startTime, endTime } = timeRange;

        let patientsFilter = patients?.filter((patient) => {
            const patientTime = patient[accessor];
            if (!patientTime) return false;

            // Convert time to comparable format (24-hour)
            const normalizeTime = (time) => {
                if (typeof time === 'string') {
                    // Handle different time formats
                    const timeStr = time.includes(':') ? time : `${time}:00`;
                    const [hours, minutes] = timeStr.split(':').map(Number);
                    return hours * 60 + (minutes || 0); // Convert to minutes for comparison
                }
                return 0;
            };

            const patientTimeMinutes = normalizeTime(patientTime);
            const startTimeMinutes = normalizeTime(startTime);
            const endTimeMinutes = normalizeTime(endTime);

            // Handle cases where end time is next day (e.g., 18:00 to 06:00)
            if (endTimeMinutes < startTimeMinutes) {
                return patientTimeMinutes >= startTimeMinutes || patientTimeMinutes <= endTimeMinutes;
            } else {
                return patientTimeMinutes >= startTimeMinutes && patientTimeMinutes < endTimeMinutes;
            }
        }) ?? [];

        const total = patientsFilter?.length || 0;
        const patientIds = patientsFilter?.length > 0 ? patientsFilter.map((item) => item._id) : [];
        const patientRefIds = patientsFilter?.length > 0 ? patientsFilter.map((item) => item.refPatientId) : [];
        const admissionIds = patientsFilter?.length > 0 ? patientsFilter.map((item) => item.admissionId) : [];
        let percentageTotal = totalFilterData?.totalForPercentage ? totalFilterData.totalForPercentage : patients?.length;

        return {
            _id: label,
            id: label,
            label,
            name: label,
            total,
            value: total,
            key: label,
            originalTotal: total,
            percentage: await itemPercentage(total, percentageTotal, "number"),
            patientIds: isOtherDashboard ? patientRefIds : patientIds,
            level: totalFilterData?.level || 0,
            ...(isOtherDashboard || totalFilterData?.level > 0) && { otherDashboardIds: patientIds },
            ...(isADT && { transferIds: patientIds }),
            ...(isADT && { admissionIds: admissionIds }),
            ...(isADT && { ids: patientIds }),
            ...(isADT && { type: ADT_TABLE_TYPE.CUSTOM }),
        };
    }));

    return results;
};

const processDynamicCard = async (item, patientFilterData, objectCustomRes, totalFilterData) => {
    const { customQuestionInputType, accessor, validationOptions, customQuestionOptions = [], timeRangeType } = item;
    
    switch (customQuestionInputType) {
        case QUESTION_INPUT_TYPE.UNLIMITED_ANSWERS:
            objectCustomRes[accessor] = await calculateTotalResponses(patientFilterData, accessor, validationOptions, { ...totalFilterData, totalType: QUESTION_INPUT_TYPE.UNLIMITED_ANSWERS });
            break;

        case QUESTION_INPUT_TYPE.LIMITED_ANSWERS:
            objectCustomRes[accessor] = await calculateTotalResponses(patientFilterData, accessor, customQuestionOptions, totalFilterData);
            break;

        case QUESTION_INPUT_TYPE.NUMBER_RANGE:
            objectCustomRes[accessor] = await calculateTotalResponsesNumberRange(patientFilterData, accessor, customQuestionOptions, totalFilterData);
            break;

        case QUESTION_INPUT_TYPE.TIME_TAB_RANGE:
            console.log(`🕐 Processing time range filter - accessor: ${accessor}, timeRangeType: ${timeRangeType}`);
            objectCustomRes[accessor] = await calculateTotalResponsesTimeRange(patientFilterData, accessor, customQuestionOptions, totalFilterData, timeRangeType);
            console.log(`✅ Time range filter completed for ${accessor} - found ${objectCustomRes[accessor]?.length || 0} time ranges`);
            break;

        case QUESTION_INPUT_TYPE.NUMBER_RANGE_LIMITED_ANSWERS:
            objectCustomRes[accessor] = await calculateTotalRangeLimitedResponses(patientFilterData, accessor, customQuestionOptions, totalFilterData);
            break;

        default:
            // console.log('Invalid customQuestionInputType:', customQuestionInputType);
            break;
    }
}

const filterCustomPatientData = async (patientFilterData, cardFilter, ele) => {
    return await _.filter(patientFilterData, (eleAccessor) => {
        const questionType = ele?.question?.customQuestionInputType;

        if (questionType === QUESTION_INPUT_TYPE.UNLIMITED_ANSWERS) {
            const accessor = ele?.type;
            const filterValues = cardFilter?.[accessor] || [];
            const elementValue = eleAccessor?.[accessor] || null;

            return filterValues.length === 0 || _.includes(filterValues, elementValue);
        }

        if (questionType === QUESTION_INPUT_TYPE.LIMITED_ANSWERS) {
            if (!cardFilter) return true;

            const accessor = ele?.type;
            const filterValues = cardFilter?.[accessor] || [];
            const elementValue = eleAccessor?.[accessor] || null;

            return filterValues.length === 0 || _.includes(filterValues, elementValue);
        }

        if (
            questionType === QUESTION_INPUT_TYPE.NUMBER_RANGE ||
            questionType === QUESTION_INPUT_TYPE.NUMBER_RANGE_LIMITED_ANSWERS
        ) {
            return ele?.patientIds?.includes(String(eleAccessor?._id));
        }
        if (!questionType) {
            return ele?.patientIds?.includes(String(eleAccessor?._id));
        }
        return true;
    });
}

const dynamicCardFilter = async (patientFilterData, dynamicCards, totalFilterData) => {
    const objectCustomRes = {};
    for (const item of dynamicCards) {
        await processDynamicCard(item, patientFilterData, objectCustomRes, totalFilterData);
    }
    return objectCustomRes;
}

async function buildCustomTabsObject({
    customTabs,
    patientFilterData,
    totalFilterData,
    patientList,
    dynamicCards,
    newSavedFilters,
    objectCustom,
    diffDashboardPatients,
    pageType
}) {
    if (!Array.isArray(customTabs) || customTabs.length === 0) return objectCustom;

    const isCheckNewFilters = true;

    const customTabsObjRes = await getCustomTabsCards(
        patientFilterData,
        customTabs,
        totalFilterData,
        {
            ninetyDaysData: patientList.ninetyDaysData,
            dynamicCards,
            newSavedFilters,
            isCheckNewFilters,
            objectCustom,
            pageType,
            diffDashboardPatients,
            customCombineTabData
        }
    );

    return { ...objectCustom, ...customTabsObjRes };
}

const deceaseUpdateFilterListData = async (
    cardFilter,
    res,
    priorityData = [],
    forComparison,
    lockedTotalBy,
    dynamicCards,
    isOnlyHospitalTabAccess = false
) => {
    let objectCustom = Object();
    let customTabsObj = Object();
    let dynamicCardsObj = {}; // 
    let newSavedFilters = [];
    let patientList = res.data;
    const customTabs = patientList?.customTabs ?? [];
    const diffDashboardPatients = patientList?.diffDashboardPatients ?? [];
    const customCombineTabData = patientList?.customCombineTabData ?? [];

    let totals = res.totals;
    let patientFilterData = patientList.list;
    const {
        lockedTotal = null,
        transferType = null,
    } = totals;
    let lockedTotalModified = lockedTotal;
    const censusAverage = isOnlyHospitalTabAccess ? totals?.bedCapacity : totals?.censusAverage;
    let totalFilterData = {
        originalData: patientList.list,
        totalType: TOTAL_TYPE.FILTER,
        totalForPercentage: !transferType ? censusAverage : patientFilterData.length,
    };
    let mainNumPercentage = censusAverage;
    if (transferType) {
        mainNumPercentage = null;
        totalFilterData.totalForPercentage = patientFilterData.length;
    }
    if (priorityData.length > 0) {
        totalFilterData.totalForPercentage = null;
    }

    if (lockedTotalBy && !forComparison) {
        if (lockedTotalBy === TYPES.ALL || lockedTotalBy === "census") {
            lockedTotalModified = patientFilterData.length;
        } else {
            const lockedFilterRemoved = _.find(priorityData, { type: lockedTotalBy });
            lockedTotalModified = patientFilterData.length;
            if (!lockedFilterRemoved) {
                lockedTotalModified = null;
            }
        }
    }
    if (lockedTotalModified) {
        totalFilterData.totalForPercentage = lockedTotalModified;
    }
    if (lockedTotalModified && priorityData.length === 1 && !transferType && !forComparison) {
        totalFilterData.totalForPercentage = lockedTotalModified;
    }

    if (priorityData.length > 0) {
        let i = 0;
        for await (const ele of priorityData) {
            i++;

            if (ele?.question?.isCustom) {
                if (customTabs.length > 0) {
                    const customTabsRes = customTabs.filter((x) => x.accessor === ele.type);
                    if (customTabsRes.length > 0) {
                        customTabsObj = await getCustomTabsCards(patientFilterData, customTabsRes, totalFilterData, {
                            ninetyDaysData,
                            dynamicCards,
                            diffDashboardPatients,
                            pageType: PAGE_TYPE.DECEASED,
                            forComparison,
                            customCombineTabData
                        });
                    }
                }
                objectCustom = { ...objectCustom, ...dynamicCardsObj, ...customTabsObj }

                const dynamicItemData = await _.find(dynamicCards, { accessor: ele.type });
                if (dynamicItemData && !_.isEmpty(dynamicItemData)) {
                    let objectCustomResDynamic = {};
                    await processDynamicCard(dynamicItemData, patientFilterData, objectCustomResDynamic, {
                        ...totalFilterData,
                        totalForPercentage: mainNumPercentage && i === 1 ? mainNumPercentage : totalFilterData.totalForPercentage,
                    });
                    if (!_.isEmpty(objectCustomResDynamic)) {
                        objectCustom[dynamicItemData?.accessor] = objectCustomResDynamic?.[ele.type];
                    }
                }
                await assignMatchedPatientIds(ele, objectCustom);
                patientFilterData = await filterCustomPatientData(patientFilterData, cardFilter, ele);
            }

            if (ele.type === DECEASED_CARDS_TYPE.INSURANCE_DATA) {
                objectCustom.insuranceData = await insuranceData(patientFilterData, {
                    ...totalFilterData,
                    totalType: TOTAL_TYPE.MAIN,
                    totalForPercentage: mainNumPercentage && i === 1 ? mainNumPercentage : totalFilterData.totalForPercentage,
                });

                patientFilterData = await _.filter(patientFilterData, ({ insuranceId }) =>
                    _.every([_.includes(cardFilter.insuranceData, insuranceId)])
                );
            }

            if (ele.type === DECEASED_CARDS_TYPE.FLOORS_DATA) {
                objectCustom.floorsData = await floorsData(patientFilterData, {
                    ...totalFilterData,
                    totalType: TOTAL_TYPE.MAIN,
                    totalForPercentage: mainNumPercentage && i === 1 ? mainNumPercentage : totalFilterData.totalForPercentage,
                });
                patientFilterData = await _.filter(patientFilterData, ({ floorId }) =>
                    _.every([_.includes(cardFilter.floorsData, floorId)])
                );
            }

            if (ele.type === DECEASED_CARDS_TYPE.DOCTOR_DATA) {
                objectCustom.doctorData = await doctorData(patientFilterData, {
                    ...totalFilterData,
                    totalType: TOTAL_TYPE.MAIN,
                    totalForPercentage: mainNumPercentage && i === 1 ? mainNumPercentage : totalFilterData.totalForPercentage,
                });

                patientFilterData = await _.filter(patientFilterData, ({ doctorId }) =>
                    _.every([_.includes(cardFilter.doctorData, doctorId)])
                );
            }

            if (ele.type === DECEASED_CARDS_TYPE.NINETY_DAYS_DATA) {
                objectCustom.ninetyDaysData = await ninetyDaysDataList(patientList.ninetyDaysData, patientFilterData, {
                    ...totalFilterData,
                    totalType: TOTAL_TYPE.MAIN,
                    totalForPercentage: mainNumPercentage && i === 1 ? mainNumPercentage : totalFilterData.totalForPercentage,
                });
                patientFilterData = await ninetyDaysDataFilter(
                    cardFilter.ninetyDaysData,
                    patientFilterData,
                    patientList.ninetyDaysData
                );
            }
            if (ele.type === lockedTotalBy) {
                lockedTotalModified = patientFilterData.length;
            }
            newSavedFilters = [...newSavedFilters, ele.type];
        }

        const totalCount = lockedTotalModified ? lockedTotalModified : patientFilterData.length;
        //store.dispatch(forComparison ? setFilterTotalComparison(totalCount) : setFilterTotal(totalCount));

        if (!_.includes(newSavedFilters, DECEASED_CARDS_TYPE.INSURANCE_DATA)) {
            objectCustom.insuranceData = await insuranceData(patientFilterData, {
                ...totalFilterData,
                totalForPercentage: totalCount,
            });
        }
        if (!_.includes(newSavedFilters, DECEASED_CARDS_TYPE.FLOORS_DATA)) {
            objectCustom.floorsData = await floorsData(patientFilterData, {
                ...totalFilterData,
                totalForPercentage: totalCount,
            });
        }
        if (!_.includes(newSavedFilters, DECEASED_CARDS_TYPE.DOCTOR_DATA)) {
            objectCustom.doctorData = await doctorData(patientFilterData, {
                ...totalFilterData,
                totalForPercentage: totalCount,
            });
        }
        if (!_.includes(newSavedFilters, DECEASED_CARDS_TYPE.NINETY_DAYS_DATA)) {
            objectCustom.ninetyDaysData = await ninetyDaysDataList(patientList.ninetyDaysData, patientFilterData, {
                ...totalFilterData,
                totalForPercentage: totalCount,
            });
        }
        if (dynamicCards.length > 0) {
            for (const item of dynamicCards) {
                let objectCustomRes = {};
                if (!_.includes(newSavedFilters, item?.accessor)) {
                    await processDynamicCard(item, patientFilterData, objectCustomRes, {
                        ...totalFilterData,
                        totalForPercentage: totalCount,
                    });
                    if (!_.isEmpty(objectCustomRes)) {
                        objectCustom[item?.accessor] = objectCustomRes?.[item?.accessor];
                    }
                }
            }
        }
        if (customTabs.length > 0) {
            objectCustom = await buildCustomTabsObject({
                customTabs,
                patientFilterData,
                totalFilterData: { ...totalFilterData, totalType: TOTAL_TYPE.FILTER, totalForPercentage: totalCount },
                patientList,
                dynamicCards,
                newSavedFilters,
                objectCustom,
                diffDashboardPatients,
                pageType: PAGE_TYPE.DECEASED
            });
        }
    } else {
        objectCustom.insuranceData = await insuranceData(patientFilterData, totalFilterData);
        objectCustom.floorsData = await floorsData(patientFilterData, totalFilterData);
        objectCustom.doctorData = await doctorData(patientFilterData, totalFilterData);
        objectCustom.ninetyDaysData = await ninetyDaysDataList(
            patientList.ninetyDaysData,
            patientFilterData,
            totalFilterData
        );

        if (dynamicCards.length > 0) {
            dynamicCardsObj = await dynamicCardFilter(patientFilterData, dynamicCards, totalFilterData);
        }
        if (customTabs.length > 0) {
            customTabsObj = await getCustomTabsCards(patientFilterData, customTabs, totalFilterData, { ninetyDaysData: patientList.ninetyDaysData, dynamicCards, pageType: PAGE_TYPE.DECEASED, diffDashboardPatients, customCombineTabData });
        }
        objectCustom = { ...objectCustom, ...dynamicCardsObj, ...customTabsObj }
        objectCustom = { ...objectCustom, ...dynamicCardsObj }
    }


    return objectCustom;
}

const matchedArray = (array1, array2) => {
    return _.intersectionWith(array1, array2, _.isEqual);
}

const getCommunicationFieldNames = (defaultData, row, relation, type, filters) => {
    if (type === CO_TRANSFER_CARDS_TYPE.SIXTY_DAYS_DATA) {
        let labelName = null;
        _.filter(defaultData, ({ ids, label }) => {
            if (_.includes(ids, row._id.toString())) {
                labelName = label;
            }
        });
        return labelName;
    } else if (type === CO_TRANSFER_CARDS_TYPE.RETURNS_DATA) {
        return row.wasReturned ? "Returned" : "Didn't Return";
    } else if (
        type === CO_TRANSFER_CARDS_TYPE.SNF ||
        type === CO_TRANSFER_CARDS_TYPE.AMA ||
        type === CO_TRANSFER_CARDS_TYPE.SAFE_DISCHARGE ||
        type === CO_TRANSFER_CARDS_TYPE.TOTAL
    ) {
        return type;
    } else if (relation && row[relation]) {
        return row[relation].label;
    } else if (filters?.question && filters?.question?.isCustom) {
        let labelName = "-";

        for (const item of defaultData) {
            const patientIdsArr = item?.patientIds?.map((id) => id.toString());
            if (patientIdsArr?.includes(row._id.toString())) {
                labelName = item.label;
                break; // Exit the loop once a match is found
            }
        }
        return labelName;
    }
    return "-";
}

const getOverallFieldNames = (row, relation, type, defaultData) => {
    if (type === OVERALL_CARDS_TYPE.NINETY_DAYS_DATA) {
        let labelName = null;
        _.filter(defaultData, ({ ids, label }) => {
            if (_.includes(ids, row._id.toString())) {
                labelName = label;
            }
        });
        return labelName;
    } else if (type === OVERALL_CARDS_TYPE.TOTAL || type === OVERALL_CARDS_TYPE.TOTAL_INCOMING || type === OVERALL_CARDS_TYPE.TOTAL_OUTGOING) {
        return row.type;
    } else if (relation && row[relation]) {
        return row[relation].label;
    }
    return "-";
}

const getDeceasedFieldNames = (row, relation, type, defaultData, filters) => {
    if (type === HOSPITAL_CARDS_TYPE.NINETY_DAYS_DATA) {
        let labelName = null;
        _.filter(defaultData, ({ ids, label }) => {
            if (_.includes(ids, row._id.toString())) {
                labelName = label;
            }
        });
        return labelName;
    } else if (type === DECEASED_CARDS_TYPE.TOTAL) {
        return row.transferType;
    } else if (relation && row[relation]) {
        return row[relation].label;
    } else if (filters?.question && filters?.question?.isCustom) {
        let labelName = "-";

        for (const item of defaultData) {
            const patientIdsArr = item?.patientIds?.map((id) => id.toString());
            if (patientIdsArr?.includes(row._id.toString())) {
                labelName = item.label;
                break; // Exit the loop once a match is found
            }
        }
        return labelName;
    }
    return "-";
}

const getAdmissionFieldNames = (row, relation, type, filters) => {
    if (relation && type === ADMISSION_CARDS_TYPE.DX_DATA) {
        if (row[relation] && row[relation].length > 0) {
            return _.map(row[relation], "label").join(", ");
        }
        return "-";
    } else if (type === ADMISSION_CARDS_TYPE.DAYS_DATA) {
        return row.day;
    } else if (
        type === ADMISSION_CARDS_TYPE.ADMISSION ||
        type === ADMISSION_CARDS_TYPE.READMISSION ||
        type === ADMISSION_CARDS_TYPE.TOTAL
    ) {
        return row.type;
    } else if (relation && row[relation]) {
        return row[relation].label;
    } else if (filters?.question && filters?.question?.isCustom) {
        let labelName = "-";

        for (const item of defaultData) {
            const patientIdsArr = item?.patientIds?.map((id) => id.toString());
            if (patientIdsArr?.includes(row._id.toString())) {
                labelName = item.label;
                break; // Exit the loop once a match is found
            }
        }
        return labelName;
    }
    return "-";
}

const getHospitalFieldNames = (defaultData, row, relation, type, filters) => {
    if (relation && type === HOSPITAL_CARDS_TYPE.DX_DATA) {
        if (row[relation] && row[relation].length > 0) {
            return _.map(row[relation], "label").join(", ");
        }
        return "-";
    } else if (type === HOSPITAL_CARDS_TYPE.DAYS_DATA) {
        return row.day;
    } else if (type === HOSPITAL_CARDS_TYPE.DCER_DATA) {
        return row.wasAdmitted ? "DC" : "ER";
    } else if (type === HOSPITAL_CARDS_TYPE.NINETY_DAYS_DATA) {
        let labelName = null;
        _.filter(defaultData, ({ ids, label }) => {
            if (_.includes(ids, row._id.toString())) {
                labelName = label;
            }
        });
        return labelName;
    } else if (type === HOSPITAL_CARDS_TYPE.HOSPITALIZATIONS) {
        return !row.reHospitalization
            ? "New Hospitalizations"
            : "Re-Hospitalizations";
    } else if (type === HOSPITAL_CARDS_TYPE.RETURNS_DATA) {
        return row.wasReturned ? "Returned" : "Didn't Return";
    } else if (type === HOSPITAL_CARDS_TYPE.SHIFT_DATA) {
        return row.shiftName;
    } else if (
        type === HOSPITAL_CARDS_TYPE.UNPLANNED ||
        type === HOSPITAL_CARDS_TYPE.PLANNED ||
        type === HOSPITAL_CARDS_TYPE.TOTAL
    ) {
        return row.transferType;
    } else if (relation && row[relation]) {
        return row[relation].label;
    } else if (filters?.question && filters?.question?.isCustom) {
        let labelName = "-";

        for (const item of defaultData) {
            const patientIdsArr = item?.patientIds?.map((id) => id.toString());
            if (patientIdsArr?.includes(row._id.toString())) {
                labelName = item.label;
                break; // Exit the loop once a match is found
            }
        }
        return labelName;
    }
    return "-";
}

const getAdmissionADTFieldNames = (
    defaultData,
    row,
    relation,
    selectedCardItem
) => {
    const { type, childId } = selectedCardItem;
    if (relation && type === ADT_TABLE_TYPE.GROUP_ARRAY) {
        if (row[relation] && row[relation].length > 0) {
            return _.map(row[relation], "label").join(", ");
        }
        return "-";
    }
    if (relation && type === ADT_TABLE_TYPE.GROUP) {
        return row[relation]?.label || "-";
    }
    if (childId === "wasAdmitted") {
        return row.wasAdmitted ? "DC" : "ER";
    }
    if (childId === "reHospitalization") {
        return !row.reHospitalization
            ? "New Hospitalizations"
            : "Re-Hospitalizations";
    }
    if (childId === "wasReturned") {
        return row.wasReturned ? "Returned" : "Didn't Return";
    }
    if (childId === "shiftName") {
        return row.shiftName;
    }
    if (childId === "day") {
        return row.day;
    }
    if (type === ADT_TABLE_TYPE.TOTAL || type === ADT_TABLE_TYPE.ALL) {
        return row.transferType;
    }
    if (type === ADT_TABLE_TYPE.CUSTOM) {
        let labelName = "-";
        for (const item of defaultData) {
            const patientIdsArr = item?.patientIds?.map((id) => String(id));
            if (patientIdsArr?.includes(String(row._id))) {
                labelName = item?.label ?? "-";
                break;
            }
        }
        return labelName;
    }
    if (
        childId === "90Days" ||
        childId === "60Days" ||
        childId === "90DaysDeceased" ||
        childId === "90DaysOverall"
    ) {
        let labelName = null;
        _.filter(defaultData, ({ ids, label }) => {
            if (_.includes(ids, String(row._id))) {
                labelName = label;
            }
        });
        return labelName;
    }
}


// custom tabs functions 

async function processFilterForTab({
    filter,
    patientData,
    totalFilterData,
    dynamicCards,
    ninetyDaysData,
    newSavedFilters,
    isCheckNewFilters,
    objectCustom,
    result,
    customTab,
    sixtyDaysData,
    pageType,
    diffDashboardPatients,
    forComparison,
    level,
    isDebug
}) {
    const { filters = [] } = filter;
    for (const ele of filters) {
        // console.log(ele, 'ele');

        const { card, isMainCard, items = [] } = ele;
        const key = card?.value;
        if (!key) continue;

        const customKey = customTab?.accessor;
        const currentCardFilter = isCheckNewFilters && _.includes(newSavedFilters, customKey);
        const question = ele?.question ?? dynamicCards?.find(({ _id }) => _id === card?.questionId);

        if (isMainCard) {
            // console.log(card, 'card');

            if (card?.questionId) {
                if (currentCardFilter) {
                    result[customKey] = objectCustom?.[customKey];
                    continue;
                }

                const objectCustomRes = {};
                const patientIds = new Set(patientData.map(item => item?.id));
                const dashboardWiseData = diffDashboardPatients[filter.dashboard]?.list ?? [];
                let filteredPatients;
                if (pageType !== filter.dashboard) {
                    filteredPatients = dashboardWiseData.filter(item => patientIds.has(item.refPatientId));
                    await processDynamicCard(question, filteredPatients, objectCustomRes, { ...totalFilterData, isOtherDashboard: true });
                } else if (level > 0) {
                    filteredPatients = dashboardWiseData.filter(item => item.level === level && patientIds.has(item.refPatientId));
                    await processDynamicCard(question, filteredPatients, objectCustomRes, { ...totalFilterData, level, isOtherDashboard: true });
                } else {
                    await processDynamicCard(question, patientData, objectCustomRes, { ...totalFilterData, level });
                }

                if (!_.isEmpty(objectCustomRes)) {
                    result[customKey] = objectCustomRes?.[key];
                }
            } else {
                if (currentCardFilter) {
                    result[customKey] = objectCustom?.[customKey];
                    continue;
                }

                const dashboardWiseData = diffDashboardPatients[filter.dashboard]?.list ?? [];

                if (pageType === filter.dashboard) {

                    if (level > 0) {

                        const patientIdsSet = new Set(patientData.map(item => item?.id));

                        const filteredPatients = dashboardWiseData.filter(item => item.level === level && patientIdsSet.has(item.refPatientId));

                        result[customKey] = await handleMainCard(key,
                            filteredPatients,
                            { ...totalFilterData, level, isOtherDashboard: true, filterDashboard: filter.dashboard },
                            diffDashboardPatients[filter.dashboard]?.ninetyDaysData ?? [],
                            diffDashboardPatients[filter.dashboard]?.ninetyDaysData?.sixtyDaysData ?? [],
                        );
                    } else {
                        result[customKey] = await handleMainCard(key, patientData, { ...totalFilterData, level, filterDashboard: filter.dashboard }, ninetyDaysData, sixtyDaysData);
                    }
                } else {
                    const patientIds = new Set(patientData.map(item => item?.id));
                    const filteredPatients = dashboardWiseData.filter(item => item.level === level + 1 && patientIds.has(item.refPatientId));
                    result[customKey] = await handleMainCard(key, filteredPatients, { ...totalFilterData, isOtherDashboard: true, level: level + 1, filterDashboard: filter.dashboard }, diffDashboardPatients[filter.dashboard]?.ninetyDaysData, diffDashboardPatients[filter.dashboard]?.sixtyDaysData);

                }
            }
        } else {
            if (card?.questionId) {
                if (!question) continue;
                const objectCustomRes = {};

                if (pageType !== filter.dashboard) {
                    const dashboardWiseData = diffDashboardPatients[filter.dashboard]?.list ?? [];
                    const patientIdsRef = new Set(patientData.map(item => item?.id));
                    let filteredPatients = dashboardWiseData.filter(item => patientIdsRef.has(item.refPatientId));

                    await processDynamicCard(question, filteredPatients, objectCustomRes, { ...totalFilterData });

                    const resultItems = objectCustomRes?.[key] || [];
                    const patientIds = resultItems.reduce((acc, ele) => {
                        if (_.includes(items, ele?._id)) {
                            acc.push(...(ele?.patientIds || []));
                        }
                        return acc;
                    }, []);

                    filteredPatients = await filterCustomPatientData(filteredPatients, { [key]: items }, { type: key, patientIds, question: { customQuestionInputType: question.customQuestionInputType } }, true);
                    const patientIdsRefOther = await filteredPatients.map(item => item.refPatientId);
                    patientData = await patientData.filter(item => patientIdsRefOther.includes(item?.id));
                } else {
                    let dashboardWiseData = diffDashboardPatients[filter.dashboard]?.list ?? [];
                    const patientIdsMainSet = new Set(patientData.map(item => item?.id));
                    if (level > 0) {
                        dashboardWiseData = dashboardWiseData.filter(item => item.level === level && patientIdsMainSet.has(item.refPatientId));
                        await processDynamicCard(question, dashboardWiseData, objectCustomRes, { ...totalFilterData, level, isOtherDashboard: true });
                    } else {
                        await processDynamicCard(question, patientData, objectCustomRes, { ...totalFilterData });
                    }
                    const resultItems = objectCustomRes?.[key] || [];
                    const patientIds = resultItems.reduce((acc, ele) => {
                        if (_.includes(items, ele?._id)) {
                            acc.push(...(ele?.patientIds || []));
                        }
                        return acc;
                    }, []);

                    patientData = await filterCustomPatientData(level > 0 ? dashboardWiseData : patientData, { [key]: items }, { type: key, patientIds, question: { customQuestionInputType: question.customQuestionInputType } }, true);
                }
            } else {
                let dashboardWiseData = diffDashboardPatients[filter.dashboard]?.list ?? [];

                if (pageType === filter.dashboard) {
                    if (level > 0) {
                        const levelWiseIdsSet = new Set(dashboardWiseData.filter(item => item.level === level).map(item => item.refPatientId));
                        patientData = await patientData.filter(item => levelWiseIdsSet.has(item?.id));
                    }
                    patientData = await applyFilter(key, items, patientData, ninetyDaysData, sixtyDaysData);
                } else {
                    if (level > 0) {
                        dashboardWiseData = await dashboardWiseData.filter(item => item.level === level - 1);
                    }
                    dashboardWiseData = await applyFilter(key, items, dashboardWiseData, dashboardWiseData?.ninetyDaysData ?? [], dashboardWiseData?.ninetyDaysData?.sixtyDaysData ?? []);
                    const patientIds = await dashboardWiseData.map(item => item.refPatientId);
                    patientData = await patientData.filter(item => patientIds.includes(item?.id));
                }
            }
        }
    }

    return patientData;
}

async function getDashboardWiseData(diffDashboardPatients, pageType) {
    const typeMapping = {
        [PAGE_TYPE.ADMISSION]: {
            types: [ADT_TYPES.ADMISSIONS, ADT_TYPES.READMISSIONS]
        },
        [PAGE_TYPE.COMMUNITY_TRANSFER]: {
            type: ADT_TYPES.TRANSFER,
            subTypes: [ADT_SUB_TYPES.SAFE_DISCHARGE, ADT_SUB_TYPES.SNF, ADT_SUB_TYPES.AMA]
        },
        [PAGE_TYPE.DECEASED]: {
            type: ADT_TYPES.TRANSFER,
            subTypes: [ADT_SUB_TYPES.DECEASED]
        },
        [PAGE_TYPE.HOSPITAL]: {
            type: ADT_TYPES.TRANSFER,
            subTypes: [ADT_SUB_TYPES.PLANNED_HOSPITAL_TRANSFER, ADT_SUB_TYPES.UNPLANNED_HOSPITAL_TRANSFER]
        }
    };

    const config = typeMapping[pageType];
    if (!config) return [];

    return diffDashboardPatients.filter(patient => {
        if (config.types) return config.types.includes(patient.type);
        if (config.type && config.subTypes) return patient.type === config.type && config.subTypes.includes(patient.transferType);
        return false;
    });
}

async function getCustomTabsCards(
    patientFilterData,
    customTabs,
    totalFilterData,
    {
        ninetyDaysData,
        dynamicCards,
        newSavedFilters,
        isCheckNewFilters = false,
        objectCustom,
        sixtyDaysData,
        diffDashboardPatients = [],
        pageType,
        forComparison,
        isDebug = false,
        customCombineTabData = []
    }) {

    const result = {};

    for (const customTab of customTabs) {
        let patientData = [...patientFilterData];
        if (customTab?.type === CUSTOM_TAB_TYPE.combineTab) {
            patientData = await processFilterForCombineTab({
                customTab,
                patientData,
                totalFilterData,
                customCombineTabData,
                pageType,
                ninetyDaysData,
                sixtyDaysData,
                isCheckNewFilters,
                newSavedFilters,
                result,
                forComparison,
            });
        } else {
            let index = 0;
            let dataPoints = [];
            let level = 0;

            for (const filter of customTab.filters) {

                if (index > 0) {
                    if (dataPoints.includes(filter?.dashboard)) {
                        level = level + 1;
                    }
                }
                dataPoints.push(filter?.dashboard);

                const customKey = customTab?.accessor;

                const currentCardFilter = isCheckNewFilters && _.includes(newSavedFilters, customKey);

                if (!currentCardFilter) {
                    patientData = await processFilterForTab({
                        level,
                        customTab,
                        filter,
                        patientData,
                        totalFilterData,
                        dynamicCards,
                        ninetyDaysData,
                        newSavedFilters,
                        isCheckNewFilters,
                        objectCustom,
                        result,
                        sixtyDaysData,
                        pageType,
                        diffDashboardPatients,
                        forComparison,
                        isDebug
                    });
                }
                index++;
            }
        }
    }

    return result;
}

const mainCardHandlers = {
    [HOSPITAL_CARDS_TYPE.INSURANCE_DATA]: insuranceData,
    [HOSPITAL_CARDS_TYPE.FLOORS_DATA]: floorsData,
    [HOSPITAL_CARDS_TYPE.DCER_DATA]: dcErData,
    [HOSPITAL_CARDS_TYPE.HOSPITALIZATIONS]: hospitalizationData,
    [HOSPITAL_CARDS_TYPE.RETURNS_DATA]: returnsData,
    [HOSPITAL_CARDS_TYPE.NINETY_DAYS_DATA]: ninetyDaysDataList,
    [CO_TRANSFER_CARDS_TYPE.SIXTY_DAYS_DATA]: sixtyDaysDataList,
    [HOSPITAL_CARDS_TYPE.DAYS_DATA]: daysData,
    [HOSPITAL_CARDS_TYPE.DX_DATA]: dxData,
    [HOSPITAL_CARDS_TYPE.NURSE_DATA]: nurseData,
    [HOSPITAL_CARDS_TYPE.DOCTOR_DATA]: doctorData,
    [HOSPITAL_CARDS_TYPE.SHIFT_DATA]: shiftData,
    [HOSPITAL_CARDS_TYPE.HOSPITAL_DATA]: customHospitalData,
    [CO_TRANSFER_CARDS_TYPE.SAFE_DISCHARGE_ASS_LIV_DATA]: safeDischargeAssLivData,
    [CO_TRANSFER_CARDS_TYPE.SNF_FACILITY_DATA]: snfFacilityData,
};

function isSimpleCardKey(key) {
    return [
        HOSPITAL_CARDS_TYPE.UNPLANNED,
        HOSPITAL_CARDS_TYPE.PLANNED,
        HOSPITAL_CARDS_TYPE.TOTAL,
        CO_TRANSFER_CARDS_TYPE.SNF,
        CO_TRANSFER_CARDS_TYPE.AMA,
        CO_TRANSFER_CARDS_TYPE.SAFE_DISCHARGE,
        CO_TRANSFER_CARDS_TYPE.TOTAL,
        DECEASED_CARDS_TYPE.TOTAL,
        ADMISSION_CARDS_TYPE.ADMISSION,
        ADMISSION_CARDS_TYPE.READMISSION,
        ADMISSION_CARDS_TYPE.TOTAL
    ].includes(key);
}

async function buildSimpleCard(key, patientData, totalFilterData) {
    let filteredPatients = patientData;
    const { filterDashboard = null } = totalFilterData;
    if (key !== HOSPITAL_CARDS_TYPE.TOTAL) {
        if ([ADMISSION_CARDS_TYPE.ADMISSION, ADMISSION_CARDS_TYPE.READMISSION].includes(key)) {
            filteredPatients = await patientData.filter(item => item.type === key);
        } else {
            filteredPatients = await patientData.filter(item => item.type === "transfer" && item.transferType === key);
        }
    }

    const total = filteredPatients.length;
    const patientIds = filteredPatients.map(item => totalFilterData.isOtherDashboard ? item.refPatientId : item._id);

    let label = key;
    if (filterDashboard === PAGE_TYPE.COMMUNITY_TRANSFER) {
        label = COMMUNITY_CARD_LABELS?.[key];
    } else if (filterDashboard === PAGE_TYPE.HOSPITAL) {
        label = HOSPITAL_CARDS_LABELS?.[key];
    } else if (filterDashboard === PAGE_TYPE.DECEASED) {
        label = DECEASED_CARDS_LABELS?.[key];
    } else {
        label = ADMISSION_CARDS_LABELS?.[key];
    }

    return [{
        _id: key,
        id: key,
        label,
        name: key,
        total,
        value: total,
        originalTotal: total,
        percentage: await itemPercentage(total, totalFilterData.totalForPercentage, "number"),
        patientIds,
        ...(totalFilterData.isOtherDashboard && { otherDashboardIds: filteredPatients.map(item => item._id) })
    }];
}

async function handleMainCard(key, patientData, totalFilterData, ninetyDaysData, sixtyDaysData) {
    if (isSimpleCardKey(key)) {
        return await buildSimpleCard(key, patientData, totalFilterData);
    }

    const handler = mainCardHandlers[key];
    if (!handler) return null;
    if (key === HOSPITAL_CARDS_TYPE.NINETY_DAYS_DATA) {
        return await handler(ninetyDaysData, patientData, totalFilterData);
    } else if (key === CO_TRANSFER_CARDS_TYPE.SIXTY_DAYS_DATA) {
        return await handler(sixtyDaysData, patientData, totalFilterData);
    } else {
        return await handler(patientData, totalFilterData);
    }
}

const directMatchFilters = {
    [HOSPITAL_CARDS_TYPE.INSURANCE_DATA]: 'insuranceId',
    [HOSPITAL_CARDS_TYPE.FLOORS_DATA]: 'floorId',
    [HOSPITAL_CARDS_TYPE.DOCTOR_DATA]: 'doctorId',
    [HOSPITAL_CARDS_TYPE.SHIFT_DATA]: 'shiftName',
    [HOSPITAL_CARDS_TYPE.NURSE_DATA]: 'nurseId',
    [HOSPITAL_CARDS_TYPE.HOSPITAL_DATA]: 'hospitalId',
    [CO_TRANSFER_CARDS_TYPE.SAFE_DISCHARGE_ASS_LIV_DATA]: 'assistantLivId',
    [CO_TRANSFER_CARDS_TYPE.SNF_FACILITY_DATA]: 'facilityId',
};

const customSyncFilters = {
    [HOSPITAL_CARDS_TYPE.DCER_DATA]: (items) => ({
        wasAdmitted: DCERDataFilter({ DCERData: items }),
    }),
    [HOSPITAL_CARDS_TYPE.HOSPITALIZATIONS]: (items) => ({
        reHospitalization: hospitalizationsFilter({ hospitalizations: items }),
    }),
    [HOSPITAL_CARDS_TYPE.RETURNS_DATA]: (items) => ({
        reHospitalization: returnsDataFilter({ returnsData: items }),
    }),
};

async function applyFilter(key, items, patientData, ninetyDaysData, sixtyDaysData) {
    if (isSimpleCardKey(key)) {
        if ([
            CO_TRANSFER_CARDS_TYPE.AMA,
            CO_TRANSFER_CARDS_TYPE.SAFE_DISCHARGE,
            CO_TRANSFER_CARDS_TYPE.SNF,
            HOSPITAL_CARDS_TYPE.UNPLANNED,
            HOSPITAL_CARDS_TYPE.PLANNED
        ].includes(key)) {
            return patientData.filter(item => item.type === "transfer" && item.transferType === key);
        }

        if (key === HOSPITAL_CARDS_TYPE.TOTAL) {
            return patientData;
        }

        if ([ADMISSION_CARDS_TYPE.ADMISSION, ADMISSION_CARDS_TYPE.READMISSION].includes(key)) {
            return patientData.filter(item => item.type === key);
        }
    }

    if (!items || items.length === 0) return patientData;

    if (directMatchFilters[key]) {
        const field = directMatchFilters[key];
        const res = await patientData?.filter((p) => _.includes(items, p[field])) ?? [];
        return res;
    }

    if (customSyncFilters[key] && items.length === 1) {
        const filter = customSyncFilters[key](items);
        return _.isEmpty(filter) ? patientData : await _.filter(patientData, filter) ?? [];
    }

    switch (key) {
        case HOSPITAL_CARDS_TYPE.DAYS_DATA:
            return await patientData.filter(({ day }) => _.includes(items, day));
        case HOSPITAL_CARDS_TYPE.DX_DATA:
            return await patientData.filter(({ dxIds }) => _.intersection(items, dxIds).length > 0);
        case CO_TRANSFER_CARDS_TYPE.SIXTY_DAYS_DATA:
            return await ninetyDaysDataFilter(items, patientData, sixtyDaysData);
        case HOSPITAL_CARDS_TYPE.NINETY_DAYS_DATA:
            return await ninetyDaysDataFilter(items, patientData, ninetyDaysData);
        default:
            return patientData;
    }
}

async function processQuestionFilter(filterItem, data, totalFilterData, items) {
    try {
        const { question } = filterItem;
        const key = filterItem.card?.value;

        if (!question || !key || !Array.isArray(data)) {
            return data;
        }

        let objectCustomRes = {};
        await processDynamicCard(question, data, objectCustomRes, { ...totalFilterData });

        const resultItems = objectCustomRes[key] || [];
        const patientIds = resultItems.reduce((acc, ele) => {
            if (ele?._id && _.includes(items, ele._id)) {
                acc.push(...(ele?.patientIds || []));
            }
            return acc;
        }, []);

        return await filterCustomPatientData(
            data,
            { [key]: items },
            {
                type: key,
                patientIds,
                question: { customQuestionInputType: question.customQuestionInputType },
            },
            true
        );
    } catch (error) {
        console.error('Error in processQuestionFilter:', error);
        return data;
    }
}

async function processCurrentPageFilters(
    filterItems,
    patientData,
    ninetyDaysData,
    sixtyDaysData,
    totalFilterData,
    forComparison
) {
    if (!Array.isArray(filterItems) || !Array.isArray(patientData)) {
        return patientData;
    }

    let processedData = [...patientData];

    for (const filterItem of filterItems) {
        try {
            const { card, items = [] } = filterItem;
            if (!card?.value) continue;

            if (card?.questionId) {
                processedData = await processQuestionFilter(
                    filterItem,
                    processedData,
                    totalFilterData,
                    items
                );
            } else {
                processedData = await applyFilter(
                    card?.value,
                    items,
                    processedData,
                    ninetyDaysData,
                    sixtyDaysData,
                    forComparison
                );
            }
        } catch (error) {
            console.error('Error processing filter item:', error);
            continue; // Continue with next filter item
        }
    }

    return processedData;
}

function validateFilterInputs(patientData, customTab) {
    if (!Array.isArray(patientData)) {
        throw new Error('patientData must be an array');
    }

    if (!customTab || typeof customTab !== 'object') {
        throw new Error('customTab is required and must be an object');
    }

    if (!Array.isArray(customTab.filters)) {
        throw new Error('customTab.filters must be an array');
    }
}

function mergeAndDeduplicateData(filteredPatientData, allDashboardData) {
    const mergedData = [
        ...(Array.isArray(filteredPatientData) ? filteredPatientData : []),
        ...allDashboardData
    ];

    // Remove duplicates based on ID
    const uniqueData = mergedData.filter((item, index, self) =>
        index === self.findIndex(t => t.id === item.id || t._id === item._id)
    );

    return uniqueData;
}

async function processOtherDashboardFilters(
    filterItems,
    dashboardCombineData,
    totalFilterData,
    forComparison
) {
    if (!Array.isArray(filterItems)) {
        return [];
    }

    let dashboardData = dashboardCombineData?.list ?? [];
    if (!Array.isArray(dashboardData)) {
        return [];
    }

    const dashboardNinetyDays = dashboardCombineData?.ninetyDaysData ?? [];
    const dashboardSixtyDays = dashboardCombineData?.sixtyDaysData ?? [];

    for (const filterItem of filterItems) {
        try {
            const { card, items = [] } = filterItem;
            if (!card?.value) continue;

            if (card?.questionId) {
                dashboardData = await processQuestionFilter(
                    filterItem,
                    dashboardData,
                    totalFilterData,
                    items
                );
            } else {
                dashboardData = await applyFilter(
                    card?.value,
                    items,
                    dashboardData,
                    dashboardNinetyDays,
                    dashboardSixtyDays,
                    false
                );
            }
        } catch (error) {
            console.error('Error processing dashboard filter item:', error);
            continue; // Continue with next filter item
        }
    }

    return dashboardData;
}


async function applyCombineFilters({
    patientData,
    ninetyDaysData,
    customTab,
    sixtyDaysData,
    pageType,
    customCombineTabData = [],
    forComparison = false,
    totalFilterData = {}
}) {
    try {
        // Input validation
        validateFilterInputs(patientData, customTab);

        const filters = customTab.filters;
        let filteredPatientData = [...patientData];
        let allDashboardData = [];

        // Process filters in parallel where possible
        const filterPromises = filters.map(async (filter) => {
            const filterDashboard = filter?.dashboard;

            if (filterDashboard === pageType || (pageType === PAGE_TYPE.OVERALL && (filterDashboard === CUSTOM_TAB_PAGE_TYPE.OVERALL_INCOMING || filterDashboard === CUSTOM_TAB_PAGE_TYPE.OVERALL_OUTGOING))) {
                return {
                    type: 'currentPage',
                    data: await processCurrentPageFilters(
                        filter.filters,
                        filteredPatientData,
                        ninetyDaysData,
                        sixtyDaysData,
                        totalFilterData,
                        forComparison
                    )
                };
            } else if (filterDashboard !== pageType) {

                return {
                    type: 'otherDashboard',
                    data: await processOtherDashboardFilters(
                        filter.filters,
                        customCombineTabData[filterDashboard],
                        totalFilterData,
                        forComparison
                    )
                };
            }
            return null;
        });

        const results = await Promise.all(filterPromises);

        // Process results
        for (const result of results) {
            if (!result) continue;

            if (result.type === 'currentPage') {
                filteredPatientData = result.data;
            } else if (result.type === 'otherDashboard' && Array.isArray(result.data) && result.data.length > 0) {
                allDashboardData.push(...result.data);
            }
        }

        // Merge all filtered data with deduplication
        return mergeAndDeduplicateData(filteredPatientData, allDashboardData);

    } catch (error) {
        console.error('Error in applyCombineFilters:', error);
        return Array.isArray(patientData) ? patientData : [];
    }
}

async function processCustomChartFilters(chartMainDataArr, filters, newChecked) {
    const filterList = filters?.filterListData?.[filters?.type];

    const isOtherDashboard = filters?.question?.isAnotherDashboard || filters?.question?.level > 0;

    const patientIds =
        filterList?.reduce((acc, ele) => {
            const { _id, patientIds = [], Ids = [], otherDashboardIds = [] } = ele || {};

            if (!_.includes(newChecked, _id)) return acc;

            const isSixtyOrNinetyDay =
                typeof filters?.type === 'string' &&
                (
                    filters.type.includes('sixtyDaysData') ||
                    filters.type.includes('ninetyDaysData') ||
                    /Days.*tab/i.test(filters.type)
                );
            if (filters?.question?.type === CUSTOM_TAB_TYPE.combineTab) {
                if (isOtherDashboard) {
                    acc.push(...(isSixtyOrNinetyDay ? patientIds.length ? patientIds : Ids : otherDashboardIds));
                } else {
                    acc.push(...(patientIds.length ? patientIds : Ids));
                }
            } else {
                acc.push(...(patientIds.length ? patientIds : Ids));
            }
            return acc;
        }, []) || [];

    const newChartFilters = filterCustomPatientData(
        chartMainDataArr,
        { [filters?.type]: newChecked },
        {
            ...filters,
            patientIds,
        }
    );
    return newChartFilters;
}
// end of custom tabs functions
module.exports = {
    processCustomChartFilters,
    applyCombineFilters,
    getAdmissionADTFieldNames,
    getAdmissionFieldNames,
    getHospitalFieldNames,
    getDeceasedFieldNames,
    getOverallFieldNames,
    getCommunicationFieldNames,
    communityUpdateFilterListData,
    deceaseUpdateFilterListData,
    hospitalUpdateFilterListData,
    hospitalCustomAlertFilterListData,
    communityTransferCustomAlertFilterListData,
    deceasedCustomAlertFilterListData,
    processDynamicCard,
    filterCustomPatientData,
    dynamicCardFilter,
    dynamicCardFilterAlerts,
    // custom tabs functions
    processFilterForTab,
    getDashboardWiseData,
    getCustomTabsCards,
    handleMainCard,
    applyFilter,
    assignMatchedPatientIds,
    buildCustomTabsObject
};
