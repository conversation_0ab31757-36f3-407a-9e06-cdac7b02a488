import { LOGIN, LOGOUT, UPDATE_PASSWORD_RESET_STATUS } from "../types";

const reducer = (state = {}, action) => {
  switch (action.type) {
    case LOGIN:
      return action.payload;
    case LOGOUT:
      return state = {};
    case UPDATE_PASSWORD_RESET_STATUS:
      return {
        ...state,
        requirePasswordReset: action.payload,
      };
    default:
      return state;
  }
};

export default reducer;
