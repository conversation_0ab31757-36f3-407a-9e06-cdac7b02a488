import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  LineElement,
  PointElement,
} from "chart.js";
import dayjs from "dayjs";
import ordinal from "ordinal";
import { useEffect, useState } from "react";
import { Line } from "react-chartjs-2";
ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend
);

const tooltipLabel = ({ raw }) => {
  return convertMinutes(raw);
};

// Convert minutes to hours and minutes
const convertMinutes = (minutes, forDecimal) => {
  if (!minutes) return "0:00";
  const hours = Math.floor(minutes / 60);
  const minutesLeft = minutes % 60;

  if (forDecimal) {
    return `${hours}:${minutesLeft}`;
  }

  return `${
    hours > 0 ? `${hours} hour${hours > 1 ? "s" : ""} ` : ""
  }${minutesLeft} ${minutesLeft > 1 ? "minutes" : "minute"}`;
};

// const timeToDecimal = (t) => {
//   var arr = t.split(":");
//   var dec = parseInt((arr[1] / 6) * 10, 10);

//   return parseFloat(parseInt(arr[0], 10) + "." + (dec < 10 ? "0" : "") + dec);
// };

// const getHoursDecimal = (value) => {
//   const forDecimal = convertMinutes(value, true);
//   const inDecimal = timeToDecimal(forDecimal);
//   return inDecimal;
// };

export const options = {
  responsive: true,
  scales: {
    y: {
      ticks: {
        display: false,
      },
      min: 0,
      max: 540,
    },
    x: {},
  },
  plugins: {
    legend: {
      position: "top",
      align: "start",
      display: true,
    },
    title: {
      display: true,
      // text: "Chart.js Bar Chart",
      align: "start",
    },
    tooltip: {
      callbacks: {
        label: tooltipLabel,
      },
    },
  },
};

const UserTimeLog = ({ user, dateInWeek }) => {
  const [labels, setLabels] = useState([]);
  const [weekTotal, setWeekTotal] = useState(undefined);
  // const [values, setValues] = useState([]);
  const [userData, setUserData] = useState(undefined);
  const setUp = async () => {
    // localLogs.data.map(async (log) => {

    // const uniqueHours = [...new Set(user.logs.map((item) => item.hour))];

    // const standardTimeLabels = uniqueHours.map((hour) => {
    //   const militaryTime = hour;
    //   const standardTime =
    //     militaryTime > 12 ? militaryTime - 12 : militaryTime;
    //   return standardTime;
    // });

    let objectified = await user.values.map((value, idx) => {
      return { day: user.labels[idx], value: value };
    });

    let labels = [];
    let values = [];

    [0, 1, 2, 3, 4, 5, 6].map((day) => {
      let date = dayjs(dateInWeek).startOf("week").add(day, "day").date();
      labels.push(
        `${dayjs(dateInWeek)
          .startOf("week")
          .add(day, "day")
          .format("ddd")} - ${dayjs(dateInWeek)
          .startOf("week")
          .add(day, "day")
          .format("MMM")} ${ordinal(
          dayjs(dateInWeek).startOf("week").add(day, "day").date()
        )}`
      );
      values.push(
        objectified.find((item) => item.day === date)?.value
          ? objectified.find((item) => item.day === date)?.value
          : 0
      );
      return dayjs(dateInWeek).startOf("week").add(day, "day").date();
    });

    setLabels(labels);
    setUserData(values);
    setWeekTotal(user.weekTotal);
    // setUser(user);
    // });
  };

  useEffect(() => {
    setUp();
    // eslint-disable-next-line
  }, [user]);
  return (
    <Line
      height={"100%"}
      options={{ ...options, maintainAspectRatio: false, bezierCurve: true }}
      data={{
        labels,
        datasets: [
          {
            // stepped: "middle",
            // fill: "origin",
            tension: 0.12,
            tooltip: {
              enabled: false,
            },

            label: `Logs for ${user.user.fullName}, total: ${convertMinutes(
              weekTotal
            )}`,
            data: userData,
            borderColor: "rgb(255, 99, 132)",
            backgroundColor: "rgba(255, 99, 132, 0.5)",
          },
        ],
      }}
    />
  );
};

export default UserTimeLog;
