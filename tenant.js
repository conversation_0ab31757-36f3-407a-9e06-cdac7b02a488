const axios = require('axios');

const apiRequest = async (endpoint, method, data) => {
    const baseUrl = process.env.REACT_APP_TENANT_API_BASE_URL;
    const apiKey = process.env.REACT_APP_TENANT_API_KEY;

    if (!baseUrl) {
        console.error("Required environment variable REACT_APP_TENANT_API_BASE_URL is not provided.");
        return;
    }

    if (!apiKey) {
        console.error("Required environment variable REACT_APP_TENANT_API_KEY is not provided.");
        return;
    }

    try {
        const response = await axios({
            url: `${baseUrl}${endpoint}`,
            method,
            headers: {
                "x-api-key": apiKey,
            },
            data,
        });

        return response;
    } catch (error) {
        console.error(`Error making ${method} request to ${endpoint}. Error:`, error);
        throw error;
    }
};

const createExternalUser = async (data) => {
    try {
        const payload = {
            email: data.email,
            tenantId: data.tenantId,
        };

        const response = await apiRequest('/users', 'POST', payload);

        if (response.status === 201) {
            return response.data;
        } else {
            const message = `Failed to create user for email: ${data.email}, tenantId: ${data.tenantId}. Status code: ${response.status}`;
            console.error(message);
            throw new Error(message);
        }
    } catch (error) {
        console.error(`Error creating external user for email: ${data.email}, tenantId: ${data.tenantId}.`);
        throw error;
    }
};

const updateExternalUser = async (data) => {
    try {
        const endpoint = `/users/${data.tenantUserId}`;

        const payload = {
            email: data.email,
        };

        const response = await apiRequest(endpoint, 'PUT', payload);

        if (response.status === 200) {
            return response.data;
        } else {
            const message = `Failed to update user with ID: ${data.tenantUserId}. Status code: ${response.status}`;
            console.error(message);
            throw new Error(message);
        }
    } catch (error) {
        console.error(`Error updating external user with ID: ${data.tenantUserId}.`);
        throw error;
    }
};

const deleteExternalUser = async (data) => {
    try {
        const endpoint = `/users/${data.tenantUserId}`;

        const response = await apiRequest(endpoint, 'DELETE', null);

        if (response.status === 200 || response.status === 204) {
        } else {
            const message = `Failed to delete user with ID: ${data.userId}. Status code: ${response.status}`;
            console.error(message);
            throw new Error(message);
        }
    } catch (error) {
        console.error(`Error deleting external user with ID: ${data.userId}.`);
        throw error;
    }
};

module.exports = {
    createExternalUser,
    updateExternalUser,
    deleteExternalUser
};