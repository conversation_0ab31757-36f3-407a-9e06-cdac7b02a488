const authWithRole = require("../middleware/auth-with-role");
const { createNote, getNotes, getNote, deleteNote, shareNoteEmail } = require("../helpers/note");

const route = require("express").Router();

// Create new note
route.post("/", authWithRole('manageNote'), async (req, res) => {
  try {
    let note = await createNote(req);
    res.send(note);
  } catch (error) {
    return res.status(500).send({ message: error.message });
  }
});

route.post("/share", authWithRole('manageNote'), async (req, res) => {
  try {
    let note = await shareNoteEmail(req);
    res.send(note);
  } catch (error) {
    return res.status(500).send({ message: error.message });
  }
});

route.get("/all", authWithRole('manageNote'), async (req, res) => {
  try {
    const notes = await getNotes(req);
    res.send(notes);
  } catch (error) {
    res.status(500).send(`Error getting user`);
  }
});

// Get note/account by ID
route.get("/:id?", authWithRole('manageNote'), async (req, res) => {
  try {
    let note = await getNote(req);
    res.send(note);
  } catch (error) {
    res.status(500).send(error);
  }
});

// route.put("/:id", authWithRole('manageNote'), async (req, res) => {
//   try {
//     const account = await updateAccount(req.params.id, req.body);
//     res.send(account);
//   } catch (error) {
//     res.status(500).send({ message: error.message });
//   }
// });

route.delete("/:id", authWithRole('manageNote'), async (req, res) => {
  try {
    let note = await deleteNote(req.params.id);
    res.send(note);
  } catch (error) {
    res.status(500).send(error);
  }
});

module.exports = route;
