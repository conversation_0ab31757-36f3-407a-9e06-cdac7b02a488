require("../models/User");
const axios = require('axios');
const mongoose = require('mongoose');
const readline = require('readline');
const User = mongoose.model("user");


// Helper function to prompt for console input
const promptUser = (query) => {
  return new Promise((resolve) => {
    const rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout,
    });
    rl.question(query, (answer) => {
      rl.close();
      resolve(answer.trim());
    });
  });
};

async function main() {
  try {
    // Prompt for API details
    const baseApiUrl = await promptUser(
      "Enter Tenant API Base URL (e.g. https://tenant-api.simplesnf.com): "
    );
    const apiKey = await promptUser("Enter x-api-key: ");

    // Prompt for tenant creation details
    const domain = await promptUser("Enter tenant domain (e.g. org1): ");
    const auth0Domain = await promptUser("Enter auth0 domain: (e.g. dev-88r7h7v3duwwxtjn.us.auth0.com): ");
    const auth0ClientId = await promptUser("Enter auth0 client id: (e.g. g4674MUIIjOz4bvkgnxHovyN1yjzEd8o) ");
    const tenantApiBaseUrl = await promptUser( "Enter ApiBaseUrl: (e.g. https://org1-api.snfdatasolutions.com) ");
    const tenantName = await promptUser( "Enter Tenant Name: (e.g. Org1) ");

    // Connect to MongoDB
    const mongoURI = await promptUser("Enter MongoDB URI: ");
    console.log("Connecting to MongoDB...");
    await mongoose.connect(mongoURI, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    console.log("Connected to MongoDB.");

    // Prepare tenant creation payload
    const tenantPayload = {
      domain,
      auth0Domain,
      auth0ClientId,
      apiBaseUrl: tenantApiBaseUrl,
      name: tenantName,
    };

    // Create tenant by calling the /tenants endpoint
    console.log("Creating tenant...");
    const tenantResponse = await axios.post(
      `${baseApiUrl}/tenants`,
      tenantPayload,
      {
        headers: {
          "x-api-key": apiKey,
          "Content-Type": "application/json",
        },
      }
    );

    // Assuming the response contains an 'id' for the created tenant
    const tenantId = tenantResponse.data.id;
    console.log(`Tenant created with id: ${tenantId}`);

    // Retrieve all users from the current DB
    const users = await User.find();
    console.log(`Found ${users.length} user(s) in the database.`);

    if (!users.length) {
      console.log("No users found, nothing to create. Exiting.");
      process.exit(0);
    }

    const userPayloads = users.map((user) => {
      return {
        email: user.email,
        tenantId: tenantId,
      };
    });

    console.log("Creating tenant users in a single batch...");
    const batchResponse = await axios.post(
      `${baseApiUrl}/users/batch`,
      userPayloads,
      {
        headers: {
          "x-api-key": apiKey,
          "Content-Type": "application/json",
        },
      }
    );
    console.log("All tenant users created successfully.");

    const createdUsers = batchResponse.data;

    const emailToCreatedUserId = new Map();
    for (const createdUser of createdUsers) {
      emailToCreatedUserId.set(createdUser.email, createdUser.id);
    }

    const bulkOps = users.map((localUser) => ({
      updateOne: {
        filter: { _id: localUser._id },
        update: {
          $set: { tenantUserId: emailToCreatedUserId.get(localUser.email) },
        },
      },
    }));

    await User.bulkWrite(bulkOps);
    console.log("Local user documents updated with created user IDs successfully.");

    console.log("Process completed successfully.");
    process.exit(0);
  } catch (err) {
    console.error("Error occurred:", err.message);
    process.exit(1);
  }
}

main();
