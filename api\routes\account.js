const { createAccount, getAccount, getAllAccounts, updateAccount } = require("../helpers/account");
const mongoose = require("mongoose");
const authWithRole = require("../middleware/auth-with-role");
const { saveLogs } = require("../utilis/common");
const Account = mongoose.model("account");
const Facility = mongoose.model("facility");

const route = require("express").Router();

// Create new account
route.post("/", authWithRole('manageAccount'), async (req, res) => {
  try {
    let account = await createAccount(req.body);
    res.send(account);
  } catch (error) {
    return res.status(500).send({ message: error.message });
  }
});

route.get("/all", authWithRole('manageAccount'), async (req, res) => {
  try {
    const accounts = await getAllAccounts();
    res.send(accounts);
  } catch (error) {
    res.status(500).send(`Error getting user`);
  }
});

// Get accounts/account by ID
route.get("/:id?", authWithRole('getAccountList'), async (req, res) => {
  try {
    let account = await getAccount(req.params.id, req.user);
    res.send(account);
  } catch (error) {
    res.status(500).send(error);
  }
});

route.put("/:id", authWithRole('manageAccount'), async (req, res) => {
  try {
    const account = await updateAccount(req.params.id, req.body);
    res.send(account);
  } catch (error) {
    res.status(500).send({ message: error.message });
  }
});

route.delete("/:id", authWithRole('manageAccount'), async (req, res) => {
  try {
    const accountId = req.params.id;
    await Facility.remove({ accountId });
    const deleted = await Account.findByIdAndDelete(mongoose.Types.ObjectId(accountId));
    await saveLogs(
      deleted,
      "accountDelete",
      {
        accountId: req?.headers?.accountid,
        facilityId: req?.headers?.facilityid ? mongoose.Types.ObjectId(req?.headers?.facilityid) : null,
        userId: req?.user?._id
      },
      "Account deleted successfully"
    );
    res.send(deleted);
  } catch (error) {
    res.send(error);
  }
});

module.exports = route;
