const mongoose = require("mongoose");
const { ADMISSION_FILTER_TYPE } = require("../../types/common.type");
const ReportsSubscriptions = mongoose.model("reportsSubscriptions");

const createSubscription = async (req) => {
    const user = req?.user;
    const { accountid } = req.headers;
    const { automaticallyReport, page, type, activeFacilities = [], title, isAdt = false, transferType = null, transferTypes = null, admissionReportType, isGraphReport = false } = req.body;
    try {
        const reportsSubscriptionsSave = new ReportsSubscriptions({
            userId: mongoose.Types.ObjectId(user._id),
            accountId: mongoose.Types.ObjectId(accountid),
            facilityIds: activeFacilities,
            page: page,
            filterCardType: admissionReportType === ADMISSION_FILTER_TYPE.ADT ? null : type,
            adtType: admissionReportType === ADMISSION_FILTER_TYPE.ADT ? type : null,
            reportFileType: automaticallyReport?.reportFileType || null,
            interval: automaticallyReport?.type,
            name: automaticallyReport?.name,
            transferType: transferType,
            transferTypes: transferTypes,
            filtersData: req.body,
            title: title,
            isGraphReport,
            isSendReportSeparate: automaticallyReport?.isSendReportSeparate ?? false
        });

        const saved = await reportsSubscriptionsSave.save()
        return { status: 200, data: saved }

    } catch (err) {
        console.log(err, 'err for saving');
    }
};

const findSavedReportConfig = async (page, userId) => {
    const reportsSubscriptions = await ReportsSubscriptions.findOne({
        page,
        userId: mongoose.Types.ObjectId(userId),
    });
    if (reportsSubscriptions) {
        return true
    } else {
        return false
    }
}

const getSubscriptionByPage = async (req) => {
    const user = req?.user;

    const reportsSubscriptions = await ReportsSubscriptions.aggregate([
        {
            $match: {
                // page: req?.query?.page,
                userId: mongoose.Types.ObjectId(user?.id),
            }
        },
        {
            $lookup: {
                from: "facilities", // The name of the Facility collection
                localField: "facilityIds",
                foreignField: "_id",
                as: "facilities"
            }
        },
        {
            $addFields: {
                facilitiesNames: { $map: { input: "$facilities", as: "facility", in: "$$facility.name" } }
            }
        },
        {
            $project: {
                facilities: 0 // Exclude the full facilities data if not needed
            }
        }
    ]);

    // const reportsSubscriptions = await ReportsSubscriptions.find({
    //     page: req?.query?.page,
    //     userId: mongoose.Types.ObjectId(user?.id),
    // });
    return reportsSubscriptions;
}

const deleteSubscription = async id => {
    let deleted = await ReportsSubscriptions.findByIdAndDelete(id);
    return { status: 200, data: deleted }
};

const updateSubscription = async (id, data) => {
    let subscription = await ReportsSubscriptions.findById(id);
    subscription.name = data.name;
    subscription.interval = data.interval;
    const res = await subscription.save();
    return { status: 200, data: res }
}

module.exports = {
    createSubscription,
    findSavedReportConfig,
    getSubscriptionByPage,
    deleteSubscription,
    updateSubscription
};
