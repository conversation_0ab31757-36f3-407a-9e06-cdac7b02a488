import React, {  } from "react";
import {
    Dialog,
    Divider,
    DialogTitle,
    DialogContent,
    DialogActions,
    Button,
} from "@mui/material";
import { Box } from "@mui/system";
import QuestionAccordion from "./components/QuestionAccordion";


const QuestionsPreview = ({
    question,
    handleClose,
}) => {

    return (
        <>        
            <Dialog
                open={true}
                onClose={handleClose}
                maxWidth="lg"
                width="md"
            >
                <DialogTitle>Question preview</DialogTitle>
                <Divider />
                <DialogContent>
                    <Box sx={{ display: "flex", justifyContent: "center", alignItems: "center", height: "100%", minWidth: "300px" }}>
                        <>
                            <QuestionAccordion
                                key={question._id}
                                question={question}
                                isPreview={true}
                                isSelected={[question].includes(question._id)}                                
                            />
                        </>
                    </Box>
                </DialogContent>
                <Divider />
                <DialogActions>
                    <Button onClick={handleClose} color="secondary">
                        Close
                    </Button>
                </DialogActions>


            </Dialog>

        </>
    );
};

export default QuestionsPreview;
