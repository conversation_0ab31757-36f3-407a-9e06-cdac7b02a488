const mongoose = require("mongoose");
const QuickGlace = mongoose.model("quickGlace");
const Facility = mongoose.model("facility");
const Patient = mongoose.model("patient");
const { getCensusAverageInfo } = require("./census");
const { getPercentage, projectionPerMonth } = require("../utilis/common");
const _ = require("lodash");
const { ADT_TYPES, ADT_SUB_TYPES } = require("../../types");
const { toStartFilterDate, toEndFilterDate } = require("../utilis/date-format");

// Create new account
const createQuickGlace = async req => {
  const user = req?.user;
  const { accountid } = req.headers;
  await QuickGlace.deleteMany({ accountId: accountid, userId: user._id });
  try {
    const quickGlaceSave = new QuickGlace({
      accountId: mongoose.Types.ObjectId(accountid),
      userId: user._id,
      ...req.body,
    });
    const saved = await quickGlaceSave.save();
    return saved;
  } catch (e) {
    console.log(e, "erro");
  }
};

const getQuickGlaces = async req => {
  const { accountid } = req.headers;
  const user = req.user;
  const quickGlace = await QuickGlace.find({
    accountId: accountid,
    userId: mongoose.Types.ObjectId(user._id),
  });

  return quickGlace;
};

const getQuickGlace = async req => {
  const user = req.user;
  const { id } = req.query;
  const quickGlace = await QuickGlace.findOne({
    _id: mongoose.Types.ObjectId(id),
    userId: mongoose.Types.ObjectId(user._id),
  });
  return quickGlace;
};

const getTotalCount = async query => {
  let total = 0;
  const resData = await Patient.aggregate([
    {
      $match: {
        $and: [query],
      },
    },
    {
      $group: {
        _id: "$id",
        total: {
          $sum: 1,
        },
      },
    },
  ]);
  if (resData.length > 0) {
    total = resData[0].total;
  }
  return total;
};

const getQuickGlacesFilterData = async req => {
  const { accountid } = req.headers;
  const user = req.user;
  let facilities;
  const { filter, filterValues } = req.body;
  const { startDate, endDate } = filter;
  let listData = [];

  let startDateFilter = await toStartFilterDate(startDate)
  let endDateFilter = await toEndFilterDate(endDate);

  if (user && user.role && user.role.slug == "user") {
    const filterIds = [];
    const userFacilities = user.facilities || [];
    userFacilities.map(ele => {
      if (ele.read || ele.write) {
        filterIds.push(ele.facilityId.toString());
      }
    });
    facilities = await Facility.find({ accountId: accountid, _id: { $in: filterIds } });
  } else {
    facilities = await Facility.find({ accountId: accountid });
  }

  if (facilities && facilities.length > 0) {
    await filterData(facilities, async item => {
      const censusInfo = await getCensusAverageInfo(
        startDateFilter,
        endDateFilter,
        [item._id]        
      );
      let totalData = [];

      if (filterValues && filterValues.length > 0) {
        let queryDefault = new Object();
        queryDefault.dateOfADT = {
          $gte: startDateFilter,
          $lt: endDateFilter,
        };
        queryDefault.facilityId = item._id;
        let filterValuesData = _.orderBy(filterValues, "index", "asc");
        await filterData(filterValuesData, async element => {
          const { id, parentId } = element;
          let totalDataObject = new Object();
          totalDataObject.label = element.label;
          totalDataObject.id = element.parentId;
          totalDataObject.index = element.index;
          totalDataObject.total = 0;
          totalDataObject.percentage = 0;
          totalDataObject.projectionPercentage = 0;
          totalDataObject.totalProjection = null;

          let query = new Object();
          query = { ...queryDefault };
          if (parentId == "transfer") {
            query.type = { $eq: ADT_TYPES.TRANSFER };
            if (id === "totalTransfer") {
              query.transferType = {
                $in: [ADT_SUB_TYPES.PLANNED_HOSPITAL_TRANSFER, ADT_SUB_TYPES.UNPLANNED_HOSPITAL_TRANSFER],
              };
            } else {
              query.transferType = { $in: [id] };
            }
            const totalValue = await getTotalCount(query);
            totalDataObject.total = totalValue;
            totalDataObject.percentage = await getPercentage(
              totalDataObject.total,
              censusInfo.censusAverage
            );
            totalDataObject.projectionPercentage = await getPercentage(
              await projectionPerMonth(totalDataObject.total, filter),
              censusInfo.censusAverage
            );
            totalData.push(totalDataObject);
          }

          if (parentId == "communityTransfer") {
            if (id === "total") {
              query.transferType = { $in: ["safeDischarge", "SNF", "AMA"] };
            } else {
              query.transferType = { $in: [id] };
            }
            const totalValue = await getTotalCount(query);
            totalDataObject.total = totalValue;
            totalDataObject.percentage = await getPercentage(
              totalDataObject.total,
              censusInfo.censusAverage
            );
            totalDataObject.projectionPercentage = await getPercentage(
              await projectionPerMonth(totalDataObject.total, filter),
              censusInfo.censusAverage
            );
            totalData.push(totalDataObject);
          }

          if (parentId == "deceased") {
            query.transferType = { $in: ["deceased"] };
            const totalValue = await getTotalCount(query);
            totalDataObject.total = totalValue;
            totalDataObject.percentage = await getPercentage(
              totalDataObject.total,
              censusInfo.censusAverage
            );
            totalDataObject.projectionPercentage = await getPercentage(
              await projectionPerMonth(totalDataObject.total, filter),
              censusInfo.censusAverage
            );
            totalData.push(totalDataObject);
          }
          if (parentId == "admission") {
            if (id === "totalAdmissions") {
              query.type = { $in: [ADT_TYPES.ADMISSIONS, ADT_TYPES.READMISSIONS] };
            } else {
              query.type = { $in: [id] };
            }
            const totalValue = await getTotalCount(query);
            totalDataObject.total = totalValue;
            totalDataObject.percentage = await getPercentage(
              totalDataObject.total,
              censusInfo.censusAverage
            );
            totalDataObject.projectionPercentage = await getPercentage(
              await projectionPerMonth(totalDataObject.total, filter),
              censusInfo.censusAverage
            );
            totalData.push(totalDataObject);
          }

          if (parentId == "overall") {
            if (id === "totalOverall") {
              query.type = { $nin: [ADT_TYPES.TRANSFER] };
              const totalIncoming = await getTotalCount(query);
              delete query.type;
              query.type = { $in: [ADT_TYPES.TRANSFER] };
              const totalOutgoing = await getTotalCount(query);
              let totalOverall = totalIncoming - totalOutgoing;
              if (totalOverall == 0) {
                totalDataObject.total = 0;
              } else {
                totalDataObject.total = totalOverall > 0 ? `+${totalOverall}` : `-${totalOverall}`; 
              }
              totalDataObject.percentage = null;
              totalDataObject.totalProjection = await projectionPerMonth(totalOverall, filter);
              totalData.push(totalDataObject);
            } else {
              if (id == "totalOutgoing") {
                query.type = { $in: [ADT_TYPES.TRANSFER] };
              }
              if (id == "totalIncoming") {
                query.type = { $nin: [ADT_TYPES.TRANSFER] };
              }
              const totalValue = await getTotalCount(query);

              totalDataObject.total = totalValue;
              totalDataObject.percentage = await getPercentage(
                totalDataObject.total,
                censusInfo.censusAverage
              );
              totalDataObject.projectionPercentage = await getPercentage(
                await projectionPerMonth(totalDataObject.total, filter),
                censusInfo.censusAverage
              );
              totalData.push(totalDataObject);
            }
          }
        });
      }

      let latestObj = new Object();
      const ID = item._doc._id.toString();
      latestObj = item._doc;
      latestObj.censusInfo = censusInfo;
      latestObj.censusAverage = censusInfo.censusAverage;
      latestObj.id = ID;
      latestObj.totalData = totalData.length > 0 ? _.orderBy(totalData, "index", "asc") : totalData;
      listData.push(latestObj);
    });
  }
  listData = _.orderBy(listData, "censusAverage", "desc");
  return listData;
};

const filterData = async (arr, callback) => {
  const fail = Symbol();
  return (await Promise.all(arr.map(async item => ((await callback(item)) ? item : fail)))).filter(
    i => i !== fail
  );
};
module.exports = { createQuickGlace, getQuickGlace, getQuickGlaces, getQuickGlacesFilterData };
