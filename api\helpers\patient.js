const mongoose = require("mongoose");
const Patient = mongoose.model("patient");
const Facility = mongoose.model("facility");
const Account = mongoose.model("account");
const Census = mongoose.model("census");
const Log = mongoose.model("log");
const dayjs = require("dayjs");
const moment = require("moment");
const { checkADTDuplicate, toStartFilterDate, toEndFilterDate, toSaveDate } = require("../utilis/date-format");
const { ADT_TYPES, ADT_SUB_TYPES } = require("../../types");
const _ = require("lodash");
const { RELATIONS } = require("../../types/common.type");
const { saveLogs } = require("../utilis/common");
const { isOnlyHospitalTabAccess } = require("../../utils/common");

const FacilityManuallyEndOfADT = mongoose.model("facilityManuallyEndOfADT");
const User = mongoose.model("user");
const ReportsSubscriptions = mongoose.model("reportsSubscriptions");
const Question = mongoose.model("question");
const Setting = mongoose.model("settings");
const Logs = mongoose.model("log");
const Filter = mongoose.model("filter");
const AlertReport = mongoose.model("alertReport");


const addPatient = async (data, params, user = null) => {
    let facility, localPatient;
    let isAdd = false;

    if (params?.id) {
        localPatient = await Patient.findById(params?.id);
        if (localPatient.type !== data.type) {
            isAdd = true;
        }
        localPatient = Object.assign(localPatient, new Patient(data));
        localPatient.logs.push({
            description: "edited",
            userId: user?.id,
            date: Date.now(),
            userName: user?.fullName
        });
        if (!localPatient.accountId || !localPatient.facilityId) {
            await saveLogs(
                localPatient,
                "updatePatient",
                {
                    accountId: localPatient?.accountId,
                    facilityId: localPatient?.facilityId,
                    userId: user?.id
                },
                "patient updated without facility or account"
            );
        }
    } else {
        isAdd = true;
        facility = await Facility.findById(data.facilityId);
        data.accountId = facility.accountId;
        data.createdBy = user?.id;
        data.logs = [
            {
                description: "Input",
                userId: user?.id,
                date: Date.now(),
                userName: user?.fullName
            }
        ];
        localPatient = new Patient(data);

        if (!localPatient.accountId || !localPatient.facilityId) {
            await saveLogs(localPatient, "addPatient", {
                accountId: localPatient?.accountId,
                facilityId: localPatient?.facilityId,
                userId: user?.id
            },
                "patient updated without facility or account");
        }
    }

    let saved = await localPatient
        .save()
        .catch(e => {
            return e;
        })
        .catch(() => {
            return "error";
        });

    if (saved === "error") {
        return "error";
    }

    await updateAdmissionIdForTransfer(saved);
    if (isAdd) {
        await updateCensusInfo(saved);
    }
    if (saved && saved._id) {
        let relations = [
            RELATIONS.FACILITY,
            RELATIONS.DOCTOR,
            RELATIONS.UNIT,
            RELATIONS.PAYER_SOURCE_INSURANCE,
            RELATIONS.INSURANCE,
            RELATIONS.DX,
            RELATIONS.NURSE,
            RELATIONS.HOSPITAL,
            RELATIONS.SNF,
            RELATIONS.TRANSFER_TO_WHICH_ASSISTED_LIVING,
        ];
        let patient = await Patient.findById(saved?._id).populate(relations);
        return patient
    }
    return saved;
};

const updateAdmissionIdForTransfer = async (saved) => {
    if (saved.type === "transfer") {
        let query = {
            ...(await checkADTDuplicate(saved, true)),
            facilityId: mongoose.Types.ObjectId(saved.facilityId),
            dateOfADT: {
                $lte: saved.dateOfADT
            },
            type: { $in: ["admission", "readmission", "return"] }
        };
        let latestAdmission = await Patient.findOne({ ...query }).sort({ dateOfADT: -1, createdAt: -1 });
        if (latestAdmission) {
            if (latestAdmission.type == "admission" || latestAdmission.type == "readmission") {
                let isSameDate = false;

                if (moment.utc(saved.dateOfADT).isSame(latestAdmission.dateOfADT) && moment.utc(latestAdmission.createdAt).isBefore(saved.createdAt)) {
                    isSameDate = true;
                }
                if (moment.utc(latestAdmission.dateOfADT).isBefore(saved.dateOfADT) || isSameDate == true) {

                    return await Patient.findByIdAndUpdate(
                        saved._id,
                        { admissionId: latestAdmission._id }
                    );
                }
            }
        } else if (saved.admissionId) {
            return await Patient.findByIdAndUpdate(
                saved._id,
                { admissionId: null }
            );
        }
    }

    return true
}
const updateCensusInfo = async saved => {
    if (dayjs(saved.dateOfADT).isBefore(dayjs(new Date()).startOf("day"), "day")) {
        const inc = saved.type === "transfer" ? -1 : 1;
        // await Census.updateMany(
        //     {
        //         facilityId: saved.facilityId,
        //         date: { $gte: saved.dateOfADT },
        //     },
        //     {
        //         $inc: {
        //             count: inc,
        //         },
        //     }
        // );

        await Census.updateMany(
            {
                facilityId: saved.facilityId,
                date: { $gte: saved.dateOfADT },
            },
            [
                {
                    $set: {
                        count: {
                            $cond: [{ $or: [{ $eq: ['$count', null] }, { $not: ['$count'] }] }, 0, '$count'],
                        },
                    },
                },
                {
                    $set: {
                        count: { $add: ['$count', inc] },
                    },
                },
            ]
        );
    }
};

const getSameDateTransfer = async (body = null) => {
    if (body) {
        let transferType = [];
        if (body.transferType && body.transferType === ADT_SUB_TYPES.PLANNED_HOSPITAL_TRANSFER || body.transferType === ADT_SUB_TYPES.UNPLANNED_HOSPITAL_TRANSFER) {
            transferType = [ADT_SUB_TYPES.PLANNED_HOSPITAL_TRANSFER, ADT_SUB_TYPES.UNPLANNED_HOSPITAL_TRANSFER];
        } else if (body.transferType && body.transferType === ADT_SUB_TYPES.SAFE_DISCHARGE || body.transferType === ADT_SUB_TYPES.SNF || body.transferType === ADT_SUB_TYPES.AMA) {
            transferType = [ADT_SUB_TYPES.SAFE_DISCHARGE, ADT_SUB_TYPES.SNF, ADT_SUB_TYPES.AMA];
        } else if (body.transferType && body.transferType === ADT_SUB_TYPES.DECEASED) {
            transferType = [ADT_SUB_TYPES.DECEASED];
        }

        let query = {
            ...(await checkADTDuplicate(body)),
            ...transferType.length > 0 && { transferType: { $in: transferType } },
            ...body.ADTtype && { type: body.ADTtype },
            facilityId: body.facilityId,
            // transferType: { $in: [ADT_SUB_TYPES.PLANNED_HOSPITAL_TRANSFER, ADT_SUB_TYPES.UNPLANNED_HOSPITAL_TRANSFER] },
            dateOfADT: {
                $gte: await toStartFilterDate(body.dateOfADT),
                $lte: await toEndFilterDate(body.dateOfADT),
            }
        };

        if (body.type && body.type == "edit" && body.id) {
            query._id = { $ne: mongoose.Types.ObjectId(body.id) };
        }

        let ADTResponse = await Patient.findOne(query)
            .select({ _id: 1, firstName: 1, lastName: 1, dateOfADT: 1, type: 1, facilityId: 1, DOB: 1, transferType: 1 })
            .sort({ createdAt: -1 })
            .limit(1);
        return ADTResponse ?? null;
    }
    return null;
};

const getPatient = async (facilityId, type, fromDate, toDate, transferType, facilityIds = [], admissionType) => {
    let query = { facilityId };

    if (facilityIds && facilityIds.length > 0) {
        const facilityData = [];
        facilityIds.map((ele) => {
            facilityData.push(mongoose.Types.ObjectId(ele));
        });
        query.facilityId = { $in: facilityIds };
    } else {
        if (facilityId) {
            query.facilityId = facilityId;
        }
    }

    if (type !== "all") {
        if (type) {
            query.type = type.toLowerCase();
        }
        if (admissionType) {
            if (admissionType === "allAdmissions") {
                query.type = { $in: ["admission", "readmission"] };
            } else {
                query.type = admissionType.toLowerCase();;
            }
        }
        if (transferType) {
            switch (transferType) {
                case "allHospitalTransfers":
                    query.transferType = { $in: ["plannedHospitalTransfer", "unplannedHospitalTransfer"] };
                    break;
                case "allCommunityTransfers":
                    query.transferType = {
                        $nin: ["plannedHospitalTransfer", "unplannedHospitalTransfer", "deceased"],
                    };
                    break;
                case "allTransfers":
                    break;
                case "deceased":
                    query.transferType = "deceased";
                    break;
                default:
                    query.transferType = transferType;
                    break;
            }
        }
    }

    if (fromDate && toDate) {
        query.dateOfADT = {
            $gte: await toStartFilterDate(fromDate),
            $lte: await toEndFilterDate(toDate),
        };
    }
    let relations = [
        RELATIONS.FACILITY,
        RELATIONS.DOCTOR,
        RELATIONS.UNIT,
        RELATIONS.PAYER_SOURCE_INSURANCE,
        RELATIONS.INSURANCE,
        RELATIONS.DX,
        RELATIONS.NURSE,
        RELATIONS.HOSPITAL,
        RELATIONS.SNF,
        RELATIONS.TRANSFER_TO_WHICH_ASSISTED_LIVING,
    ];
    let list = await Patient.find({ ...query }).populate(relations).sort({ dateOfADT: -1 });
    return list;
};

const getOpenTransfer = async data => {
    let query = {
        firstName: new RegExp(`^${data.firstName}$`, "i"),
        lastName: new RegExp(`^${data.lastName}$`, "i"),
        middleInitial: new RegExp(`^${data.middleInitial}$`, "i"),
        suffix: new RegExp(`^${data.suffix}$`, "i"),
        DOB: data.DOB,
        type: "transfer",
    };

    let patient = await Patient.findOne({
        ...query,
    })
        .sort({ dateOfADT: -1 })

        .catch(e => {
            return e;
        });

    let admission;
    if (patient) {
        admission = await Patient.findOne({
            ...query,
            type: "admission",
        })
            .sort({ dateOfAdmission: -1 })

            .catch(e => {
                return e;
            });
    }

    return { patient, admission };
};

const getHasAdmission = async data => {
    const firstName = data.firstName.replace(/[^\w\s]/gi, "");
    const lastName = data.lastName.replace(/[^\w\s]/gi, "");

    let query = {
        firstName: new RegExp(`^${firstName}$`, "i"),
        lastName: new RegExp(`^${lastName}$`, "i"),
        DOB: data.DOB,
        type: { $in: ["admission", "readmission"] },
    };

    if (data.suffix) {
        query.suffix = new RegExp(`^${data.suffix}$`, "i");
    }
    if (data.middleInitial) {
        query.middleInitial = new RegExp(`^${data.middleInitial}$`, "i");
    }

    let admission = await Patient.findOne({
        ...query,
    });

    return admission;
};

const getPatientDetails = async data => {
    let query = {
        ...(await checkADTDuplicate(data, true)),
        DOB: {
            $gte: moment(data.DOB).startOf("day").utc().toISOString(),
            $lte: moment(data.DOB).endOf("day").utc().toISOString(),
        },
        facilityId: data.facilityId,
    };

    let patient = await Patient.find({ ...query })
        .sort({ dateOfADT: -1, createdAt: -1 })
        .catch(e => {
            return e;
        });

    return patient;
};

const getPatientDetailsRelation = async data => {
    let query = {
        ...(await checkADTDuplicate(data, true)),
        DOB: {
            $gte: moment(data.DOB).startOf("day").utc().toISOString(),
            $lte: moment(data.DOB).endOf("day").utc().toISOString(),
        },
        facilityId: data.facilityId,
    };
    let relations = [
        RELATIONS.FACILITY,
        RELATIONS.DOCTOR,
        RELATIONS.UNIT,
        RELATIONS.PAYER_SOURCE_INSURANCE,
        RELATIONS.INSURANCE,
        RELATIONS.DX,
        RELATIONS.NURSE,
        RELATIONS.HOSPITAL,
        RELATIONS.SNF,
        RELATIONS.TRANSFER_TO_WHICH_ASSISTED_LIVING,
    ];

    let patient = await Patient.find({ ...query })
        .populate(relations)
        .sort({ dateOfADT: -1, createdAt: -1 })
        .catch(e => {
            return e;
        });

    return patient;
};

const getPatientAutoComplete = async (facilityId, type, search) => {
    let query = {
        facilityId: mongoose.Types.ObjectId(facilityId),
        [type]: { $regex: search, $options: "i" },
    };

    let list = await Patient.aggregate([
        {
            $match: query,
        },
        {
            $group: {
                _id: "$" + type,
            },
        },
        { $limit: 5 },
    ]);

    return list;
};

const checkAdmissionPriorData = async data => {
    let query = {
        ...(await checkADTDuplicate(data)),
        ...data.dateOfADT && { dateOfADT: { $lte: await toEndFilterDate(data.dateOfADT) } }
    };

    let ADTResponse = await Patient.findOne({ ...query, facilityId: data.facilityId })
        .sort({ createdAt: -1 })
        .limit(1);
    const ADTResponseCount = await Patient.countDocuments({ ...query, facilityId: data.facilityId });

    // if (ADTResponse && data.id) {
    //     // return false;      
    //     const dateOfADTDate = moment(data.dateOfADT).toDate();
    //     if (data.dateOfADT && moment.utc(ADTResponse.dateOfADT).isSame(dateOfADTDate)) {
    //         return ADTResponse;
    //     } else {
    //         return false
    //     }
    // }
    return { data: ADTResponse, total: ADTResponseCount };
}

const checkHospitalPriorData = async data => {
    let query = {
        ...(await checkADTDuplicate(data)),
        transferType: { $in: [ADT_SUB_TYPES.PLANNED_HOSPITAL_TRANSFER, ADT_SUB_TYPES.UNPLANNED_HOSPITAL_TRANSFER] },
        ...data.dateOfADT && { dateOfADT: { $lte: await toEndFilterDate(data.dateOfADT) } }
    };

    let ADTResponse = await Patient.findOne({ ...query, facilityId: data.facilityId })
        .sort({ createdAt: -1 })
        .limit(1);
    if (ADTResponse && data.id) {
        // return false;      
        const dateOfADTDate = moment(data.dateOfADT).toDate();
        if (data.dateOfADT && moment.utc(ADTResponse.dateOfADT).isSame(dateOfADTDate)) {
            return ADTResponse;
        } else {
            return false
        }
    }
    return ADTResponse;
}

const getLatestADT = async data => {
    let query = {
        ...(await checkADTDuplicate(data))
    };
    if (data.type && data.type == "edit" && data.id) {
        query._id = { $ne: mongoose.Types.ObjectId(data.id) };
    }

    let ADTResponse = await Patient.findOne({
        ...query,
        facilityId: data.facilityId
    }).sort({ dateOfADT: -1, createdAt: -1 });

    let diffMonth = null;

    if (ADTResponse) {
        const lastADTPatient = ADTResponse;
        let admissionType = null;
        let priorType = lastADTPatient?.type;
        let priorTransferType = lastADTPatient?.transferType;

        const dxDataResponse = await Patient.findOne({
            ...query,
            type: { $in: ["admission", "readmission"] },
            facilityId: data.facilityId,
        }).populate("dx").sort({ createdAt: -1 });
        if (
            data &&
            // data.ADTtype === "admissionOrReadmission" &&
            lastADTPatient.type == ADT_TYPES.TRANSFER
        ) {
            const dateOfADTDate = lastADTPatient.dateOfADT;
            const todayDate = moment().startOf("day").utc();
            diffMonth = todayDate.diff(dateOfADTDate, "months");
            if (
                _.includes(
                    [ADT_SUB_TYPES.SAFE_DISCHARGE, ADT_SUB_TYPES.AMA, ADT_SUB_TYPES.SNF],
                    lastADTPatient.transferType
                )
            ) {
                admissionType = null; // ADT_TYPES.ADMISSIONS;
            } else if (diffMonth > 6) {
                admissionType = null; // ADT_TYPES.ADMISSIONS;
            } else if (diffMonth <= 6) {
                admissionType = ADT_TYPES.READMISSIONS;
            }
        }

        return {
            data: lastADTPatient,
            dxData: dxDataResponse?.dx || [],
            message: "Record found",
            admissionType: admissionType,
            priorType,
            priorTransferType,
            diffMonth
        };
    } else {
        return {
            data: null,
            message: "No record found",
            admissionType: null,
            priorType: null,
            priorTransferType: null,
            diffMonth: null
        };
    }
};

const deletePatient = async (req) => {
    const facilityid = req.headers.facilityid

    const onlyHospitalTabAccess = await isOnlyHospitalTabAccess(req.headers.accountid);

    const id = req.params.id;

    let patient = await Patient.findOne({ _id: id });

    if (!patient) {
        throw new Error("Something went wrong, Please try again later.");
    }

    if (onlyHospitalTabAccess) {
        const deleted = await Patient.findByIdAndDelete(id);
        if (deleted) {
            await saveLogs(
                _.pick(patient, ['_id', 'firstName', 'lastName', "DOB", "dateOfADT", "type", "transferType", "facilityId", "accountId"]),
                "deletePatient",
                {
                    accountId: req.headers.accountid,
                    facilityId: mongoose.Types.ObjectId(facilityid),
                    userId: req?.user?.id
                },
                "Patient deleted successfully"
            );
            return patient;
        }
    } else {
        let query = {
            ...(await checkADTDuplicate(patient)),
            facilityId: mongoose.Types.ObjectId(facilityid),
        };
        const existingPatient = await Patient.find({ ...query }).sort({ createdAt: -1 });

        if (existingPatient.length > 0) {
            const firstItem = _.head(existingPatient);
            const lastItem = _.last(existingPatient);
            const existingIds = [firstItem._id.toString(), lastItem._id.toString()];
            if (_.includes(existingIds, id)) {
                const deleted = await Patient.findByIdAndDelete(id);
                if (deleted) {
                    await saveLogs(
                        _.pick(patient, ['_id', 'firstName', 'lastName', "DOB", "dateOfADT", "type", "transferType", "facilityId", "accountId"]),
                        "deletePatient",
                        {
                            accountId: req.headers.accountid,
                            facilityId: mongoose.Types.ObjectId(facilityid),
                            userId: req?.user?.id
                        },
                        "Patient deleted successfully"
                    );
                    return patient;
                } else {
                    throw new Error("Something went wrong, Please try again later.");
                }
            } else {
                throw new Error("You can not delete this ADT because of it is not last or first ADT");
            }
        }
    }
};

const updateAccountToFacility = async (data) => {
    const { facilityId, newAccountId } = data;
    const account = await Account.findById(newAccountId);
    const facility = await Facility.findById(facilityId);
    let response = data
    if (account && facility) {
        response.message = `You changes facility id from '${facility.name}' to '${account.name}'`
        return response;
        // update facility 
        facility.accountId = mongoose.Types.ObjectId(account.id);
        await facility.save();

        // update data for census
        const census = await Census.findOne({ facilityId: facilityId });
        if (census) {
            census.accountId = account.id;
            await census.save();
        }

        // update account id to facility table 
        facility.accountId = account.id;
        await facility.save();

        // update account id for FacilityManuallyEndOfADT
        const manuallyEndOfADT = await FacilityManuallyEndOfADT.findOne({ facilityId: facilityId });
        if (manuallyEndOfADT) {
            manuallyEndOfADT.accountId = account.id;
            await manuallyEndOfADT.save();
        }



        // const reportData = await ReportsSubscriptions.find({
        //     facilityIds: {
        //         $elemMatch: { $in: [facilityId.toString()] }
        //     }
        // });

        // if (reportData && reportData.length > 0) {
        //     await filterData(reportData, async report => {
        //         let facilityIds = report?.facilityIds?.map((ele) => ele.toString());
        //         if (facilityIds.length === 1) {
        //             // remove full record
        //             // await ReportsSubscriptions.findByIdAndDelete(report?._id);
        //         } else {
        //             // remove only facility id
        //             const index = facilityIds.indexOf(id.toString());
        //             if (index > -1) {
        //                 facilityIds.splice(index, 1);
        //             }
        //             facilityIds = facilityIds.map((e) => mongoose.Types.ObjectId(e))
        //             await ReportsSubscriptions.updateOne({ _id: report._id }, {
        //                 $set: {
        //                     facilityIds: facilityIds
        //                 },
        //             });

        //             // const createNewReport = report;
        //             // delete createNewReport._id;
        //             // createNewReport.facilityIds = [facilityId];
        //             // createNewReport.accountId = account.id;
        //             // await ReportsSubscriptions.create(createNewReport);
        //         }
        //     });
        // }

        // update question
        const questions = await Question.find({ facilityId: facilityId });
        if (questions && questions.length > 0) {
            await filterData(questions, async question => {
                question.accountId = mongoose.Types.ObjectId(account.id);
                await question.save();
            });
        }

        // Update patient table
        const patients = await Patient.find({ facilityId: facilityId });
        if (patients && patients.length > 0) {
            await filterData(patients, async patient => {
                patient.accountId = mongoose.Types.ObjectId(account.id);
                await patient.save();
            });
        }

        // settings update 
        const settings = await Setting.find({ facilityId: facilityId });
        if (settings && settings.length > 0) {
            await filterData(settings, async setting => {
                setting.accountId = mongoose.Types.ObjectId(account.id);
                await setting.save();
            });
        }

        // update logs
        const logs = await Logs.find({ facilityId: facilityId });
        if (logs && logs.length > 0) {
            await filterData(logs, async log => {
                log.accountId = mongoose.Types.ObjectId(account.id);
                await log.save();
            });
        }

        // update filters
        const filters = await Filter.find({ facilityId: facilityId });
        if (filters && filters.length > 0) {
            await filterData(filters, async filter => {
                filter.accountId = mongoose.Types.ObjectId(account.id);
                await filter.save();
            });
        }

        // update AlertReport
        const alertReports = await AlertReport.find({ facilityId: facilityId });
        if (alertReports && alertReports.length > 0) {
            await filterData(alertReports, async alertReport => {
                alertReport.accountId = mongoose.Types.ObjectId(account.id);
                await alertReport.save();
            });
        }

        // update in user table
        const users = await User.find({
            facilities: {
                $elemMatch: {
                    facilityId: facilityId
                },
            },
        }).exec();


        if (users && users.length > 0) {
            for (const user of users) {
                // Update facilities array
                const userFacilityData = [];
                await Promise.all(user.facilities.map(async (ele) => {
                    // Ensure the comparison happens correctly (e.g., ObjectId vs string comparison)
                    if (ele.facilityId.toString() === facilityId.toString()) {
                        let eleObj = { ...ele._doc, accountId: mongoose.Types.ObjectId(account.id), isUpdated: "Updated ++++++++++++++++++++" };
                        userFacilityData.push(eleObj)
                    } else {
                        userFacilityData.push(ele)
                    }
                }));

                await User.updateOne(
                    { _id: user._id },
                    {
                        $set: {
                            facilities: userFacilityData,
                        },
                    }
                );
            }
        }
    } else {
        return "not matched data fount"
    }

    return response;
}

const filterData = async (arr, callback) => {
    const fail = Symbol();
    return (
        await Promise.all(
            arr.map(async (item) => ((await callback(item)) ? item : fail))
        )
    ).filter((i) => i !== fail);
};

module.exports = {
    getPatientDetailsRelation,
    addPatient,
    getPatient,
    getOpenTransfer,
    getHasAdmission,
    getPatientDetails,
    getPatientAutoComplete,
    getLatestADT,
    deletePatient,
    checkHospitalPriorData,
    getSameDateTransfer,
    updateAccountToFacility,
    checkAdmissionPriorData
};
