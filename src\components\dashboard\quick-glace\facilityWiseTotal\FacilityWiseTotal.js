import * as React from 'react';
import styles from "./FacilityWiseTotal.module.scss";
import { <PERSON>ton, Card, CardContent, CircularProgress, Grid, Typography } from '@mui/material';
import { Stack } from '@mui/system';
import TotalCard from './total-card/TotalCard';


export default function FacilityWiseTotal(props) {
    const { item, loading } = props;
    const { name = "", censusInfo, totalData } = item;

    return (
        <Card className={styles.facilityWiseTotal} key={`${name}-facility-total-card`}>
            <CardContent>
                <Grid container direction={"row"} spacing={4}>
                    <Grid item xs={3}>
                        <Grid container direction={"column"}>
                            <Grid item className={styles.topText}>
                                <Typography className={styles.primaryText}>Facility Name</Typography>
                                <Typography className={styles.secondaryText}>{name}</Typography>
                            </Grid>
                            <Grid item className={styles.middleText}>
                                <Stack
                                    className={styles.averageText}
                                    direction={{ xs: 'column', sm: 'row' }}
                                    justifyContent="space-between"
                                    alignItems={"center"}
                                >
                                    <Stack item>
                                        <Typography align="center" component={"span"} sx={{ fontSize: "0.8rem" }}>Avg occupancy rate <span className='subtitleOverall'>(of time frame selected)</span></Typography>
                                    </Stack>
                                    <Stack>
                                        <Button size='small' className={styles.averageTextButton}>{censusInfo.censusAverage}</Button>
                                    </Stack>
                                </Stack>
                            </Grid>
                        </Grid>
                    </Grid>

                    <Grid item xs={9} sx={{ overflow: "auto" }}>
                        <Stack direction={"row"} spacing={2}>
                            {!loading && totalData.length > 0 &&
                                totalData.map((ele) => (
                                    <TotalCard
                                        item={ele}
                                    />
                                ))
                            }
                            {loading && (
                                <Stack width={"100%"} sx={{ display: "flex", alignItems: "center", mt: 4 }}> <CircularProgress /> </Stack>
                            )}
                        </Stack>
                    </Grid>
                </Grid>
            </CardContent>
        </Card>
    );
}
