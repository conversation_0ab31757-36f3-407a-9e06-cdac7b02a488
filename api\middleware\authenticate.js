const mongoose = require("mongoose");
const config = require("../../config");
const User = mongoose.model("user");

const authenticate = async (req, res, next) => {
  try {
    const incomingAccessToken = req.header("x-auth");
    const refreshToken = req.cookies[config.refreshCookieName];
    const { accessToken, userQuery } = await User.findByToken(
      req,
      res,
      incomingAccessToken,
      refreshToken
    );
    const user = await userQuery.populate("role");

    if (!user) {
      throw new Error("User not found");
    }

    req.user = user;
    req.token = accessToken;
    next();
  } catch (e) {
    res.status(401).send({ status: 401 });
  }
};

module.exports = { authenticate };
