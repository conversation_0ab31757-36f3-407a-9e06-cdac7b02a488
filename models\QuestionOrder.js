const mongoose = require("mongoose");
const { Schema, Types } = mongoose;

const QuestionOrderSchema = new Schema(
  {
    facilityId: { type: Types.ObjectId, ref: "facility" },
    accountId: { type: Types.ObjectId, ref: "account", require: false },
    forType: String,
    forTransferType: String,
    order: [
      {
        questionId: { type: Types.ObjectId, ref: "question" },
        order: Number,
        label: { type: String, required: false, default: null },
        tableLabel: { type: String, required: false, default: null },
      },
    ],
  },
  {
    timestamps: true,
  }
);

mongoose.model("questionOrder", QuestionOrderSchema);
