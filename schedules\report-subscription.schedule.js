const mongoose = require("mongoose");
const ReportsSubscriptions = mongoose.model("reportsSubscriptions");
const addJobToQueue = require('./bullmq/add-job-to-queue');
const { createReportLog } = require("../api/helpers/report-log");


const reportSubscriptionData = async () => {
    try {
        let reportsSubscriptions = await ReportsSubscriptions.find({ _id: "684973e14275dccc53c0932c" }).populate({
            path: "userId",
            select: "email _id fullName accountId jobTitle",
        }).exec();
        // console.log(reportsSubscriptions, 'reportsSubscriptions');

        const processSubscriptions = async () => {
            const promises = reportsSubscriptions.map(async (reportsSubscription, j) => {
                if (!reportsSubscription) return;

                if (reportsSubscription.isSendReportSeparate) {
                    const facilityIds = reportsSubscription?.facilityIds || [];
                    if (facilityIds.length > 0) {
                        const facilityPromises = facilityIds.map(async (facilityId, i) => {
                            let jobData = {
                                ...reportsSubscription._doc,
                                facilityIds: [facilityId],
                                jobName: `${reportsSubscription.interval}_dashboard_separate_report_${reportsSubscription.id}_${i + 1}`,
                                index: `${j + 1}.${i + 1}`
                            };
                            return addJobToQueue(jobData);
                        });

                        await Promise.all(facilityPromises);
                    }
                } else {
                    const facilityIds = reportsSubscription?.facilityIds || [];
                    if (facilityIds.length > 0) {
                        let jobData = {
                            ...reportsSubscription._doc,
                            facilityIds: facilityIds,
                            jobName: `${reportsSubscription.interval}_dashboard_report_${reportsSubscription.id}`,
                            index: `${j + 1}`
                        };
                        return addJobToQueue(jobData);
                    }
                }
            });

            await Promise.all(promises);
        };

        if (reportsSubscriptions && reportsSubscriptions.length > 0) {
            await processSubscriptions();
        }
    } catch (error) {
        // await createReportLog({ event: "ADD_JOB_TO_QUEUE_ERROR", description: "Add job to queue error", data: error })
        console.log(error, "Reporter schedular errors inside function");
    }
}

module.exports = {
    reportSubscriptionData,
}