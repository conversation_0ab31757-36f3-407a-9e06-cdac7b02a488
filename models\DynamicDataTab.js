const mongoose = require("mongoose");
const { PAGE_TYPE, ALERT_TYPE_TYPE } = require("../types/common.type");
const { Schema, Types } = mongoose;

// Alert DynamicDataTab Schema
const DynamicDataTabSchema = new Schema(
  {
    // Reference to account (required field)
    accountId: {
      type: Types.ObjectId,
      ref: "account",
      required: true,
    },

    // Optional reference to user
    userId: {
      type: Types.ObjectId,
      ref: "user",
    },
    questionId: { type: Types.ObjectId, ref: "question" },
    // Page type with defined enum values and default
    page: {
      type: String,
      enum: Object.values(PAGE_TYPE),
      default: PAGE_TYPE.HOSPITAL,
      required: true,
    },

    title: {
      type: [String],
      required: true,
    },

    type: {
      type: [String],
      required: true,
    },

  },
  {
    // Automatically adds createdAt and updatedAt timestamps
    timestamps: true,

    // Enforce strict schema (extra fields will be ignored)
    strict: false,
  }
);

// Create and export the AlertReport model
module.exports = mongoose.model("dynamicDataTab", DynamicDataTabSchema);
