const HOSPITAL_CARDS_TYPE = {
    DOCTOR_DATA: 'doctorData',
    DCER_DATA: 'DCERData',
    INSURANCE_DATA: 'insuranceData',
    RETURNS_DATA: 'returnsData',
    NINETY_DAYS_DATA: 'ninetyDaysData',
    NURSE_DATA: 'nurseData',
    FLOORS_DATA: 'floorsData',
    DAYS_DATA: 'daysData',
    DX_DATA: 'dxData',
    SHIFT_DATA: 'shiftData',
    PERMISSION: 'permission',
    HOSPITAL_DATA: 'hospitalData',
    HOSPITALIZATIONS: 'hospitalizations',
    UNPLANNED: "unplannedHospitalTransfer",
    PLANNED: "plannedHospitalTransfer",
    TOTAL: "total",
}

const HOSPITAL_CARDS_LABELS = {
    hospitalizations: 'New Hospitalizations & Re-Hospitalizations',
    DCERData: 'DC / ER',
    insuranceData: 'Per Insurance',
    returnsData: "Returned / Didn't Return",
    ninetyDaysData: '90 Days Analysis',
    floorsData: 'Per Floor',
    doctorData: 'Per Doctor',
    daysData: 'Per Day',
    dxData: 'Per Diagnosis',
    shiftData: 'Per Shift',
    nurseData: 'Per Nurse',
    hospitalData: 'Hospitals residents transferred to',
    unplanned: "Unplanned Transfers",
    planned: "Planned Transfers",
}

const defaultSixtyDaysData = async () => {
    return [
        {
            _id: "a",
            key: "a",
            value: 0,
            originalTotal: 0,
            color: "#21B1D0",
            percentage: 0,
            label: "1 - 7 Days",
            ids: [],
            admissionIds: [],
        },
        {
            _id: "b",
            key: "b",
            value: 0,
            originalTotal: 0,
            color: "#5195DD",
            percentage: 0,
            label: "8 - 14 Days",
            ids: [],
            admissionIds: [],
        },
        {
            _id: "c",
            key: "c",
            value: 0,
            originalTotal: 0,
            color: "#4F80FF",
            percentage: 0,
            label: "15 - 30 Days",
            ids: [],
            admissionIds: [],
        },
        {
            _id: "d",
            key: "d",
            value: 0,
            originalTotal: 0,
            color: "#6B69DA",
            percentage: 0,
            label: "31 - 60 Days",
            ids: [],
            admissionIds: [],
        },
    ];
};

const defaultNineteenDaysData = async () => {
    return [
        {
            _id: "a",
            key: "a",
            value: 0,
            originalTotal: 0,
            color: "#21B1D0",
            percentage: 0,
            label: "1 - 7 Days",
            ids: [],
            admissionIds: [],
        },
        {
            _id: "b",
            key: "b",
            value: 0,
            originalTotal: 0,
            color: "#5195DD",
            percentage: 0,
            label: "8 - 14 Days",
            ids: [],
            admissionIds: [],
        },
        {
            _id: "c",
            key: "c",
            value: 0,
            originalTotal: 0,
            color: "#4F80FF",
            percentage: 0,
            label: "15 - 30 Days",
            ids: [],
            admissionIds: [],
        },
        {
            _id: "d",
            key: "d",
            value: 0,
            originalTotal: 0,
            color: "#6B69DA",
            percentage: 0,
            label: "31 - 60 Days",
            ids: [],
            admissionIds: [],
        },
        {
            _id: "e",
            key: "e",
            value: 0,
            originalTotal: 0,
            color: "#1D7CD3",
            percentage: 0,
            label: "61 - 90 Days",
            ids: [],
            admissionIds: [],
        },
    ];
};


module.exports = {
    HOSPITAL_CARDS_TYPE,
    defaultNineteenDaysData,
    defaultSixtyDaysData,
    HOSPITAL_CARDS_LABELS
}