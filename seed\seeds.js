const mongoose = require('mongoose');
const keys = require("../config");
//const { seedPermissions } = require('./permission.seed');
const { seedRoles } = require('./role.seed');

mongoose.connect(process.env.MONGO_URI || keys.MONGO_URI, {
    useNewUrlParser: true,
    useUnifiedTopology: true,
}).catch((err) => {
    console.log(err, 'err');
});

const seedDB = async () => {
    console.log("seed start");
    await seedRoles();
    //await seedPermissions();
    console.log("seed completed");
}

seedDB().then(() => {
    mongoose.connection.close()
});