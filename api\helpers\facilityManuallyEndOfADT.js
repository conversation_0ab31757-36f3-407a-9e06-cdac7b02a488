const mongoose = require("mongoose");
const FacilityManuallyEndOfADT = mongoose.model("facilityManuallyEndOfADT");
const Patient = mongoose.model("patient");
const Census = mongoose.model("census");
const moment = require("moment");

const createFacilityManuallyEndOfADT = async (req) => {
    const user = req?.user;
    const { facilityid, accountid } = req.headers;
    const { endDateOfADT, facilityIds } = req.body;

    if (!facilityIds) return { status: 400, message: 'Missing facilityIds in request body' };

    await Promise.all(facilityIds.map(async facilityId => {
        const existing = await FacilityManuallyEndOfADT.findOne({
            accountId: mongoose.Types.ObjectId(accountid),
            facilityId: mongoose.Types.ObjectId(facilityId),
        }).sort({ endDateOfADT: -1 });

        if (existing) {
            existing.endDateOfADT = endDateOfADT;
            await existing.save();
        } else {
            const newFacilityManuallyEndOfADT = new FacilityManuallyEndOfADT({
                facilityId: mongoose.Types.ObjectId(facilityId),
                accountId: mongoose.Types.ObjectId(accountid),
                userId: user._id,
                endDateOfADT,
            });
            await newFacilityManuallyEndOfADT.save();
        }
    }));

    return { status: 200, data: req.body };
};

const getFacilityManuallyEndOfADTs = async (req) => {
    const { accountid } = req.headers;
    const { facilityIds } = req.query;
    const user = req?.user;
    let facilityIdsData = [];
    if (!Array.isArray(facilityIds)) {
        facilityIdsData.push(facilityIds);
    } else {
        facilityIdsData = facilityIds;
    }
    const latestEndDateOfADTManually = [];
    const facilityLatestEndDateOfADT = [];
    
    if (facilityIdsData.length > 0) {
        await Promise.all(facilityIdsData.map(async facilityId => {
            const latestDateOfADTDates = await Patient.findOne({
                facilityId: mongoose.Types.ObjectId(facilityId),
                dateOfADT: { $ne: null },
            })
                .sort({ dateOfADT: -1 })
                .exec();

            if (latestDateOfADTDates) {
                facilityLatestEndDateOfADT.push({ endDateOfADT: latestDateOfADTDates.dateOfADT, facilityId });
            }

            const facilityManuallyEndOfADT = await FacilityManuallyEndOfADT.findOne({
                accountId: mongoose.Types.ObjectId(accountid),
                facilityId: mongoose.Types.ObjectId(facilityId),
            }).sort({ endDateOfADT: -1 });;

            if (facilityManuallyEndOfADT && facilityManuallyEndOfADT.endDateOfADT) {
                latestEndDateOfADTManually.push({ endDateOfADT: facilityManuallyEndOfADT.endDateOfADT, facilityId });
            } else {
                if (latestDateOfADTDates) {
                    latestEndDateOfADTManually.push({ endDateOfADT: latestDateOfADTDates.dateOfADT, facilityId });
                }
            }
        }));
    }

    return { latestEndDateOfADTManually, facilityLatestEndDateOfADT };
};

const getFacilityManuallyEndOfADT = async (req) => {
    const { id } = req.query;
    const filters = await FacilityManuallyEndOfADT.findOne({ _id: mongoose.Types.ObjectId(id) });
    return filters;
};

const deleteFacilityManuallyEndOfADT = async id => {
    let deleted = await FacilityManuallyEndOfADT.findByIdAndDelete(id);
    return deleted;
};

const getEndOfDateReportSettings = async (userId, accountid, facilityIds = []) => {
    let facilityIdsData = facilityIds;

    if (facilityIdsData.length === 1) {
        const facilityId = facilityIdsData[0];
        const census = await Census.findOne({
            facilityId: facilityId,
            isBaseline: true,
        });
        const latestDateOfADT = await Patient.findOne({
            facilityId: mongoose.Types.ObjectId(facilityId),
            dateOfADT: { $ne: null },
        })
            .sort({ dateOfADT: -1 })
            .select('dateOfADT')
            .exec();

        const facilityManuallyEndOfADT = await FacilityManuallyEndOfADT.findOne({
            accountId: mongoose.Types.ObjectId(accountid),
            facilityId: mongoose.Types.ObjectId(facilityId),
        }).sort({ endDateOfADT: -1 });

        if (facilityManuallyEndOfADT && facilityManuallyEndOfADT.endDateOfADT) {
            const toDate = moment(latestDateOfADT.dateOfADT).isBefore(facilityManuallyEndOfADT.endDateOfADT) ? facilityManuallyEndOfADT.endDateOfADT : latestDateOfADT.dateOfADT;
            return { endDateOfADT: toDate, censusDate: census?.date }
        }
        return { endDateOfADT: latestDateOfADT?.dateOfADT || null, censusDate: census?.date }
    } else {
        const latestEndDateOfADTManually = [];
        const facilityLatestEndDateOfADT = [];
        await Promise.all(facilityIdsData.map(async facilityId => {
            const latestDateOfADTDates = await Patient.findOne({
                facilityId: mongoose.Types.ObjectId(facilityId),
                dateOfADT: { $ne: null },
            })
                .sort({ dateOfADT: -1 })
                .select('dateOfADT')
                .exec();
            if (latestDateOfADTDates) {
                facilityLatestEndDateOfADT.push({ endDateOfADT: latestDateOfADTDates.dateOfADT, facilityId });
            }
            const facilityManuallyEndOfADT = await FacilityManuallyEndOfADT.findOne({
                accountId: mongoose.Types.ObjectId(accountid),
                facilityId: mongoose.Types.ObjectId(facilityId),
            }).sort({ endDateOfADT: -1 });
            if (facilityManuallyEndOfADT && facilityManuallyEndOfADT.endDateOfADT) {
                latestEndDateOfADTManually.push({ endDateOfADT: facilityManuallyEndOfADT.endDateOfADT, facilityId });
            } else if (latestDateOfADTDates) {
                latestEndDateOfADTManually.push({ endDateOfADT: latestDateOfADTDates.dateOfADT, facilityId });
            }
        }));
        const latestUpdatedEndOfADTArr = [...latestEndDateOfADTManually ?? [], facilityLatestEndDateOfADT ?? []];
        earliestDateData = latestUpdatedEndOfADTArr?.reduce((earliest, current) => {
            return moment(earliest).isAfter(moment(current.endDateOfADT)) ? earliest : current.endDateOfADT;
        }, latestUpdatedEndOfADTArr[0]?.endDateOfADT);
        return { endDateOfADT: earliestDateData }
    }
}


module.exports = {
    createFacilityManuallyEndOfADT,
    getFacilityManuallyEndOfADT,
    getFacilityManuallyEndOfADTs,
    deleteFacilityManuallyEndOfADT,
    getEndOfDateReportSettings
};
