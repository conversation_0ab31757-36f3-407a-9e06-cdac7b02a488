const _ = require("lodash");
const { } = require('number-to-words');
const { ALERT_TYPE_TYPE } = require("../../types/common.type");

async function checkDroppingTransferAlerts(currentWeek, last4Weeks, handleEmptyData, dataTypes, alertReport, pageLabel) {
    const alertMessages = [];
    const { type } = alertReport;

    const calculateDropPercentage = (lastAverage = 0, currentTotal = 0) => {
        const change = ((lastAverage - currentTotal) / lastAverage) * 100;  // Removed * 100 to get the decimal value
        return change ? Math.round(change) : 0;
    }

    const evaluateConditions = async (last4WeeksItem, currentWeekItem) => {
        const lastAvg = Math.round(last4WeeksItem.average);
        const currentTransfers = Math.round(currentWeekItem.total);
        const category = currentWeekItem.label;

        let alertMessage = '';
        const dropPercentage50 = 0.5;    // Drop percentage (50%)      
        const dropPercentage = await calculateDropPercentage(lastAvg, currentTransfers);

        if (type === ALERT_TYPE_TYPE.WEEKS) {
            if (lastAvg === 3 && currentTransfers === 0) {
                alertMessage = `${category} ${pageLabel} were on pace for ${lastAvg} ${pageLabel} a week, over the previous four weeks, and last week there was ${currentTransfers} which is a ${dropPercentage}% decrease.`;
            } else if ((lastAvg >= 4 && lastAvg <= 8) && currentTransfers <= lastAvg * dropPercentage50) {
                alertMessage = `${category} ${pageLabel} were on pace for ${lastAvg} ${pageLabel} a week, over the previous four weeks, and last week there was ${currentTransfers} which is a ${dropPercentage}% decrease.`;
            } else if (lastAvg >= 9 && currentTransfers <= lastAvg * 0.6) {
                alertMessage = `${category} ${pageLabel} were on pace for ${lastAvg} ${pageLabel} a week, over the previous four weeks, and last week there was ${currentTransfers} which is a ${dropPercentage}% decrease.`;
            }
        }

        if (type === ALERT_TYPE_TYPE.BI_WEEKS) {
            if (lastAvg == 3 && currentTransfers === 0) {
                alertMessage = `${category} averaged ${lastAvg} ${pageLabel} every two weeks over the previous two months but has sent out only ${currentTransfers} ${pageLabel} in the past two weeks, marking a ${dropPercentage}% decrease`;
            } else if ((lastAvg >= 4 && lastAvg <= 8) && currentTransfers <= lastAvg * 0.5) {
                alertMessage = `${category} averaged ${lastAvg} ${pageLabel} every two weeks over the previous two months but has sent out only ${currentTransfers} ${pageLabel} in the past two weeks, marking a ${dropPercentage}% decrease`;
            } else if (lastAvg >= 9 && currentTransfers <= lastAvg * 0.6) {
                alertMessage = `${category} averaged ${lastAvg} ${pageLabel} every two weeks over the previous two months but has sent out only ${currentTransfers} ${pageLabel} in the past two weeks, marking a ${dropPercentage}% decrease`;
            }
        }

        if (type === ALERT_TYPE_TYPE.MONTHS) {
            if (lastAvg == 3 && currentTransfers === 0) {
                alertMessage = `${category} averaged ${lastAvg} ${pageLabel} per month over the previous four months but has sent out only ${currentTransfers} ${pageLabel} this past month, marking a ${dropPercentage}% decrease.`;
            } else if ((lastAvg >= 4 && lastAvg <= 8) && currentTransfers <= lastAvg * 0.5) {
                alertMessage = `${category} averaged ${lastAvg} ${pageLabel} per month over the previous four months but has sent out only ${currentTransfers} ${pageLabel} this past month, marking a ${dropPercentage}% decrease.`;
            } else if (lastAvg >= 9 && currentTransfers <= lastAvg * 0.6) {
                alertMessage = `${category} averaged ${lastAvg} ${pageLabel} per month over the previous four months but has sent out only ${currentTransfers} ${pageLabel} this past month, marking a ${dropPercentage}% decrease.`;
            }
        }

        if (alertMessage) {
            const name = currentWeekItem?.name || last4WeeksItem?.name;
            return {
                category,
                _id: currentWeekItem._id,
                label: category,
                total: currentTransfers,
                message: alertMessage,
                percentage: dropPercentage,
                ...name && { name }
            }
        }
        return null
    };

    const compareData = async (currentWeekData, last4WeeksData) => {
        // Get the processed datasets from handleEmptyData
        const { currentData, averageData } = await handleEmptyData(currentWeekData, last4WeeksData);
        const currentWeekItems = currentData ?? [];
        const last4WeeksItems = averageData ?? [];

        // Iterate over last4WeeksData and compare with currentWeekData
        for (const lastWeekItem of last4WeeksItems) {
            let currentWeekItem = currentWeekItems.find(item => item._id === lastWeekItem._id);
            currentWeekItem = !_.isEmpty(currentWeekItem) ? currentWeekItem : { ...lastWeekItem, total: 0, average: 0 };
            if (currentWeekItem) {
                const alert = await evaluateConditions(lastWeekItem, currentWeekItem);

                if (alert) {
                    alertMessages.push(alert);
                }
            }
        }
    };

    for (const dataType of dataTypes) {
        await compareData(currentWeek?.[0]?.[dataType] ?? [], last4Weeks?.[0]?.[dataType] ?? []);
    }

    return alertMessages;
}

// Execute the comparison and check for alerts
module.exports = {
    checkDroppingTransferAlerts
}