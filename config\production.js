const { initializeQueues, setupJobMonitoring } = require('./queues');
const { isReportsEnabled, isAlertsEnabled, isJobQueuesEnabled, logFeatureFlags } = require('./features');
const { initializeWorkerManager, cleanupWorkerManager } = require('./worker-manager');

/**
 * Setup production-specific configurations
 * This includes schedules, health checks, and job queues
 */
const setupProductionConfig = async (app) => {
    console.log("Setting up production configuration...");

    // Log feature flags status
    logFeatureFlags();

    // Get feature flags
    const reportsEnabled = isReportsEnabled();
    const alertsEnabled = isAlertsEnabled();
    const jobQueuesEnabled = isJobQueuesEnabled();

    // Load Redis connection
    require('./redis-connection');

    // Initialize worker manager to prevent memory leaks
    console.log('🔧 Initializing worker management system...');
    await initializeWorkerManager();

    // Setup report schedules (if enabled)
    if (reportsEnabled) {
        console.log("📊 Initializing report schedules...");
        console.log("  🔄 Loading report subscription handlers...");
        require("../schedules/report-subscription.schedule");

        console.log("  ⏰ Setting up automatic daily reports...");
        const { setUpReportSchedule } = require("../schedules/automatic-report-daily.schedule");
        setUpReportSchedule();
        console.log("✅ Report schedules initialized - daily reports will be generated automatically!");
    } else {
        console.log("⚠️  Report schedules disabled by configuration - no automatic reports will be sent");
    }

    // Setup alert schedules (if enabled)
    if (alertsEnabled) {
        console.log("🚨 Initializing alert schedules...");
        console.log("  ⏰ Setting up automatic alert monitoring...");
        const { setUpAlertSchedule } = require("../schedules/alerts.schedule");
        setUpAlertSchedule();
        console.log("✅ Alert schedules initialized - monitoring system is active!");
    } else {
        console.log("⚠️  Alert schedules disabled by configuration - no automatic alerts will be sent");
    }

    // Setup health check API
    const health = require("../api/routes/health");
    app.use("/api/health", health);
    console.log("Health check API configured");

    // Initialize job queues and monitoring (if enabled)
    if (jobQueuesEnabled) {
        console.log("⚙️  Initializing job processing system...");
        console.log("  📋 Setting up job queues...");
        initializeQueues(app);
        console.log("  📊 Setting up job monitoring endpoints...");
        setupJobMonitoring(app);
        console.log("✅ Job queues and monitoring initialized - background processing ready!");
    } else {
        console.log("⚠️  Job queues disabled (reports are disabled) - no background processing available");
    }

    console.log("Production configuration completed successfully");
};

/**
 * Cleanup production configurations
 * This should be called during graceful shutdown
 */
const cleanupProductionConfig = async () => {
    console.log("Cleaning up production configuration...");

    try {
        // Cleanup worker manager (this will close all workers and queues)
        await cleanupWorkerManager();

        // Cleanup schedules
        const { cleanupReportSchedule } = require("../schedules/automatic-report-daily.schedule");
        const { cleanupAlertSchedule } = require("../schedules/alerts.schedule");
        const { cleanupMonthlySchedule } = require("../schedules/adt-month-complete.schedule");

        await cleanupReportSchedule();
        await cleanupAlertSchedule();
        cleanupMonthlySchedule();

        console.log("Production configuration cleanup completed");
    } catch (error) {
        console.error("Error during production configuration cleanup:", error);
    }
};

module.exports = { setupProductionConfig, cleanupProductionConfig };
