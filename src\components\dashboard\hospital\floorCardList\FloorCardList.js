import { Paper } from "@mui/material";
import { Box } from "@mui/system";
import { useMemo, useCallback } from "react";
import { OVERALL_PAGE_SUB_TYPE, PAGE_TYPE } from "../../../../types/pages.type";
import { calcProPercentsBasedOnFilterAndDays } from "../../../../utilis/common";
import ColorBox from "../../../shared/dashboard/ColorBox";
import NoRecordFound from "../../../shared/NoRecordFound";
import CheckboxButton from "../../shared/checkboxButton/CheckboxButton";
import CheckboxCircle from "../../shared/checkboxButton/CheckboxCircle";
import CheckboxLoader from "../../../shared/checkbox-loader/CheckboxLoader";
import useLoadingToggle from "../../../hooks/useLoadingToggle";
import FloorCardListSkeleton from "./FloorCardListSkeleton";
import { useFloorCardData } from "./hooks/useFloorCardData";
import { usePercentageCalculations } from "./hooks/usePercentageCalculations";
import { FloorCardListPropTypes, FloorCardListDefaultProps } from "./FloorCardList.types";
import styles from "./FloorCardList.module.scss";

// Extracted FloorCardItem component for better organization
const FloorCardItem = ({
	item,
	itemComparison,
	index,
	selected,
	percentage,
	comparisonData,
	onToggle,
	isLoading,
	isPercentageShow,
	filter,
	transferType,
	projectionDays,
	isComparingAgainstAvgCensus,
	lockedTotalBy,
	priorityNumber
}) => {
	const ID = item._id;
	const labelId = `checkbox-list-floor-label-${ID}`;
	const isSpacialItem = item?.isSpacialItem ?? false;
	const isFirstItemInPriorityData = priorityNumber === 1;

	const checkboxProps = {
		labelId,
		buttonText: item.label,
		handleToggle: onToggle,
		sx: { ...(isLoading && { opacity: 0 }) },
		checked: selected
	};

	return (
		<div className={`df aic m-b-10 ${styles.line} ${selected ? styles.selected : null}`} key={index}>
			<div className={`df fs15 fw500 ${styles.sec} ${styles.left}`} htmlFor={labelId}>
				<Box position="relative" display="inline-flex" alignItems="center">
					{isSpacialItem ? (
						<CheckboxCircle {...checkboxProps} />
					) : (
						<CheckboxButton
							className={styles.checkboxButton}
							{...checkboxProps}
							item={item}
							sx={{ mr: "-2px", ...(isLoading && { opacity: 0 }) }}
						/>
					)}
					<CheckboxLoader
						className="small-dots-left"
						isLoading={isLoading}
					/>
				</Box>
			</div>

			<div className={`df acc ${styles.sec}`}>
				<ColorBox
					color={comparisonData.comparisonColor}
					sx={{ width: "9px", height: "18px", mr: "3px" }}
					comparingAgainst={itemComparison?.total}
					comparingAgainstScaled={comparisonData.comparingAgainstScaled}
					numberOfDays={comparisonData.numberOfDays}
				/>
				<span className="ffmar-bold fs16">
					{calcProPercentsBasedOnFilterAndDays(item.total, { ...filter, transferType }, projectionDays)}
				</span>
				{isPercentageShow && (
					<span className="ffint fw400 fs14 m-l-4">
						{`(${calcProPercentsBasedOnFilterAndDays(
							percentage,
							{ ...filter, transferType },
							projectionDays,
							true,
							isComparingAgainstAvgCensus ||
							lockedTotalBy === "census" ||
							(isFirstItemInPriorityData && (!transferType || transferType?.length === 0))
						)})%`}
					</span>
				)}
			</div>
		</div>
	);
};

// Main FloorCardList component
const FloorCardList = (props) => {
	// Destructure props with defaults
	const {
		data = [],
		dataComparison = [],
		filter = {},
		filterComparison = {},
		handleToggle = () => {},
		selectedItem = [],
		spacialSelectedItem = [],
		type = '',
		page = '',
		isPercentageShow = true,
		averageCensusComparison = 0,
		averageCensus = 0,
		cardTitle = '',
		reverseColors = false,
		reverseColorsAdmissionPage = false,
		comparingAgainstDifferentFacility = false,
		admissionCompareAgainst = 0,
		admissionCompareAgainstComparison = 0,
		loading = false,
		projectionDays = 0,
		priorityNumber = 0,
		transferType = '',
		isComparingAgainstAvgCensus = false,
		lockedTotalBy = '',
		searchValue = '',
		question = null,
		isCustom = false,
		isCustomTab = false,
		customTab = null
	} = props;

	const { loadingItems, handleToggleWithLoader } = useLoadingToggle();

	// Memoized computed values
	const computedValues = useMemo(() => {
		const specialPages = [PAGE_TYPE.ADMISSION];
		const isSpecialPage = specialPages.includes(page);
		const isFirstItemInPriorityData = priorityNumber === 1;
		const isPlusSign = page === PAGE_TYPE.OVERALL && transferType === OVERALL_PAGE_SUB_TYPE.TOTAL;
		
		return {
			isSpecialPage,
			isFirstItemInPriorityData,
			isPlusSign
		};
	}, [page, priorityNumber, transferType]);

	// Custom hook for filtered data
	const filteredData = useFloorCardData(data, searchValue);

	// Custom hook for percentage calculations
	const { calculateItemComparison } = usePercentageCalculations({
		page,
		averageCensus,
		averageCensusComparison,
		admissionCompareAgainst,
		admissionCompareAgainstComparison,
		reverseColors,
		reverseColorsAdmissionPage,
		filter,
		filterComparison,
		projectionDays,
		computedValues
	});

	// Memoized toggle handler
	const createToggleHandler = useCallback((item, itemComparison) => {
		const isSpacialItem = item?.isSpacialItem ?? false;
		const ID = item._id;
		
		return () => {
			const toggleData = {
				item,
				itemComparison,
				type,
				isChecked: isSpacialItem 
					? spacialSelectedItem?.indexOf(ID) !== -1 
					: selectedItem.indexOf(ID) !== -1,
				cardTitle,
				isSpacialItem,
				isCustomTab,
				customTab,
				...(isCustom && {
					question: {
						isCustom: true,
						customQuestionInputType: question?.customQuestionInputType,
						isCustomTab,
						cardTitle,
						customTab
					}
				})
			};

			handleToggleWithLoader(() => handleToggle(toggleData), ID);
		};
	}, [
		type, cardTitle, isCustomTab, customTab, isCustom, question,
		selectedItem, spacialSelectedItem, handleToggle, handleToggleWithLoader
	]);

	// Render loading state
	if (loading) {
		return (
			<Paper style={{ minHeight: 350, height: 350, overflow: "auto", width: "100%" }}>
				<div className={styles.floor}>
					<FloorCardListSkeleton />
				</div>
			</Paper>
		);
	}

	// Render main content
	return (
		<Paper style={{ minHeight: 350, height: 350, overflow: "auto", width: "100%" }}>
			<div className={styles.floor}>
				{filteredData.length > 0 ? (
					filteredData.map((item, index) => {
						const ID = item._id;
						const itemComparison = dataComparison?.find((x) => x._id === item._id);
						const isSpacialItem = item?.isSpacialItem ?? false;
						
						// Determine selection state
						const selected = isSpacialItem
							? spacialSelectedItem?.indexOf(ID) !== -1 || selectedItem.indexOf(ID) !== -1
							: selectedItem.indexOf(ID) !== -1;

						// Calculate percentage and comparison data
						const percentage = item.percentage || item.total;
						const comparisonData = calculateItemComparison(item, itemComparison, comparingAgainstDifferentFacility);

						return (
							<FloorCardItem
								key={ID}
								item={item}
								itemComparison={itemComparison}
								index={index}
								selected={selected}
								percentage={percentage}
								comparisonData={comparisonData}
								onToggle={createToggleHandler(item, itemComparison)}
								isLoading={loadingItems[ID]}
								isPercentageShow={isPercentageShow}
								filter={filter}
								transferType={transferType}
								projectionDays={projectionDays}
								isComparingAgainstAvgCensus={isComparingAgainstAvgCensus}
								lockedTotalBy={lockedTotalBy}
								priorityNumber={priorityNumber}
							/>
						);
					})
				) : (
					<NoRecordFound data={data} />
				)}
			</div>
		</Paper>
	);
};

// Add PropTypes and default props
FloorCardList.propTypes = FloorCardListPropTypes;
FloorCardList.defaultProps = FloorCardListDefaultProps;

export default FloorCardList;
