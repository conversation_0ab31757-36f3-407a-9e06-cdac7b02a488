const { createOrUpdateOne, deleteOne, getAll, createOrUpdateCustomOne, deleteCustomAlert } = require("../helpers/alertReport");
const authWithRole = require("../middleware/auth-with-role");
const express = require("express");

const route = express.Router();

// Create or update an account
route.post("/", authWithRole("manageDashboard"), async (req, res) => {
  try {
    const account = await createOrUpdateOne(req);
    res.send(account);
  } catch (error) {
    console.error("Error creating/updating account:", error);
    res.send(500)
  }
});

// Create custom alert report
route.post("/custom", authWithRole("manageDashboard"), async (req, res) => {
  try {
    const account = await createOrUpdateCustomOne(req);
    res.send(account);
  } catch (error) {
    console.error("Error creating/updating account:", error);
    res.send(500)
  }
});

// Get accounts or account by ID (optional)
route.get("/:id?", authWithRole("manageDashboard"), async (req, res) => {
  try {
    const accounts = await getAll(req);
    res.send(accounts);
  } catch (error) {
    console.error("Error fetching accounts:", error);
    res.send(500)
  }
});

// Delete an account by ID
route.delete("/:id", authWithRole("manageDashboard"), async (req, res) => {
  try {
    const deletedAccount = await deleteOne(req.params.id);
    if (deletedAccount) {
      res.send(deletedAccount);
    } else {
      res.send(404);
    }
  } catch (error) {
    console.error("Error deleting account:", error);
    res.send(500);
  }
});

// Delete an account by ID
route.delete("/custom-alert/:id", authWithRole("manageDashboard"), async (req, res) => {
  try {
    const deletedAccount = await deleteCustomAlert(req, res);
    return deletedAccount;
  } catch (error) {
    console.error("Error deleting account:", error);
    res.send(500);
  }
});

module.exports = route;
