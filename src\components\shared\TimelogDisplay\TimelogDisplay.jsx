import React, { useMemo, useState } from "react";
import {
  parse,
  format,
  isWithinInterval,
  startOfWeek,
  endOfWeek,
  getYear,
  getMonth,
  eachDayOfInterval,
  eachWeekOfInterval,
  eachMonthOfInterval,
} from "date-fns";
import styles from "./TimelogDisplay.module.scss";

import {
  CircularProgress,
  Dialog,
  DialogTitle,
  DialogContent,
  IconButton,
} from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import TimeLogGraph from "./TimeLogGraph";
import { fmtMin } from "../../../utilis/time-format";

export default function TimeLogDisplay({ logs, range, onRangeChange, loading: fetchLoading = false, userName, accountName }) {
  const [breakdown, setBreakdown] = useState("monthly");
  const [view, setView] = useState("cards");
  const [loading, setLoading] = useState(false);
  const displayLoading = loading || fetchLoading;
  const [modalGroup, setModalGroup] = useState(null);
  const [showEmpty, setShowEmpty] = useState(true);

  const allDates = useMemo(() => logs?.[0]?.dates ?? [], [logs]);

  const filteredDates = useMemo(() => {
    if (!range.start || !range.end) return [];
    const start = parse(range.start, "yyyy-MM-dd", new Date());
    const end = parse(range.end, "yyyy-MM-dd", new Date());

    return allDates.filter((d) =>
      isWithinInterval(parse(d.date, "MM/dd/yyyy", new Date()), {
        start,
        end,
      })
    );
  }, [range, allDates]);

  /* ----------  GROUP / AGGREGATE (builds FULL timeline incl. empty periods) ---------- */
  const groups = useMemo(() => {
    if (!range.start || !range.end) return [];

    const start = parse(range.start, "yyyy-MM-dd", new Date());
    const end = parse(range.end, "yyyy-MM-dd", new Date());

    // ----------  helper to compute group key + title ----------
    const getMeta = (dateObj) => {
      if (breakdown === "daily") {
        const key = format(dateObj, "MM/dd/yyyy");
        const title = format(dateObj, "eee dd MMM");
        return { key, title };
      }
      if (breakdown === "weekly") {
        const ws = startOfWeek(dateObj, { weekStartsOn: 1 });
        const we = endOfWeek(dateObj, { weekStartsOn: 1 });
        const key = format(ws, "yyyy-MM-dd");
        const title = `${format(ws, "dd MMM yyyy")} – ${format(we, "dd MMM yyyy")}`;
        return { key, title };
      }
      // monthly
      const key = `${getYear(dateObj)}-${getMonth(dateObj)}`; // month index 0-based
      const title = format(dateObj, "MMMM yyyy");
      return { key, title };
    };

    // ----------  bucket with EXISTING work entries ----------
    const bucket = {};
    filteredDates.forEach((d) => {
      const dateObj = parse(d.date, "MM/dd/yyyy", new Date());
      const { key, title } = getMeta(dateObj);

      if (!bucket[key])
        bucket[key] = { key, title, minutes: 0, days: [], times: [] };

      bucket[key].minutes += d.times.length;
      bucket[key].days.push({
        label: format(dateObj, "dd MMM"),
        minutes: d.times.length,
      });
      if (breakdown === "daily") bucket[key].times = d.times;
    });

    // ----------  generate COMPLETE timeline keys ----------
    let expectedDates = [];
    if (breakdown === "daily") {
      expectedDates = eachDayOfInterval({ start, end });
    } else if (breakdown === "weekly") {
      expectedDates = eachWeekOfInterval(
        { start, end },
        { weekStartsOn: 1 }
      );
    } else {
      expectedDates = eachMonthOfInterval({ start, end });
    }

    expectedDates.forEach((dateObj) => {
      const { key, title } = getMeta(dateObj);
      if (!bucket[key]) {
        bucket[key] = { key, title, minutes: 0, days: [], times: [] };
      }
    });

    // ----------  convert to array + DESC sort by real date ----------
    const arr = Object.values(bucket);

    const keyToDate = (g) => {
      if (breakdown === "daily") return parse(g.key, "MM/dd/yyyy", new Date());
      if (breakdown === "weekly") return parse(g.key, "yyyy-MM-dd", new Date());
      const [y, m] = g.key.split("-").map(Number);
      return new Date(y, m, 1);
    };

    arr.sort((a, b) => keyToDate(b) - keyToDate(a));
    return arr;
  }, [filteredDates, range, breakdown]);

  const totalMinutes = filteredDates.reduce((sum, d) => sum + d.times.length, 0);
  const totalDays = filteredDates.length;
  const displayedGroups = showEmpty ? groups : groups.filter((g) => g.minutes);

  const dayLabel = (d) => `${d} day${d !== 1 ? "s" : ""}`;

  return (
    <div className={styles.container}>
      {/* ----- controls ----- */}
      <section className={styles.controls}>
        <h2 className={styles.controlsTitle}>Date range</h2>

        <div className={styles.dateInputs}>
          <div className={styles.dateInput}>
            <label htmlFor="startDate">Start</label>
            <input
              id="startDate"
              type="date"
              value={range.start}
              onChange={(e) =>
                onRangeChange({ ...range, start: e.target.value })
              }
            />
          </div>

          <div className={styles.dateInput}>
            <label htmlFor="endDate">End</label>
            <input
              id="endDate"
              type="date"
              value={range.end}
              min={range.start}
              onChange={(e) =>
                onRangeChange({ ...range, end: e.target.value })
              }
            />
          </div>
        </div>

        <div className={styles.breakdownSelector}>
          {["daily", "weekly", "monthly"].map((m) => (
            <button
              key={m}
              className={
                breakdown === m ? styles.breakdownBtnActive : styles.breakdownBtn
              }
              onClick={() => {
                setLoading(true);
                setBreakdown(m);
                setTimeout(() => setLoading(false), 300);
              }}
            >
              {m.charAt(0).toUpperCase() + m.slice(1)}
            </button>
          ))}
          <label className={styles.emptyToggle}>
            <input
              type="checkbox"
              checked={showEmpty}
              disabled={view === "graph"}
              onChange={(e) => setShowEmpty(e.target.checked)}
            />
            {" "}Show empty periods
          </label>
        </div>
      </section>

      <div className={styles.viewSelector}>
        {["cards", "graph"].map((v) => (
          <button
            key={v}
            className={view === v ? styles.breakdownBtnActive : styles.breakdownBtn}
            onClick={() => setView(v)}
          >
            {v.charAt(0).toUpperCase() + v.slice(1)}
          </button>
        ))}
      </div>

      {/* ----- summary + cards ----- */}
      {range.start && range.end ? (
        <>
          <div className={styles.summaryCard}>
            <h3>
              {format(parse(range.start, "yyyy-MM-dd", new Date()), "dd MMM yyyy")} – {" "}
              {format(parse(range.end, "yyyy-MM-dd", new Date()), "dd MMM yyyy")}
            </h3>
            <div className={styles.summaryRow}>
              <span className={styles.summaryStats}>
                <strong>
                  {breakdown === "daily" ? fmtMin(totalMinutes) : dayLabel(totalDays)}
                </strong>{" "}
                across {displayedGroups.length}{" "}
                {breakdown === "daily"
                  ? "day"
                  : breakdown === "weekly"
                    ? "week"
                    : "month"}
                {groups.length !== 1 ? "s" : ""}
              </span>

              {(userName || accountName) && (
                <span className={styles.summaryContext}>
                  {userName}
                  {userName && accountName && " | "}
                  {accountName}
                </span>
              )}
            </div>
          </div>

          {displayLoading ? (
            <div className={styles.loadingContainer}>
              <CircularProgress />
            </div>
          ) : view === "cards" ? (
            <div className={styles.cardsGrid}>
              {displayedGroups.map((g) => {
                const badge = g.minutes
                  ? breakdown === "daily"
                    ? fmtMin(g.minutes)
                    : dayLabel(g.days.length)
                  : "No work";

                const badgeClass = g.minutes ? styles.badge : styles.badgeNoWork;

                return (
                  <div key={g.key} className={styles.card}>
                    <header className={styles.cardHeader}>
                      <span>{g.title}</span>
                      <span className={badgeClass}>{badge}</span>
                      {g.minutes ? (
                        <button
                          className={styles.toggleBtn}
                          onClick={() => setModalGroup(g)}
                        >
                          Show
                        </button>
                      ) : null}
                    </header>
                  </div>
                );
              })}
            </div>
          ) : (
            <TimeLogGraph groups={displayedGroups} breakdown={breakdown} />
          )}
        </>
      ) : (
        <p className={styles.placeholder}>
          Select a start and end date to see results
        </p>
      )}

      {/* ----------  MODAL: Details per group ---------- */}
      <Dialog
        open={!!modalGroup}
        onClose={() => setModalGroup(null)}
        maxWidth="xs"
        fullWidth
      >
        {modalGroup && (
          <>
            <DialogTitle
              sx={{
                display: "flex",
                justifyContent: "space-between",
                alignItems: "center",
                pr: 1,
              }}
            >
              {modalGroup.title}
              <IconButton onClick={() => setModalGroup(null)} size="small">
                <CloseIcon fontSize="inherit" />
              </IconButton>
            </DialogTitle>

            <DialogContent dividers className={styles.modalContent}>
              {breakdown === "daily" ? (
                modalGroup.times.map((t, i) => (
                  <div key={i} className={styles.timeItem}>
                    {t}
                  </div>
                ))
              ) : (
                modalGroup.days.map((d) => (
                  <div key={d.label} className={styles.subItem}>
                    <span>{d.label}</span>
                    <span>{fmtMin(d.minutes)}</span>
                  </div>
                ))
              )}
            </DialogContent>
          </>
        )}
      </Dialog>
    </div>
  );
}
