
name: Build and Deploy to Production [AWS]

on:
  workflow_dispatch:
  push:
    branches: [ master ]
    
jobs:
  build-and-deploy:
    name: Build and Deploy ${{ matrix.deployment.name }}
    runs-on: ubuntu-latest

    environment: prod

    env:
      TYPE: 'build'
      AWS_KEY_ID: '********************'
      AWS_ACCESS_KEY: 'oyBZF9EsM+VWQf6KcwVdM8kXENXlrq7bk/UX2cqf'
      REACT_APP_REQUIRE_EMAIL: 'true'
      REACT_APP_TENANT_API_BASE_URL: 'https://tenant-api.simplesnf.com'

    strategy:
      matrix:
        deployment:
          - name: Main
            BUCKET: 'www.simplesnf.com'
            REGION: 'us-east-1'
            DIST_ID: 'E57DR0QVWEBWS'
            AWS_REGION: 'us-east-1'
            REACT_APP_BASE_URL: 'https://api.simplesnf.com'
            REACT_APP_APP_URL: 'https://www.simplesnf.com'
          - name: Paragon
            BUCKET: 'www.paragon-simplesnf.com'
            REGION: 'us-east-1'
            DIST_ID: 'E2QOPJP6GT68AK'
            AWS_REGION: 'us-east-1'
            REACT_APP_BASE_URL: 'https://paragon-api.simplesnf.com'
            REACT_APP_APP_URL: 'https://paragon.simplesnf.com'
      
    steps:
    - name: Checkout
      uses: actions/checkout@v4

    - name: Configure AWS Credentials
      uses: aws-actions/configure-aws-credentials@v4
      with:
        aws-access-key-id: ${{ env.AWS_KEY_ID }}
        aws-secret-access-key: ${{ env.AWS_ACCESS_KEY }}
        aws-region: ${{ matrix.deployment.AWS_REGION}}
    
    - name: Install Dependencies
      run: |
        node --version
        rm -rf node_modules package-lock.json
        npm install
        
    - name: Build Static Website
      run: npm run build
      env:
          REACT_APP_BASE_URL: ${{ matrix.deployment.REACT_APP_BASE_URL }}
          APP_URL: ${{ matrix.deployment.REACT_APP_APP_URL }}
          REGION: ${{ matrix.deployment.AWS_REGION }}
          REACT_APP_AUTH0_DOMAIN: ${{ secrets.REACT_APP_AUTH0_DOMAIN }}
          REACT_APP_AUTH0_CLIENT_ID: ${{ secrets.REACT_APP_AUTH0_CLIENT_ID }}
          REACT_APP_REQUIRE_EMAIL: ${{ env.REACT_APP_REQUIRE_EMAIL }}
          REACT_APP_TENANT_API_BASE_URL: ${{ env.REACT_APP_TENANT_API_BASE_URL }}

    - name: Copy files to S3 Bucket
      run: |
        aws s3 sync --delete ${{ env.TYPE }} s3://${{ matrix.deployment.BUCKET }}
        
    - name: Invalidate old sessions
      run: |
        aws cloudfront create-invalidation \
          --distribution-id ${{ matrix.deployment.DIST_ID }} \
          --paths "/*"
