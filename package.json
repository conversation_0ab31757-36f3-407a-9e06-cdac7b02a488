{"name": "adt-tech-fe", "version": "0.1.0", "private": true, "options": {"allowedHosts": ["localhost", ".localhost"], "proxy": "https://snf-api.azurewebsites.net"}, "dependencies": {"@auth0/auth0-react": "^2.2.4", "@emotion/react": "^11.10.6", "@emotion/styled": "^11.10.6", "@mui/icons-material": "^5.15.2", "@mui/lab": "^5.0.0-alpha.122", "@mui/material": "^5.11.12", "@mui/styles": "^5.15.12", "@mui/system": "^5.11.12", "@mui/utils": "^5.14.15", "@nivo/bar": "^0.87.0", "@nivo/core": "^0.87.0", "@nivo/line": "^0.87.0", "@nivo/pie": "^0.87.0", "@nivo/radial-bar": "^0.87.0", "@reduxjs/toolkit": "^2.2.7", "@sentry/react": "^7.56.0", "@testing-library/jest-dom": "^6.5.0", "@testing-library/react": "^12.1.2", "@testing-library/user-event": "^14.5.2", "async": "^3.2.6", "axios": "^1.7.7", "cardinal": "^2.1.1", "chart.js": "^4.4.4", "classnames": "^2.5.1", "cleave.js": "^1.6.0", "date-fns": "^2.29.3", "dayjs": "^1.11.13", "exceljs": "^4.4.0", "formik": "^2.4.6", "immutability-helper": "^3.1.1", "jspdf": "^2.5.1", "lodash": "^4.17.21", "material-ui-popup-state": "^5.3.1", "moment": "^2.30.1", "moment-timezone": "^0.5.45", "ordinal": "^1.0.3", "prop-types": "^15.8.1", "react": "^17.0.2", "react-beautiful-dnd": "^13.1.1", "react-chartjs-2": "^5.2.0", "react-date-range": "1.4.0", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^17.0.2", "react-helmet-async": "^1.3.0", "react-idle-timer": "^5.7.2", "react-redux": "^8.0.5", "react-router": "^6.8.2", "react-router-dom": "^6.8.2", "react-scripts": "5.0.1", "react-slick": "^0.30.2", "react-table": "^7.8.0", "react-time-picker": "^5.2.0", "react-timekeeper": "2.1.3", "react-timezone-select": "^2.0.0", "react-toastify": "^9.1.1", "react-window": "^1.8.10", "redux": "^4.2.1", "redux-thunk": "^2.4.2", "sass": "^1.78.0", "simplebar-react": "^3.2.6", "slick-carousel": "^1.8.1", "styled-components": "^6.1.13", "web-vitals": "^4.2.3", "yup": "^1.4.0"}, "scripts": {"start": "set PORT=3007 && react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "volta": {"node": "20.17.0"}, "devDependencies": {"@faker-js/faker": "^9.0.0", "@babel/plugin-proposal-private-property-in-object": "^7.18.6"}}