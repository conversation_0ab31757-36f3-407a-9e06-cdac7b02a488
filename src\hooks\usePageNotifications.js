import { useState, useEffect, useCallback } from 'react';
import { useSelector } from 'react-redux';
import { getNotifications, markNotificationRead } from '../services/custom-tab-sharing.service';
// import { PAGE_TYPE } from '../types/pages.type';
import { useDispatch } from 'react-redux';
import { setIsCustomTabAdded } from '../store/reducers/common.slice';
// import { useNavigate } from 'react-router-dom';
// import { setPreviousParams } from '../utilis/common';

export const usePageNotifications = (pageType) => {
    const [shareNotifications, setShareNotifications] = useState([]);
    const [showShareDialog, setShowShareDialog] = useState(false);
    const [notificationsChecked, setNotificationsChecked] = useState(false);
    const dispatch = useDispatch();
    const isCustomTabAdded = useSelector((state) => state.common.isCustomTabAdded);
    const { auth } = useSelector(({ auth }) => ({ auth }));
    const { selectedAccount } = useSelector((state) => state.common);
    // const navigate = useNavigate();

    // Check for pending share notifications for this page
    const checkPendingNotifications = useCallback(async () => {
        if (!auth?._id || !pageType || notificationsChecked) return;

        try {
            const response = await getNotifications(50, 0, true, pageType); // Get unread notifications for this page

            if (response?.data?.notifications) {
                // Filter for TAB_SHARED notifications that require action
                const shareNotifications = response.data.notifications.filter(
                    notification => (notification.type === 'TAB_SHARED' || notification.type === 'TAB_UNSHARED' || notification.type === 'TAB_UPDATED' || notification.type === 'TAB_DELETED')
                );

                if (shareNotifications.length > 0) {
                    setShareNotifications(shareNotifications);
                    setShowShareDialog(true);
                }
            }
        } catch (error) {
            console.error('Error checking page notifications:', error);
        } finally {
            setNotificationsChecked(true);
        }
    }, [auth?._id, notificationsChecked, pageType]);

    // Handle notification response
    const handleNotificationResponse = async (notificationId, response, data) => {
        try {
            // Mark the notification as read
            await markNotificationRead(notificationId);

            // Remove this notification from the list
            setShareNotifications(prev =>
                prev.filter(notification => notification._id !== notificationId)
            );
        } catch (error) {
            console.error('Error handling notification response:', error);
        }
    };

    // Handle viewing a tab immediately
    const handleViewTab = (customTab) => {
        if (customTab && customTab.page) {
            // Navigate to the appropriate dashboard page

            dispatch(setIsCustomTabAdded(!isCustomTabAdded));
            // const pageRoutes = {
            //     [PAGE_TYPE.HOSPITAL]: 'dashboard/hospital',
            //     [PAGE_TYPE.COMMUNITY_TRANSFER]: 'dashboard/community-transfer',
            //     [PAGE_TYPE.DECEASED]: 'dashboard/deceased',
            //     [PAGE_TYPE.ADMISSION]: 'dashboard/admissions',
            //     [PAGE_TYPE.OVERALL]: 'dashboard/overall'
            // };

            // const route = pageRoutes[customTab.page];
            // if (route) {
            //     //navigate(setPreviousParams(route));
            // }
        }
    };

    // Close share dialog
    const handleCloseShareDialog = () => {
        setShowShareDialog(false);
        // Mark any remaining notifications as checked
        setNotificationsChecked(true);
    };

    // Check for pending notifications when page is visited
    useEffect(() => {
        if (auth?._id && selectedAccount && pageType && !notificationsChecked) {
            // Add a small delay to ensure all auth setup is complete
            const timer = setTimeout(() => {
                checkPendingNotifications();
            }, 1000);

            return () => clearTimeout(timer);
        }
    }, [auth?._id, selectedAccount, pageType, checkPendingNotifications, notificationsChecked]);

    // Also check immediately when page type changes (if already authenticated)
    useEffect(() => {
        if (auth?._id && selectedAccount && pageType) {
            // Reset the check flag to allow re-checking
            setNotificationsChecked(false);
        }
    }, [pageType, auth?._id, selectedAccount]);

    // Reset notification check when auth, account, or page changes
    useEffect(() => {
        setNotificationsChecked(false);
        setShareNotifications([]);
        setShowShareDialog(false);
    }, [auth?._id, selectedAccount?._id, pageType]);

    return {
        shareNotifications,
        showShareDialog,
        handleNotificationResponse,
        handleViewTab,
        handleCloseShareDialog
    };
}; 