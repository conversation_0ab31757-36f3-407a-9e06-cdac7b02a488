import * as React from 'react';
import { styled } from '@mui/material/styles';
import { Stack } from '@mui/material';
import { itemPercentage } from '../../utilis/common';
import { CartItemTooltipContainer } from './CardItemTooltip';


export default function CardItemTooltipCustome({
    children,
    item,
    tooltipOpen,
    handleTooltipOpen,
    handleTooltipClose,
    key
}) {
    if (item?.isTooltip) {
        return (
            <CartItemTooltipContainer
                open={tooltipOpen}
                key={key}
                //onOpen={handleTooltipOpen}
                onClose={handleTooltipClose}
                title={
                    <Stack direction={"column"} sx={{ padding: 0.2 }} spacing={1}>
                        <Stack item>Original total : {item?.originalTotal || 0}</Stack>
                        <Stack item>Filtered total : {item?.total || item?.value || 0}</Stack>
                        {!item?.isTotalPercentage && (
                            <Stack item>Percentage : {itemPercentage(item?.total || item?.value, item?.originalTotal, "percentage")}</Stack>
                        )}
                    </Stack>
                }
            >
                {children}
            </CartItemTooltipContainer>
        );
    } else {
        return children
    }
}
