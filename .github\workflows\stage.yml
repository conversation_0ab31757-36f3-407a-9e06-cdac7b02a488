name: Build and Deploy to Staging [AWS]

on:
  workflow_dispatch:
  push:
    branches: [ stage ]
    
jobs:
  build-and-deploy:
    name: Build and Deploy ${{ matrix.deployment.name }}
    runs-on: ubuntu-latest

    environment: stage

    env:
      TYPE: 'build'
      REACT_APP_REQUIRE_EMAIL: 'true'
      REACT_APP_TENANT_API_BASE_URL: 'https://stage-tenant-api.snfdatasolutions.com'

    strategy:
      matrix:
        deployment:
          - name: Main
            BUCKET: 'live.snfdatasolutions.com'
            REGION: 'us-east-1'
            DIST_ID: 'E1WCYDPXA1G04N'
            AWS_REGION: 'us-east-1'
            REACT_APP_BASE_URL: 'https://live-api.snfdatasolutions.com'
            REACT_APP_APP_URL: 'https://live.snfdatasolutions.com'
          - name: Org1
            BUCKET: 'org1.snfdatasolutions.com'
            REGION: 'us-east-1'
            DIST_ID: 'E1V7KFK6NYIE9Z'
            AWS_REGION: 'us-east-1'
            REACT_APP_BASE_URL: 'https://org1-api.snfdatasolutions.com'
            REACT_APP_APP_URL: 'https://org1.snfdatasolutions.com'

    steps:
    - name: Checkout
      uses: actions/checkout@v4

    - name: Configure AWS Credentials
      uses: aws-actions/configure-aws-credentials@v4
      with:
        aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
        aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        aws-region: ${{ matrix.deployment.AWS_REGION }}
    
    - name: Install Dependencies
      run: |
        node --version
        rm -rf node_modules package-lock.json
        npm install
        
    - name: Build Static Website
      run: npm run build
      env:
          REACT_APP_BASE_URL: ${{ matrix.deployment.REACT_APP_BASE_URL }}
          APP_URL: ${{ matrix.deployment.REACT_APP_APP_URL  }}
          REGION: ${{ matrix.deployment.AWS_REGION  }}
          REACT_APP_AUTH0_DOMAIN: ${{ secrets.REACT_APP_AUTH0_DOMAIN }}
          REACT_APP_AUTH0_CLIENT_ID: ${{ secrets.REACT_APP_AUTH0_CLIENT_ID }}
          REACT_APP_REQUIRE_EMAIL: ${{ env.REACT_APP_REQUIRE_EMAIL }}
          REACT_APP_TENANT_API_BASE_URL: ${{ env.REACT_APP_TENANT_API_BASE_URL }}

    - name: Copy files to s3
      run: |
        aws s3 sync --delete ${{ env.TYPE }} s3://${{ matrix.deployment.BUCKET }}
        
    - name: Invalidate old sessions
      run: |
        aws cloudfront create-invalidation \
          --distribution-id ${{ matrix.deployment.DIST_ID }} \
          --paths "/*"
