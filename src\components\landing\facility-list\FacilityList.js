import axios from "../../../axios";
import { useEffect, useState } from "react";
import { useParams } from "react-router";
import styles from "./FacilityList.module.scss";
import { Link } from "react-router-dom";

const FacilityList = () => {
  const params = useParams();
  const [facilities, setFacilities] = useState([]);

  const getFacilities = async () => {
    let localFacilities = await axios.get(
      `/api/facility/list/${params.accountid}`
    );
    
    setFacilities(localFacilities.data);
  };

  useEffect(() => {
    if (!params.accountid) return;
    getFacilities();
    // eslint-disable-next-line
  }, [params.accountid]);

  return (
    <div className={styles.facilityList}>
      <h1 className={`ffmsb fs16`}>Facility List</h1>

      {facilities.map((facility) => (
        <Link
          key={facility._id}
          to={`/account/${params.accountid}/facility/${facility._id}`}
          className={styles.line}
        >
          <div className={`ffmr fs14 ${styles.item}`}>{facility.name}</div>
        </Link>
      ))}
    </div>
  );
};

export default FacilityList;
