/**
 * Load all Mongoose models
 * This ensures all models are registered with Mongoose before they're used
 */
const loadModels = () => {
    // Core models
    require("../models/Account");
    require("../models/User");
    require("../models/Facility");
    require("../models/Patient");
    require("../models/Validation");
    require("../models/Question");
    require("../models/QuestionOrder");
    require("../models/TimeLog");
    require("../models/Census");
    require("../models/Filter");
    require("../models/Role");
    require("../models/Permission");
    require("../models/RoleHasPermissions");
    require("../models/Log");
    require("../models/QuickGlace");
    
    // Report and subscription models
    require("../models/CompleteMonthADTEmail");
    require("../models/ReportsSubscriptions");
    require("../models/ReportLog");
    require("../models/PercentageAgainst");
    
    // Custom tab and card models
    require("../models/customCardFilter");
    require("../models/CustomTab");
    require("../models/CustomTabNotification");
    require("../models/CustomTabShare");
    require("../models/DynamicDataTab");
    
    // Other models
    require("../models/Settings");
    require("../models/FacilityManuallyEndOfADT");
    require("../models/AlertReport");
    require("../models/Note");
    
    console.log("All models loaded successfully");
};

module.exports = { loadModels };
