const mongoose = require("mongoose");
const Patient = mongoose.model("patient");
const _ = require("lodash");
const {
	momentDateFormat,
	getDayNameFromDate,
	getShiftName,
	toStartFilterDate,
	toEndFilterDate,
} = require("../utilis/date-format");
const { getCensusAverageInfo, getCensusAverageByPeriod, getBedCapacityByFacilityIds } = require("./census");
const {
	getReHospitalization,
	ninetyDaysDataFilter,
	getHospitalDays,
	getOtherDashboardData,
	getCustomCombineTabData,
} = require("./hospital");
const { PAGE_TYPE, ADT_TABLE_TYPE, ADMISSION_CARDS_TYPE, RELATIONS, CUSTOM_TAB_TYPE } = require("../../types/common.type");
const { ADT_TYPES, ADT_SUB_TYPES } = require("../../types");
const { findSavedReportConfig } = require("./reportsSubscription");
const { getCustomTabsByPage } = require("./custom-tab");
const { createDiffDashboardPatients, enrichDiffDashboardPatients, getReturnAndDidNotReturn, getNinetyDaysChartCount, getSixtyDaysChartCount } = require("../../utils/common");
const {
	initializeGetAllCount,
	setupFacilityFilter,
	setupDateFilter,
	getCensusInfo,
	finalizeGetAllCount
} = require("./common-dashboard");
const { formatPatientData } = require("../utilis/common");


const filterData = async (arr, callback) => {
	const fail = Symbol();
	return (await Promise.all(arr.map(async (item) => ((await callback(item)) ? item : fail)))).filter((i) => i !== fail);
};

const getCardPatientChartData = async (req) => {
	const { body } = req;
	const { accountid } = req.headers;
	const {
		facilityId,
		facilityIds,
		cardFilter,
		type,
		filter: { startDate, endDate },
		relation,
		transferType,
	} = body;
	const { admissionsIds = [], ADTFilter = false } = cardFilter;
	let diffDashboardPatients = await createDiffDashboardPatients();
	const customTabs = await getCustomTabsByPage({ accountid, page: PAGE_TYPE.ADMISSION, userId: req.user._id });
	let customCombineTabData = await createDiffDashboardPatients('combineTab');
	const isCustomCombineTab = customTabs?.some(tab => tab.type === CUSTOM_TAB_TYPE.COMBINE);

	let startDateFilter = await toStartFilterDate(startDate);
	let endDateFilter = await toEndFilterDate(endDate);

	let query = { type: { $in: [ADT_TYPES.ADMISSIONS, ADT_TYPES.READMISSIONS] } };
	if (ADTFilter) {
		const patientIds = admissionsIds.map((e) => mongoose.Types.ObjectId(e));
		query._id = { $in: patientIds };
	}
	if (type === ADMISSION_CARDS_TYPE.TOTAL) {
	} else if (type === ADMISSION_CARDS_TYPE.ADMISSION || type === ADMISSION_CARDS_TYPE.READMISSION) {
		query.type = { $in: [type] };
	} else {
		if (transferType) {
			query.type = transferType;
		}
	}

	const facilityData = [];

	if (facilityIds && facilityIds.length > 0) {
		query.facilityId = { $in: facilityIds };
		facilityIds.map((ele) => {
			facilityData.push(mongoose.Types.ObjectId(ele));
		});
		facilityFilter = { facilityId: { $in: facilityData } };
	} else {
		if (facilityId) {
			facilityFilter = { facilityId: mongoose.Types.ObjectId(facilityId) };
			query.facilityId = facilityId;
		}
	}

	if (startDate && endDate) {
		query.dateOfADT = {
			$gte: startDateFilter,
			$lt: endDateFilter,
		};
	}
	let relations = [
		RELATIONS.FACILITY,
		RELATIONS.DOCTOR,
		RELATIONS.UNIT,
		RELATIONS.PAYER_SOURCE_INSURANCE,
		RELATIONS.INSURANCE,
		RELATIONS.DX,
		RELATIONS.NURSE,
		RELATIONS.HOSPITAL,
	];

	let isMainData = false;
	if (
		type === ADMISSION_CARDS_TYPE.TOTAL ||
		type === ADMISSION_CARDS_TYPE.READMISSION ||
		type === ADMISSION_CARDS_TYPE.ADMISSION
	) {
		isMainData = true;
	}

	if (!isMainData) {
		if (cardFilter && cardFilter.doctorData.length > 0 && type !== ADMISSION_CARDS_TYPE.DOCTOR_DATA) {
			const doctorIds = cardFilter.doctorData.map((e) => mongoose.Types.ObjectId(e));
			query.doctor = { $in: doctorIds };
			//relations.push("doctor");
		}
		if (cardFilter && cardFilter.floorsData.length > 0 && type !== ADMISSION_CARDS_TYPE.FLOORS_DATA) {
			const floorIds = cardFilter.floorsData.map((e) => mongoose.Types.ObjectId(e));
			query.unit = { $in: floorIds };
			//relations.push("unit");
		}

		if (cardFilter && cardFilter.insuranceData.length > 0 && type !== ADMISSION_CARDS_TYPE.INSURANCE_DATA) {
			const insuranceIds = cardFilter.insuranceData.map((e) => mongoose.Types.ObjectId(e));
			query.payerSourceInsurance = { $in: insuranceIds };
			//relations.push("payerSourceInsurance");
		}
		if (cardFilter && cardFilter.dxData.length > 0 && type !== ADMISSION_CARDS_TYPE.DX_DATA) {
			const dxIdsFilter = cardFilter.dxData.map((e) => mongoose.Types.ObjectId(e));
			query.dx = { $elemMatch: { $in: dxIdsFilter } };
			//relations.push("dx");
		}

		if (cardFilter && cardFilter.hospitalData.length > 0 && type !== ADMISSION_CARDS_TYPE.HOSPITAL_DATA) {
			const hospitalIds = cardFilter.hospitalData.map((e) => mongoose.Types.ObjectId(e));
			query.hospital = { $in: hospitalIds };
			//relations.push("hospital");
		}

		let patientIds = [];
		let shouldUpdateQuery = false;

		if (Array.isArray(cardFilter?.mainPriorityData) && cardFilter.mainPriorityData.length > 0) {
			for (const { question, patientIds: ids = [] } of cardFilter.mainPriorityData) {
				if (question?.isCustom) {
					shouldUpdateQuery = true;
					patientIds = ids;
				}
			}
			if (shouldUpdateQuery && patientIds.length > 0) {
				const uniqueIds = [...new Set(patientIds.map(id => String(id)))];
				const ids = uniqueIds.map(id => mongoose.Types.ObjectId(id));
				query._id = { $in: ids };
			}
		}
	}
	if(isCustomCombineTab) {
		customCombineTabData = await getCustomCombineTabData(query, PAGE_TYPE.ADMISSION, facilityFilter, customCombineTabData, customTabs);
	}

	let patientList = [];
	let listData = [];
	patientList = await Patient.find({ ...query })
		.populate(relations)
		.sort({ dateOfADT: -1 })
		.exec();

	const seenPatientIds = new Set();

	await filterData(patientList, async (item) => {
		if (!isMainData) {
			if (cardFilter.daysData && cardFilter.daysData.length > 0 && type !== ADMISSION_CARDS_TYPE.DAYS_DATA) {
				const dayName = await getDayNameFromDate(item._doc.dateOfADT);
				if (!_.includes(cardFilter.daysData, dayName)) {
					return false;
				}
			}
		}
		if (customTabs?.length > 0) {
			await getOtherDashboardData(item, facilityFilter, diffDashboardPatients, seenPatientIds);
		}
		let latestObj = new Object();
		latestObj = item._doc;
		if (relation) {
			latestObj.filterId = item[relation]?._id || null;
		}
		const formattedObj = await formatPatientData(latestObj);
		listData.push(formattedObj);
	});

	let censusAverage = 0;
	let censusByPeriod = [];
	let censusByFacility = [];
	let bedCapacity = 0;
	let bedByFacility = [];
	let censusAsOfNowByFacility = [];

	const bedCapacityByFacility = await getBedCapacityByFacilityIds(facilityIds);
	diffDashboardPatients = await enrichDiffDashboardPatients(diffDashboardPatients, customTabs);

	if (startDate && endDate) {
		const facilityData = facilityIds.length > 0 ? facilityIds : [facilityId];
		if (isMainData) {
			censusByPeriod = await getCensusAverageByPeriod(startDateFilter, endDateFilter, facilityData);
		}
		const censusInfo = await getCensusAverageInfo(
			startDateFilter,
			endDateFilter,
			facilityData,
			false,
			censusByFacility,
			bedByFacility,
			censusAsOfNowByFacility
		);
		censusAverage = censusInfo.censusAverage;
		bedCapacity = censusInfo.bedCapacity;

	}
	return { diffDashboardPatients, customCombineTabData, data: listData, censusAverage, censusByPeriod, censusByFacility, bedByFacility, bedCapacity, bedCapacityByFacility, censusAsOfNowByFacility };
};

const getADTDetailsPatientChartData = async (req) => {
	const { body } = req;
	const {
		facilityId,
		facilityIds,
		cardFilter,
		filter: { startDate, endDate },
		transferType,
		selectedCardItem,
	} = body;
	let startDateFilter = await toStartFilterDate(startDate);
	let endDateFilter = await toEndFilterDate(endDate);

	let query = { type: { $in: [ADT_TYPES.ADMISSIONS, ADT_TYPES.READMISSIONS] } };
	if (transferType) {
		query.type = transferType;
	}

	let facilityFilter = null;
	const facilityData = [];

	if (facilityIds && facilityIds.length > 0) {
		query.facilityId = { $in: facilityIds };
		facilityIds.map((ele) => {
			facilityData.push(mongoose.Types.ObjectId(ele));
		});
		facilityFilter = { facilityId: { $in: facilityData } };
	} else {
		if (facilityId) {
			facilityFilter = { facilityId: mongoose.Types.ObjectId(facilityId) };
			query.facilityId = facilityId;
		}
	}

	if (startDate && endDate) {
		query.dateOfADT = {
			$gte: startDateFilter,
			$lt: endDateFilter,
		};
	}
	let relations = [
		RELATIONS.FACILITY,
		RELATIONS.DOCTOR,
		RELATIONS.DX,
		RELATIONS.HOSPITAL,
		RELATIONS.INSURANCE,
		RELATIONS.NURSE,
		RELATIONS.PAYER_SOURCE_INSURANCE,
		RELATIONS.SNF,
		RELATIONS.TRANSFER_TO_WHICH_ASSISTED_LIVING,
		RELATIONS.UNIT,
	];
	let transferPatientList = [];

	if (cardFilter && cardFilter.doctorData.length > 0) {
		const doctorIds = cardFilter.doctorData.map((e) => mongoose.Types.ObjectId(e));
		query.doctor = { $in: doctorIds };
		//relations.push("doctor");
	}
	if (cardFilter && cardFilter.floorsData.length > 0) {
		const floorIds = cardFilter.floorsData.map((e) => mongoose.Types.ObjectId(e));
		query.unit = { $in: floorIds };
		//relations.push("unit");
	}

	if (cardFilter && cardFilter.insuranceData.length > 0) {
		const insuranceIds = cardFilter.insuranceData.map((e) => mongoose.Types.ObjectId(e));
		query.payerSourceInsurance = { $in: insuranceIds };
		//relations.push("payerSourceInsurance");
	}
	if (cardFilter && cardFilter.dxData.length > 0) {
		const dxIdsFilter = cardFilter.dxData.map((e) => mongoose.Types.ObjectId(e));
		query.dx = { $elemMatch: { $in: dxIdsFilter } };
		//relations.push("dx");
	}

	if (cardFilter && cardFilter.hospitalData.length > 0) {
		const hospitalIds = cardFilter.hospitalData.map((e) => mongoose.Types.ObjectId(e));
		query.hospital = { $in: hospitalIds };
		//relations.push("hospital");
	}

	let admissionsPatients = await Patient.find({ ...query })
		.populate(relations)
		.select(["id", "dateOfADT"])
		.sort({ dateOfADT: 1 });
	let admissionsIds = [];

	if (admissionsPatients.length > 0) {
		let patientList = [];
		await filterData(admissionsPatients, async (item) => {
			if (cardFilter.daysData && cardFilter.daysData.length > 0) {
				const dayName = await getDayNameFromDate(item._doc.dateOfADT);
				if (!_.includes(cardFilter.daysData, dayName)) {
					return false;
				}
			}
			patientList.push(item._doc);
		});
		admissionsIds = _.map(patientList, "_id");
		transferPatientList = await getTransferChartData(
			admissionsIds,
			req.body,
			facilityFilter,
			cardFilter,
			admissionsPatients,
			endDateFilter
		);
	}
	let censusAverage = 0;
	let censusByFacility = [];

	if (startDate && endDate) {
		const facilityData = facilityIds.length > 0 ? facilityIds : [facilityId];
		const censusInfo = await getCensusAverageInfo(startDateFilter, endDateFilter, facilityData);
		censusAverage = censusInfo.censusAverage;
	}
	return {
		data: transferPatientList,
		censusAverage,
		admissionTotal: admissionsIds.length || 0,
		censusByFacility,
	};
};

const getTransferChartData = async (ids, body, facilityFilter, cardFilter, admissionsPatients, endDateFilter) => {
	admissionsPatients = admissionsPatients.map((ele) => {
		return { id: ele._id.toString(), ...ele._doc };
	});
	const { selectedCardItem } = body;
	const { childId, cardId, relation = null, type, parentId } = selectedCardItem;
	const { adtAdmitPatientIds = [] } = cardFilter;
	let relations = [
		RELATIONS.FACILITY,
		RELATIONS.DOCTOR,
		RELATIONS.DX,
		RELATIONS.HOSPITAL,
		RELATIONS.INSURANCE,
		RELATIONS.NURSE,
		RELATIONS.PAYER_SOURCE_INSURANCE,
		RELATIONS.SNF,
		RELATIONS.TRANSFER_TO_WHICH_ASSISTED_LIVING,
		RELATIONS.UNIT,
	];

	let query = { admissionId: { $in: ids } };

	if (cardId === PAGE_TYPE.HOSPITAL) {
		query.transferType = {
			$in: [ADT_SUB_TYPES.PLANNED_HOSPITAL_TRANSFER, ADT_SUB_TYPES.UNPLANNED_HOSPITAL_TRANSFER],
		};
	}
	if (cardId === PAGE_TYPE.COMMUNITY_TRANSFER) {
		query.transferType = { $in: [ADT_SUB_TYPES.AMA, ADT_SUB_TYPES.SNF, ADT_SUB_TYPES.SAFE_DISCHARGE] };
	}
	if (cardId === PAGE_TYPE.DECEASED) {
		query.transferType = { $in: [ADT_SUB_TYPES.DECEASED] };
	}

	if (type === ADT_TABLE_TYPE.TOTAL) {
		query.transferType = { $in: [childId] };
	}
	// if (childId === "dx" || childId === "shiftName" || childId === "nurseId") {
	// 	query.transferType = { $in: [ADT_SUB_TYPES.UNPLANNED_HOSPITAL_TRANSFER] };
	// }
	if (childId === "assistantLivId") {
		query.transferToWhichAssistedLiving = { $exists: true, $ne: null };
	}
	if (childId === "snfFacilityId") {
		query.snf = { $exists: true, $ne: null };
	}

	if (relation) {
		relations.push(relation);
	}
	if (adtAdmitPatientIds.length > 0) {
		await filterData(adtAdmitPatientIds, async (item) => {
			if (item.type === ADT_TABLE_TYPE.GROUP && item.relation && childId !== item.childId) {
				const groupFilterIds = item.selectedIds.map((e) => mongoose.Types.ObjectId(e));
				query[item.relation] = { $in: groupFilterIds };
				relations.push(item.relation);
			}

			if (item.type === ADT_TABLE_TYPE.GROUP_ARRAY && childId !== item.childId) {
				const dxIdsFilter = item.selectedIds.map((e) => mongoose.Types.ObjectId(e));
				query.dx = { $elemMatch: { $in: dxIdsFilter } };
				relations.push(item.relation);
			}
		});
	}

	let listData = await Patient.find({ ...query })
		.populate(relations)
		.sort({ dateOfADT: 1 });

	let list = [];
	let wasAdmittedSelected = null;
	let dayFilters = null;
	let shiftFilters = null;
	let reHospitalization = null;
	let ninetyDaysFilter = null;
	let sixtyDaysFilter = null;
	let ninetyDaysDeceasedFilter = null;
	let ninetyDaysOverallFilter = null;
	let totalFilter = null;
	let hospitalAllFilter = null;
	let communityAllFilter = null;
	let deceasedAllFilter = null;

	if (adtAdmitPatientIds.length > 0) {
		wasAdmittedSelected = _.find(adtAdmitPatientIds, {
			childId: "wasAdmitted",
		});
		dayFilters = _.find(adtAdmitPatientIds, { type: "dayChart" });
		shiftFilters = _.find(adtAdmitPatientIds, { childId: "shiftName" });
		reHospitalization = _.find(adtAdmitPatientIds, {
			childId: "reHospitalization",
		});
		ninetyDaysFilter = _.find(adtAdmitPatientIds, { childId: "90Days" });
		sixtyDaysFilter = _.find(adtAdmitPatientIds, { childId: "60Days" });
		ninetyDaysDeceasedFilter = _.find(adtAdmitPatientIds, {
			childId: "90DaysDeceased",
		});
		ninetyDaysOverallFilter = _.find(adtAdmitPatientIds, {
			childId: "90DaysOverall",
		});
		totalFilter = _.find(adtAdmitPatientIds, { type: ADT_TABLE_TYPE.TOTAL });

		hospitalAllFilter = _.find(adtAdmitPatientIds, {
			type: ADT_TABLE_TYPE.ALL,
			cardId: PAGE_TYPE.HOSPITAL,
		});
		communityAllFilter = _.find(adtAdmitPatientIds, {
			type: ADT_TABLE_TYPE.ALL,
			cardId: PAGE_TYPE.COMMUNITY_TRANSFER,
		});
		deceasedAllFilter = _.find(adtAdmitPatientIds, {
			type: ADT_TABLE_TYPE.ALL,
			cardId: PAGE_TYPE.DECEASED,
		});
	}

	if (listData && listData.length > 0) {
		await filterData(listData, async (item) => {
			let latestObj = new Object();
			const ID = item._doc._id.toString();
			latestObj = item._doc;
			latestObj.id = ID;
			if (relation) {
				latestObj.filterId = item[relation]?._id || null;
			}
			if (childId === "reHospitalization") {
				latestObj.reHospitalization = await getReHospitalization(item._doc, facilityFilter);
			}
			if (childId === "wasReturned") {
				latestObj.wasReturned = await getReturnAndDidNotReturn(item._doc, facilityFilter);
			}
			if (childId === "dx") {
				latestObj.dxIds = item.dx.length > 0 ? item.dx.map((ele) => ele._id.toString()) : [];
			}
			if (childId == "day") {
				latestObj.day = await getDayNameFromDate(item.dateOfADT);
			}
			if (childId == "shiftName") {
				latestObj.shiftName = await getShiftName(item.transferTime);
			}
			if (totalFilter && childId !== totalFilter.childId) {
				if (!_.includes(totalFilter.selectedIds, latestObj.transferType)) {
					return false;
				}
			}
			if (communityAllFilter && cardId !== communityAllFilter.cardId) {
				if (!_.includes([ADT_SUB_TYPES.AMA, ADT_SUB_TYPES.SNF, ADT_SUB_TYPES.SAFE_DISCHARGE], latestObj.transferType)) {
					return false;
				}
			}
			if (hospitalAllFilter && cardId !== hospitalAllFilter.cardId) {
				if (
					!_.includes(
						[ADT_TYPES.PLANNED_HOSPITAL_TRANSFER, ADT_TYPES.UNPLANNED_HOSPITAL_TRANSFER],
						latestObj.transferType
					)
				) {
					return false;
				}
			}
			if (deceasedAllFilter && cardId !== deceasedAllFilter.cardId) {
				if (!_.includes([ADT_TYPES.DECEASED], latestObj.transferType)) {
					return false;
				}
			}

			if (!_.isEmpty(dayFilters) && childId !== dayFilters.childId) {
				if (!latestObj.day) {
					latestObj.day = await getDayNameFromDate(item.dateOfADT);
				}
				if (!_.includes(dayFilters.selectedIds, latestObj.day)) {
					return false;
				}
			}
			if (!_.isEmpty(shiftFilters) && childId !== shiftFilters.childId) {
				if (!latestObj.shiftName) {
					latestObj.shiftName = await getShiftName(item.transferTime);
				}
				if (!_.includes(shiftFilters.selectedIds, latestObj.shiftName)) {
					return false;
				}
			}
			if (!_.isEmpty(wasAdmittedSelected) && childId !== wasAdmittedSelected.childId) {
				if (wasAdmittedSelected.selectedIds.length == 1) {
					let wasAdmittedSelectedIds = wasAdmittedSelected.selectedIds[0];
					if (wasAdmittedSelectedIds === "ER") {
						if (item.wasAdmitted) {
							return false;
						}
					}
					if (wasAdmittedSelectedIds === "DC") {
						if (!item.wasAdmitted) {
							return false;
						}
					}
				}
			}
			if (!_.isEmpty(reHospitalization) && childId !== reHospitalization.childId) {
				if (!latestObj.reHospitalization) {
					latestObj.reHospitalization = await getReHospitalization(item._doc, facilityFilter);
				}
				if (reHospitalization.selectedIds.length == 1) {
					let selectedHospitalization = reHospitalization.selectedIds[0];
					if (selectedHospitalization === "newHospitalizations") {
						if (latestObj.reHospitalization) {
							return false;
						}
					}
					if (selectedHospitalization === "reHospitalizations") {
						if (!latestObj.reHospitalization) {
							return false;
						}
					}
				}
			}
			latestObj.dateOfADTOriginal = item.dateOfADT;
			latestObj.DOBOriginal = item.DOB;
			latestObj.facility = item._doc.facilityId;
			latestObj.facilityId = item._doc.facilityId?._id.toString();
			latestObj.dateOfADT = await momentDateFormat(item.dateOfADT, "YYYY-MM-DD");

			const selectedAdmission = _.find(admissionsPatients, { id: item._doc.admissionId.toString() });
			latestObj.dateOfADT = await momentDateFormat(selectedAdmission.dateOfADT, "YYYY-MM-DD");

			latestObj.dateOfADT = await momentDateFormat(latestObj.dateOfADTOriginal, "YYYY-MM-DD");

			if (parentId == "hospital") {
				latestObj.hospitalDays = await getHospitalDays(item._doc, facilityFilter, endDateFilter);
			}
			latestObj.DOB = await momentDateFormat(item.DOB, "YYYY-MM-DD");
			list.push(latestObj);
		});
	}

	let sixtyDaysData = [];
	let ninetyDaysData = [];
	let deceasedNinetyDaysChartCountData = [];
	let overAllNinetyDaysChartCountData = [];

	if (childId == "60Days") {
		const analysisRes = await getSixtyDaysChartCount(list);
		sixtyDaysData = analysisRes?.sixtyDaysDataChart ?? [];
		list = analysisRes?.patientListRes ?? list;
		list = await ninetyDaysDataFilter(["a", "b", "c", "d"], list, sixtyDaysData);
	}
	if (childId === "90Days") {
		const analysisRes = await getNinetyDaysChartCount(list);
		ninetyDaysData = analysisRes?.ninetyDaysDataChart ?? [];
		list = analysisRes?.patientListRes ?? list;
		list = await ninetyDaysDataFilter(["a", "b", "c", "d", "e"], list, ninetyDaysData);
	}
	if (childId === "90DaysDeceased") {
		const analysisRes = await getNinetyDaysChartCount(list);
		deceasedNinetyDaysChartCountData = analysisRes?.ninetyDaysDataChart ?? [];
		list = analysisRes?.patientListRes ?? list;
		list = await ninetyDaysDataFilter(["a", "b", "c", "d", "e"], list, deceasedNinetyDaysChartCountData);
	}

	if (childId === "90DaysOverall") {
		const analysisRes = await getNinetyDaysChartCount(list);
		overAllNinetyDaysChartCountData = analysisRes?.ninetyDaysDataChart ?? [];
		list = analysisRes?.patientListRes ?? list;
		list = await ninetyDaysDataFilter(["a", "b", "c", "d", "e"], list, overAllNinetyDaysChartCountData);
	}

	if (ninetyDaysOverallFilter && childId != ninetyDaysOverallFilter.childId) {
		if (overAllNinetyDaysChartCountData.length === 0) {
			const analysisRes = await getNinetyDaysChartCount(list);
			overAllNinetyDaysChartCountData = analysisRes?.ninetyDaysDataChart ?? [];
			list = analysisRes?.patientListRes ?? list;
		}
		list = await ninetyDaysDataFilter(ninetyDaysOverallFilter.selectedIds, list, overAllNinetyDaysChartCountData);
	}

	if (ninetyDaysDeceasedFilter && childId != ninetyDaysDeceasedFilter.childId) {
		if (deceasedNinetyDaysChartCountData.length === 0) {
			const analysisRes = await getSixtyDaysChartCount(list);
			deceasedNinetyDaysChartCountData = analysisRes?.sixtyDaysDataChart ?? [];
			list = analysisRes?.patientListRes ?? list;
		}
		list = await ninetyDaysDataFilter(ninetyDaysDeceasedFilter.selectedIds, list, deceasedNinetyDaysChartCountData);
	}

	if (sixtyDaysFilter && childId != sixtyDaysFilter.childId) {
		if (sixtyDaysData.length === 0) {
			const analysisRes = await getSixtyDaysChartCount(list);
			sixtyDaysData = analysisRes?.sixtyDaysDataChart ?? [];
			list = analysisRes?.patientListRes ?? list;
		}
		list = await ninetyDaysDataFilter(sixtyDaysFilter.selectedIds, list, sixtyDaysData);
	}

	if (ninetyDaysFilter && childId != ninetyDaysFilter.childId) {
		if (ninetyDaysData.length === 0) {
			const analysisRes = await getNinetyDaysChartCount(list);
			ninetyDaysData = analysisRes?.ninetyDaysDataChart ?? [];
			list = analysisRes?.patientListRes ?? list;
		}
		list = await ninetyDaysDataFilter(ninetyDaysFilter.selectedIds, list, ninetyDaysData);
	}

	return { list, sixtyDaysData, ninetyDaysData, deceasedNinetyDaysChartCountData, overAllNinetyDaysChartCountData };
};

const getAllCount = async (req) => {
	// Use common initialization
	let {
		user,
		facilityid,
		facilityIds,
		startDateFilter,
		endDateFilter,
		customTabs,
		diffDashboardPatients,
		customCombineTabData,
		isCustomCombineTab
	} = await initializeGetAllCount(req, PAGE_TYPE.ADMISSION);

	// Use common facility filter setup
	let { facilityFilter, query, facilityData } = await setupFacilityFilter(facilityid, facilityIds);

	// Use common date filter setup
	await setupDateFilter(query, startDateFilter, endDateFilter);

	// Get census info using common utility
	const censusInfo = await getCensusInfo(startDateFilter, endDateFilter, facilityData);

	// Admission specific query setup
	query.type = { $in: ["admission", "readmission"] };

	let totalAdmissions = 0;
	let totalReAdmissions = 0;

	if (isCustomCombineTab) {
		customCombineTabData = await getCustomCombineTabData(query, PAGE_TYPE.ADMISSION, facilityFilter, customCombineTabData, customTabs);
	}
	let list = await Patient.find({ ...query })
		.populate(["payerSourceInsurance", "unit", "dx", "doctor", "hospital"])
		.sort({ dateOfADT: 1 });

	const total = list.length;

	// Calculate admission totals if we have data
	if (total > 0) {
		const transferTypeTotal = _.countBy(list, "type");
		totalAdmissions = transferTypeTotal?.admission || 0;
		totalReAdmissions = transferTypeTotal?.readmission || 0;
	}

	const seenPatientIds = new Set();
	const listData = [];

	// Process patient data only if we have records
	if (total > 0) {
		await filterData(list, async (item) => {
			// Handle custom tabs data processing if needed
			if (customTabs?.length > 0) {
				await getOtherDashboardData(item._doc, facilityFilter, diffDashboardPatients, seenPatientIds);
			}

			// Format patient data and add to results
			const formattedPatient = await formatPatientData(item._doc);
			listData.push(formattedPatient);
		});
	}

	// Use common finalization
	const { enrichedDiffDashboardPatients, isAutomaticReportSaved } = await finalizeGetAllCount(
		diffDashboardPatients,
		customTabs,
		PAGE_TYPE.ADMISSION,
		user?.id
	);

	return {
		data: {
			diffDashboardPatients: enrichedDiffDashboardPatients,
			list: listData,
			customTabs,
			customCombineTabData
		},
		totals: {
			...censusInfo,
			total,
			totalAdmissions,
			totalReAdmissions,
			isAutomaticReportSaved,
		},
	};
};

const getADTAllData = async (req) => {
	const { facilityid } = req.headers;
	const { startDate, endDate, facilityIds = [], relations = [] } = req.body;

	let startDateFilter = await toStartFilterDate(startDate);
	let endDateFilter = await toEndFilterDate(endDate);

	let facilityFilter = null;
	let query = new Object();

	const facilityData = [];
	if (facilityIds && facilityIds.length > 0) {
		query.facilityId = { $in: facilityIds };
		facilityIds.map((ele) => {
			facilityData.push(mongoose.Types.ObjectId(ele));
		});
		facilityFilter = { facilityId: { $in: facilityData } };
	} else {
		if (facilityid) {
			facilityFilter = { facilityId: mongoose.Types.ObjectId(facilityid) };
			query.facilityId = facilityid;
		}
	}
	if (startDate && endDate) {
		query.dateOfADT = {
			$gte: startDateFilter,
			$lt: endDateFilter,
		};
	}
	query.type = { $in: [ADT_TYPES.ADMISSIONS, ADT_TYPES.READMISSIONS] };

	let list = await Patient.find({ ...query })
		.select(["id"])
		.sort({ dateOfADT: 1 });

	if (list.length > 0) {
		const ids = _.map(list, "_id");
		return await getTransferData(
			{
				admissionId: { $in: ids },
			},
			facilityFilter,
			relations,
			req.body
		);
	} else {
		return {
			list: [],
			ninetyDaysData: [],
			sixtyDaysData: [],
			ninetyDaysDeceasedData: [],
			ninetyDaysOverallData: [],
		};
	}
};

const getTransferData = async (query, facilityFilter, relations, body) => {
	const { isReturnData, isReHospitalization, isNinetyDays, isSixtyDays, isNinetyDeceasedDays, isNinetyOverallDays } =
		body;
	let listData = await Patient.find({ ...query })
		.populate(relations)
		.sort({ dateOfADT: 1 });

	const list = [];
	let ninetyDaysData = [];
	let sixtyDaysData = [];
	let ninetyDaysDeceasedData = [];
	let ninetyDaysOverallData = [];

	if (listData && listData.length > 0) {
		await filterData(listData, async (item) => {
			let latestObj = new Object();
			const ID = item._doc._id.toString();
			latestObj = item._doc;
			latestObj.dateOfADTOriginal = item._doc.dateOfADT;
			latestObj.DOBOriginal = item._doc.DOB;
			latestObj.id = ID;
			latestObj.reHospitalization = isReHospitalization ? await getReHospitalization(item._doc, facilityFilter) : false;
			latestObj.wasReturned = isReturnData ? await getReturnAndDidNotReturn(item._doc, facilityFilter) : false;
			latestObj.dateOfADT = await momentDateFormat(item.dateOfADT, "YYYY-MM-DD");
			latestObj.DOB = await momentDateFormat(item.DOB, "YYYY-MM-DD");
			latestObj.day = await getDayNameFromDate(item.dateOfADT);
			latestObj.shiftName = item.transferTime ? await getShiftName(item.transferTime) : null;
			latestObj.insuranceId = item.insurance?._id.toString() || null;
			latestObj.floorId = item.unit?._id.toString() || null;
			latestObj.nurseId = item.nurse?._id.toString() || null;
			latestObj.doctorId = item.doctor?._id.toString() || null;
			latestObj.hospitalId = item.hospital?._id.toString() || null;
			latestObj.hospitalIdStr = item.hospital?._id.toString() || null;
			latestObj.hospitalName = item.hospital?.label || null;
			latestObj.dxIds = item.dx.length > 0 ? item.dx.map((user) => user._id.toString()) : [];
			latestObj.snfFacilityId = item.snf?._id.toString() || null;
			latestObj.assistantLivId = item.transferToWhichAssistedLiving?._id.toString() || null;
			latestObj._id = ID;
			latestObj.admissionId = item?.admissionId?.toString() || null;
			list.push(latestObj);
		});
		if (isNinetyDays) {
			let listNinetyData = [];
			await filterData(list, async (item) => {
				if (
					_.includes(
						[ADT_SUB_TYPES.PLANNED_HOSPITAL_TRANSFER, ADT_SUB_TYPES.UNPLANNED_HOSPITAL_TRANSFER],
						item.transferType
					)
				) {
					listNinetyData.push(item);
				}
			});
			const analysisRes = await getNinetyDaysChartCount(listNinetyData);
			ninetyDaysData = analysisRes?.ninetyDaysDataChart ?? [];
			listNinetyData = analysisRes?.patientListRes ?? listNinetyData;
		}
		if (isSixtyDays) {
			let listSixtyDaysData = [];
			await filterData(list, async (item) => {
				if (_.includes([ADT_SUB_TYPES.AMA, ADT_SUB_TYPES.SNF, ADT_SUB_TYPES.SAFE_DISCHARGE], item.transferType)) {
					listSixtyDaysData.push(item);
				}
			});
			const analysisRes = await getSixtyDaysChartCount(listSixtyDaysData);
			sixtyDaysData = analysisRes?.sixtyDaysDataChart ?? [];
			listSixtyDaysData = analysisRes?.patientList;
		}
		if (isNinetyDeceasedDays) {
			let listNinetyDaysDeceasedData = [];
			await filterData(list, async (item) => {
				if (_.includes([ADT_SUB_TYPES.DECEASED], item.transferType)) {
					listNinetyDaysDeceasedData.push(item);
				}
			});
			const analysisRes = await getNinetyDaysChartCount(listNinetyDaysDeceasedData);
			ninetyDaysDeceasedData = analysisRes?.ninetyDaysDataChart ?? [];
			listNinetyDaysDeceasedData = analysisRes?.patientListRes ?? listNinetyDaysDeceasedData;

		}
		if (isNinetyOverallDays) {
			const analysisRes = await getNinetyDaysChartCount(list);
			ninetyDaysOverallData = analysisRes?.ninetyDaysDataChart ?? [];
			list = analysisRes?.patientListRes ?? listData;
		}
	}
	return {
		list,
		ninetyDaysData,
		sixtyDaysData,
		ninetyDaysDeceasedData,
		ninetyDaysOverallData,
	};
};

module.exports = {
	getCardPatientChartData,
	getAllCount,
	getADTAllData,
	getADTDetailsPatientChartData,
};
