$layout-breakpoint-small: 960px;
$layout-breakpoint-medium: 960px;
$layout-breakpoint-large: 1920px;

.dayCardList {
	position: relative;
	overflow: hidden;

	&.custom {
		overflow: auto;
	}

	.footerWrapper {
		height: 100px;

		.itemsWrpr {
			// width: 100%;
			display: flex;
			justify-content: space-evenly;

			&.customItemsWrpr {
				// margin-left: 12px;
			}

			.sec {				
				display: flex;
				width: 9%;
				&.customSec {
					padding: 15px;
					margin-right: 5px;
					margin-left:5px;					
					width: 8.8%;
				}
				&.customSecSmall {
					padding: 15px;
					margin-left:0px;
					width: 18%;
				}
				&.customSecMidSmall {
					margin-left:-5px;
					width: 15%;
				}
				color: #444652;
				height: 45px;
				background: #f5f5f5;
				border-radius: 8px;
				justify-content: center;
				flex-direction: column;
				align-items: center;
				
				&:hover {
					border: 1px solid #4879f5;
				}
				&.selected {
					border: 1px solid #4879f5;

					&:before {
						content: "";
						display: block;
						height: 16px;
						width: 16px;
						background-color: #4879f5;
						border-radius: 50%;
						position: absolute;
						bottom: -130%;
						left: 50%;
						transform: translate(-50%, 50%);
					}
				}
			}

			.dayListCheckbox {				
				margin-top: -8px;
			}

			.dayListLabel {
				padding: 4px 0px 0px 14px;
			}
		}
	}
}
