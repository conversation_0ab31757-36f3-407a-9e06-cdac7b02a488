const mongoose = require("mongoose");
const { PAGE_TYPE } = require("../../types/common.type");
const Setting = mongoose.model("settings");

// create Shortcut Setting
const createShortcutSetting = async (req) => {
    const user = req?.user;

    const isExistsFilter = await Setting.findOne({
        type: "shortcut",
        userId: user._id,
    });

    if (isExistsFilter) {
        isExistsFilter.data = req.body.data
        isExistsFilter.save()
    } else {
        const filterSave = new Setting({
            userId: user._id,
            type: "shortcut",
            data: req.body.data
        });
        const saved = await filterSave.save()
        return { status: 200, data: saved }
    }
};

const getShortcutSettings = async (req) => {
    const user = req?.user;

    const filters = await Setting.findOne({
        type: "shortcut",
        userId: mongoose.Types.ObjectId(user._id),
    });
    
    return filters;
};

// Create new account
const createSetting = async (req) => {
    const user = req?.user;
    const { facilityid, accountid } = req.headers;

    const isExistsFilter = await Setting.findOne({
        type: req.body.type,
        accountId: mongoose.Types.ObjectId(accountid),
        pageType: req.body.pageType,
        userId: user._id,
        selectedTab: req.body.selectedTab,
        ...(req.body.subPageType && req.body.pageType === PAGE_TYPE.ADT) && { subPageType: req.body.subPageType },
    });

    if (isExistsFilter) {
        isExistsFilter.selectedColumns = req.body.selectedColumns
        isExistsFilter.save()
    } else {
        const filterSave = new Setting({
            facilityId: mongoose.Types.ObjectId(facilityid),
            accountId: mongoose.Types.ObjectId(accountid),
            userId: user._id,
            ...req.body
        });
        const saved = await filterSave.save()
        return { status: 200, data: saved }
    }
};

const getSettings = async (req) => {
    const { accountid } = req.headers;
    const { type, selectedTab, subPageType } = req.query;
    const user = req?.user;

    const filters = await Setting.find({
        type: type,
        accountId: accountid,
        userId: user._id,
        selectedTab: selectedTab,
        pageType: req.query.pageType,
        ...subPageType && { subPageType: req.query.subPageType },
    });

    return filters;
};

const getSetting = async (req) => {
    const { id } = req.query;
    const filters = await Filter.findOne({ _id: mongoose.Types.ObjectId(id) });
    return filters;
};

const deleteSetting = async id => {
    let deleted = await Filter.findByIdAndDelete(id);
    return deleted;
};

const createEndDateOfADT = async (req) => {
    const user = req?.user;
    const { accountid } = req.headers;

    const isEndDateOfADTExists = await Setting.findOne({
        type: "endDateOfADT",
        accountId: mongoose.Types.ObjectId(accountid),
        userId: user._id,
    });

    if (isEndDateOfADTExists) {
        isEndDateOfADTExists.endDateOfADT = req.body.endDateOfADT
        isEndDateOfADTExists.save()
    } else {
        const endDateOfADTSettings = new Setting({
            accountId: mongoose.Types.ObjectId(accountid),
            userId: user._id,
            type: "endDateOfADT",
            endDateOfADT: req.body.endDateOfADT,
        });
        const saved = await endDateOfADTSettings.save()
        return { status: 200, data: saved }
    }
};

const getEndDateOfADT = async (req) => {
    const { user, headers: { accountid } } = req;
    const query = {
        type: "endDateOfADT",
        accountId: mongoose.Types.ObjectId(accountid),
        userId: user?._id,
    };
    return await Setting.findOne(query);
};


module.exports = {
    createSetting,
    getSetting,
    getSettings,
    deleteSetting,
    createEndDateOfADT,
    getEndDateOfADT,
    createShortcutSetting,
    getShortcutSettings
};
