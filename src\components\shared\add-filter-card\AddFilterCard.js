import {
    <PERSON>,
    Card,
    CardContent,
    IconButton,
    List,
    ListItem,
    ListItemText,
    Slide,
    Stack,
    Typography,
} from "@mui/material";
import AddOutlinedIcon from "@mui/icons-material/AddOutlined";
import { useRef, useState } from "react";
import AddBoxOutlinedIcon from "@mui/icons-material/AddBoxOutlined";
import RemoveOutlinedIcon from '@mui/icons-material/RemoveOutlined';
import _ from "lodash";
import { useSelector } from "react-redux";


const AddFilterCard = ({ listData, handleAddCustomCard }) => {
    const { deceasedCards } = useSelector((state) => state.customCard);

    const [display, setDisplay] = useState(false);

    const showButton = (e) => {
        e.preventDefault();
        setDisplay(true);
    };

    const hideButton = (e) => {
        e.preventDefault();
        setDisplay(false);
    };

    const containerRef = useRef(null);

    return (
        <Box sx={{ width: "500x", height: "500x" }}>
            <Card sx={{ minWidth: 500, minHeight: 398 }}>
                <CardContent
                    onMouseEnter={(e) => showButton(e)}
                    onMouseLeave={(e) => hideButton(e)}
                    ref={containerRef}
                >
                    {/* <Typography sx={{ color: "#000" }}>Custom filter</Typography> */}
                    <Box width={"100%"}>
                        {/* <Collapse in={!display}> */}
                        <Slide
                            direction="down"
                            in={!display}
                            container={containerRef.current}
                            timeout={700}
                        >
                            <Box
                                display={"flex"}
                                justifyContent={"center"}
                                alignItems={"center"}
                                sx={{
                                    cursor: "pointer",
                                    width: "100%",
                                    mt: !display ? "45px" : "0px"
                                }}
                            >
                                <Stack direction={"column"}>
                                    <Typography>Add new data box</Typography>
                                    <IconButton disableRipple>
                                        <AddBoxOutlinedIcon sx={{ fontSize: 42 }} />
                                    </IconButton>
                                </Stack>
                            </Box>
                        </Slide>
                        {/* </Collapse> */}

                        {display && (
                            <>
                                <Slide
                                    direction="up"
                                    in={display}
                                    container={containerRef.current}
                                    timeout={700}
                                >
                                    <List
                                        sx={{
                                            width: "100%",
                                            maxWidth: 500,
                                            bgcolor: "background.paper",
                                            height: "100%"
                                        }}
                                        dense

                                    >
                                        {listData.map((ele) => (
                                            <ListItem
                                                onClick={() => handleAddCustomCard(ele.value)}
                                                key={ele.value}
                                                dense
                                                sx={{
                                                    "&.MuiListItem-root": {
                                                        borderRadius: "4px",
                                                        background: "#e1e1f3",
                                                        marginBottom: "10px",
                                                    },
                                                }}
                                                secondaryAction={
                                                    <IconButton aria-label="comment">
                                                        {_.includes(deceasedCards, ele.value) ? <RemoveOutlinedIcon /> : <AddOutlinedIcon />}
                                                    </IconButton>
                                                }
                                            >
                                                <ListItemText primary={_.capitalize(ele.label)} />
                                            </ListItem>
                                        ))}
                                    </List>
                                </Slide>
                            </>
                        )}
                    </Box>
                </CardContent>
            </Card>
        </Box>
    );
};

export default AddFilterCard;
