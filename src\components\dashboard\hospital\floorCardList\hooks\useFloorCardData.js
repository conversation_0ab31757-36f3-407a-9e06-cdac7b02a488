import { useMemo } from 'react';

/**
 * Custom hook for processing and filtering floor card data
 * @param {Array} data - The floor card data array
 * @param {string} searchValue - The search filter value
 * @returns {Array} - Filtered data array
 */
export const useFloorCardData = (data, searchValue) => {
	return useMemo(() => {
		if (!Array.isArray(data)) {
			console.warn('useFloorCardData: data is not an array');
			return [];
		}

		if (!searchValue || typeof searchValue !== 'string') {
			return data;
		}

		const searchLower = searchValue.toLowerCase().trim();
		if (searchLower === '') {
			return data;
		}

		return data.filter((item) => {
			if (!item || typeof item.label !== 'string') {
				return false;
			}
			return item.label.toLowerCase().includes(searchLower);
		});
	}, [data, searchValue]);
};

export default useFloorCardData; 