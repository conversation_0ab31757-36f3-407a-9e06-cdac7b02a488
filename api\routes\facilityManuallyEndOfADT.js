const { createFacilityManuallyEndOfADT, deleteFacilityManuallyEndOfADT, getFacilityManuallyEndOfADTs } = require("../helpers/facilityManuallyEndOfADT");
const authWithRole = require("../middleware/auth-with-role");

const route = require("express").Router();

// Create new account
route.post("/", authWithRole("manageDashboard"), async (req, res) => {
  try {
    let account = await createFacilityManuallyEndOfADT(req);
    res.send(account);
  } catch (error) {
    res.status(500).send({ message: error.message });
  }
});

// Get accounts/account by ID
route.get("/:id?", authWithRole("manageDashboard"), async (req, res) => {
  try {
    let account = await getFacilityManuallyEndOfADTs(req);
    res.send(account);
  } catch (error) {
    console.log(error, 'error');
    res.status(500).send(error);
  }
});

route.delete("/:id", authWithRole("manageDashboard"), async (req, res) => {
  try {
    const filter = await deleteFacilityManuallyEndOfADT(req.params.id);
    res.send(filter);
  } catch (error) {
    res.status(500).send(error);
  }
});


module.exports = route;
