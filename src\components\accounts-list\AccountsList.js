import { useEffect, useState } from "react";
import { useSelector } from "react-redux";
import axios from "../../axios";
import update from "immutability-helper";
import styles from "./AccountsList.module.scss";
import AddAccount from "./add-account/AddAccount";

const AccountsList = props => {
    const [accounts, setAccounts] = useState([]);
    const [showAdd, setShowAdd] = useState(false);

    const { auth } = useSelector(({ auth }) => ({ auth }));

    const toggleShowAdd = () => setShowAdd(!showAdd);

    const getAccounts = async () => {
        let re = await axios.get("/api/account");

        setAccounts(re.data);
    };

    useEffect(() => {
        getAccounts();
    }, []);

    const accountAdded = account => {
        const newData = update(accounts, {
            $push: [account],
        });

        setAccounts(newData);
    };
    return (
        <div className={styles.accounts}>
            <div className={`df aic ${styles.pageHdr}`}>
                <div className={`mla`}>
                    <button className={`ttuc fs12 ffmm ${styles.addBtn}`} onClick={toggleShowAdd}>
                        + Add account
                    </button>
                </div>
            </div>

            <div className={styles.list}>
                {auth?.accountId &&
                    accounts
                        .filter(acc => acc._id !== auth.accountId)
                        .map(account => <div className={styles.line}>{account.name}</div>)}
            </div>

            {showAdd && <AddAccount close={toggleShowAdd} accountAdded={accountAdded} />}
        </div>
    );
};

export default AccountsList;
