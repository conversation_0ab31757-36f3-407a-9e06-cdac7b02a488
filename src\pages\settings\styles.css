/* styles.css */
.lds-ellipsis {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 60px; /* Reduced width */
    height: 15px; /* Reduced height */
}

.lds-ellipsis div {
    position: absolute;
    top: 0px;
    width: 6px; /* Smaller dots */
    height: 6px; /* Smaller dots */
    border-radius: 50%;
    background: #3498db; /* Blue */
    animation: lds-ellipsis 1.2s cubic-bezier(0.37, 0.45, 0.57, 0.8) infinite;
}

.lds-ellipsis div:nth-child(1) {
    left: 4px; /* Adjusted spacing */
    animation-delay: 0s;
}

.lds-ellipsis div:nth-child(2) {
    left: 14px; /* Adjusted spacing */
    animation-delay: 0.1s;
}

.lds-ellipsis div:nth-child(3) {
    left: 24px; /* Adjusted spacing */
    animation-delay: 0.2s;
}

@keyframes lds-ellipsis {
    0% {
        transform: translate(0, 0);
    }

    100% {
        transform: translate(12px, 0); /* Adjusted translation */
        opacity: 0;
    }
}