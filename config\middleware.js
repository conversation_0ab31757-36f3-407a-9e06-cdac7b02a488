const express = require("express");
const cors = require("cors");
const cookieParser = require("cookie-parser");
const bodyParser = require("body-parser");
const mongoSanitize = require("express-mongo-sanitize");
const helmet = require("helmet");
const { getCorsOptions } = require("./cors");

/**
 * Apply all middleware to Express app
 */
const applyMiddleware = (app) => {
    // Security middleware
    app.use(helmet());
    
    // Helmet CSP configuration
    helmet.contentSecurityPolicy({
        useDefaults: true,
        directives: {
            "script-src": [
                "'self'",
                "https://o4505432342265856.ingest.sentry.io/api/4505433086885888/security/?sentry_key=24eeb772527e416496ab99479f3bc1bc",
            ],
        },
    });

    // CORS
    const corsOptions = getCorsOptions();
    app.use(cors(corsOptions));

    // Body parsing middleware
    app.use(express.json({ limit: "200mb" }));
    app.use(express.urlencoded({ 
        extended: true, 
        limit: "200mb", 
        parameterLimit: 100000 
    }));
    app.use(bodyParser.json());

    // Cookie parser
    app.use(cookieParser());

    // MongoDB sanitization
    app.use(mongoSanitize());
    
    // Additional mongo sanitize with logging
    app.use(
        mongoSanitize({
            onSanitize: ({ req, key }) => {
                console.warn(`This request[${key}] is sanitized`, req);
            },
        })
    );

    console.log("All middleware applied successfully");
};

module.exports = { applyMiddleware };
