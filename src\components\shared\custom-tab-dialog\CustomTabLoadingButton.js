import React from 'react';
import { Button, CircularProgress } from '@mui/material';

const CustomTabLoadingButton = ({ loading, children, ...props }) => (
    <Button
        {...props}
        disabled={loading || props.disabled}
        startIcon={loading ? <CircularProgress size={20} color="inherit" /> : props.startIcon}
    >
        {loading ? (props.loadingText || children) : children}
    </Button>
);

export default CustomTabLoadingButton; 