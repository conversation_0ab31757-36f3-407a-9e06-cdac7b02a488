const mongoose = require("mongoose");
const { QUESTION_INPUT_TYPE, QUESTIONS_TEMPLATE_TYPE } = require("../types/question.type");
const { Schema, Types } = mongoose;

const QuestionSchema = new Schema(
  {
    facilityId: { type: Types.ObjectId, ref: "facility" },
    accountId: { type: Types.ObjectId, ref: "account", required: false },
    label: String,
    tableLabel: { type: String, required: false, default: null },
    accessor: String,
    type: String,
    validationType: String,
    multiple: Boolean,
    forType: String,
    forTransferType: String,
    allowAfter: Boolean,
    active: Boolean,
    isEditable: Boolean,
    isRequired: { type: Boolean, default: false },
    defaultOrder: Number,
    validationBase: String, //account / facility
    isCustom: { type: Boolean, required: false, default: null },
    timeRangeType: { type: String, enum: ["hourly", "custom", null], default: null },
    // numberRangeType: { type: String, enum: ["range", "average", "total"], default: null },
    customQuestionInputType: {
      type: String,
      enum: Object.values(QUESTION_INPUT_TYPE),
      default: null,
      required: false,
    },
    customQuestionOptions: {
      type: Array, // Allows any array
      default: []
    },
    isArchived: {
      type: Boolean,
      default: false
    },
    archivedAt: {
      type: Date,
      default: null,
    },
    questionRefId: { type: Types.ObjectId, ref: "question", required: false },
    templateType: {
      type: String,
      enum: Object.values(QUESTIONS_TEMPLATE_TYPE),
      default: QUESTIONS_TEMPLATE_TYPE.DX_CARD,
      required: false,
    },
  },
  {
    timestamps: true,
  }
);

mongoose.model("question", QuestionSchema);
