const DataInputNavSVG = props => (
  <svg width="21" height="21" viewBox="0 0 21 21" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      d="M10.2512 21C10.246 21 10.2409 21 10.2357 21C9.00031 20.9919 7.9566 19.9223 7.9566 18.6644V9.32174C7.9566 8.43487 8.45963 7.64976 9.26935 7.27276C10.0772 6.89672 10.9993 7.01237 11.6827 7.57376L19.0831 13.0629C19.1021 13.077 19.1206 13.0919 19.1383 13.1076C19.8004 13.694 20.0219 14.5883 19.7164 15.4413C19.3911 16.3498 18.5701 16.9596 17.6248 16.9947C17.6149 16.9951 17.605 16.9953 17.595 16.9953L14.1479 16.9982L12.0378 20.0375C12.0354 20.0409 12.0331 20.0443 12.0306 20.0477C11.5836 20.671 10.9686 21 10.2512 21ZM10.2476 8.69682C10.1148 8.69682 10.0079 8.73857 9.96177 8.76002C9.85215 8.81104 9.59713 8.96796 9.59713 9.32174V18.6644C9.59713 19.0598 9.93996 19.3575 10.2465 19.3594C10.3754 19.3612 10.5173 19.3406 10.6941 19.0962L13.0447 15.7105C13.1979 15.4899 13.4493 15.3582 13.7178 15.358L17.5758 15.3547C17.8986 15.337 18.0984 15.0935 18.1719 14.8883C18.2252 14.7396 18.2492 14.5286 18.071 14.3547L10.6873 8.87801C10.6739 8.86805 10.6608 8.85771 10.648 8.84693C10.5106 8.73135 10.3683 8.69682 10.2476 8.69682ZM6.45717 15.0457C6.67893 14.6507 6.5385 14.1507 6.14354 13.9289C4.07435 12.767 2.78896 10.5728 2.78896 8.20261C2.78896 4.58423 5.73267 1.64052 9.35105 1.64052C12.9694 1.64052 15.9131 4.58423 15.9131 8.20261C15.9131 8.65564 16.2804 9.02287 16.7334 9.02287C17.1864 9.02287 17.5537 8.65564 17.5537 8.20261C17.5537 3.67969 13.874 0 9.35105 0C4.82813 0 1.14844 3.67969 1.14844 8.20261C1.14844 11.1651 2.75467 13.9075 5.3403 15.3593C5.46732 15.4306 5.60516 15.4645 5.74112 15.4645C6.02809 15.4645 6.30665 15.3137 6.45717 15.0457ZM6.45676 11.1759C6.8285 10.9169 6.92 10.4057 6.66108 10.034C6.27437 9.47873 6.07 8.8313 6.07 8.1616C6.07 6.35243 7.54188 4.88055 9.35105 4.88055C10.5555 4.88055 11.6213 5.50477 12.2022 6.55028C12.4221 6.94626 12.9214 7.08907 13.3175 6.86899C13.7135 6.64904 13.8563 6.14966 13.6362 5.75364C13.2173 4.99945 12.6073 4.36867 11.8722 3.92946C11.1173 3.47844 10.2455 3.24003 9.35105 3.24003C6.6373 3.24003 4.42948 5.44785 4.42948 8.1616C4.42948 9.16818 4.73564 10.1398 5.31487 10.9715C5.47433 11.2005 5.72939 11.3231 5.98872 11.3231C6.15051 11.3231 6.31391 11.2753 6.45676 11.1759Z"
      fill="currentColor"
    />
  </svg>
);

export default DataInputNavSVG;
