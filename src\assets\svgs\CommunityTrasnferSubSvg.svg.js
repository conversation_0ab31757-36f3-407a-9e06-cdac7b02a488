const CommunityTransferSubSvg = () => {
  return (
    <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M6.0127 2.22166C5.81856 2.22166 5.66113 2.06427 5.66113 1.8701V1.41113C5.66113 1.21696 5.81856 1.05957 6.0127 1.05957C6.20683 1.05957 6.36426 1.21696 6.36426 1.41113V1.87013C6.36426 2.06427 6.20686 2.22166 6.0127 2.22166Z"
        fill="currentColor"
      />
      <path
        d="M4.33233 2.9183C4.24233 2.9183 4.1524 2.88399 4.08374 2.81533L3.75921 2.4908C3.62193 2.35352 3.62193 2.13091 3.75921 1.99359C3.89653 1.8563 4.11907 1.8563 4.25643 1.99359L4.58096 2.31812C4.71824 2.4554 4.71824 2.67801 4.58096 2.81533C4.51226 2.88399 4.4223 2.9183 4.33233 2.9183Z"
        fill="currentColor"
      />
      <path
        d="M7.69335 2.9183C7.60335 2.9183 7.51342 2.88399 7.44476 2.81533C7.30748 2.67805 7.30748 2.45544 7.44476 2.31812L7.76929 1.99359C7.90661 1.8563 8.12915 1.8563 8.2665 1.99359C8.40379 2.13087 8.40379 2.35348 8.2665 2.4908L7.94198 2.81533C7.87328 2.88399 7.78335 2.9183 7.69335 2.9183Z"
        fill="currentColor"
      />
      <path
        d="M6.79941 11.5037H5.22585C5.03172 11.5037 4.87429 11.3463 4.87429 11.1521V10.2916H4.01465C3.82052 10.2916 3.66309 10.1342 3.66309 9.94005V8.3654C3.66309 8.17123 3.82052 8.01384 4.01465 8.01384H4.87429V7.15332C4.87429 6.95915 5.03172 6.80176 5.22585 6.80176H6.79941C6.99354 6.80176 7.15097 6.95915 7.15097 7.15332V8.01384H8.01058C8.20471 8.01384 8.36214 8.17123 8.36214 8.3654V9.94005C8.36214 10.1342 8.20471 10.2916 8.01058 10.2916H7.15097V11.1521C7.15097 11.3463 6.99358 11.5037 6.79941 11.5037ZM5.57741 10.8005H6.44785V9.94005C6.44785 9.74588 6.60528 9.58849 6.79941 9.58849H7.65902V8.71696H6.79941C6.60528 8.71696 6.44785 8.55957 6.44785 8.3654V7.50488H5.57741V8.3654C5.57741 8.55957 5.41998 8.71696 5.22585 8.71696H4.36621V9.58849H5.22585C5.41998 9.58849 5.57741 9.74588 5.57741 9.94005V10.8005Z"
        fill="currentColor"
      />
      <path
        d="M17.6484 13.0577H17.4662V11.7168C17.4662 11.1559 17.2237 10.623 16.801 10.2547L15.263 8.91476C15.2626 8.91441 15.1553 8.83657 15.1553 8.83657L14.2672 5.95274C14.0079 5.11068 13.2417 4.54491 12.3606 4.54491H7.434V4.39496C7.434 3.61126 6.79637 2.97363 6.01263 2.97363C5.22893 2.97363 4.5913 3.61126 4.5913 4.39496V4.54491H1.40977C0.926789 4.54491 0.533813 4.93802 0.533813 5.42125V13.0577H0.351562C0.15743 13.0577 0 13.2151 0 13.4093V15.1709C0 15.365 0.15743 15.5224 0.351562 15.5224H1.92586C2.15448 16.3404 2.90612 16.9421 3.79561 16.9421C4.68506 16.9421 5.4367 16.3404 5.66532 15.5224H7.43154C7.62567 15.5224 7.7831 15.365 7.7831 15.1709C7.7831 14.9767 7.62567 14.8193 7.43154 14.8193H5.72836C5.6913 14.4191 5.53243 14.0538 5.28926 13.7608H12.7107C12.4676 14.0538 12.3087 14.4192 12.2716 14.8193H10.6101C10.416 14.8193 10.2585 14.9767 10.2585 15.1709C10.2585 15.365 10.416 15.5224 10.6101 15.5224H12.3347C12.5633 16.3404 13.3149 16.9421 14.2044 16.9421C15.0939 16.9421 15.8455 16.3404 16.0741 15.5224H17.6484C17.8426 15.5224 18 15.365 18 15.1709V13.4093C18 13.2151 17.8426 13.0577 17.6484 13.0577ZM13.5953 6.15967L14.4164 8.82627H11.2913V5.24803H12.3606C12.9311 5.24803 13.4273 5.61436 13.5953 6.15967ZM5.29446 4.39496C5.29446 3.99893 5.61663 3.67676 6.01267 3.67676C6.4087 3.67676 6.73091 3.99896 6.73091 4.39496V4.54491H5.2945V4.39496H5.29446ZM1.23694 5.42121C1.23694 5.32569 1.31446 5.248 1.40977 5.248H10.5882V13.0577H1.23694V5.42121ZM0.703125 13.7608H2.30193C2.05875 14.0538 1.89988 14.4192 1.86282 14.8193H0.703125V13.7608ZM5.03357 14.9999C5.03357 15.6831 4.4782 16.239 3.79561 16.239C3.11298 16.239 2.55762 15.6831 2.55762 14.9999C2.55762 14.3167 3.11298 13.7608 3.79561 13.7608C4.4782 13.7608 5.03357 14.3167 5.03357 14.9999ZM11.2913 9.52939H14.8981L16.3391 10.7848C16.6086 11.0196 16.7631 11.3592 16.7631 11.7168V13.0577H11.2913V9.52939ZM14.2044 16.239C13.5218 16.239 12.9664 15.6831 12.9664 14.9999C12.9664 14.3167 13.5218 13.7608 14.2044 13.7608C14.887 13.7608 15.4424 14.3167 15.4424 14.9999C15.4424 15.6831 14.887 16.239 14.2044 16.239ZM17.2969 14.8193H16.1372C16.1001 14.4191 15.9413 14.0538 15.6981 13.7608H17.2969V14.8193Z"
        fill="currentColor"
      />
      <path
        d="M9.02715 15.5225C8.88006 15.5225 8.74611 15.4273 8.69651 15.2892C8.64725 15.152 8.68993 14.9936 8.80254 14.9006C8.91145 14.8107 9.06825 14.7945 9.19281 14.8611C9.32243 14.9305 9.39643 15.0769 9.37495 15.2225C9.34981 15.3925 9.19903 15.5225 9.02715 15.5225Z"
        fill="currentColor"
      />
    </svg>
  );
};

export default CommunityTransferSubSvg;
