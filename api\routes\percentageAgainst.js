const { createPercentageAgainst, getPercentageAgainst, deletePercentageAgainst, getPercentageAgists } = require("../helpers/percentageAgainst");
const authWithRole = require("../middleware/auth-with-role");

const route = require("express").Router();

// Create new percentage against
route.post("/", authWithRole("manageDashboard"), async (req, res) => {
  try {
    let account = await createPercentageAgainst(req);
    res.send(account);
  } catch (error) {
    res.status(500).send({ message: error.message });
  }
});

route.get("/all", authWithRole("manageDashboard"), async (req, res) => {
  try {
    let account = await getPercentageAgists(req);
    res.send(account);
  } catch (error) {    
    res.status(500).send(error);
  }
});

// Get percentage against by ID
route.get("/:id?", authWithRole("manageDashboard"), async (req, res) => {
  try {
    let account = await getPercentageAgainst(req);
    res.send(account);
  } catch (error) {
    res.status(500).send(error);
  }
});



route.delete("/:id", authWithRole("manageDashboard"), async (req, res) => {
  try {
    const filter = await deletePercentageAgainst(req.params.id);
    res.send(filter);
  } catch (error) {
    res.status(500).send(error);
  }
});

module.exports = route;
