const {
    PAGE_TYPE,
    ADMISSION_TYPES,
    TYPES,
    OVERALL_PAGE_SUB_TYPE,
    TRANSFER_TYPE,
    ADMISSION_FILTER_TYPE,
    HOSPITAL_CARDS_TYPE,
    CO_TRANSFER_CARDS_TYPE,
    DASHBOARD_FILTER_TYPE,
    REPORT_FILE_TYPE,
    ACCOUNT_PERCENTAGE_BY,
    CUSTOM_TAB_TYPE,
} = require("../../../types/common.type");
const _ = require("lodash");
const ExcelJS = require('exceljs');
const { itemPercentage, filterAsyncData, getQuestionAnswer, getDynamicPercentageBy, patientDataOrderBy } = require("../common");
const { toDisplayTime, dateFormat } = require("../date-format");
const { getFacilityNames } = require("../../helpers/facility");
const { ReportTransferTypeTitleEnum, CHART_TAB_BUTTON } = require("../../../types/report.type");
const moment = require("moment");
const { getCommunicationFieldNames, getDeceasedFieldNames, getOverallFieldNames, getHospitalFieldNames, getAdmissionFieldNames, getAdmissionADTFieldNames } = require("./report-common");
const { getQuestionAsColumns } = require("../../helpers/question");

const filterWithChangeObj = async (
    data,
    percentageAgainst,
    filterItem = null,
    filterItemGraph
) => {
    if (data.length === 0) return [];

    const allZero = data
        .filter(ele => !ele.isSpacialItem)
        .every(ele => Number(ele?.total || ele?.value || 0) === 0);
    if (allZero) return [];

    const result = await Promise.all(data.map(async (ele) => ({
        ...ele,
        total: ele?.total || ele?.value || 0,
        percentage: ele.percentage ? `${ele.percentage}%` : "",
        percentageAgainst,
        ...(filterItem && { filterItem }),
        ...(filterItemGraph && { filterItemGraph }),
    })));

    return result;
};

const filterByItems = async (cardFilter, ele, dataType, isSpecialFilter = false) => {
    const specialFilter = isSpecialFilter ? await _.get(cardFilter, `${ele.type}_spacial`, []) : [];
    const baseFilter = await _.get(cardFilter, ele.type, []);

    const responseArr = await _.filter(dataType, ({ _id }) =>
        _.includes([...baseFilter, ...specialFilter], _id)  // Combine both filters
    );
    return responseArr;
};

const renderContent = async (props) => {
    const facilityNames = await getFacilityNames(
        props?.reportsSubscription?.facilityIds
    );
    const {
        cardFilter,
        filterListData,
        type,
        page = null,
        dbData,
        isTotalCard = false,
        isAdt = false,
        selectedADTTableData = [],
        admissionReportType = null,
        graphData,
        isGraphReport = false,
        lockedTotalBy,
        reportFileType = null,
        filter,
        accountId,
        userId,
        tableFilterType,
        isOnlyHospitalTabAccess,
        percentageBy,
        dynamicPercentageLabel,
        activeFacilitiesCount,
        questions
    } = props;
    const questionType = questions?.customTab?.type ?? null;
    const cardTitleCustom = questions?.cardTitle ?? null;
    const censusAverageDynamic = await getDynamicPercentageBy(dbData, dynamicPercentageLabel, { accountId, userId });
    let censusAverage = percentageBy && percentageBy === ACCOUNT_PERCENTAGE_BY.BED ? dbData?.bedCapacity : censusAverageDynamic;
    transferType = props?.transferType;

    let cardData = [];
    let cardFilterData = [];

    if (page !== PAGE_TYPE.ADMISSION) {
        cardFilterData = cardFilter.priorityData;
    } else {
        cardFilterData = cardFilter.mainPriorityData;
    }

    let averageByText = isOnlyHospitalTabAccess ? "Against Bed Capacity" : `Against ${dynamicPercentageLabel}`
    let percentageAgainst = lockedTotalBy ? lockedTotalBy : averageByText;
    let isPercentage =
        page === PAGE_TYPE.OVERALL && transferType === "total" ? false : true;
    let lockedTitleSet = true;
    let alreadyChecked = false;
    let isFilterData = true;

    if (isTotalCard) {
        const totalCardNumber = dbData[type];
        if (totalCardNumber && totalCardNumber > 0) {
            cardData.push({
                title: await _.capitalize(props?.title),
                type: "total",
                data: [
                    {
                        label: await _.capitalize(type),
                        total: totalCardNumber,
                        percentage: `${await itemPercentage(
                            totalCardNumber,
                            censusAverage,
                            "number"
                        )}%`,
                        percentageAgainst: isOnlyHospitalTabAccess ? "Against Bed Capacity" : `Against ${dynamicPercentageLabel}`,
                    },
                ],
            });
        } else {
            return null;
        }
    } else {
        if (transferType) {
            let total;
            if (page === PAGE_TYPE.ADMISSION) {
                total =
                    transferType === "all"
                        ? dbData?.total
                        : transferType === ADMISSION_TYPES.ADMISSION
                            ? dbData?.totalAdmissions
                            : dbData?.totalReAdmissions;
            } else {
                if (!Array.isArray(transferType)) {
                    total =
                        transferType === "all" ? dbData?.total : dbData[transferType] || 0;
                }
            }
            if (Array.isArray(transferType)) {
                if (transferType.length > 0) {
                    let title;
                    if (transferType.length > 0 && _.includes(transferType, "all")) {
                        percentageAgainst = `Against Total Community Transfers`;
                        total = dbData?.total;
                        title = "Total Community Transfers";
                        if (lockedTotalBy && lockedTotalBy === "transferType") {
                            percentageAgainst = `Against ${title}`;
                            title += " (Locked)";
                            lockedTitleSet = false;
                        }
                    } else {
                        percentageAgainst = `Against ${transferType.toString()}`;
                        let totalSub = 0;
                        title = transferType.toString();
                        if (lockedTotalBy && lockedTotalBy === "transferType") {
                            percentageAgainst = `Against ${title}`;
                            title += " (Locked)";
                            lockedTitleSet = false;
                        }
                        // eslint-disable-next-line array-callback-return
                        transferType.map((ele) => {
                            if (ele === TRANSFER_TYPE.SAFE_DISCHARGE) {
                                totalSub += dbData?.totalSafeDischarge || 0;
                            }
                            if (ele === TRANSFER_TYPE.SNF) {
                                totalSub += dbData?.totalSNF || 0;
                            }
                            if (ele === TRANSFER_TYPE.AMA) {
                                totalSub += dbData?.totalAMA || 0;
                            }
                        });
                        total = totalSub;
                    }
                    cardData.push({
                        title: title,
                        type: "total",
                        data: [
                            {
                                total: total,
                                percentage: `${await itemPercentage(
                                    total,
                                    dbData?.censusAverage,
                                    "number"
                                )}%`,
                                percentageAgainst: "Against Avg Census",
                            },
                        ],
                    });
                }
            } else {
                let transferTypeTitle = ReportTransferTypeTitleEnum[transferType]
                    ? ReportTransferTypeTitleEnum[transferType]
                    : transferType;
                percentageAgainst = `Against ${transferTypeTitle}`;
                let mainTitle =
                    transferType === "all"
                        ? transferTypeTitle + " " + page
                        : transferTypeTitle;
                if (transferType === "all" && page === PAGE_TYPE.HOSPITAL) {
                    mainTitle = "Total Hospital Transfers";
                    percentageAgainst = `Against ${transferTypeTitle}`;
                } else if (transferType === "all" && page === PAGE_TYPE.ADMISSION) {
                    mainTitle = "Total Admissions";
                    percentageAgainst =
                        transferType === "all"
                            ? `Against ${mainTitle}`
                            : `Against ${transferTypeTitle}`;
                }
                if (
                    lockedTotalBy &&
                    (lockedTotalBy === "all" ||
                        lockedTotalBy === ADMISSION_TYPES.ADMISSION ||
                        lockedTotalBy === ADMISSION_TYPES.READMISSION ||
                        lockedTotalBy === TYPES.PLANNED ||
                        lockedTotalBy === TYPES.UNPLANNED ||
                        lockedTotalBy === OVERALL_PAGE_SUB_TYPE.TOTAL_INCOMING ||
                        lockedTotalBy === OVERALL_PAGE_SUB_TYPE.TOTAL_OUTGOING)
                ) {
                    let transferTypeLockedTitle = ReportTransferTypeTitleEnum[
                        lockedTotalBy
                    ]
                        ? ReportTransferTypeTitleEnum[lockedTotalBy]
                        : transferType;
                    let mainTitleLocked =
                        lockedTotalBy === "all"
                            ? transferTypeLockedTitle + " " + page
                            : transferTypeLockedTitle;
                    if (lockedTotalBy === "all" && page === PAGE_TYPE.HOSPITAL) {
                        mainTitleLocked = "Total Hospital Transfers";
                    } else if (lockedTotalBy === "all" && page === PAGE_TYPE.ADMISSION) {
                        mainTitleLocked = "Total Admissions";
                    }
                    percentageAgainst = `Against ${mainTitleLocked}`;
                    mainTitleLocked += " (Locked)";
                    lockedTitleSet = false;
                    mainTitle = mainTitleLocked;
                    if (page === PAGE_TYPE.ADMISSION) {
                        total =
                            lockedTotalBy === "all"
                                ? dbData?.total
                                : lockedTotalBy === ADMISSION_TYPES.ADMISSION
                                    ? dbData?.totalAdmissions
                                    : dbData?.totalReAdmissions;
                    } else {
                        total =
                            lockedTotalBy === "all"
                                ? dbData?.total
                                : dbData[lockedTotalBy] || 0;
                    }
                }
                cardData.push({
                    title: mainTitle,
                    type: "total",
                    data: [
                        {
                            total: total,
                            percentage: `${await itemPercentage(
                                total,
                                censusAverage,
                                "number"
                            )}%`,
                            percentageAgainst: isOnlyHospitalTabAccess ? "Against Bed Capacity" : `Against ${dynamicPercentageLabel}`,
                        },
                    ],
                });

                if (lockedTotalBy === "all" && transferType !== "all") {
                    let transferTotal =
                        transferType === "all" ? dbData?.total : dbData[transferType] || 0;
                    let transferTypeTitle2 = (await ReportTransferTypeTitleEnum[
                        transferType
                    ])
                        ? await ReportTransferTypeTitleEnum[transferType]
                        : transferType;
                    if (page === PAGE_TYPE.ADMISSION) {
                        transferTotal =
                            transferType === "all"
                                ? dbData?.total
                                : transferType === ADMISSION_TYPES.ADMISSION
                                    ? dbData?.totalAdmissions
                                    : dbData?.totalReAdmissions;
                    }
                    cardData.push({
                        title: transferTypeTitle2,
                        type: "total",
                        data: [
                            {
                                total: transferTotal,
                                percentage: `${await itemPercentage(
                                    transferTotal,
                                    censusAverage,
                                    "number"
                                )}%`,
                                percentageAgainst: isOnlyHospitalTabAccess ? "Against Bed Capacity" : `Against ${dynamicPercentageLabel}`,
                            },
                        ],
                    });
                }
            }
        }
        if (cardFilterData.length > 0 && !isTotalCard) {
            let i = 0;
            let filterItems = [];
            let filterItemGraph = [];

            if (isAdt) {
                await filterAsyncData(cardFilterData, async (ele) => {

                    if (ele.filterType === ADMISSION_FILTER_TYPE.ADMISSION) {
                        let dataType = filterListData[ele.type] || [];

                        if (dataType.length > 0 && !alreadyChecked) {
                            const onlyFilterData = await filterByItems(cardFilter, ele, dataType, ele.type === type);
                            let title = ele.cardTitle;
                            if (lockedTotalBy && lockedTotalBy === ele.type) {
                                title = `${ele.cardTitle} (Locked)`;
                                percentageAgainst = ele.cardTitle;
                                lockedTitleSet = false;
                            }
                            if (onlyFilterData.length > 0) {
                                let filtersNames = _.map(onlyFilterData, "label");
                                filterItemGraph.push(filtersNames);
                                const resFilterData = await filterWithChangeObj(
                                    onlyFilterData,
                                    percentageAgainst,
                                    filterItems[i - 1] || null,
                                    filterItemGraph[i]
                                )
                                if (resFilterData.length > 0) {
                                    cardData.push({
                                        title: title,
                                        data: resFilterData,
                                    });
                                    filterItems.push(filtersNames);
                                } else {
                                    isFilterData = false;
                                }
                            }
                        }

                        if (lockedTitleSet) {
                            let againstTitle = ReportTransferTypeTitleEnum[ele.type]
                                ? ReportTransferTypeTitleEnum[ele.type]
                                : ele.type;
                            percentageAgainst = `Against ${againstTitle}`;
                        }
                        if (ele.type === type) {
                            alreadyChecked = true;
                        }
                    } else {
                        const { parentId, childId } = ele;
                        const selectedADTTable = _.find(selectedADTTableData, {
                            parentId,
                            childId,
                        });
                        let selectedADTTableDataArr = [];
                        if (cardFilter?.adtAdmitPatientIds?.length > 0) {
                            selectedADTTableDataArr = _.find(cardFilter?.adtAdmitPatientIds, ({ parentId, childId }));
                        }
                        let onlyFilterData = selectedADTTable?.tableData;
                        if (
                            (selectedADTTableDataArr?.selectedIds?.length || 0) > 0 ||
                            (selectedADTTableDataArr?.selectedSpecialIds?.length || 0) > 0
                        ) {
                            const selectedIds = selectedADTTableDataArr?.selectedIds || [];
                            const selectedSpecialIds = selectedADTTableDataArr?.selectedSpecialIds || [];

                            onlyFilterData = selectedADTTable?.tableData?.filter((eleItem) => {
                                const applicableSpecialIds =
                                    ele.parentId === type.parentId && ele.childId === type.childId
                                        ? selectedSpecialIds
                                        : [];

                                return _.includes([...selectedIds, ...applicableSpecialIds], eleItem?.id);
                            });
                        }
                        let title = `${ele?.subTitle} (${_.capitalize(ele.title)})`;
                        if (
                            lockedTotalBy &&
                            lockedTotalBy === ele.type + "_" + ADMISSION_FILTER_TYPE.ADT
                        ) {
                            title = `${selectedADTTable?.subTitle} (${_.capitalize(
                                selectedADTTable.title
                            )}) (Locked)`;
                            percentageAgainst = `${selectedADTTable?.subTitle
                                } (${_.capitalize(selectedADTTable.title)})`;
                            lockedTitleSet = false;
                        }
                        if (onlyFilterData.length > 0) {
                            let filtersNames = _.map(onlyFilterData, "label");
                            const resFilterData = await filterWithChangeObj(
                                onlyFilterData,
                                percentageAgainst,
                                filterItems[i - 1] || null,
                                filterItemGraph[i]
                            )
                            if (resFilterData.length > 0) {
                                filterItemGraph.push(filtersNames);
                                cardData.push({
                                    title: title,
                                    data: resFilterData,
                                });
                                filterItems.push(filtersNames);
                            } else {
                                isFilterData = false;
                            }
                        }
                        if (lockedTitleSet) {
                            percentageAgainst = `Against ${selectedADTTable?.subTitle
                                } (${await _.capitalize(selectedADTTable.title)})`;
                        }
                        if (
                            ele.parentId === type.parentId &&
                            ele.childId === type.childId
                        ) {
                            alreadyChecked = true;
                        }
                    }
                    i++;
                });

                if (!alreadyChecked) {
                    if (admissionReportType === ADMISSION_FILTER_TYPE.ADT) {
                        let title2 = await _.capitalize(props?.title);
                        const { parentId, childId } = type;
                        const selectedADT = await _.find(selectedADTTableData, {
                            parentId,
                            childId,
                        });
                        if (selectedADT) {
                            title2 = `${selectedADT?.subTitle} (${await _.capitalize(
                                selectedADT.title
                            )})`;
                        }
                        const resFilterData = await filterWithChangeObj(
                            selectedADT?.tableData || [],
                            percentageAgainst
                        )
                        if (resFilterData.length > 0) {
                            cardData.push({
                                title: await _.capitalize(title2),
                                data: resFilterData,
                            });
                        } else {
                            isFilterData = false;
                        }
                    } else {
                        let cardItemsArr = filterListData[type] || [];

                        if (_.get(cardFilter, `${type}_spacial`, []).length > 0) {
                            cardItemsArr = cardItemsArr.filter(({ _id }) =>
                                _.includes(cardFilter[`${type}_spacial`], _id)
                            );
                        }
                        const resFilterData = await filterWithChangeObj(
                            cardItemsArr || [],
                            percentageAgainst,
                            filterItems[cardFilterData?.length - 1],
                            filterItemGraph[cardFilterData?.length]
                        );

                        if (resFilterData.length > 0) {
                            cardData.push({
                                title: props?.title,
                                data: resFilterData,
                            });
                        } else {
                            isFilterData = false;
                            return null;
                        }
                    }
                }
                //END OF ADT report DATA
            } else {
                await filterAsyncData(cardFilterData, async (ele) => {
                    let dataType = filterListData[ele.type] || [];

                    if (dataType.length > 0 && !alreadyChecked) {
                        const onlyFilterData = await filterByItems(cardFilter, ele, dataType, ele.type === type);
                        let title = ele.cardTitle;
                        if (lockedTotalBy && lockedTotalBy === ele.type) {
                            title = `${ele.cardTitle} (Locked)`;
                            percentageAgainst = ele.cardTitle;
                            lockedTitleSet = false;
                        }

                        if (onlyFilterData.length > 0) {
                            const resFilterData = await filterWithChangeObj(
                                onlyFilterData,
                                percentageAgainst,
                                filterItems[i - 1] || null,
                                filterItemGraph[i] || null
                            );

                            if (resFilterData.length > 0) {
                                let filtersNames = await _.map(onlyFilterData, "label");
                                filterItemGraph.push(filtersNames);
                                filterItems.push(filtersNames);
                                cardData.push({
                                    title: title,
                                    data: resFilterData,
                                });
                            } else {
                                isFilterData = false;
                            }
                        }
                    }
                    if (lockedTitleSet) {
                        percentageAgainst = `Against ${ele.cardTitle}`;
                    }
                    if (ele.type === type) {
                        alreadyChecked = true;
                    }
                    i++;
                });
                if (!alreadyChecked) {

                    let cardItemsArr = filterListData[type] || [];

                    if (_.get(cardFilter, `${type}_spacial`, []).length > 0) {
                        cardItemsArr = cardItemsArr.filter(({ _id }) =>
                            _.includes(cardFilter[`${type}_spacial`], _id)
                        );
                    }

                    const resFilterData = await filterWithChangeObj(
                        cardItemsArr || [],
                        percentageAgainst,
                        filterItems[cardFilterData?.length - 1],
                        filterItemGraph[cardFilterData?.length]
                    )
                    if (resFilterData.length > 0) {
                        cardData.push({
                            title: props?.title,
                            data: resFilterData,
                        });
                    } else {
                        isFilterData = false;
                        return null
                    }
                }
            }
        } else {
            if (admissionReportType === ADMISSION_FILTER_TYPE.ADT) {
                const { parentId, childId } = type;
                const selectedADT = _.find(selectedADTTableData, { parentId, childId });
                const resFilterData = await filterWithChangeObj(
                    selectedADT?.tableData || [],
                    percentageAgainst
                )
                if (resFilterData.length > 0) {
                    cardData.push({
                        title: selectedADT?.title,
                        data: resFilterData,
                    });
                } else {
                    isFilterData = false;
                }
            } else {

                let cardItemsArr = filterListData[type] || [];

                if (_.get(cardFilter, `${type}_spacial`, []).length > 0) {
                    cardItemsArr = cardItemsArr.filter(({ _id }) =>
                        _.includes(cardFilter[`${type}_spacial`], _id)
                    );
                }
                const resFilterData = await filterWithChangeObj(
                    cardItemsArr || [],
                    percentageAgainst
                )

                if (resFilterData.length > 0) {
                    cardData.push({
                        title: props?.title,
                        data: resFilterData,
                    });
                } else {
                    isFilterData = false;
                }
            }
        }
    }
    let graphCardData = [];
    if (!isFilterData) {
        return null;
    }

    if (isGraphReport) {
        let findTotalFilter = _.find(cardData, { type: "total" });
        if (
            !findTotalFilter &&
            graphData &&
            graphData.selectedTab === CHART_TAB_BUTTON.BUILDING
        ) {

            if (lockedTotalBy === "census") {
                graphCardData.push({ title: isOnlyHospitalTabAccess ? "Bed Capacity (Locked)" : `Against ${dynamicPercentageLabel} (Locked)` });
            } else {
                graphCardData.push({ title: isOnlyHospitalTabAccess ? "Bed Capacity" : `Avg ${dynamicPercentageLabel}` });
            }
        }
        if (cardData.length > 0) {
            for (let i = 0; i < cardData.length; i++) {
                const ele = cardData[i];
                if (ele && ele.data && ele.data.length > 0) {
                    if (ele.type && ele.type === "total") {
                        let isAllTransfer = false;
                        if (
                            Array.isArray(transferType) &&
                            transferType.length > 0 &&
                            _.includes(transferType, "all")
                        ) {
                            isAllTransfer = true;
                        } else {
                            isAllTransfer =
                                transferType === "all" || transferType === "total";
                        }
                        if (graphData.selectedTab === CHART_TAB_BUTTON.BUILDING) {
                            graphCardData.push({ title: ele?.title, filterItemGraph: [] });
                        } else if (!isAllTransfer) {
                            graphCardData.push({ title: ele?.title, filterItemGraph: [] });
                        }
                    } else {
                        let filterItemGraph = ele.data[0]?.filterItemGraph;
                        if (filterItemGraph && filterItemGraph.length > 0) {
                            graphCardData.push({ title: ele?.title, filterItemGraph });
                        }
                    }
                }
            }
        }

        if (
            type === HOSPITAL_CARDS_TYPE.TOTAL ||
            type === HOSPITAL_CARDS_TYPE.PLANNED ||
            type === HOSPITAL_CARDS_TYPE.UNPLANNED ||
            type === CO_TRANSFER_CARDS_TYPE.AMA ||
            type === CO_TRANSFER_CARDS_TYPE.SAFE_DISCHARGE ||
            type === CO_TRANSFER_CARDS_TYPE.SNF ||
            type === OVERALL_PAGE_SUB_TYPE.TOTAL_INCOMING ||
            type === OVERALL_PAGE_SUB_TYPE.TOTAL_OUTGOING
        ) {
            graphCardData = [];
        }
    }

    if (graphData && graphData.selectedTab === CHART_TAB_BUTTON.TABLE && reportFileType === REPORT_FILE_TYPE.EXCEL) {

        let selectedColumns = graphData?.automaticallyReport?.selectedColumns || [];
        let patientList = graphData.chartData?.filterPatients || [];
        let admissionReportType = graphData?.filtersData?.admissionReportType || null;
        let selectedCardItem = admissionReportType == ADMISSION_FILTER_TYPE.ADT ? graphData.chartData?.selectedCardItem : null
        let pageType = selectedCardItem?.parentId == "hospital" ? DASHBOARD_FILTER_TYPE.HOSPITAL : null
        if (patientList.length === 0) {
            return null;
        }
        const workbook = new ExcelJS.Workbook();
        const title = graphData?.title ? graphData?.title.replace(/[^a-zA-Z ]/g, "") : null
        const sheet = workbook.addWorksheet(title || 'Report Sheet');
        // Add header data to the sheet
        sheet.addRow();

        sheet.addRow(["Facility", facilityNames]);
        sheet.addRow(["Date", `${dateFormat(filter?.startDate, "MM/DD/YYYY")} - ${dateFormat(filter?.endDate, "MM/DD/YYYY")}`]);
        sheet.addRow(["Bed cap", props?.dbData?.bedCapacity]);

        if (!isOnlyHospitalTabAccess) {
            sheet.addRow(["Census as of", props?.dbData?.censusAverage]);
        }

        sheet.addRow();

        let questions = await getQuestionAsColumns({ pageType: props?.reportsSubscription?.page }, { accountid: accountId });
        
        if ((tableFilterType && tableFilterType == "all") || questionType == CUSTOM_TAB_TYPE.combineTab) {
            questions = []
        } else if (tableFilterType) {
            questions = _.filter(questions, { forTransferType: tableFilterType })
        }
        

        const headerLabels = [
            ...(activeFacilitiesCount > 1 ? ["Facility Name"] : []),
            "Last Name",
            "First Name",
            "Date of birth",
            questionType == CUSTOM_TAB_TYPE.combineTab ? "Type" : [],
            ...questions?.length === 0 ? ["Date of transfer"] : [],
            ...(pageType === DASHBOARD_FILTER_TYPE.HOSPITAL && questionType !== CUSTOM_TAB_TYPE.combineTab ? "# of days in hospital" : [])
        ]

        if (questions && questions.length > 0) {
            for await (const ele of questions) {
                let isSelected = true;
                if (selectedColumns && selectedColumns.length > 0) {
                    isSelected = await selectedColumns.includes(ele?.question?.accessor);
                }
                if (isSelected) {
                    await headerLabels.push(ele?.order?.tableLabel || ele?.order?.label || ele?.question?.tableLabel || ele?.question?.label)
                }
            }
        }
        // Fill Header label
        const headerRow = sheet.addRow(headerLabels);


        headerRow.font = { bold: true };

        headerRow.state = 'frozen';

        patientList = patientList?.map((ele) => { return { ...ele, facilityName: ele?.facility?.name } })

        // Fill data to excel report
        if (graphData.orderBy && graphData.order) {
            patientList = await patientDataOrderBy(patientList, graphData.order, graphData.orderBy);
        }

        for await (const row of patientList) {
            try {
                const rowData = [
                    ...(activeFacilitiesCount > 1 ? [row?.facilityName] : []),
                    row?.lastName,
                    row?.firstName,
                    await toDisplayTime(row.DOB, "MM/DD/YYYY"),
                    // await getFieldNames(row),
                    questionType == CUSTOM_TAB_TYPE.combineTab ? row?.type === "transfer" ? row?.transferType : row?.type : [],
                    ...questions?.length === 0 ? [await toDisplayTime(row.dateOfADT, "MM/DD/YYYY")] : [],
                    ...(pageType === DASHBOARD_FILTER_TYPE.HOSPITAL && questionType !== CUSTOM_TAB_TYPE.combineTab ? [row.hospitalDays] : [])
                ];
                if (questions && questions.length > 0) {
                    for await (const ele of questions) {
                        let isSelectedVal = true;
                        if (selectedColumns && selectedColumns.length > 0) {
                            isSelectedVal = await selectedColumns.includes(ele?.question?.accessor);
                        }
                        if (isSelectedVal) {
                            const questionValue = await getQuestionAnswer(ele?.question, row)
                            rowData.push(questionValue);
                        }
                    }
                }
                await sheet.addRow(rowData);
            } catch (error) {
                console.log(error, 'error');
            }
        }

        // Generate the Excel file
        const buffer = await workbook.xlsx.writeBuffer();
        const base64String = await buffer.toString("base64");
        return base64String
        // const base64 = buffer.toString('base64');
        // // Create a Blob from the buffer
        // const blob = new Blob([buffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
        // const reader = new FileReader();
        // reader.readAsDataURL(blob);
        // reader.onloadend = function () {
        //     const base64data = reader.result;
        //     return base64datal            
        // }
        // //return blob;

    } else {
        return await generateReportHtml(
            {
                ...props,
                facilityNames,
                isPercentage,
                isLockedTotalByCensus: lockedTotalBy === "census",
                graphCardData,
                graphData,
                alreadyChecked,
                censusAverage,
                isOnlyHospitalTabAccess,
                dynamicPercentageLabel,
                censusAverageDynamic,
                cardTitleCustom,
                questionType
            },
            cardData,
        );
    }
};

const tableHead = (title, colSpan) => {
    return `
        <tr>
        <th align="center" textAlign="center" colSpan="${colSpan}"><b>${title}</b></th>
      </tr>
        `;
};

const getFilterNames = async (graphData) => {
    if (graphData && graphData.selectedItem.length > 0 && graphData.chartData.mainFilterData.length > 0) {
        let filterOptions = _.filter(graphData.chartData.mainFilterData, ({ _id }) => {
            return _.every([_.includes(graphData.selectedItem, _id)]);
        });
        return _.map(filterOptions, "label").join(" | ");
    } else {
        return null;
    }
}
const generateReportHtml = async (props, cardContentData) => {
    const {
        facilityNames = null,
        filter,
        dbData,
        isLockedTotalByCensus = false,
        isGraphReport = false,
        isPercentage = false,
        graphCardData,
        graphData,
        alreadyChecked,
        page,
        adtType,
        reportsSubscription,
        censusAverage,
        isOnlyHospitalTabAccess,
        dynamicPercentageLabel,
        censusAverageDynamic,
        activeFacilitiesCount,
        cardTitleCustom,
        questionType
    } = props;
    let censusOrBedTitle = isOnlyHospitalTabAccess ? "Bed Capacity" : `AVG ${dynamicPercentageLabel}`;

    let averageTable = `
    <table style="width:100%">
        <tr textAlign="center">
          <th align="center" textAlign="center"><b>${censusOrBedTitle} </b></th>
        </tr>
        <tr>
          <th>${censusAverageDynamic}</th>
        </tr>
      </table><br /><br />
    `;

    let headerTable = `
            <table style="width:70%">
                <tr>
                <th align="center" textAlign="center"><b>Facility</b></th>
                <td colSpan="2">${facilityNames}</td>
                </tr>
                <tr>
                <th align="center" textAlign="center"><b>Date</b></th>
                <td>${await moment(filter?.startDate).format("MM/DD/YYYY")}</td>
                <td>${await moment(filter?.endDate).format("MM/DD/YYYY")}</td>
                </tr>
                <tr>
                <th align="center" textAlign="center"><b>Bed cap</b></th>
                <td align="center" colSpan="2">${dbData?.bedCapacity}</td>
                </tr>
                `;

    if (!isOnlyHospitalTabAccess) {
        headerTable += `<tr>
                            <th align="center" textAlign="center"><b>Census as of</b></th>
                            <td align="center" colSpan="2">${dbData?.censusAsOfNow}</td>
                            </tr>
                        </table>
                        <br />
                        <br />`;
    } else {
        headerTable += `</table> <br /> <br />`;
    }

    if (isGraphReport) {
        let dynamicTable = ``;
        if (graphCardData && graphCardData.length > 0) {
            dynamicTable += `<table style="width:100%">`;
            dynamicTable += `<tr>
            <th>Filters</th>
            </tr>`;
            await filterAsyncData(graphCardData, async (ele) => {
                let graphFilter = ele?.filterItemGraph && ele?.filterItemGraph.length > 0 ? ele?.filterItemGraph.join(" | ") : null;
                dynamicTable += `
                        <tr><td>${ele?.title}</td></tr>
                        <tr><td>${graphFilter}</td></tr>
                `;
            })
            dynamicTable += `</table>`;
        }

        dynamicTable += `<h5>Graph report list:</h5>`;
        if (graphData && graphData.selectedTab === CHART_TAB_BUTTON.CHART) {
            dynamicTable += `<h5>FilterBy: ${_.capitalize(graphData?.selectedFilter)}</h5>`;
            dynamicTable += `<table style="width:100%">`;
            dynamicTable += `<tr><th colSpan="2">${graphData?.title}</th></tr>`;
            if (!alreadyChecked) {
                dynamicTable += `<tr>
                                    <td colSpan="2">
                                    Filters : ${await getFilterNames(graphData)}
                                    </td>
                                <tr>`
            }
            dynamicTable += `<tr>
                                <td>Date</td>
                                <td>Value</td>
                            </tr>`;

            if (graphData?.chartData?.filterData.length > 0) {
                graphData?.chartData?.filterData.forEach(ele => {
                    dynamicTable += `<tr>
                        <th align="center" textAlign="center"><b>${ele?.x}</b></th>
                        <td>${ele?.y}</td>
                    </tr>`
                });
            }
            dynamicTable += `</table>`;
        }

        if (graphData && graphData.selectedTab === CHART_TAB_BUTTON.TABLE) {
            let relation = graphData?.filters?.relation ? _.capitalize(graphData?.filters?.relation) : null;
            let patientList = graphData.chartData?.filterPatients || [];
            let admissionReportType = reportsSubscription?.filtersData?.admissionReportType || null;
            let selectedCardItem = admissionReportType == ADMISSION_FILTER_TYPE.ADT ? graphData.chartData?.selectedCardItem : null
            let pageType = selectedCardItem?.parentId == "hospital" ? DASHBOARD_FILTER_TYPE.HOSPITAL : null
            dynamicTable += `<table style="width:100%">`;
            if (activeFacilitiesCount > 1) {
                dynamicTable += `<tr>
                <th>Facility</th>
                <th>Name</th>
                <th>DATE OF BIRTH</th>
                <th>DATE OF TRANSFER</th>
                <th>${cardTitleCustom || relation || "Type"}</th>
                ${pageType === DASHBOARD_FILTER_TYPE.HOSPITAL || page === "hospital" ? '<th># of days in hospital</th>' : ''}
            </tr>`;
            } else {
                dynamicTable += `<tr>
                <th>Name</th>
                <th>DATE OF BIRTH</th>
                <th>DATE OF TRANSFER</th>
                <th>${cardTitleCustom || relation || "Type"}</th>
                ${pageType === DASHBOARD_FILTER_TYPE.HOSPITAL || page === "hospital" ? '<th># of days in hospital</th>' : ''}
            </tr>`;
            }

            if (patientList.length > 0) {
                patientList = patientList?.map((ele) => { return { ...ele, facilityName: ele?.facility?.name } })
                if (graphData.orderBy && graphData.order) {
                    patientList = await patientDataOrderBy(patientList, graphData.order, graphData.orderBy);
                }
                for await (let ele of patientList) {
                    if (page === PAGE_TYPE.COMMUNITY_TRANSFER) {
                        relation = await getCommunicationFieldNames(graphData.chartData?.mainFilterData, ele, graphData?.filters?.relation, graphData?.filters?.type, graphData?.filters);
                    } else if (page === PAGE_TYPE.OVERALL) {
                        relation = await getOverallFieldNames(graphData.chartData?.mainFilterData, ele, graphData?.filters?.relation, graphData?.filters?.type);
                    } else if (page === PAGE_TYPE.DECEASED) {
                        relation = await getDeceasedFieldNames(graphData.chartData?.mainFilterData, ele, graphData?.filters?.relation, graphData?.filters?.type, graphData?.filters);
                    } else if (page == PAGE_TYPE.HOSPITAL) {
                        relation = await getHospitalFieldNames(graphData.chartData?.mainFilterData, ele, graphData?.filters?.relation, graphData?.filters?.type, graphData?.filters);
                    } else if (page === PAGE_TYPE.ADMISSION) {
                        if (admissionReportType == ADMISSION_FILTER_TYPE.ADT) {
                            relation = await getAdmissionADTFieldNames(graphData.chartData?.defaultData, ele, graphData?.filters?.relation, graphData?.filters?.type);
                        } else {
                            relation = await getAdmissionFieldNames(ele, graphData?.filters?.relation, graphData?.filters?.type, graphData.chartData?.mainFilterData, graphData?.filters);
                        }
                    }

                    if (activeFacilitiesCount > 1) {
                        dynamicTable += `<tr>
                        <td align="center">${ele?.facilityName}</td>
                        <td align="center">${ele?.firstName} ${ele?.lastName}</td>
                        <td>${moment.utc(ele.DOB).format("MM/DD/YYYY")}</td>
                        <td>${moment.utc(ele.dateOfADT).format("MM/DD/YYYY")}</td>
                        <td>${questionType === CUSTOM_TAB_TYPE.combineTab ? ele?.type === "transfer" ? ele?.transferType : ele?.type : relation}</td>
                        ${pageType === DASHBOARD_FILTER_TYPE.HOSPITAL || page === "hospital" ? `<td>${ele.hospitalDays}</td>` : ''}                        
                    </tr>`
                    } else {
                        dynamicTable += `<tr>
                        <td align="center">${ele?.firstName} ${ele?.lastName}</td>
                        <td>${moment.utc(ele.DOB).format("MM/DD/YYYY")}</td>
                        <td>${moment.utc(ele.dateOfADT).format("MM/DD/YYYY")}</td>
                        <td>${questionType === CUSTOM_TAB_TYPE.combineTab ? ele?.type === "transfer" ? ele?.transferType : ele?.type : relation}</td>
                        ${pageType === DASHBOARD_FILTER_TYPE.HOSPITAL || page === "hospital" ? `<td>${ele.hospitalDays}</td>` : ''}                        
                    </tr>`
                    }

                }

                if (pageType === DASHBOARD_FILTER_TYPE.HOSPITAL) {
                    dynamicTable += `<tr>
                    <td colSpan="5" align="right">Total : ${_.sumBy(patientList, 'hospitalDays')}</td>
                    <tr>`;

                    dynamicTable += `<tr>
                    <td colSpan="5" align="right">Avg. : ${_.round(_.meanBy(patientList, (p) => p?.hospitalDays) || 0, 2).toFixed(2)}</td>
                    <tr>`
                }
            }
            dynamicTable += `</table>`;
        }

        if (graphData && graphData.selectedTab === CHART_TAB_BUTTON.BUILDING) {
            let data = graphData.chartData?.filterPatients;
            let censusByFacility = graphData?.dbData?.censusByFacility ?
                graphData?.dbData?.censusByFacility.map((ele) => { return { ...ele, id: ele.id.toString() } })
                : []
            let filterSelected = graphData.filters.filterSelected;
            let filter = graphData.filters;
            let adtFacilityPercentage = [];
            const chartData = graphData.chartData;
            let filterTotal = null;

            let tableDataArr = []

            await _(data)
                .groupBy("facilityId")
                .sortBy((group) => data.indexOf(group[0]))
                .map(async (product) => {
                    let totalByFacility = 0;
                    if (chartData.mainData && chartData.mainData.length > 0 && filterSelected) {
                        totalByFacility = _.filter(chartData.mainData, function (o) {
                            if (o.facilityId === product[0]?.facilityId) return o;
                        }).length;
                    }

                    if (
                        filter &&
                        (filter.type === "ninetyDaysData" || filter.type === "sixtyDaysData") &&
                        filter.facilityPercentage &&
                        filter.facilityPercentage.length > 0
                    ) {
                        totalByFacility =
                            _.find(filter.facilityPercentage, function (o) {
                                if (o.id === product[0]?.facilityId) return o;
                            })?.total || 0;
                    }
                    if (censusByFacility && censusByFacility.length > 0 && !filterSelected) {
                        totalByFacility = Math.round(_.find(censusByFacility, { id: product[0]?.facilityId })?.total) || 0;
                    }
                    if (adtFacilityPercentage && adtFacilityPercentage.length > 0) {
                        totalByFacility =
                            _.find(adtFacilityPercentage, function (o) {
                                if (o.id === product[0]?.facilityId) return o;
                            })?.total || 0;
                    }
                    if (filter && filter.lockedTotal && filter.lockedByFacility) {
                        totalByFacility =
                            _.filter(filter.lockedByFacility, function (o) {
                                if (o.facilityId === product[0]?.facilityId) return o;
                            })?.length || 0;
                    }
                    if (censusByFacility && censusByFacility.length > 0 && filter?.isCensusTotalLocked) {
                        totalByFacility = Math.round(_.find(censusByFacility, { id: product[0]?.facilityId })?.total) || 0;
                    }
                    let facilityWiseCount = await itemPercentage(
                        product.length,
                        filterTotal ? filterTotal : totalByFacility,
                        "number"
                    );
                    tableDataArr.push({
                        id: product[0]?.facilityId,
                        name: product[0]?.facility?.name || "",
                        total: product?.length || 0,
                        facilityPercentage: facilityWiseCount ? Number(facilityWiseCount) : null,
                    })
                })
                .value();
            if (tableDataArr.length > 0) {
                tableDataArr = await _.orderBy(tableDataArr, "total", "desc");
            }

            dynamicTable += `<table style="width:100%">`;
            dynamicTable += `<tr>
                                <th>NAME</th>
                                <th>TOTAL</th>
                                <th>% OF FACILITY</th>                                
                            </tr>`;
            if (tableDataArr.length > 0) {
                for (let i = 0; i < tableDataArr.length; i++) {
                    const ele = tableDataArr[i];
                    let percentage = await itemPercentage(ele.total, data.length);
                    dynamicTable += `<tr>
                        <td align="center" textAlign="center">${ele?.name}</td>
                        <td>${ele?.total} ${percentage}</td>
                        <td>${ele?.facilityPercentage}%</td>                        
                    </tr>`
                }
            }
            dynamicTable += `</table>`;
        }

        let htmlContainer = `<html>
                <style>
                  table {
                    border: 0.2em solid black;
                  }
                
                  td,
                  th {
                    padding: 7px;
                    border: 0.00001em solid #bfb8b8;
                  }
                
                  th {
                    font-weight: 700;
                  }
                
                  table {
                    text-align: center;
                  }
                
                  .print-table-ref table {
                    border-top: 0px !important;
                  }
                </style>
                <h5>Report</h5>
                <div>
                    ${headerTable}
                    ${averageTable}        
                    ${dynamicTable}
                <div>
                </html>
                `;
        return htmlContainer;
    } else {
        let dynamicTable = "";

        if (cardContentData && cardContentData.length > 0) {
            await filterAsyncData(cardContentData, async (row) => {
                dynamicTable += `<table style="width:100%">
                        ${tableHead(
                    row?.title,
                    row.data.reduce((a, obj) => a + Object.keys(obj).length, 0)
                )}`;

                row?.data &&
                    row.data.map((obj) => {
                        let percentageHead = "";
                        let filterItemHead = "";
                        let labelItemCell = "";
                        if (isPercentage) {
                            percentageHead = `<td>${obj?.percentage}</td>`;
                        }
                        if (obj?.filterItem) {
                            filterItemHead = `<td>${obj?.filterItem}</td>`;
                        }
                        if (obj?.label) {
                            labelItemCell = `<td align="center">${obj?.label}</td>`;
                        }

                        dynamicTable += `<tr>
                                                ${labelItemCell}
                                                <td>${obj?.total || obj?.value || 0}</td>
                                                ${percentageHead}
                                                <td>${obj?.percentageAgainst}</td>
                                                ${filterItemHead}
                                            </tr>`;
                    });

                dynamicTable += `</table ><br /><br />`;
            });
        }

        let htmlContainer = `<html>
                <style>
                  table {
                    border: 0.2em solid black;
                  }
                
                  td,
                  th {
                    padding: 7px;
                    border: 0.00001em solid #bfb8b8;
                  }
                
                  th {
                    font-weight: 700;
                  }
                
                  table {
                    text-align: center;
                  }
                
                  .print-table-ref table {
                    border-top: 0px !important;
                  }
                </style>
                <h5>Report</h5>
                <div>
                    ${headerTable}
                    ${averageTable}        
                    ${dynamicTable}
                <div>
                </html>
                `;
        return htmlContainer;
    }

};

module.exports = {
    renderContent,
};
