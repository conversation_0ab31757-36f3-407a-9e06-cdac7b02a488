import React, { useState } from 'react';
import {
    Dialog,
    DialogTitle,
    DialogContent,
    DialogActions,
    Typography,
    TextField,
    Button,
    Box,
    Slide,
    Alert
} from '@mui/material';
import {
    Warning as WarningIcon,    
    Save as SaveIcon,
    Cancel as CancelIcon
} from '@mui/icons-material';
import { styled } from '@mui/material/styles';

const Transition = React.forwardRef(function Transition(props, ref) {
    return <Slide direction="up" ref={ref} {...props} />;
});

const StyledDialog = styled(Dialog)(({ theme }) => ({
    '& .MuiDialog-paper': {
        borderRadius: 16,
        boxShadow: '0 24px 38px 3px rgba(0,0,0,0.14), 0 9px 46px 8px rgba(0,0,0,0.12), 0 11px 15px -7px rgba(0,0,0,0.2)',
        background: '#ffffff',
        color: '#333333',
        minWidth: '500px',
        maxWidth: '600px',
    },
}));

const StyledDialogTitle = styled(DialogTitle)(({ theme }) => ({
    background: '#fff3cd',
    color: '#856404',
    padding: theme.spacing(3),
    display: 'flex',
    alignItems: 'center',
    gap: theme.spacing(2),
    borderBottom: '1px solid #ffeaa7'
}));

const StyledDialogContent = styled(DialogContent)(({ theme }) => ({
    padding: theme.spacing(3),
    background: '#ffffff',
}));

const ActionButton = styled(Button)(({ theme }) => ({
    borderRadius: 12,
    padding: theme.spacing(1.5, 3),
    fontWeight: 'bold',
    textTransform: 'none',
    fontSize: '1rem',
    minWidth: '120px',
    margin: theme.spacing(0.5),
    transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
    '&.save-button': {
        background: 'linear-gradient(45deg, #4caf50, #81c784)',
        color: 'white',
        boxShadow: '0 4px 15px rgba(76, 175, 80, 0.4)',
        '&:hover': {
            background: 'linear-gradient(45deg, #43a047, #66bb6a)',
            transform: 'translateY(-2px)',
            boxShadow: '0 6px 20px rgba(76, 175, 80, 0.6)',
        },
        '&:disabled': {
            background: '#cccccc',
            color: '#666666',
            transform: 'none',
            boxShadow: 'none',
        }
    },
    '&.cancel-button': {
        background: 'linear-gradient(45deg, #f44336, #ef5350)',
        color: 'white',
        boxShadow: '0 4px 15px rgba(244, 67, 54, 0.4)',
        '&:hover': {
            background: 'linear-gradient(45deg, #d32f2f, #e57373)',
            transform: 'translateY(-2px)',
            boxShadow: '0 6px 20px rgba(244, 67, 54, 0.6)',
        }
    }
}));

const TitleConflictDialog = ({
    open,
    onClose,
    onConfirm,
    originalTitle,
    conflictingTitle,
    isLoading = false
}) => {
    const [newTitle, setNewTitle] = useState('');
    const [error, setError] = useState('');

    const handleTitleChange = (event) => {
        const value = event.target.value;
        setNewTitle(value);
        
        // Clear error when user starts typing
        if (error && value.trim()) {
            setError('');
        }
    };

    const handleConfirm = () => {
        const trimmedTitle = newTitle.trim();
        
        if (!trimmedTitle) {
            setError('Please enter a title for the custom tab');
            return;
        }

        if (trimmedTitle.toLowerCase() === conflictingTitle.toLowerCase()) {
            setError('Please choose a different title - this one already exists');
            return;
        }

        onConfirm(trimmedTitle);
    };

    const handleClose = () => {
        setNewTitle('');
        setError('');
        onClose();
    };

    return (
        <StyledDialog
            open={open}
            TransitionComponent={Transition}
            keepMounted
            disableEscapeKeyDown
            maxWidth="md"
            fullWidth
            sx={{ zIndex: 50000 }}
        >
            <StyledDialogTitle>
                <WarningIcon sx={{ fontSize: 32, color: '#ff9800' }} />
                <Typography variant="h5" component="div" sx={{ fontWeight: 'bold' }}>
                    Title Conflict Detected
                </Typography>
            </StyledDialogTitle>

            <StyledDialogContent>
                <Alert severity="warning" sx={{ mb: 3 }}>
                    <Typography variant="body1" sx={{ fontWeight: 'bold', mb: 1 }}>
                        You already have a custom tab with this title!
                    </Typography>
                    <Typography variant="body2">
                        Existing tab: <strong>"{conflictingTitle}"</strong>
                    </Typography>
                </Alert>

                <Typography variant="body1" sx={{ mb: 3, color: '#666666' }}>
                    Please choose a different title for the shared tab "{originalTitle}" to avoid conflicts.
                </Typography>

                <Box sx={{ mb: 3 }}>
                    <Typography variant="body2" sx={{ mb: 1, fontWeight: 'bold', color: '#333333' }}>
                        New Title:
                    </Typography>
                    <TextField
                        fullWidth
                        variant="outlined"
                        placeholder="Enter a new title for this custom tab"
                        value={newTitle}
                        onChange={handleTitleChange}
                        error={!!error}
                        helperText={error}
                        disabled={isLoading}
                        autoFocus
                        sx={{
                            '& .MuiOutlinedInput-root': {
                                borderRadius: 2,
                                '&:hover fieldset': {
                                    borderColor: '#2196f3',
                                },
                                '&.Mui-focused fieldset': {
                                    borderColor: '#2196f3',
                                }
                            }
                        }}
                    />
                </Box>

                <Typography variant="caption" sx={{ color: '#888888', fontStyle: 'italic' }}>
                    Tip: You can use a descriptive name like "{originalTitle} (Shared)" or "{originalTitle} - Copy"
                </Typography>
            </StyledDialogContent>

            <DialogActions sx={{ p: 3, background: '#f8f9fa', borderTop: '1px solid rgba(0,0,0,0.1)' }}>
                <ActionButton
                    className="cancel-button"
                    onClick={handleClose}
                    disabled={isLoading}
                    startIcon={<CancelIcon />}
                >
                    Cancel
                </ActionButton>
                <ActionButton
                    className="save-button"
                    onClick={handleConfirm}
                    disabled={isLoading || !newTitle.trim()}
                    startIcon={isLoading ? null : <SaveIcon />}
                >
                    {isLoading ? 'Saving...' : 'Save with New Title'}
                </ActionButton>
            </DialogActions>
        </StyledDialog>
    );
};

export default TitleConflictDialog;
