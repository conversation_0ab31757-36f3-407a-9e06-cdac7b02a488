const mongoose = require("mongoose");
const Patient = mongoose.model("patient");
const { momentDateFormat, toStartFilterDate, toEndFilterDate } = require("../utilis/date-format");
const { getCensusAverageInfo, getCensusAverageByPeriod } = require("./census");
const { DECEASED_CARDS_TYPE, RELATIONS, PAGE_TYPE, CUSTOM_TAB_TYPE } = require("../../types/common.type");
const { ninetyDaysDataFilter, getOtherDashboardData, getCustomCombineTabData } = require("./hospital");
const { ADT_SUB_TYPES, ADT_TYPES } = require("../../types");
const { findSavedReportConfig } = require("./reportsSubscription");
const { getCustomTabsByPage } = require("./custom-tab");
const { createDiffDashboardPatients, getNinetyDaysChartCount, enrichDiffDashboardPatients, applyCustomQuestionPatientFilter } = require("../../utils/common");
const {
    initializeGetAllCount,
    setupFacilityFilter,
    setupDateFilter,
    getCensusInfo,
    finalizeGetAllCount
} = require("./common-dashboard");
const { formatPatientData } = require("../utilis/common");

const filterData = async (arr, callback) => {
    const fail = Symbol();
    return (await Promise.all(arr.map(async item => ((await callback(item)) ? item : fail)))).filter(
        i => i !== fail
    );
};

const getAllCount = async req => {
    // Use common initialization
    let {
        user,
        facilityid,
        facilityIds,
        startDateFilter,
        endDateFilter,
        customTabs,
        diffDashboardPatients,
        customCombineTabData,
        isCustomCombineTab
    } = await initializeGetAllCount(req, PAGE_TYPE.DECEASED);
    // Use common facility filter setup
    let { facilityFilter, query, facilityData } = await setupFacilityFilter(facilityid, facilityIds);

    // Use common date filter setup
    await setupDateFilter(query, startDateFilter, endDateFilter);

    // Get census info using common utility
    const censusInfo = await getCensusInfo(startDateFilter, endDateFilter, facilityData);

    // Deceased specific query setup
    query.type = { $in: [ADT_TYPES.TRANSFER, ADT_TYPES.READMISSIONS] };
    query.transferType = { $in: [ADT_SUB_TYPES.DECEASED] };

    if (isCustomCombineTab) {
        customCombineTabData = await getCustomCombineTabData(query, PAGE_TYPE.DECEASED, facilityFilter, customCombineTabData, customTabs);
    }

    let list = await Patient.find({ ...query })
        .populate([RELATIONS.INSURANCE, RELATIONS.UNIT, RELATIONS.DOCTOR])
        .sort({ dateOfADT: 1 });

    const total = list.length;

    let listData = [];
    const seenPatientIds = new Set();

    if (list && list.length > 0) {
        await filterData(list, async item => {
            let latestObj = new Object();
            if (customTabs?.length > 0) {
                await getOtherDashboardData(item._doc, facilityFilter, diffDashboardPatients, seenPatientIds);
            }
            const ID = item._doc._id.toString();
            latestObj = item._doc;
            latestObj.dateOfADTOriginal = item._doc.dateOfADT;
            latestObj.DOBOriginal = item._doc.DOB;
            latestObj.id = ID;
            latestObj.dateOfADT = await momentDateFormat(item.dateOfADT, "YYYY-MM-DD");
            latestObj.DOB = await momentDateFormat(item.DOB, "YYYY-MM-DD");
            latestObj.insuranceId = await item.insurance?._id?.toString() || null;
            latestObj.floorId = await item.unit?._id?.toString() || null;
            latestObj.doctorId = await item.doctor?._id?.toString() || null;
            listData.push(latestObj);
        });
    }
    const resAnalysis = await getNinetyDaysChartCount(listData);
    const ninetyDaysData = resAnalysis?.ninetyDaysDataChart ?? [];
    listData = resAnalysis?.patientListRes ?? listData;

    // Use common finalization
    const { enrichedDiffDashboardPatients, isAutomaticReportSaved } = await finalizeGetAllCount(
        diffDashboardPatients,
        customTabs,
        PAGE_TYPE.DECEASED,
        user?.id
    );

    return {
        data: {
            diffDashboardPatients: enrichedDiffDashboardPatients,
            customTabs,
            list: listData,
            ninetyDaysData,
            customCombineTabData
        },
        totals: {
            ...censusInfo,
            isAutomaticReportSaved,
            total,
        },
    };
};

const getCardPatientChartData = async (req) => {
    const { body } = req;
    const { accountid } = req.headers;
    const {
        facilityId,
        facilityIds,
        cardFilter,
        type,
        filter: { startDate, endDate },
        relation,
    } = body;

    let startDateFilter = await toStartFilterDate(startDate)
    let endDateFilter = await toEndFilterDate(endDate);
    let diffDashboardPatients = await createDiffDashboardPatients();
    const customTabs = await getCustomTabsByPage({ accountid, page: PAGE_TYPE.DECEASED, userId: req.user._id });
    let customCombineTabData = await createDiffDashboardPatients('combineTab');
    const isCustomCombineTab = customTabs?.some(tab => tab.type === CUSTOM_TAB_TYPE.COMBINE);

    let query = { transferType: { $in: [ADT_SUB_TYPES.DECEASED] } };
    let facilityFilter = null;
    const facilityData = [];
    if (facilityIds && facilityIds.length > 0) {
        facilityIds.map(ele => {
            facilityData.push(ele);
        });
        facilityFilter = { facilityId: { $in: facilityData } };
        query.facilityId = { $in: facilityIds };
    } else {
        if (facilityId) {
            facilityFilter = { facilityId: mongoose.Types.ObjectId(facilityId) };
            query.facilityId = facilityId;
        }
    }
    if (startDate && endDate) {
        query.dateOfADT = {
            $gte: startDateFilter,
            $lt: endDateFilter,
        };
    }
    let relations = [...new Set([RELATIONS.FACILITY, RELATIONS.DOCTOR, RELATIONS.UNIT, RELATIONS.INSURANCE, RELATIONS.SNF, RELATIONS.TRANSFER_TO_WHICH_ASSISTED_LIVING, RELATIONS.NURSE, RELATIONS.HOSPITAL, RELATIONS.DX, RELATIONS.PAYER_SOURCE_INSURANCE])];

    if (isCustomCombineTab) {
        customCombineTabData = await getCustomCombineTabData(query, PAGE_TYPE.COMMUNITY_TRANSFER, facilityFilter, customCombineTabData, customTabs);
    }

    if (type !== DECEASED_CARDS_TYPE.TOTAL) {
        if (cardFilter && cardFilter.doctorData.length > 0 && type !== DECEASED_CARDS_TYPE.DOCTOR_DATA) {
            const doctorIds = cardFilter.doctorData.map(e => mongoose.Types.ObjectId(e));
            query.doctor = { $in: doctorIds };
            //relations.push(RELATIONS.DOCTOR);
        }
        if (cardFilter && cardFilter.floorsData.length > 0 && type !== DECEASED_CARDS_TYPE.FLOORS_DATA) {
            const floorIds = cardFilter.floorsData.map(e => mongoose.Types.ObjectId(e));
            query.unit = { $in: floorIds };
            //relations.push(RELATIONS.UNIT);
        }

        if (cardFilter && cardFilter.insuranceData.length > 0 && type !== DECEASED_CARDS_TYPE.INSURANCE_DATA) {
            const insuranceIds = cardFilter.insuranceData.map(e => mongoose.Types.ObjectId(e));
            query.insurance = { $in: insuranceIds };
            //relations.push(RELATIONS.INSURANCE);
        }
        query = await applyCustomQuestionPatientFilter(query, cardFilter);
    }

    let patientList = [];
    let listData = [];

    patientList = await Patient.find({ ...query })
        .populate(relations)
        .sort({ dateOfADT: -1 })
        .exec();
    const seenPatientIds = new Set();

    await filterData(patientList, async item => {
        if (customTabs?.length > 0) {
            await getOtherDashboardData(item, facilityFilter, diffDashboardPatients, seenPatientIds);
        }
        let latestObj = new Object();
        latestObj = item._doc;
        if (relation) {
            latestObj.filterId = item[relation]?._id || null;
        }
        const formattedPatientData = await formatPatientData(latestObj);
        listData.push(formattedPatientData);
    });

    diffDashboardPatients = await enrichDiffDashboardPatients(diffDashboardPatients, customTabs);
    let ninetyDaysData = [];
    const resAnalysis = await getNinetyDaysChartCount(listData);
    ninetyDaysData = resAnalysis?.ninetyDaysDataChart ?? [];
    listData = resAnalysis?.patientListRes ?? listData;

    if (type !== DECEASED_CARDS_TYPE.TOTAL && (cardFilter.ninetyDaysData?.length > 0 || type === DECEASED_CARDS_TYPE.NINETY_DAYS_DATA)) {
        const filterData = type === DECEASED_CARDS_TYPE.NINETY_DAYS_DATA ? ["a", "b", "c", "d", "e"] : cardFilter.ninetyDaysData;
        listData = await ninetyDaysDataFilter(filterData, listData, ninetyDaysData);
    }

    let censusAverage = 0;
    let censusByPeriod = [];
    let censusByFacility = []
    let bedCapacity = 0;
    let bedByFacility = [];
    let censusAsOfNowByFacility = [];

    if (startDate && endDate) {
        const facilityData = facilityIds.length > 0 ? facilityIds : [facilityId];
        if (type === DECEASED_CARDS_TYPE.TOTAL) {
            censusByPeriod = await getCensusAverageByPeriod(startDateFilter, endDateFilter, facilityData);
        }
        const censusInfo = await getCensusAverageInfo(startDateFilter, endDateFilter, facilityData, false, censusByFacility, bedByFacility, censusAsOfNowByFacility);
        censusAverage = censusInfo.censusAverage;
        bedCapacity = censusInfo?.bedCapacity;
    }
    return { diffDashboardPatients, customCombineTabData, data: listData, censusAverage, bedCapacity, censusByPeriod, ninetyDaysData, censusByFacility, bedByFacility, censusAsOfNowByFacility };
};

module.exports = {
    getCardPatientChartData,
    getAllCount,
};
