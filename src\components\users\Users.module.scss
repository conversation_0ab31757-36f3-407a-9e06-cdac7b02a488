.usersPage {
  padding: 40px 60px;

  .pageHdr {
    margin-bottom: 18px;

    .addBtn {
      background-color: transparentize($color: #00388d, $amount: 0.85);
      border: none;
      border-radius: 4px;
      padding: 12px 22px;
      cursor: pointer;
    }
  }

  .userLine {
    height: 54px;
    padding: 0px 20px;
    background-color: transparentize($color: #00388d, $amount: 0.95);
    margin-bottom: 4px;
    border-radius: 4px;

    .sec {
      padding: 0px 12px 0px 0px;

      &.name,
      &.email,
      &.type {
        flex: 2;
      }

      &.actions {
        flex: 0.2;
        justify-content: flex-end;
      }

      .lbl {
        color: transparentize($color: #000000, $amount: 0.35);
      }
    }
  }

  .moreIcon {
    background-color: rgb(163, 163, 163);
    height: 4px;
    width: 4px;
    border-radius: 50%;

    &::after,
    &::before {
      content: "";
      display: block;
      height: 4px;
      width: 4px;
      background-color: rgb(163, 163, 163);
      border-radius: 50%;
    }

    &::before {
      transform: translateY(-7px);
    }
    &::after {
      transform: translateY(3px);
    }
  }
}
