import * as React from 'react';
import styles from "./TotalCard.module.scss";
import { <PERSON><PERSON>, <PERSON>, CardContent, Divider, Grid, Typography } from '@mui/material';
import { Stack } from '@mui/system';

export default function TotalCard(props) {
    const {
        id,
        label,
        total,
        percentage,
        projectionPercentage,
        totalProjection = null
    } = props.item;
    
    return (
        <Stack className={styles.cardContainer} key={id}>
            <Card className={`${styles.totalCard} ${styles[id]}`}>
                <CardContent sx={{ padding: "22px 10px !important" }}>
                    <Grid container direction={"column"} justifyContent="center">
                        <Grid item>
                            <Typography className={styles.mainTitle}>
                                {label}
                            </Typography>
                        </Grid>
                        <Divider light={true} className={styles.divider} />
                        <Grid item className={styles.middleContainer}>
                            <Button className={styles.totalNumberButton}>{total}</Button>
                            {percentage && (
                                <Typography component={"span"} className={styles.percentageNumber}>{percentage}%</Typography>
                            )}
                        </Grid>
                        <Divider className={styles.divider} light={true} />
                        <Grid item className={styles.bottomContainer}>
                            <Typography
                                className={styles.primaryText}
                                component="div"
                            >
                                {id === "admission" ? "30 day admission rate" : "30 Day Transfer Rate"}
                            </Typography>
                            <Typography
                                component="div"
                                className={styles.secondaryText}
                            >
                                {totalProjection ? totalProjection : `${projectionPercentage}%`}
                            </Typography>
                        </Grid>
                    </Grid>
                </CardContent>
            </Card>
        </Stack>
    );
}
