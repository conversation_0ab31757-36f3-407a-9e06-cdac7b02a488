export const QUICK_GLACE_OPTIONS = [
    {
        id: "overall",
        label: "Overall",
        isSelected: false,
        color: "linear-gradient(180.24deg, #FCC779 0.21%, #FC3813 112.99%)",
        children: [
            { id: "totalOverall", parentId: "overall", label: "Total +/-" },
            { id: "totalOutgoing", parentId: "overall", label: "Total Outgoing" },
            { id: "totalIncoming", parentId: "overall", label: "Total Incoming" }
        ]
    },
    {
        id: "transfer",
        label: "Hospital Transfers",
        isSelected: false,
        color: "linear-gradient(359.47deg, #497AF5 0.5%, #90C5FF 99.58%)",
        children: [
            { id: "totalTransfer", parentId: "transfer", label: "Total Hospital Transfers" },
            { id: "unplannedHospitalTransfer", parentId: "transfer", label: "Unplanned Transfers" },
            { id: "plannedHospitalTransfer", parentId: "transfer", label: "Planned Transfers" }
        ]
    },
    {
        id: "communityTransfer",
        label: "Community Transfers",
        isSelected: false,
        color: "linear-gradient(180.34deg, #4CEBEB 0.3%, #076673 99.73%)",
        children: [
            { id: "total", parentId: "communityTransfer", label: "Total Community Transfers" },
            { id: "safeDischarge", parentId: "communityTransfer", label: "Safe Discharges" },
            { id: "SNF", parentId: "communityTransfer", label: "SNF Transfers" },
            { id: "AMA", parentId: "communityTransfer", label: "AMA Transfers" }
        ]
    },
    {
        id: "deceased",
        label: "Deceased",
        isSelected: false,
        color: "linear-gradient(180.24deg, #FF9196 0.21%, #6B0014 112.99%)",
        children: [
            { id: "totalDeceased", parentId: "deceased", label: "Total Deceased" }
        ]
    },
    {
        id: "admission",
        label: "Admissions",
        isSelected: false,
        color: "linear-gradient(180.24deg, #736AD1 0.21%, #00BAEB 112.99%)",
        children: [
            { id: "totalAdmissions", parentId: "admission", label: "Total Admissions" },
            { id: "admission", parentId: "admission", label: "New Admissions" },
            { id: "readmission", parentId: "admission", label: "Readmissions" }
        ]
    }
]