import React, { useEffect, useState } from "react";
import {
    <PERSON><PERSON>,
    <PERSON><PERSON>Title,
    DialogContent,
    <PERSON>alogA<PERSON>,
    Button,
    Typography,
    CircularProgress,
    Divider,
    Popover,
} from "@mui/material";
import { Box } from "@mui/system";
import { getAccountQuestions, removeArchivedQuestions, saveSelectedQuestions } from "../../../../../services/questions.service";
import { useDispatch } from "react-redux";
import { ADD_NOTIFICATION } from "../../../../../store/types";
import QuestionAccordion from "./components/QuestionAccordion";


const AccountQuestions = ({
    handleClose,
    latestType,
    transferType,
    onClose
}) => {
    const [questions, setQuestions] = useState([]);
    const [loading, setLoading] = useState(true);
    const [selectedQuestions, setSelectedQuestions] = useState([]);
    const dispatch = useDispatch();

    // Popover state
    const [confirming, setConfirming] = useState(false);

    // Fetch questions
    useEffect(() => {
        let transferTypeArr = [transferType];
        if (transferType === "SNF" || transferType === "AMA" || transferType === "safeDischarge") {
            transferTypeArr = ["SNF", "AMA", "safeDischarge"];
        }
        const fetchQuestions = async () => {
            const res = await getAccountQuestions({
                forType: latestType,
                ...(latestType === "transfer" && { forTransferType: transferTypeArr }),
            });
            if (res) {
                setQuestions(res);
                setLoading(false);
            }
        };
        fetchQuestions();
    }, [latestType, transferType]);

    // Handle checkbox toggle
    const handleCheckboxChange = (questionId) => {
        setSelectedQuestions((prev) =>
            prev.includes(questionId) ? prev.filter((id) => id !== questionId) : [...prev, questionId]
        );
    };

    // Open popover alert
    const handleConfirmClick = (event) => {
        if (selectedQuestions.length === 0) {
            alert("No questions selected.");
            return;
        }
        setConfirming(true);
    };

    // Close popover
    const handleClosePopover = () => {
        setConfirming(false);
    };

    // Proceed with saving questions
    const handleSaveQuestions = async () => {
        try {
            const res = await saveSelectedQuestions({ questionIds: selectedQuestions });
            if (res) {
                dispatch({
                    type: ADD_NOTIFICATION,
                    payload: {
                        type: "success",
                        label: "New question saved successfully!",
                        id: "newQuestionSaved"
                    },
                });
                handleClose();
            }
        } catch (e) {
            dispatch({
                type: ADD_NOTIFICATION,
                payload: {
                    type: "error",
                    label: e?.response?.data?.message || "Error while trying to add account.",
                    id: 'addAccountError'
                },
            });
        }
        handleClosePopover();
    };

    const [deletePopover, setDeletePopover] = useState({ open: false, anchorEl: null, questionId: null });


    const handleDeleteClick = (event, id) => {
        event.stopPropagation();
        setDeletePopover({
            open: true,
            anchorEl: event.currentTarget,
            questionId: id
        });
    };

    const confirmDelete = async () => {
        try {
            const res = await removeArchivedQuestions(deletePopover.questionId);
            if (res) {
                setQuestions(prev => prev.filter(q => q._id !== deletePopover.questionId));
                setSelectedQuestions(prev => prev.filter(id => id !== deletePopover.questionId));
                dispatch({
                    type: ADD_NOTIFICATION,
                    payload: {
                        type: "success",
                        label: "Question removed successfully!",
                        id: "deleteQuestionSuccess"
                    },
                });
            } else {
                dispatch({
                    type: ADD_NOTIFICATION,
                    payload: {
                        type: "error",
                        label: "Failed to delete question.",
                        id: "deleteQuestionError"
                    },
                });
            }
            setDeletePopover({ open: false, anchorEl: null, questionId: null });
        } catch (error) {
            dispatch({
                type: ADD_NOTIFICATION,
                payload: {
                    type: "error",
                    label: "Failed to delete question.",
                    id: "deleteQuestionError"
                },
            });
        } finally {
            setDeletePopover({ open: false, anchorEl: null, questionId: null });
        }
    };

    const cancelDelete = () => {
        setDeletePopover({ open: false, anchorEl: null, questionId: null });
    };

    return (
        <>
            <Dialog
                open={true}
                onClose={onClose}
                maxWidth="lg"
                width="md"
            >
                <DialogTitle>Question Archive</DialogTitle>
                <Divider />
                <DialogContent>
                    <Box sx={{ display: "flex", justifyContent: "center", alignItems: "center", height: "100%", minWidth: "300px" }}>
                        <>

                            {loading ? (
                                <div className="df aic m-t-20">
                                    <CircularProgress />
                                </div>
                            ) : (
                                questions.length > 0 && (
                                    <div style={{ width: "600px" }}>
                                        {questions.map((ele) => (
                                            <QuestionAccordion
                                                key={ele._id}
                                                question={ele}
                                                isSelected={selectedQuestions.includes(ele._id)}
                                                onCheckboxChange={handleCheckboxChange}
                                                onDeleteClick={handleDeleteClick}
                                            />
                                        ))}
                                        {/* {questions.map((ele) => (
                                            <Accordion key={ele._id} disableGutters={false}>
                                                <AccordionSummary
                                                    expandIcon={<ExpandMoreIcon />}>
                                                    <FormControlLabel
                                                        control={
                                                            <Checkbox
                                                                checked={selectedQuestions.includes(ele._id)}
                                                                onChange={() => handleCheckboxChange(ele._id)}
                                                            />
                                                        }
                                                        label={<FormLabel>{ele?.label}</FormLabel>}
                                                    />
                                                    <IconButton
                                                        edge="end"
                                                        onClick={(e) => handleDeleteClick(e, ele._id)}
                                                        aria-label="delete"
                                                        size="small"
                                                        color="error"
                                                        sx={{ marginLeft: "auto" }} // Pushes delete to the right
                                                    >
                                                        <DeleteOutlineOutlinedIcon sx={{ size: "small" }} />
                                                    </IconButton>
                                                </AccordionSummary>

                                                <AccordionDetails>
                                                    <TextField
                                                        variant="outlined"
                                                        size="small"
                                                        value={ele.label}
                                                        fullWidth
                                                        label="Question Label"
                                                        InputProps={{ readOnly: true }}
                                                        className="m-t-10"
                                                        disabled
                                                    />
                                                    <TextField
                                                        variant="outlined"
                                                        size="small"
                                                        value={ele.tableLabel}
                                                        fullWidth
                                                        label="Table Question Label"
                                                        InputProps={{ readOnly: true }}
                                                        className="m-t-10"
                                                        disabled
                                                    />

                                                    <Typography className="m-t-10">
                                                        Question Type:{" "}
                                                        {QUESTION_TYPE_OPTIONS?.find(
                                                            (option) => option.value === ele?.customQuestionInputType
                                                        )?.label}
                                                    </Typography>

                                                    {ele?.customQuestionOptions?.length > 0 &&
                                                        (() => {
                                                            switch (ele.customQuestionInputType) {
                                                                case QUESTION_INPUT_TYPE.LIMITED_ANSWERS:
                                                                    return ele.customQuestionOptions.map((option, index) => (
                                                                        <TextField
                                                                            key={index}
                                                                            variant="outlined"
                                                                            size="small"
                                                                            value={option}
                                                                            fullWidth
                                                                            label={`Option ${index + 1}`}                                 
                                                                            disabled
                                                                            className="m-t-10"
                                                                        />
                                                                    ));

                                                                case QUESTION_INPUT_TYPE.NUMBER_RANGE:
                                                                case QUESTION_INPUT_TYPE.NUMBER_RANGE_LIMITED_ANSWERS:
                                                                    return ele.customQuestionOptions.map((option, index) => (
                                                                        <div key={index} className="df aic m-b-10">
                                                                            {option.type === NUMBER_RANGE_TYPE.TOTAL || option.type === NUMBER_RANGE_TYPE.AVERAGE ? (
                                                                                <TextField
                                                                                    variant="outlined"
                                                                                    size="small"
                                                                                    value={option.title}
                                                                                    fullWidth
                                                                                    label={`Range Type ${index + 1}`}
                                                                                    disabled
                                                                                />
                                                                            ) : (
                                                                                <>
                                                                                    <TextField
                                                                                        variant="outlined"
                                                                                        size="small"
                                                                                        value={option.min}
                                                                                        fullWidth
                                                                                        label={`Min Value ${index + 1}`}
                                                                                        disabled
                                                                                    />
                                                                                    <TextField
                                                                                        variant="outlined"
                                                                                        size="small"
                                                                                        value={option.max}
                                                                                        fullWidth
                                                                                        label={`Max Value ${index + 1}`}
                                                                                        disabled
                                                                                        className="m-l-10"
                                                                                    />
                                                                                </>
                                                                            )}
                                                                        </div>
                                                                    ));
                                                                    ;
                                                                case QUESTION_INPUT_TYPE.UNLIMITED_ANSWERS:
                                                                    return null;

                                                                default:
                                                                    return null;
                                                            }
                                                        })()}
                                                </AccordionDetails>
                                            </Accordion>
                                        ))} */}
                                    </div>
                                )
                            )}
                            {(questions?.length === 0 && !loading) && (
                                <Box sx={{ display: "flex", justifyContent: "center", alignItems: "center", height: "100%" }}>
                                    <Typography variant="subtitle1" color="text.secondary" sx={{ fontWeight: "bold" }}>
                                        No Archive Questions Found.
                                    </Typography>
                                </Box>
                            )}
                        </>
                    </Box>
                </DialogContent>
                <Divider />
                <DialogActions>
                    <Button onClick={onClose} color="secondary">
                        Cancel
                    </Button>
                    <Button
                        onClick={handleConfirmClick}
                        color="primary"
                        variant="contained"
                        disabled={selectedQuestions?.length === 0}
                    >
                        Confirm
                    </Button>
                </DialogActions>

                {/* Popover Alert */}
                <Popover
                    open={confirming}
                    onClose={handleClosePopover}
                    anchorReference="anchorPosition"
                    anchorPosition={{ top: window.innerHeight / 2, left: window.innerWidth / 2 }}
                    anchorOrigin={{
                        vertical: 'center',
                        horizontal: 'center',
                    }}
                    transformOrigin={{
                        vertical: 'center',
                        horizontal: 'center',
                    }}
                >
                    <div style={{ padding: '20px', width: '300px' }}>
                        <Typography variant="h6">Confirm Selection</Typography>
                        <Typography variant="body1" style={{ margin: '10px 0' }}>
                            You have selected {selectedQuestions.length} question(s).
                        </Typography>
                        <Divider />
                        <div style={{ marginTop: '10px', display: 'flex', justifyContent: 'space-between' }}>
                            <Button onClick={handleClosePopover} color="secondary">
                                Cancel
                            </Button>
                            <Button onClick={handleSaveQuestions} color="primary" variant="contained">
                                Save
                            </Button>
                        </div>
                    </div>
                </Popover>
            </Dialog>
            <Popover
                open={deletePopover.open}
                anchorEl={deletePopover.anchorEl}
                onClose={cancelDelete}
                anchorOrigin={{
                    vertical: 'bottom',
                    horizontal: 'right',
                }}
                transformOrigin={{
                    vertical: 'top',
                    horizontal: 'right',
                }}
            >
                <Box sx={{ padding: 1.5, width: 250 }}>
                    <Typography variant="subtitle1" gutterBottom>
                        Confirm Remove
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                        Are you sure you want remove the selected questions?
                    </Typography>
                    <Box sx={{ display: "flex", justifyContent: "flex-end", gap: 1, marginTop: 1.5 }}>
                        <Button onClick={cancelDelete} size="small">
                            Cancel
                        </Button>
                        <Button onClick={confirmDelete} color="error" variant="contained" size="small">
                            Remove
                        </Button>
                    </Box>
                </Box>
            </Popover>

        </>
    );
};

export default AccountQuestions;
