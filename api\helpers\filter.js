const mongoose = require("mongoose");
const Filter = mongoose.model("filter");

// Create new account
const createFilter = async (req) => {
    const user = req?.user;
    const { facilityid, accountid } = req.headers;

    const isExistsFilter = await Filter.findOne({
        facilityId: mongoose.Types.ObjectId(facilityid),
        accountId: mongoose.Types.ObjectId(accountid),
        name: req.body.name,
        type: req.body.type
    });

    if (isExistsFilter) {
        return { status: 401, error: "Filter name already taken" }
    } else {
        const filterSave = new Filter({
            facilityId: mongoose.Types.ObjectId(facilityid),
            accountId: mongoose.Types.ObjectId(accountid),
            userId: user._id,
            ...req.body
        });
        const saved = await filterSave.save()
        return { status: 200, data: saved }
    }
};

const getFilters = async (req) => {
    const { accountid } = req.headers;
    const { type } = req.query;

    const filters = await Filter.find({
        type: type,
        accountId: accountid
    });

    return filters;
};

const getFilter = async (req) => {
    const { id } = req.query;
    const filters = await Filter.findOne({ _id: mongoose.Types.ObjectId(id) });
    return filters;
};

const deleteFilter = async id => {
    let deleted = await Filter.findByIdAndDelete(id);
    return deleted;
};

module.exports = { createFilter, getFilter, getFilters, deleteFilter };
