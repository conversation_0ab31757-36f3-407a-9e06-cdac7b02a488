import { Box, Grid, Toolbar } from "@mui/material";
import update from "immutability-helper";
import _ from "lodash";
import moment from "moment";
import { useCallback, useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { getFilters, removeFilter, saveFilter } from "../../../services/filter.service";
import {
	setDBSelectedFilters as setDBSelectedFiltersComparison,
	setLockTotal as setLockTotalComparison,
	setSelectedFilter as setSelectedFilterComparison,
} from "../../../store/reducers/comparisonReducers/overallComparison.slice";
import {
	setDBSelectedFilters,
	setFilterDateRange,
	setFilterTotal,
	setIsMainCensusPercentage,
	setLockTotal,
	setSelectedFilter,
} from "../../../store/reducers/overall.slice";
import { setFacilityIds, setMultipleFacility } from "../../../store/reducers/permission.slice";
import { ADD_NOTIFICATION } from "../../../store/types";
import { DASHBOARD_FILTER_TYPE } from "../../../types/common.type";
import { OVERALL_CARDS_TYPE } from "../../../types/overall.type";
import { OVERALL_PAGE_SUB_TYPE, PAGE_TYPE } from "../../../types/pages.type";
import { calcProPercentsBasedOnFilterAndDays } from "../../../utilis/common";
import DateFilterComponent from "../filter/DateFilterComponent";
import DashboardHeader from "../header/DashboardHeader";
import CensusAverageBox from "../shared/CensusAverageBox/CensusAverageBox";
import DashboardTotalCount from "../shared/DashboardTotalCount";
import HeaderFilterOptions from "../shared/headerFilterOptions/HeaderFilterOptions";
import HeaderMenu from "../../shared/header-menu";
import OverallHeaderFilterList from "./OverallHeaderFilterList";

const OverallHeader = ({
	handleFilterUpdate,
	loading,
	setLoading,
	setLoadingComparison,
	projectionDays,
	isComparingAgainstAvgCensus,
	percentageAgainst,
	projectionSelectionBlock,
	filterListData,	
}) => {
	const dispatch = useDispatch();
	const [selectedFilterMenu, setSelectedFilterMenu] = useState("all");
	const {
		dbData,
		totals,
		cardFilter,
		filter,
		transferType,
		filterTotal,
		lockedTotal,
		mainNumPercentage,
		lockedTotalBy,
	} = useSelector((state) => state.overall);
	const { facilityIds, isMultipleFacility } = useSelector((state) => state.permission);
	const { censusAverage } = dbData;
	const [filterValue, setFilterValue] = useState("");
	const [filterOptions, setFilterOptions] = useState([]);
	const { lockedTotal: lockedTotalComparison, filterTotal: filterTotalComparison } = useSelector(
		(state) => state.overallComparison
	);
	const { facilities: activeFacilities, prevStateFacilities } = useSelector((state) => state.activeFacilities);
	const totalFilterForViewer = calcProPercentsBasedOnFilterAndDays(
		filterTotal,
		filter,
		projectionDays,
		true,
		(!lockedTotalBy && !isComparingAgainstAvgCensus) || (lockedTotalBy && lockedTotalBy !== "census")
	);

	const handleChangeSelect = useCallback(
		(event) => {
			const selectedValue = event.target.value;
			if (selectedValue && selectedValue.facilityIds && selectedValue.facilityIds.length > 1) {
				dispatch(setFacilityIds(selectedValue.facilityIds));
				dispatch(setMultipleFacility(true));
			} else {
				dispatch(setFacilityIds([]));
				dispatch(setMultipleFacility(false));
			}

			handleFilterUpdate(true);
			let latestValues = {
				...selectedValue.filter,
				filter: {
					...selectedValue.filter.filter,
					startDate: moment(selectedValue.filter.filter.startDate).toDate(),
					endDate: moment(selectedValue.filter.filter.endDate).toDate(),
				},
			};
			setSelectedFilterMenu({ ...selectedValue, filter: latestValues });
			dispatch(setSelectedFilter({ ...selectedValue, filter: latestValues }));
			dispatch(setDBSelectedFilters({ ...selectedValue, filter: latestValues }));
		},
		[dispatch, handleFilterUpdate]
	);

	const getFiltersData = useCallback(() => {
		getFilters({ type: DASHBOARD_FILTER_TYPE.OVERALL }).then((res) => {
			if (res && res.length > 0) {
				setFilterOptions(res);
			}
		});
	}, []);

	useEffect(
		() => {
			if (localStorage.getItem("facilityId")) {
				getFiltersData();
			}
		},
		// eslint-disable-next-line react-hooks/exhaustive-deps
		[getFiltersData, localStorage.getItem("facilityId")]
	);

	const handleClearFilter = useCallback(() => {
		setSelectedFilterMenu("all");
		dispatch(setSelectedFilter("all"));
		dispatch(setDBSelectedFilters(null));
		dispatch(setSelectedFilterComparison("all"));
		dispatch(setDBSelectedFiltersComparison(null));
	}, [dispatch]);

	useEffect(() => {
		if (!_.isEqual(activeFacilities, prevStateFacilities)) {
			handleClearFilter();
		}
	}, [activeFacilities, handleClearFilter, prevStateFacilities]);

	const handleRemoveFilter = async (selectedItem) => {
		await removeFilter(selectedItem._id);
		const latestFacilityList = update(filterOptions, {
			$splice: [[filterOptions.indexOf(selectedItem), 1]],
		});
		setFilterOptions(latestFacilityList);
		return true;
	};

	const handleSaveFilter = async () => {
		let facilityIdsData = [];
		if (isMultipleFacility) {
			facilityIdsData = facilityIds;
		} else {
			facilityIdsData = [localStorage.getItem("facilityId")];
		}
		const resData = await saveFilter({
			type: DASHBOARD_FILTER_TYPE.OVERALL,
			name: filterValue,
			facilityIds: facilityIdsData,
			filter: { cardFilter, filter, totals, transferType },
		});
		if (resData && resData.status === 200) {
			getFiltersData();
			setFilterValue("");
			return true;
		} else {
			dispatch({
				type: ADD_NOTIFICATION,
				payload: {
					label: resData.error,
					type: "error",
					id: "overallSaveFilterError",
				},
			});
			return false;
		}
	};

	const onChangeFilter = useCallback(
		(value) => {
			dispatch(setFilterDateRange(value));
		},
		[dispatch]
	);

	const handlePercentage = useCallback(() => {
		if (transferType !== OVERALL_PAGE_SUB_TYPE.TOTAL) {
			if (!mainNumPercentage) {
				dispatch(setFilterTotal(dbData?.censusAverage));
				dispatch(setIsMainCensusPercentage(dbData?.censusAverage));
			} else {
				dispatch(setIsMainCensusPercentage(null));
			}
		}
	}, [dispatch, dbData, transferType, mainNumPercentage]);

	const handleSetDefaultTotal = useCallback(() => {
		dispatch(setLockTotal(lockedTotal ? null : filterTotal));
		dispatch(setLockTotalComparison(lockedTotalComparison ? null : filterTotalComparison));
	}, [dispatch, filterTotal, filterTotalComparison, lockedTotal, lockedTotalComparison]);

	return (
		<DashboardHeader>
			<Box
				display={"flex"}
				sx={{
					width: "100%",
					height: "60px",
					backgroundColor: "#636578",
					display: "flex",
					alignItems: "center",
					padding: "0px 16px",
				}}
			>
				<DashboardTotalCount
					isLockedTotal={lockedTotal ? true : false}
					filterTotal={totalFilterForViewer}
					handleChange={handleSetDefaultTotal}
					background="linear-gradient(180deg, #FF0000 0%, #F5D948 100%)"
					cardFilter={cardFilter}
				/>
				<Grid container direction={"row"} justifyContent="space-between" alignItems="center" sx={{ ml: "-10px", mt: "10px" }}>
					<Grid item lg={8} md={6} xs={12}>
						<OverallHeaderFilterList
							filterListData={filterListData}
							cardFilter={cardFilter}
							transferType={transferType}
							lockedTotal={lockedTotal}
							lockedTotalBy={lockedTotalBy}
						/>
					</Grid>
					<Grid item lg={4} md={6} xs={12}>
						<HeaderFilterOptions
							selectedFilterMenu={selectedFilterMenu}
							handleChangeSelect={handleChangeSelect}
							filterOptions={filterOptions}
							filterValue={filterValue}
							setFilterValue={setFilterValue}
							handleSaveFilter={handleSaveFilter}
							handleClearFilter={handleClearFilter}
							handleRemoveFilter={handleRemoveFilter}
						/>
					</Grid>
				</Grid>
			</Box>
			<Toolbar sx={{ height: 91 }}>

				<Grid
					container
					direction={"row"}
					spacing={2}
					justifyContent="space-between"
					alignItems="center"
					sx={{ ml: "10px" }}
				>
					{/* <Grid item xs={1} md={2} xl={1}>
						<Stack direction={"row"} spacing={1} sx={{ ml: "-20px" }}>
							<Stack>
								<ComparisonPopup forPage="overall" loading={loading} setLoadingComparison={setLoadingComparison} />
							</Stack>
							<Stack>
								{projectionSelectionBlock}
							</Stack>
						</Stack>
					</Grid> */}
					<Grid item xs={4} md={4} xl={5}>
						<CensusAverageBox
							censusAverage={censusAverage}
							loading={loading}
							dbData={dbData}
							pageType={PAGE_TYPE.OVERALL}
							percentageAgainst={percentageAgainst}							
							{...(cardFilter.priorityData.length === 0 &&
								transferType !== OVERALL_CARDS_TYPE.TOTAL && {
								handlePercentage: handlePercentage,
							})}

						//selected={Boolean(mainNumPercentage)}
						/>
					</Grid>
					<Grid item xs={8} md={8} xl={7} display="flex" justifyContent="space-between" alignItems="center">
						<DateFilterComponent
							handleFilterUpdate={handleFilterUpdate}
							onChangeFilter={onChangeFilter}
							filter={filter}
							loading={loading}
							setLoading={setLoading}
							page={PAGE_TYPE.OVERALL}
						/>

						<HeaderMenu
							page={PAGE_TYPE.OVERALL}
							loading={loading}
							setLoadingComparison={setLoadingComparison}
							isAutomaticReportSaved={true}
							activeFacilities={activeFacilities}
						/>
					</Grid>
				</Grid>
			</Toolbar>
		</DashboardHeader>
	);
};

export default OverallHeader;
