const mongoose = require("mongoose");
const { PAGE_TYPE, ALERT_TYPE_TYPE } = require("../types/common.type");
const { Schema, Types } = mongoose;

// Alert Report Schema
const AlertReportSchema = new Schema(
  {
    // Reference to account (required field)
    accountId: {
      type: Types.ObjectId,
      ref: "account",
      required: true,
    },

    // Optional reference to facility
    facilityId: {
      type: Types.ObjectId,
      ref: "facility",
    },

    // Optional reference to user
    userId: {
      type: Types.ObjectId,
      ref: "user",
    },

    // Page type with defined enum values and default
    page: {
      type: String,
      enum: Object.values(PAGE_TYPE),
      default: PAGE_TYPE.HOSPITAL,
      required: true,
    },

    type: {
      type: [String],
      required: true,
    },

    // Alerts object (could add validation if structure is known)
    alerts: {
      type: Object,
      default: {},
      required: false,
    },
    isRising: {
      type: <PERSON>olean,
      default: false,
      required: false
    },
    isDropping: {
      type: Boolean,
      required: false,
      required: false
    },
    isTransferNeedWork: {
      type: Boolean,
      default: false,
      required: false
    },
    customAlert: [
      {
        name: { type: String, required: false },
        filter: { type: Object, required: false },
        cardFilter: { type: Object, required: false },
        transferType: { type: mongoose.Schema.Types.Mixed, required: false },
        slug: { type: String, required: false },
        alertType: { type: String, required: false, default: "main" }, // type will be main, question, or customTab
        alertTypeId: { type: String, required: false, default: "" }, // type will be main, question, or customTab
        refSlug : { type: String, required: false, default: "" }, // type will be main, question, or customTab
      },
    ],
  },
  {
    // Automatically adds createdAt and updatedAt timestamps
    timestamps: true,

    // Enforce strict schema (extra fields will be ignored)
    strict: false,

    versionKey: false,
  }
);

// Create and export the AlertReport model
module.exports = mongoose.model("alertReport", AlertReportSchema);
