const mongoose = require("mongoose");
const Validation = mongoose.model("validation");
const Facility = mongoose.model("facility");
const authWithRole = require("../middleware/auth-with-role");
const route = require("express").Router();

route.get("/list", authWithRole("manageValidation"), async (req, res) => {
    try {
        const { accountid } = req.headers;
        let validations = [];
        const { validationBase = null, active } = req.query;
        if (validationBase && validationBase === "account") {
            facilities = await Facility.find({ accountId: accountid });

            const facilitiesIds = []
            if (facilities.length > 0) {
                facilities.map((item) => facilitiesIds.push(item._id.toString()))
            }
            validations = await Validation.find({
                type: req.query.type,
                facilityId: {
                    $in: facilitiesIds,
                },
                ...active && { active: true }
            }).sort({
                label: 1,
            });
        } else {
            validations = await Validation.find({
                type: req.query.type,
                facilityId: {
                    $in: [req.query.facilityid],
                },
            }).sort({
                label: 1,
            });
        }
        res.send(validations);
    } catch (error) {
        res.status(500).send(error);
    }
});

route.get("/all", authWithRole("manageValidation"), async (req, res) => {
    try {
        const validations = await Validation.find().sort({
            label: 1,
        });
        res.send(validations);
    } catch (error) {
        res.status(500).send(error);
    }
});

route.post("/", authWithRole("manageValidation"), async (req, res) => {
    try {
        const validation = await Validation.create({
            ...req.body,
            isEditable: true,
        });
        res.send(validation);
    } catch (error) {
        res.status(500).send(error);
    }
});

route.put("/:id", authWithRole("manageValidation"), async (req, res) => {
    try {
        const validation = await Validation.findByIdAndUpdate(req.params.id, req.body, { new: true });
        res.send(validation);
    } catch (error) {
        res.status(500).send(error);
    }
});

route.get("/:id", authWithRole("manageValidation"), async (req, res) => {
    try {
        const validation = await Validation.findById(req.params.id);
        res.send(validation);
    } catch (error) {
        res.status(500).send(error);
    }
});

module.exports = route;
