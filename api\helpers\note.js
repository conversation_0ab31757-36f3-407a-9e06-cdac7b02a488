const mongoose = require("mongoose");
const { sendAlertEmail } = require("../../sendEmail");
const Note = mongoose.model("note");
const User = mongoose.model("user");

// Create new account
const createNote = async (req) => {
    const user = req?.user;
    const { accountid = null } = req.headers;
    const { _id = null, description, page } = req.body;
    if (!user) {
        return { status: 400, data: "User required" }
    }

    if (req.body && req.body._id) {
        const isExistsNote = await Note.findOne({ _id: mongoose.Types.ObjectId(req.body._id) });
        isExistsNote.description = description
        await isExistsNote.save();
        const note = await Note.findOne({ _id: mongoose.Types.ObjectId(_id) });

        return { status: 200, data: note }
    } else {
        const noteSave = new Note({
            userId: user?._id,
            ...req.body
        });

        const saved = await noteSave.save();

        return { status: 200, data: saved }
    }
};

const getNotes = async (req) => {
    const { accountid } = req.headers;
    const user = req?.user;
    const notes = await Note.find({
        userId: user?._id
    });

    return notes;
};

const getNote = async (req) => {
    const { id } = req.query;
    const note = await Note.findOne({ _id: mongoose.Types.ObjectId(id) });
    return note;
};

const deleteNote = async id => {
    let deleted = await Note.findByIdAndDelete(id);
    return deleted;
};

const shareNoteEmail = async (req) => {
    const { noteId, selectedUsers } = req.body;
    const note = await Note.findOne({ _id: mongoose.Types.ObjectId(noteId) }).populate("userId");

    if (note) {
        await selectedUsers.forEach(async user => {
            let emailTemplate = `Hi ${user?.fullName}, ${note?.userId?.fullName} sent you a note.\n\n ${note?.description} \n`;
            let subject = `SimpleSNF Note From ${note?.userId?.fullName}`;

            await sendAlertEmail(user?.email, emailTemplate, subject);            
        });
    }

    return note;
}

module.exports = { createNote, getNote, getNotes, deleteNote, shareNoteEmail };
