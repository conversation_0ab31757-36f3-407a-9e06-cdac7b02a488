const Role = require("../../models/Role");

const seedRolesData = [
    { name: 'Super', active: false, order: 1, slug: 'super' },
    { name: 'Owner', active: true, order: 2, slug: 'owner' },
    { name: 'Total', active: true, order: 3, slug: 'total' },
    { name: 'Regional', active: true, order: 4, slug: 'regional' },
    { name: 'Admin', active: true, order: 5, slug: 'admin' },
    { name: 'User', active: true, order: 6, slug: 'user' },
];

const seedRoles = async () => {
    try {
        const roles = await Role.insertMany(seedRolesData);
        console.log('✅ Roles seeded successfully.');
        return roles;
    } catch (err) {
        console.error('❌ Error seeding roles:', err.message);
        throw err;
    }
};

module.exports = {
    seedRolesData,
    seedRoles,
};
