const mongoose = require("mongoose");
const Patient = mongoose.model("patient");
const _ = require("lodash");
const {
    momentDateFormat,
    toStartFilterDate,
    toEndFilterDate,
} = require("../utilis/date-format");
const { getCensusAverageInfo, getCensusAverageByPeriod } = require("./census");
const { CO_TRANSFER_CARDS_TYPE, PAGE_TYPE, RELATIONS, CUSTOM_TAB_TYPE } = require("../../types/common.type");
const {
    ninetyDaysDataFilter,
    getOtherDashboardData,
    getCustomCombineTabData,
} = require("./hospital");
const { ADT_SUB_TYPES, ADT_TYPES } = require("../../types");
const { findSavedReportConfig } = require("./reportsSubscription");
const { getCustomTabsByPage } = require("./custom-tab");
const { getReturnAndDidNotReturn, getSixtyDaysChartCount, createDiffDashboardPatients, enrichDiffDashboardPatients, applyCustomQuestionPatientFilter, getNinetyDaysChartCount } = require("../../utils/common");
const {
    initializeGetAllCount,
    setupFacilityFilter,
    setupDateFilter,
    getCensusInfo,
    finalizeGetAllCount
} = require("./common-dashboard");
const { formatPatientData } = require("../utilis/common");

const filterData = async (arr, callback) => {
    const fail = Symbol();
    return (
        await Promise.all(
            arr.map(async (item) => ((await callback(item)) ? item : fail))
        )
    ).filter((i) => i !== fail);
};

const getAllCount = async (req) => {
    // Use common initialization
    let {
        user,
        facilityid,
        startDate,
        endDate,
        facilityIds,
        startDateFilter,
        endDateFilter,
        customTabs,
        diffDashboardPatients,
        customCombineTabData,
        isCustomCombineTab
    } = await initializeGetAllCount(req, PAGE_TYPE.COMMUNITY_TRANSFER);

    // Use common facility filter setup
    let { facilityFilter, query, facilityData } = await setupFacilityFilter(facilityid, facilityIds);

    // Use common date filter setup
    await setupDateFilter(query, startDateFilter, endDateFilter);

    // Get census info using common utility
    const censusInfo = await getCensusInfo(startDateFilter, endDateFilter, facilityData);

    // Community transfer specific query setup
    query.type = { $in: [ADT_TYPES.TRANSFER] };
    query.transferType = {
        $in: [ADT_SUB_TYPES.AMA, ADT_SUB_TYPES.SNF, ADT_SUB_TYPES.SAFE_DISCHARGE],
    };

    let totalAMA = 0;
    let totalSNF = 0;
    let totalSafeDischarge = 0;

    if (isCustomCombineTab) {
        customCombineTabData = await getCustomCombineTabData(query, PAGE_TYPE.COMMUNITY_TRANSFER, facilityFilter, customCombineTabData, customTabs);
    }

    let list = await Patient.find({ ...query })
        .populate([
            "insurance",
            "unit",
            "doctor",
            "snf",
            "transferToWhichAssistedLiving",
        ])
        .sort({ dateOfADT: 1 });

    const total = list.length;

    if (total > 0) {
        const transferTypeTotal = _.countBy(list, "transferType");
        totalAMA = transferTypeTotal?.AMA || 0;
        totalSNF = transferTypeTotal?.SNF || 0;
        totalSafeDischarge = transferTypeTotal?.safeDischarge || 0;
    }
    const seenPatientIds = new Set();

    let listData = [];
    if (list && list.length > 0) {
        await filterData(list, async (item) => {
            if (customTabs?.length > 0) {
                await getOtherDashboardData(item._doc, facilityFilter, diffDashboardPatients, seenPatientIds);
            }
            let latestObj = new Object();
            latestObj = item._doc;
            let wasReturned = null;
            wasReturned = await getReturnAndDidNotReturn(item._doc, facilityFilter);
            const formattedPatientData = await formatPatientData(latestObj, wasReturned);
            listData.push(formattedPatientData);
        });
    }

    const resAnalysis = await getSixtyDaysChartCount(listData);
    const sixtyDaysData = resAnalysis?.sixtyDaysDataChart ?? [];
    listData = resAnalysis?.patientListRes ?? listData;

    // Use common finalization
    const { enrichedDiffDashboardPatients, isAutomaticReportSaved } = await finalizeGetAllCount(
        diffDashboardPatients,
        customTabs,
        PAGE_TYPE.COMMUNITY_TRANSFER,
        user?.id
    );

    return {
        data: {
            customCombineTabData,
            diffDashboardPatients: enrichedDiffDashboardPatients,
            list: listData,
            sixtyDaysData,
            customTabs,
        },
        totals: {
            ...censusInfo,
            total,
            totalAMA,
            totalSNF,
            totalSafeDischarge,
            isAutomaticReportSaved
        },
    };
};

const getCardPatientChartData = async (req) => {
    const { body } = req;
    const { accountid } = req.headers;
    const {
        facilityId,
        facilityIds,
        cardFilter,
        type,
        filter: { startDate, endDate },
        relation,
        transferType,
    } = body;

    let startDateFilter = await toStartFilterDate(startDate);
    let endDateFilter = await toEndFilterDate(endDate);
    let diffDashboardPatients = await createDiffDashboardPatients();
    const customTabs = await getCustomTabsByPage({ accountid, page: PAGE_TYPE.COMMUNITY_TRANSFER, userId: req.user._id });
    let customCombineTabData = await createDiffDashboardPatients('combineTab');
    const isCustomCombineTab = customTabs?.some(tab => tab.type === CUSTOM_TAB_TYPE.COMBINE);

    let query = {
        transferType: {
            $in: [ADT_SUB_TYPES.AMA, ADT_SUB_TYPES.SNF, ADT_SUB_TYPES.SAFE_DISCHARGE],
        },
    };
    let facilityFilter = null;
    const facilityData = [];

    if (facilityIds && facilityIds.length > 0) {
        query.facilityId = { $in: facilityIds };
        facilityIds.map((ele) => {
            facilityData.push(mongoose.Types.ObjectId(ele));
        });
        facilityFilter = { facilityId: { $in: facilityData } };
    } else {
        if (facilityId) {
            facilityFilter = { facilityId: mongoose.Types.ObjectId(facilityId) };
            query.facilityId = facilityId;
        }
    }

    if (startDate && endDate) {
        query.dateOfADT = {
            $gte: startDateFilter,
            $lt: endDateFilter,
        };
    }

    if (isCustomCombineTab) {
        customCombineTabData = await getCustomCombineTabData(query, PAGE_TYPE.COMMUNITY_TRANSFER, facilityFilter, customCombineTabData, customTabs);
    }

    let relations = [...new Set([RELATIONS.FACILITY, RELATIONS.DOCTOR, RELATIONS.UNIT, RELATIONS.INSURANCE, RELATIONS.SNF, RELATIONS.TRANSFER_TO_WHICH_ASSISTED_LIVING, RELATIONS.NURSE, RELATIONS.HOSPITAL, RELATIONS.DX, RELATIONS.PAYER_SOURCE_INSURANCE])];

    let isMainData = false;
    if (type === CO_TRANSFER_CARDS_TYPE.TOTAL) {
    } else if (
        type === CO_TRANSFER_CARDS_TYPE.SNF ||
        type === CO_TRANSFER_CARDS_TYPE.SAFE_DISCHARGE ||
        type === CO_TRANSFER_CARDS_TYPE.AMA
    ) {
        query.transferType = { $in: [type] };
    } else {
        if (transferType && transferType.length > 0) {
            query.transferType = { $in: transferType };
        }
    }
    if (
        type === CO_TRANSFER_CARDS_TYPE.TOTAL ||
        type === CO_TRANSFER_CARDS_TYPE.AMA ||
        type === CO_TRANSFER_CARDS_TYPE.SAFE_DISCHARGE ||
        type === CO_TRANSFER_CARDS_TYPE.SNF
    ) {
        isMainData = true;
    }

    if (!isMainData) {
        if (
            cardFilter &&
            cardFilter.doctorData.length > 0 &&
            type !== CO_TRANSFER_CARDS_TYPE.DOCTOR_DATA
        ) {
            const doctorIds = cardFilter.doctorData.map((e) =>
                mongoose.Types.ObjectId(e)
            );
            query.doctor = { $in: doctorIds };
            //relations.push("doctor");
        }
        if (
            cardFilter &&
            cardFilter.floorsData.length > 0 &&
            type !== CO_TRANSFER_CARDS_TYPE.FLOORS_DATA
        ) {
            const floorIds = cardFilter.floorsData.map((e) =>
                mongoose.Types.ObjectId(e)
            );
            query.unit = { $in: floorIds };
            //relations.push("unit");
        }

        if (
            cardFilter &&
            cardFilter.insuranceData.length > 0 &&
            type !== CO_TRANSFER_CARDS_TYPE.INSURANCE_DATA
        ) {
            const insuranceIds = cardFilter.insuranceData.map((e) =>
                mongoose.Types.ObjectId(e)
            );
            query.insurance = { $in: insuranceIds };
            //relations.push("insurance");
        }
        if (
            cardFilter &&
            cardFilter.snfFacilityData.length > 0 &&
            type !== CO_TRANSFER_CARDS_TYPE.SNF_FACILITY_DATA
        ) {
            const snfFacilityIds = cardFilter.snfFacilityData.map((e) =>
                mongoose.Types.ObjectId(e)
            );
            query.snf = { $in: snfFacilityIds };
            //relations.push("snf");
        }
        if (
            cardFilter &&
            cardFilter.safeDischargeAssLivData.length > 0 &&
            type !== CO_TRANSFER_CARDS_TYPE.SAFE_DISCHARGE_ASS_LIV_DATA
        ) {
            const safeDischargeAssLivIds = cardFilter.safeDischargeAssLivData.map(
                (e) => mongoose.Types.ObjectId(e)
            );
            query.transferToWhichAssistedLiving = { $in: safeDischargeAssLivIds };
            //relations.push("transferToWhichAssistedLiving");
        }

        if (type === CO_TRANSFER_CARDS_TYPE.SAFE_DISCHARGE_ASS_LIV_DATA) {
            query.transferToWhichAssistedLiving = { $exists: true, $ne: null };
        }
        if (type === CO_TRANSFER_CARDS_TYPE.SNF_FACILITY_DATA) {
            query.snf = { $exists: true, $ne: null };
        }
        query = await applyCustomQuestionPatientFilter(query, cardFilter);
    }

    let patientList = [];
    let listData = [];

    patientList = await Patient.find({ ...query })
        .populate(relations)
        .sort({ dateOfADT: -1 })
        .exec();
    const seenPatientIds = new Set();

    await filterData(patientList, async (item) => {
        if (customTabs?.length > 0) {
            await getOtherDashboardData(item, facilityFilter, diffDashboardPatients, seenPatientIds);
        }
        let latestObj = new Object();
        latestObj = item._doc;
        if (relation) {
            latestObj.filterId = item[relation]?._id || null;
        }
        if (type == CO_TRANSFER_CARDS_TYPE.RETURNS_DATA) {
            latestObj.wasReturned = await getReturnAndDidNotReturn(
                item._doc,
                facilityFilter
            );
        }
        if (
            cardFilter.returnsData &&
            cardFilter.returnsData.length > 0 &&
            type !== CO_TRANSFER_CARDS_TYPE.RETURNS_DATA
        ) {
            if (cardFilter.returnsData.length == 1) {
                const selectedReturnsData = cardFilter.returnsData[0];
                let returnData;
                if (!latestObj.wasReturned) {
                    returnData = await getReturnAndDidNotReturn(
                        item._doc,
                        facilityFilter
                    );
                    if (selectedReturnsData == "Returned") {
                        if (!returnData) {
                            return false;
                        }
                    }
                    if (selectedReturnsData == "Didn't Return") {
                        if (returnData) {
                            return false;
                        }
                    }
                } else {
                    if (selectedReturnsData == "Returned") {
                        if (!latestObj.wasReturned) {
                            return false;
                        }
                    } else {
                        if (latestObj.wasReturned) {
                            return false;
                        }
                    }
                }
            }
        }
        const formattedPatientData = await formatPatientData(latestObj);
        listData.push(formattedPatientData);
    });


    let sixtyDaysData = [];
    const analysisRes = await getNinetyDaysChartCount(listData);
    sixtyDaysData = analysisRes?.ninetyDaysDataChart ?? [];
    listData = analysisRes?.patientListRes ?? listData;

    diffDashboardPatients = await enrichDiffDashboardPatients(diffDashboardPatients, customTabs);

    if (!isMainData) {
        if (
            (cardFilter.sixtyDaysData && cardFilter.sixtyDaysData.length > 0) || type === CO_TRANSFER_CARDS_TYPE.SIXTY_DAYS_DATA
        ) {
            if (type === CO_TRANSFER_CARDS_TYPE.SIXTY_DAYS_DATA) {
                listData = await ninetyDaysDataFilter(
                    ["a", "b", "c", "d"],
                    listData,
                    sixtyDaysData
                );
            } else {
                listData = await ninetyDaysDataFilter(
                    cardFilter.sixtyDaysData,
                    listData,
                    sixtyDaysData
                );
            }
        }
    }
    let censusAverage = 0;
    let censusByPeriod = [];
    let censusByFacility = []
    let bedCapacity = 0;
    let bedByFacility = [];
    let censusAsOfNowByFacility = [];

    if (startDate && endDate) {
        const facilityData = facilityIds.length > 0 ? facilityIds : [facilityId];
        if (isMainData) {
            censusByPeriod = await getCensusAverageByPeriod(
                startDateFilter,
                endDateFilter,
                facilityData,
            );
        }
        const censusInfo = await getCensusAverageInfo(
            startDateFilter,
            endDateFilter,
            facilityData,
            false,
            censusByFacility,
            bedByFacility,
            censusAsOfNowByFacility
        );
        censusAverage = censusInfo.censusAverage;
        bedCapacity = censusInfo.bedCapacity;

    }

    return { diffDashboardPatients, data: listData, censusAverage, censusByPeriod, sixtyDaysData, censusByFacility, bedByFacility, bedCapacity, censusAsOfNowByFacility, customCombineTabData };
};

module.exports = {
    getCardPatientChartData,
    getAllCount
};
