const mongoose = require("mongoose");
const { Schema, Types } = mongoose;

const PatientSchema = new Schema(
  {
    facilityId: { type: Types.ObjectId, ref: "facility" },
    firstName: String,
    lastName: String,
    type: String,
    transferType: String,
    DOB: Date,
    payerSourceInsurance: { type: Types.ObjectId, ref: "validation" },
    insurance: { type: Types.ObjectId, ref: "validation" },
    unit: { type: Types.ObjectId, ref: "validation" },
    hospital: { type: Types.ObjectId, ref: "validation" },
    doctor: { type: Types.ObjectId, ref: "validation" },
    nurse: { type: Types.ObjectId, ref: "validation" },
    snf: { type: Types.ObjectId, ref: "validation" },
    dx: [{ type: Types.ObjectId, ref: "validation" }],
    logs: [{
      description: { type: String, required: false },
      userId: { type: Types.ObjectId, ref: "user", required: false, },
      date: { type: Date, required: false, default: Date.now() },
      userName: { type: String, required: false },
    }],
    highlighter: [{
      color: {
        type: String,
        required: false
      },
      fieldName: {
        type: String,
        required: false
      },
      description: {
        type: String,
        required: false
      }
    }],
    notes: String,
    dateOfADT: Date,
    middleInitial: String,
    suffix: String,
    transferTime: String,
    wasAdmitted: Boolean,
    isHospitalPrior: Boolean,
    wasReturned: Boolean,
    admissionId: { type: Types.ObjectId, ref: "patient" },
    transferToWhichAssistedLiving: { type: Types.ObjectId, ref: "validation" },
    other: String,
    timezone: String,
    dateOfLatestAdmission: { type: Date, nullable: true },
    dateOfActualAdmission: { type: Date, nullable: true },
    createdBy: { type: Types.ObjectId, ref: "user", required: false },
  },
  {
    timestamps: true,
    strict: false,
  }
);

PatientSchema.index({ facilityId: 1, dateOfADT: -1, type: 1, DOB: 1 });

mongoose.model("patient", PatientSchema);
