export const getStaticTransferTypes = () => [
    { value: "allTransfers", label: "All Transfers", subOption: false },
    { value: "allHospitalTransfers", label: "All Hospital Transfers", subOption: false },
    { value: "unplannedHospitalTransfer", label: "Unplanned Hospital Transfer", subOption: true },
    { value: "plannedHospitalTransfer", label: "Planned Hospital Transfer", subOption: true },
    { value: "allCommunityTransfers", label: "All Community Transfers", subOption: false },
    { value: "safeDischarge", label: "Safe Discharge", subOption: true },
    { value: "SNF", label: "SNF", subOption: true },
    { value: "AMA", label: "AMA", subOption: true },
    { value: "deceased", label: "Deceased", subOption: false },
];

export const getDynamicTransferTypes = ({ allowHospitalTransfer, allowCommunityTransfer, allowDeceased }) => {
    const options = [];

    const allowAllTransfers = allowHospitalTransfer && allowCommunityTransfer && allowDeceased;
    if (allowAllTransfers) {
        options.push({ value: "allTransfers", label: "All Transfers", subOption: false });
    }

    if (allowHospitalTransfer) {
        options.push(
            { value: "allHospitalTransfers", label: "All Hospital Transfers", subOption: false },
            { value: "unplannedHospitalTransfer", label: "Unplanned Hospital Transfer", subOption: true },
            { value: "plannedHospitalTransfer", label: "Planned Hospital Transfer", subOption: true }
        );
    }

    if (allowCommunityTransfer) {
        options.push(
            { value: "allCommunityTransfers", label: "All Community Transfers", subOption: false },
            { value: "safeDischarge", label: "Safe Discharge", subOption: true },
            { value: "SNF", label: "SNF", subOption: true },
            { value: "AMA", label: "AMA", subOption: true }
        );
    }

    if (allowDeceased) {
        options.push({ value: "deceased", label: "Deceased", subOption: false });
    }

    return options;
};