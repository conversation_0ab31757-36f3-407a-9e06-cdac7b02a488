# adt-tech-be

## Environments

- `production` -> use `master` branch and deploy to AWS
- `stage` -> use `stage` branch and deploy to AWS
- `development` -> use `develop` branch and deploy to Azure

### Development

- Platform: Azure
- Branch: develop
- API URL: <https://snf-api.azurewebsites.net> (https://snf-api.snfsolutions.com).
- Web URL: https://app.snfdatasolutions.com

### Stage

- Platform: Amazon
- Branch: stage
- API URL: <https://live-api.snfdatasolutions.com>. Health endpoint: <https://live-api.snfdatasolutions.com/api/health>
- Web URL: https://live.snfdatasolutions.com

### Production

- Platform: Amazon
- Branch: master
- API URL: https://api.simplesnf.com
- Web URL: https://www.simplesnf.com

## BullMQ

- We are using AWS ElastiCache with Redis in production.
