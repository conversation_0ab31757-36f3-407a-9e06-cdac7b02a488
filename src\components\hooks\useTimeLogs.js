import { useEffect, useRef, useCallback } from "react";
import { useIdleTimer } from "react-idle-timer";
import axios from "../../axios";

const useTimeLogs = ({ accountId }) => {
	const intervalRef = useRef(null);
	const containerRef = useRef(null);

	const startLogging = useCallback(() => {
		if (!intervalRef.current && accountId && accountId !== 'null') {
			axios.post("/api/log/timelog", { accountId });
			intervalRef.current = setInterval(() => {
				axios.post("/api/log/timelog", { accountId });

				const scrollableContainer = document.getElementById("scroll-container");
				
				if (scrollableContainer) {
					if (containerRef.current) {
						containerRef.current.removeEventListener("scroll", startLogging);
					}

					containerRef.current = scrollableContainer;
					containerRef.current.addEventListener("scroll", startLogging, {
						passive: true,
					});
				}

			}, 60000); // 60 seconds
		}
	}, [accountId]);

	const stopLogging = useCallback(() => {
		clearInterval(intervalRef.current);
		intervalRef.current = null;
	}, []);

	useEffect(() => {
		startLogging();
		return () => stopLogging();
	}, [startLogging, stopLogging]);

	useIdleTimer({
		onIdle: stopLogging,
		timeout: 600000, // 10 minutes
	});

	const handleActivity = useCallback(() => {
		startLogging();

	}, [startLogging]);

	useEffect(() => {
		window.addEventListener("click", handleActivity);

		return () => {
			window.removeEventListener("click", handleActivity);

			if (containerRef.current) {
				containerRef.current.removeEventListener("scroll", startLogging);
			}
		};
	}, [handleActivity, startLogging]);

	return { startLogging, stopLogging };
};

export default useTimeLogs;
