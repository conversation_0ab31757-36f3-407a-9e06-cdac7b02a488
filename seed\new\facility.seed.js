const mongoose = require('mongoose');
const Facility = mongoose.model("facility");

const seedFacility = async (name, accountId) => {
    try {
        const facility = new Facility({
            name,
            accountId,
        });

        await facility.save();
        console.log('✅ Facility created successfully.');
        return facility;
    } catch (err) {
        console.error('❌ Error creating facility:', err.message);
        throw err;
    }
};

module.exports = {
    seedFacility,
};
