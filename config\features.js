/**
 * Feature flags configuration
 * Centralized management of feature toggles
 */

/**
 * Check if reports functionality is enabled
 * @returns {boolean} True if reports are enabled, false otherwise
 */
const isReportsEnabled = () => {
    return true //process.env.ENABLE_REPORTS !== 'false'; // Default: true
};

/**
 * Check if alerts functionality is enabled
 * @returns {boolean} True if alerts are enabled, false otherwise
 */
const isAlertsEnabled = () => {
    return true; // process.env.ENABLE_ALERTS !== 'false'; // Default: true
};

/**
 * Check if job queues should be enabled
 * Job queues are enabled when reports are enabled
 * @returns {boolean} True if job queues should be enabled
 */
const isJobQueuesEnabled = () => {
    return isReportsEnabled();
};

/**
 * Check if schedules should be enabled
 * @returns {boolean} True if any schedules should be enabled
 */
const isSchedulesEnabled = () => {
    return isReportsEnabled() || isAlertsEnabled();
};

/**
 * Get all feature flags status
 * @returns {object} Object containing all feature flags
 */
const getFeatureFlags = () => {
    return {
        reports: isReportsEnabled(),
        alerts: isAlertsEnabled(),
        jobQueues: isJobQueuesEnabled(),
        schedules: isSchedulesEnabled()
    };
};

/**
 * Log feature flags status
 */
const logFeatureFlags = () => {
    const flags = getFeatureFlags();
    console.log("\n🎛️  ===== FEATURE FLAGS STATUS =====");
    console.log(`📊 Reports: ${flags.reports ? '🟢 ENABLED' : '🔴 DISABLED'}`);
    console.log(`🚨 Alerts: ${flags.alerts ? '🟢 ENABLED' : '🔴 DISABLED'}`);
    console.log(`⚙️  Job Queues: ${flags.jobQueues ? '🟢 ENABLED' : '🔴 DISABLED'}`);
    console.log(`⏰ Schedules: ${flags.schedules ? '🟢 ENABLED' : '🔴 DISABLED'}`);

    if (flags.reports && flags.alerts) {
        console.log("✨ All features are active - full functionality available!");
    } else if (!flags.reports && !flags.alerts) {
        console.log("⚠️  All features disabled - running in minimal mode");
    } else {
        console.log("ℹ️  Partial feature set enabled - some functionality may be limited");
    }
    console.log("===================================\n");
};

module.exports = {
    isReportsEnabled,
    isAlertsEnabled,
    isJobQueuesEnabled,
    isSchedulesEnabled,
    getFeatureFlags,
    logFeatureFlags
};
