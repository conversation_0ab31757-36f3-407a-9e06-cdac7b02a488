const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');
const User = mongoose.model("user");

const createAdmin = async (email, password, roles) => {
    try {
        const adminRole = roles.find(role => role.slug === 'super'); // TODO if need admin
        if (!adminRole) {
            throw new Error('🚫 Admin role not found.');
        }

        const admin = new User({
            fullName: 'Admin',
            email: email.toLowerCase(),
            password,
            role: adminRole._id,
            jobTitle: 'Administrator',
            type: 'admin',
            hashedKey: await bcrypt.hash('someRandomKey', 10),
            accepted: true,
            avatar: 'avatar_1',
            requirePasswordReset: true
        });

        await admin.save();
        console.log('✅ Admin Admin user created successfully.');
        return admin;
    } catch (err) {
        console.error('❌ Error creating Admin Admin user:', err.message);
        throw err;
    }
};

module.exports = {
    createAdmin,
};
