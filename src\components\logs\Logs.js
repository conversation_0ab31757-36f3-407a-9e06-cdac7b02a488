import { useEffect, useState, useCallback } from "react";
import { useLocation } from "react-router";
import axios from "../../axios";
import TimeLogDisplay from "../shared/TimelogDisplay/TimelogDisplay";
import { format, subMonths, startOfMonth } from "date-fns";

const Logs = () => {
	const location = useLocation();
	const { userId, userName, accountName } = location.state || {};

	/* ----------  RANGE STATE (default = first day six months ago → today) ---------- */
	const today = new Date();
	const [range, setRange] = useState({
		start: format(startOfMonth(subMonths(today, 2)), "yyyy-MM-dd"),
		end: format(today, "yyyy-MM-dd"),
	});

	/* ----------  LOG DATA ---------- */
	const [logs, setLogs] = useState([]);
	const [isFetching, setIsFetching] = useState(false);

	const fetchLogs = useCallback(async (start, end) => {
		if (!userId) return;

		try {
			setIsFetching(true);
			const params = new URLSearchParams({
				userId,
				start,
				end,
				accountId: localStorage.getItem("accountId") || "",
			});

			const res = await axios.get(`/api/log/timeLogs?${params.toString()}`);
			setLogs(res.data);
		} catch (err) {
			console.error("Failed to fetch logs:", err);
		} finally {
			setIsFetching(false);
		}
	}, [userId]);;

	/* fetch when userId or the chosen range changes */
	useEffect(() => {
		fetchLogs(range.start, range.end);
	}, [fetchLogs, range]);

	return (
		<TimeLogDisplay
			logs={logs}
			range={range}
			onRangeChange={setRange}
			loading={isFetching}
			userName={userName}
			accountName={accountName}
		/>
	);
};

export default Logs;
