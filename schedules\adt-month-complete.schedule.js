const cron = require('node-cron');
const mongoose = require("mongoose");
const { filterAsyncData } = require('../api/utilis/common');
const moment = require("moment");
const { sendPostmarkEmail } = require('../sendEmail');
const { AUTH_ROLES } = require('../types/auth.type');
const _ = require("lodash");
const { isReportsEnabled } = require('../config/features');

// Global variable to prevent duplicate initialization
let isMonthlyScheduleInitialized = false;

// Function to initialize the monthly schedule
const initializeMonthlySchedule = () => {
    // Prevent duplicate initialization
    if (isMonthlyScheduleInitialized) {
        console.log("🔄 Monthly ADT complete schedule already initialized - skipping duplicate setup");
        return;
    }

    // Only schedule if reports are enabled
    const reportsEnabled = isReportsEnabled();

    if (reportsEnabled) {
        console.log("📅 Setting up monthly ADT completion tracking...");
        cron.schedule('0 0 * * *', async () => {
            console.log("🔄 Running monthly ADT completion check...");
            await monthlyScheduleData();
        }, {
            scheduled: true,
            timezone: "America/Atikokan"
        });
        isMonthlyScheduleInitialized = true;
        console.log("✅ Monthly ADT complete schedule initialized - daily checks at midnight EST!");
    } else {
        console.log("⚠️  Monthly ADT complete schedule disabled (reports disabled) - no completion tracking");
    }
};

// Initialize the schedule
initializeMonthlySchedule();

const monthlyScheduleData = async () => {
    try {
        const User = mongoose.model("user");
        const Census = mongoose.model("census");
        const Patient = mongoose.model("patient");
        const CompleteMonthAdt = mongoose.model("completeMonthAdt");

        const superAdminUsers = [];
        let users = await User.find({
            facilities: {
                $elemMatch: {
                    facilityId: { $exists: true },
                    "$and": [
                        { access: { $eq: true } },
                        { write: { $eq: true } },
                        { read: { $eq: true } },
                    ]
                }
            },
        })
            .populate("role")
            .exec();

        if (users && users.length > 0) {
            await filterAsyncData(users, (async (ele) => {
                const roleName = ele.role?.slug || null;
                if (_.includes([AUTH_ROLES.SUPER], roleName)) {
                    superAdminUsers.push(ele);
                }
            }));

            await filterAsyncData(users, (async (ele) => {
                const roleName = ele.role?.slug || null;
                if (_.includes([AUTH_ROLES.SUPER], roleName)) {
                    return false;
                }
                const facilityIds = [];

                await filterAsyncData(ele.facilities, ((facility) => {
                    if (facility.access || facility.write || facility.read) {
                        facilityIds.push(mongoose.Types.ObjectId(facility.facilityId));
                    }
                }));


                if (facilityIds.length > 0) {
                    let currentDate = moment().format("YYYY-MM-DD");
                    currentDate = moment.utc(currentDate).toISOString();
                    const currentStartMonthDate = moment.utc(currentDate).startOf('month').startOf("day").toDate();
                    const currentEndMonthDate = moment.utc(currentDate).endOf('month').startOf("day").toDate();

                    let latestADT = await Patient.findOne({ facilityId: { $in: [...facilityIds] } }).sort({ dateOfADT: -1, createdAt: -1 });

                    let latestADTDate = latestADT?.dateOfADT || null;

                    const completeMonthAdtSaved = await CompleteMonthAdt.findOne({ userId: mongoose.Types.ObjectId(ele?._id) });
                    if (completeMonthAdtSaved) {
                        const sameMonthSend = completeMonthAdtSaved && moment(completeMonthAdtSaved.lastEmailDate).isSame(currentEndMonthDate, 'month');

                        if (moment(currentEndMonthDate).isSameOrBefore(latestADTDate) && !sameMonthSend) {
                            const monthName = moment(currentDate).format('MMMM');
                            await sendEmail(ele, monthName, currentDate, superAdminUsers);
                            console.log("Send mail");
                            console.log("**********");

                        } else {
                            console.log("**********");
                            // Not send mail
                            console.log("Already Send mail send");
                        }
                    } else {
                        let oldestCensus = await Census.findOne({ facilityId: { $in: [...facilityIds] } }).sort({ date: 1, createdAt: 1 });
                        if (oldestCensus) {
                            const initialDate = oldestCensus.date;
                            const firstDayOfMonth = moment.utc(initialDate).startOf('month').toDate();

                            if (moment(currentStartMonthDate).isSameOrAfter(firstDayOfMonth)) {
                                if (moment(currentEndMonthDate).isSameOrBefore(latestADTDate)) {
                                    const monthName = moment(currentDate).format('MMMM');
                                    console.log("0000000000000000000");
                                    await sendEmail(ele, monthName, currentDate, superAdminUsers);
                                }
                            } else {
                                await saveCompleteMonthAdt(ele?._id, currentDate)
                            }
                        }
                    }
                }
            }));
        }
    } catch (error) {
        console.log(error, "errors here");
    }
}
const sendEmail = async (user, month, date, superAdminUsers) => {

    const action_url = `${process.env.APP_URL}`;

    let templateModel = {
        "name": user?.fullName,
        "action_url": action_url,
        "company_name": "SimpleSNF Team",
        "product_name": "SimpleSNF",
        "month": month,
        "support_url": process.env.APP_URL,
    }

    await saveCompleteMonthAdt(user?._id, date);
    if (user?.email) {
        if (process.env.NODE_ENV === "production") {
            await sendPostmarkEmail(user?.email, "complete-month-adt", templateModel);
        }
    }
    if (superAdminUsers && superAdminUsers.length > 0) {
        let admin = superAdminUsers[0];
        let adminMail = superAdminUsers[0]?.email || "<EMAIL>";
        templateModel = Object.assign(templateModel, { account_name: user?.fullName, name: admin?.fullName })
        if (process.env.NODE_ENV === "production") {
            await sendPostmarkEmail(adminMail, "complete-month-adt-super-admin", templateModel);
        }
    }
}

const saveCompleteMonthAdt = async (userId, lastEmailDate) => {
    const CompleteMonthAdt = mongoose.model("completeMonthAdt");
    const newDateOfADT = moment(lastEmailDate).format("YYYY-MM-DD");
    const latestStringDate = moment.utc(newDateOfADT).toISOString();

    const completeMonthAdtSaved = await CompleteMonthAdt.findOne({ userId: userId });
    if (completeMonthAdtSaved) {
        completeMonthAdtSaved.lastEmailDate = latestStringDate;
        completeMonthAdtSaved.isEmailSent = false;
        await completeMonthAdtSaved.save();
    } else {
        let completeMonthAdt = new CompleteMonthAdt()
        completeMonthAdt.lastEmailDate = latestStringDate;
        completeMonthAdt.userId = userId;
        completeMonthAdt.isEmailSent = false;
        await completeMonthAdt.save();
    }
}

// Cleanup function for graceful shutdown
const cleanupMonthlySchedule = () => {
    isMonthlyScheduleInitialized = false;
    console.log('Monthly schedule cleaned up');
};

module.exports = {
    monthlyScheduleData,
    saveCompleteMonthAdt,
    initializeMonthlySchedule,
    cleanupMonthlySchedule
};
