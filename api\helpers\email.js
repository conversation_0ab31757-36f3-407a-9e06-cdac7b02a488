const { sendPostmarkEmail } = require("../../sendEmail");

const sendOnboardEmail = async (user, password) => {
    const APP_URL = `${process.env.APP_URL}/#`;
    let templateModel = {
        "name": user?.fullName,
        "action_url": APP_URL,
        "company_name": "SimpleSNF Team",
        "product_name": "SimpleSNF",
        "support_url": process.env.APP_URL,
        "password": password,
        "username": user.email,
        "login_url": APP_URL,
    }
    await sendPostmarkEmail(user.email, "welcome", templateModel);
};

const sendForgotPasswordEmail = async (user, token) => {
    const APP_URL = `${process.env.APP_URL}/#/new-password/${token}`;
    let templateModel = {
        "name": user?.fullName,
        "action_url": APP_URL,
        "company_name": "SimpleSNF Team",
        "product_name": "SimpleSNF",
        "support_url": process.env.APP_URL,
    }
    await sendPostmarkEmail(user.email, "password-reset", templateModel);
};


const sendADTMonthCompleteEmail = async (user, token) => {
    const action_url = `${process.env.APP_URL}`;
    let templateModel = {
        "name": user?.fullName,
        "action_url": action_url,
        "company_name": "SimpleSNF Team",
        "product_name": "SimpleSNF",
        "support_url": process.env.APP_URL,
    }
    await sendPostmarkEmail(user.email, "password-reset", templateModel);
};

module.exports = { sendOnboardEmail, sendForgotPasswordEmail, sendADTMonthCompleteEmail };
