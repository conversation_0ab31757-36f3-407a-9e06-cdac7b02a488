const {
  getCardPatientChartData,
  getAllCount,
  getADTAllData,
  getADTDetailsPatientChartData
} = require("../helpers/admission");
const authWithRole = require("../middleware/auth-with-role");

const route = require("express").Router();

route.post("/card-patient-chart-data", authWithRole("manageDashboard"), async (req, res) => {
  try {
    let response = await getCardPatientChartData(req);
    res.send(response);
  } catch (e) {
    console.log(e, 'e here');
    
    res.status(500).send('Something went wrong');
  }
});

route.post("/view-transfer-admission-chart", authWithRole("manageDashboard"), async (req, res) => {
  try {
    let response = await getADTDetailsPatientChartData(req);
    res.send(response);
  } catch (e) {
    res.status(500).send('Something went wrong');
  }
});

route.get("/get-all-count", authWithRole("manageDashboard"), async (req, res) => {
  try {
    let response = await getAllCount(req);
    res.send(response);
  } catch (e) {
    res.status(500).send('Something went wrong');
  }
});

route.post("/get-adt-data", authWithRole("manageDashboard"), async (req, res) => {
  try {
    let response = await getADTAllData(req);
    res.send(response);
  } catch (e) {
    console.log(e, 'e');
    res.status(500).send('Something went wrong');
  }
});


module.exports = route;
