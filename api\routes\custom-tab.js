const { createCustomTab, getCustomTabs, deleteCustomTab, getCustomTabById } = require("../helpers/custom-tab");
const authWithRole = require("../middleware/auth-with-role");

const route = require("express").Router();

// Create new account
route.post("/", authWithRole("manageDashboard"), async (req, res) => {
  try {
    let account = await createCustomTab(req);
    res.send(account);
  } catch (error) {
    res.status(500).send({ message: error.message });
  }
});

// Get accounts/account by ID
route.get("/:id?", authWithRole("manageDashboard"), async (req, res) => {
  try {
    let account = await getCustomTabs(req);
    res.send(account);
  } catch (error) {    
    res.status(500).send(error);
  }
});

route.get("/tab-by-id/:id", authWithRole("manageDashboard"), async (req, res) => {
  try {
    let account = await getCustomTabById(req.params.id);
    res.send(account);
  } catch (error) {
    console.log(error, 'error');
    
    res.status(500).send(error);
  }
});

route.delete("/:id", authWithRole("manageDashboard"), async (req, res) => {
  try {
    console.log(req.params.id, 'req.params.id');
    console.log(req.user._id, 'req.user._id');
    console.log(req.user._id, 'req.user._id');
    const filter = await deleteCustomTab(req.params.id, req.user._id);
    res.send(filter);
  } catch (error) {
    console.log(error, 'error');
    res.status(500).send(error);
  }
});

module.exports = route;
