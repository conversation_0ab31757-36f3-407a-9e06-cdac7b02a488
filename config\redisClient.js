const IORedis = require('ioredis');
const configModule = require('./redis-connection');

const redisClient = new IORedis({
    host: process.env.REDIS_HOST,
    port: Number(process.env.REDIS_PORT),
    maxRetriesPerRequest: null, // ✅ Required for BullMQ
    enableReadyCheck: false,    // ✅ Helps avoid unnecessary delays
});

redisClient.on('error', (err) => {
    console.log('Redis Error:', err);
    // console.error('Redis Error:', err);
});

redisClient.on('connect', () => {
    console.log('Connected to Redis');
});

redisClient.connect(); // Connect asynchronously


module.exports = redisClient;