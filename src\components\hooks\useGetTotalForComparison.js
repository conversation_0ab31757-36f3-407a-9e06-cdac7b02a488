import { useSelector } from "react-redux";

const useGetTotalForComparison = ({
  filterListData,
  filterListDataComparison,
  averageCensus,
  averageCensusComparison,
}) => {
  const { cardFilter } = useSelector((state) => state.hospital);
  let totalTransfers;
  let totalTransfersComparison;
  const returnedValues = ["Returned", "Didn't Return"];
  const shouldCompareToAlternativeTotal =
    cardFilter.returnsData.length > 0
      ? cardFilter.returnsData.every((value) => {
          const isPresent = returnedValues.includes(value);
          if (isPresent) {
            const returnedData = cardFilter.returnsData.find((x) => x === "Returned")
              ? filterListData.returnsData.find((x) => x.id === "Returned")?.total
              : 0;
            const didntReturnData = cardFilter.returnsData.find((x) => x === "Didn't Return")
              ? filterListData.returnsData.find((x) => x.id === "Didn't Return")?.total
              : 0;
            const returnedDataComparison = cardFilter.returnsData.find((x) => x === "Returned")
              ? filterListDataComparison.returnsData.find((x) => x.id === "Returned")?.total
              : 0;
            const didntReturnDataComparison = cardFilter.returnsData.find((x) => x === "Didn't Return")
              ? filterListDataComparison.returnsData.find((x) => x.id === "Didn't Return")?.total
              : 0;

            totalTransfers = returnedData + didntReturnData;
            totalTransfersComparison = returnedDataComparison + didntReturnDataComparison;
          }

          return isPresent;
        })
      : false;

  const compareAgainst = shouldCompareToAlternativeTotal ? totalTransfers : averageCensus;
  const compareAgainstComparison = shouldCompareToAlternativeTotal ? totalTransfersComparison : averageCensusComparison;

  return { compareAgainst, compareAgainstComparison };
};

export default useGetTotalForComparison;
