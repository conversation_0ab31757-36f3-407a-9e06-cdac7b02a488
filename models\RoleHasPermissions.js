const mongoose = require('mongoose');
//const { toJSON, paginate } = require('./plugins');
//const autoIncrement = require('mongoose-auto-increment');
const { Schema, Types } = mongoose;

const roleHasPermissionsSchema = mongoose.Schema(
    {
        roleId: {
            type: Schema.Types.ObjectId,
            ref: 'role',
            required: true
        },
        permissionId: {
            type: String,
            ref: 'permission',
            required: true
        }
    }
);

// add plugin that converts mongoose to json
// roleHasPermissionsSchema.plugin(toJSON);
// roleHasPermissionsSchema.plugin(paginate);

//autoIncrement.initialize(mongoose.connection);

// roleHasPermissionsSchema.plugin(autoIncrement.plugin, {
//     model: 'roleHasPermissions',
//     startAt: 1,
//     incrementBy: 1,
// });
/**
 * @typedef RoleHasPermissions
 */
const RoleHasPermissions = mongoose.model('roleHasPermissions', roleHasPermissionsSchema);

module.exports = RoleHasPermissions;
