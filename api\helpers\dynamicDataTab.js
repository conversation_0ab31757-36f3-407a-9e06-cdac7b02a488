const mongoose = require("mongoose");
const { QUESTION_INPUT_TYPE } = require("../../types/question.type");
const DynamicDataTab = mongoose.model("dynamicDataTab");
const Question = mongoose.model("question");

const createDynamicDataTab = async (req) => {
    const user = req?.user;
    const { accountid, facilityid } = req.headers;
    const { page, type, title } = req.body;
    try {
        const reportsSubscriptionsSave = new DynamicDataTab({
            userId: mongoose.Types.ObjectId(user._id),
            accountId: mongoose.Types.ObjectId(accountid),
            facilityId: mongoose.Types.ObjectId(facilityid),
            page,
            type,
            title
        });

        const saved = await reportsSubscriptionsSave.save()
        return { status: 200, data: saved }

    } catch (err) {
        console.log(err, 'err for saving');
    }
};

const findSavedReportConfig = async (page, userId) => {
    const reportsSubscriptions = await DynamicDataTab.findOne({
        page,
        userId: mongoose.Types.ObjectId(userId),
    });
    if (reportsSubscriptions) {
        return true
    } else {
        return false
    }
}

const getQuestionsData = async (req) => {
    const { accountid } = req.headers;
    const { forType, forTransferType } = req.query;
    
    const dynamicDataTabs = await Question.aggregate([
        {
            $match: {
                accountId: mongoose.Types.ObjectId(accountid),
                ...(forType && { forType }),
                ...(forTransferType && Array.isArray(forTransferType) && forTransferType.length > 0
                    ? { forTransferType: { $in: forTransferType } }
                    : {}
                ),
                customQuestionInputType: {
                    $exists: true,
                    $nin: [QUESTION_INPUT_TYPE.RESIDENCE_LIST, QUESTION_INPUT_TYPE.DATE, QUESTION_INPUT_TYPE.TIME_TAB_RESIDENT_LIST]
                },
                isCustom: true
            }
        },
        {
            $lookup: {
                from: "validations", // The name of the Validation collection
                localField: "validationBase", // The field in the Question collection
                foreignField: "type", // The field in the Validation collection
                as: "validationOptions"
            }
        },
        {
            $project: {
                _id: 1,
                accountId: 1,
                forType: 1,
                forTransferType: 1,
                isCustom: 1,
                validationBase: 1,
                accessor: 1,
                customQuestionInputType: 1,
                customQuestionOptions: 1,
                label: 1,
                tableLabel: 1,
                templateType: 1,
                timeRangeType: 1,       
                shareCount: 1,
                isUniversalAccess: 1,
                isShared: 1,                
                validationOptions: {
                    _id: 1,
                    label: 1,
                    type: 1
                }
            }
        }
    ]);

    return dynamicDataTabs;
}

const getDynamicDataTabByPage = async (req) => {
    
    const { forType, forTransferType } = req.query;
    const { accountid } = req.headers;  

    const dynamicDataTabs = await DynamicDataTab.find({
        accountId: mongoose.Types.ObjectId(accountid),
        forType: forType,
        forTransferType: forTransferType,
        isCustom: true,
    });   

    return dynamicDataTabs;
}

const deleteDynamicDataTab = async id => {
    let deleted = await DynamicDataTab.findByIdAndDelete(id);
    return { status: 200, data: deleted }
};

const updateDynamicDataTab = async (id, data) => {
    let dynamicDataTab = await DynamicDataTab.findById(id);
    dynamicDataTab.title = data.title;
    const res = await dynamicDataTab.save();
    return { status: 200, data: res }
}

module.exports = {
    createDynamicDataTab,
    findSavedReportConfig,
    getDynamicDataTabByPage,
    deleteDynamicDataTab,
    updateDynamicDataTab,
    getQuestionsData
};
