const mongoose = require("mongoose");
const ReportLogs = mongoose.model("reportLogs");

const createReportLog = async (data) => {
    let inputValues = {};
    inputValues.data = data?.data || null
    inputValues.description = data?.description || null;
    inputValues.event = data?.event || null
    inputValues.date = data?.date || new Date();

    const reportLogsSave = new ReportLogs(inputValues);

    await reportLogsSave.save().catch(e => e);
};

module.exports = {
    createReportLog
};
