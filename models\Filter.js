const mongoose = require("mongoose");
const { Schema, Types } = mongoose;

const FilterSchema = new Schema(
  {
    accountId: { type: Types.ObjectId, ref: "account" },
    facilityId: { type: Types.ObjectId, ref: "facility" },
    facilityIds: [{ type: Types.ObjectId, ref: 'facility' }],
    userId: { type: Types.ObjectId, ref: "user" },
    name: String,
    type: String, // doctor || hospital || facility    
    filter: {
      type: Object,
      required: true
    },
  },
  { timestamps: true }
);

mongoose.model("filter", FilterSchema);
