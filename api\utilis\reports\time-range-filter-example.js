/**
 * Example usage of TIME_TAB_RANGE filtering in processDynamicCardAlerts
 * 
 * This file demonstrates how to use the time range filtering functionality
 * for alert processing with different time range configurations.
 */

const { QUESTION_INPUT_TYPE, TIME_RANGE_TYPE } = require("../../../types/common.type");

/**
 * Example 1: Using hourly time ranges for alert processing
 * This will automatically use the predefined 24-hour ranges (00:00-01:00, 01:00-02:00, etc.)
 */
const hourlyTimeRangeExample = {
    customQuestionInputType: QUESTION_INPUT_TYPE.TIME_TAB_RANGE,
    accessor: 'transferTime', // Field in patient data to filter by
    timeRangeType: TIME_RANGE_TYPE.HOURLY,
    customQuestionOptions: [], // Empty for hourly - uses predefined ranges
    label: 'Transfer Time Analysis (Hourly)',
    description: 'Analyze patient transfers by hour of day for alert monitoring'
};

/**
 * Example 2: Using custom time ranges for shift-based analysis
 * This allows you to define specific time periods that match your facility's shifts
 */
const customShiftTimeRangeExample = {
    customQuestionInputType: QUESTION_INPUT_TYPE.TIME_TAB_RANGE,
    accessor: 'transferTime',
    timeRangeType: 'custom',
    customQuestionOptions: [
        {
            label: 'Night Shift',
            startTime: '23:00',
            endTime: '07:00' // Crosses midnight
        },
        {
            label: 'Day Shift',
            startTime: '07:00',
            endTime: '15:00'
        },
        {
            label: 'Evening Shift',
            startTime: '15:00',
            endTime: '23:00'
        }
    ],
    label: 'Transfer Time Analysis (Shifts)',
    description: 'Analyze patient transfers by nursing shifts for alert monitoring'
};

/**
 * Example 3: Custom business hours analysis
 * Useful for analyzing patterns during specific operational periods
 */
const businessHoursTimeRangeExample = {
    customQuestionInputType: QUESTION_INPUT_TYPE.TIME_TAB_RANGE,
    accessor: 'admissionTime',
    timeRangeType: 'custom',
    customQuestionOptions: [
        {
            label: 'Business Hours',
            startTime: '08:00',
            endTime: '17:00'
        },
        {
            label: 'After Hours',
            startTime: '17:00',
            endTime: '08:00' // Crosses midnight
        },
        {
            label: 'Weekend Emergency',
            startTime: '00:00',
            endTime: '23:59'
        }
    ],
    label: 'Admission Time Analysis (Business Hours)',
    description: 'Monitor admission patterns during business vs after hours'
};

/**
 * Example usage in alert processing:
 * 
 * const alertItem = hourlyTimeRangeExample;
 * const patientData = [
 *   { _id: '1', transferTime: '14:30', ... },
 *   { _id: '2', transferTime: '02:15', ... },
 *   { _id: '3', transferTime: '09:45', ... }
 * ];
 * const objectCustomRes = {};
 * 
 * await processDynamicCardAlerts(alertItem, patientData, objectCustomRes);
 * 
 * // Result will be in objectCustomRes['transferTime']
 * // Each time range will have:
 * // {
 * //   _id: 'timeRangeLabel',
 * //   label: 'timeRangeLabel', 
 * //   total: numberOfPatientsInRange,
 * //   patientIds: [arrayOfPatientIds],
 * //   percentage: percentageOfTotal
 * // }
 */

/**
 * Expected output structure for time range filtering:
 */
const expectedOutputExample = [
    {
        _id: 'Night Shift',
        id: 'Night Shift',
        label: 'Night Shift',
        name: 'Night Shift',
        total: 5, // Number of patients transferred during night shift
        value: 5,
        key: 'Night Shift',
        originalTotal: 5,
        percentage: '25.0', // Percentage of total transfers
        patientIds: ['patient1', 'patient2', 'patient3', 'patient4', 'patient5'],
        level: 0
    },
    {
        _id: 'Day Shift',
        id: 'Day Shift',
        label: 'Day Shift',
        name: 'Day Shift',
        total: 12,
        value: 12,
        key: 'Day Shift',
        originalTotal: 12,
        percentage: '60.0',
        patientIds: ['patient6', 'patient7', /* ... more patient IDs */],
        level: 0
    },
    {
        _id: 'Evening Shift',
        id: 'Evening Shift',
        label: 'Evening Shift',
        name: 'Evening Shift',
        total: 3,
        value: 3,
        key: 'Evening Shift',
        originalTotal: 3,
        percentage: '15.0',
        patientIds: ['patient18', 'patient19', 'patient20'],
        level: 0
    }
];

/**
 * Tips for using time range filtering in alerts:
 * 
 * 1. Field Selection: Make sure the 'accessor' field exists in your patient data
 *    Common fields: 'transferTime', 'admissionTime', 'dischargeTime'
 * 
 * 2. Time Format: Times should be in 24-hour format (HH:MM)
 *    Examples: '09:30', '14:15', '23:45'
 * 
 * 3. Midnight Crossing: For ranges that cross midnight (e.g., 23:00 to 07:00),
 *    the system automatically handles the logic
 * 
 * 4. Alert Thresholds: You can set up alerts based on:
 *    - High activity during specific time ranges
 *    - Unusual patterns compared to historical data
 *    - Imbalanced distribution across shifts
 * 
 * 5. Performance: Hourly ranges provide more granular data but may be slower
 *    Custom ranges are more efficient for specific business needs
 */

module.exports = {
    hourlyTimeRangeExample,
    customShiftTimeRangeExample,
    businessHoursTimeRangeExample,
    expectedOutputExample
};
