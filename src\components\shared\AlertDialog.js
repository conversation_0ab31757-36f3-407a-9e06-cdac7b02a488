import * as React from 'react';
import Button from '@mui/material/Button';
import Dialog from '@mui/material/Dialog';
import DialogActions from '@mui/material/DialogActions';
import DialogContent from '@mui/material/DialogContent';
import DialogContentText from '@mui/material/DialogContentText';
import DialogTitle from '@mui/material/DialogTitle';
import { Box } from '@mui/material';

export default function AlertDialog({
    handleClose,
    content = '',
    title = '',
    handleSubmit,
    isConfirm = true,
    onlyOkay = false,
    maxWidth = 'sm',
    withYesNo = false,
    isArchived 
}) {

    return (
        <Dialog
            sx={{ zIndex: 13000 }}
            maxWidth={maxWidth}
            open={true}
            hideBackdrop={true}
            onClose={handleClose}
            aria-labelledby="alert-dialog-title"
            aria-describedby="alert-dialog-description"
        >
            {title && (
                <DialogTitle id="alert-dialog-title">
                    {title}
                </DialogTitle>
            )}
            <DialogContent>
                <DialogContentText id="alert-dialog-description">
                    {content}
                </DialogContentText>
            </DialogContent>
            <DialogActions>
                {withYesNo ? (
                    <Box sx={{ display: 'flex', justifyContent: 'center', gap: 2, width: '100%' }}>
                        <Button
                            onClick={handleClose}
                            variant={isArchived !== undefined && isArchived === false ? 'contained' : 'outlined'}                            
                        >
                            No
                        </Button>
                        <Button
                            onClick={handleSubmit}
                            variant='outlined'
                            color='primary'                            
                        >
                            Yes
                        </Button>
                    </Box>
                ) : (
                    <>
                        {onlyOkay ? (
                            <Button onClick={handleClose} variant='contained' color='primary'>Okay</Button>
                        ) : (
                            <>
                                <Button onClick={handleClose}>Cancel</Button>
                                {isConfirm && (
                                    <Button onClick={handleSubmit} autoFocus>
                                        Confirm
                                    </Button>
                                )}
                            </>
                        )}
                    </>
                )}


            </DialogActions >
        </Dialog >
    );
}
