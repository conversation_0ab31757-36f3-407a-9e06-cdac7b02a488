import { useEffect, useMemo, useState } from "react";
import { useSortBy, useTable } from "react-table";
import NewCloseSVG from "../../../../assets/svgs/new-close.svg";
import axios from "../../../../axios";
import { toDisplayTime } from "../../../../utilis/date-formats";
import styles from "./ResidentDetails.module.scss";

function Table({ columns, data, setSelectedResident }) {
	const { getTableProps, getTableBodyProps, headerGroups, rows, prepareRow, setSortBy } = useTable(
		{
			columns,
			data,
		},
		useSortBy
	);

	return (
		<div className={styles.tableWrpr}>
			<table {...getTableProps()}>
				<thead className={`ffmar fs12 ttuc`}>
					{headerGroups.map((headerGroup) => (
						<tr {...headerGroup.getHeaderGroupProps()}>
							{headerGroup.headers.map((column) => (
								<th
									{...column.getHeaderProps(column.getSortByToggleProps())}
									{...(column.isSortable
										? {
												onClick: () => {
													//set sort desc, aesc or none?
													const desc =
														column.isSortedDesc === true ? undefined : column.isSortedDesc === false ? true : false;
													setSortBy([
														{ id: column.id, desc },
														{ id: "createdAt", desc },
													]);
												},
										  }
										: {})}
								>
									{column.render("Header")}
									<span>
										{column.isSorted
											? column.isSortedDesc
												? // ? " 🔽"
												  // : " 🔼"
												  " 🔽"
												: " 🔼"
											: ""}
									</span>
								</th>
							))}
						</tr>
					))}
				</thead>
				<tbody {...getTableBodyProps()}>
					{rows.map((row, i) => {
						prepareRow(row);
						return (
							<tr className={`ffmar fs15`} {...row.getRowProps()} onClick={() => setSelectedResident(row.original)}>
								{row.cells.map((cell) => {
									return <td {...cell.getCellProps()}>{cell.render("Cell")}</td>;
								})}
							</tr>
						);
					})}
				</tbody>
			</table>
		</div>
	);
}

const ResidentDetails = ({ details, close, tableColumns }) => {
	// eslint-disable-next-line
	const columns = useMemo(() => [
		{
			Header: "Last Name",
			accessor: "lastName",
			disableSortBy: true,			
		},
		{
			Header: "First Name",
			accessor: "firstName",
			disableSortBy: true,			
		},
		{
			Header: "MIddle Initial",
			accessor: "middleInitial",
			disableSortBy: true,			
		},
		{
			Header: "suffix",
			accessor: "suffix",
			disableSortBy: true,
		},
		{
			Header: "DOB",
			accessor: "DOB",
			disableSortBy: true,
			Cell: ({ value }) => <div>{toDisplayTime(value)}</div>,
		},

		{
			Header: "Type",
			accessor: "type",
			disableSortBy: true,
			Cell: ({ row }) => (
				<div>
					{row.original.type === "transfer"
						? row.original.transferType
								.replace(/([A-Z])/g, " $1")
								// uppercase the first character
								.replace(/^./, function (str) {
									return str.toUpperCase();
								})
						: row.original.type.replace(/^./, function (str) {
								return str.toUpperCase();
						  })}
				</div>
			),
		},
	]);

	const [list, setList] = useState([]);

	const getUser = async () => {
		let data = await axios.post(
			"/api/patient/details-relations",
			{
				...details,
			},
			{ headers: { "x-auth": `${localStorage.getItem("x-auth")}` } }
		);

		setList(data.data || []);
	};

	useEffect(() => {
		getUser();
		// eslint-disable-next-line
	}, []);

	return (
		<div className={styles.residentDetailsWrpr}>
			<div className={`${styles.residentDetailsContent}`}>
				<div className={`df aic ${styles.hdr}`}>
					<p className={`df aic ffmar fs15 fw700`}>
						Resident details {details.lastName} {details.firstName} {details.middleInitial} {details.suffix}
					</p>
					<div className={`df acc mla ${styles.closeBtn}`} onClick={close}>
						<NewCloseSVG />
					</div>
				</div>
				<div className={`${styles.tableSec}`}>
					<Table columns={tableColumns ? tableColumns : columns} data={list} />					
				</div>
			</div>
		</div>
	);
};

export default ResidentDetails;
