/**
 * Configure all API routes
 */
const setupRoutes = (app) => {
    // Import all route modules
    const account = require("../api/routes/account");
    const quickGlace = require("../api/routes/quickGlace");
    const user = require("../api/routes/user");
    const facility = require("../api/routes/facility");
    const patient = require("../api/routes/patient");
    const validation = require("../api/routes/validation");
    const question = require("../api/routes/question");
    const log = require("../api/routes/logs");
    const census = require("../api/routes/census");
    const hospital = require("../api/routes/hospital");
    const admission = require("../api/routes/admission");
    const filter = require("../api/routes/filter");
    const role = require("../api/routes/role");
    const deceased = require("../api/routes/deceased");
    const overall = require("../api/routes/overall");
    const communityTransfer = require("../api/routes/community-transfer");
    const reportsSubscription = require("../api/routes/reportsSubscription");
    const customCard = require("../api/routes/custom-card");
    const customTab = require("../api/routes/custom-tab");
    const percentageAgainst = require("../api/routes/percentageAgainst");
    const settings = require("../api/routes/settings");
    const facilityManuallyEndOfADT = require("../api/routes/facilityManuallyEndOfADT");
    const alertReport = require("../api/routes/alertReport");
    const note = require("../api/routes/notes");
    const dynamicDataTab = require("../api/routes/dynamicDataTab");
    const customTabShare = require("../api/routes/custom-tab-sharing");

    // Core API routes
    app.use("/api/account", account);
    app.use("/api/user", user);
    app.use("/api/facility", facility);
    app.use("/api/patient", patient);
    app.use("/api/role", role);

    // Data and analytics routes
    app.use("/api/overall", overall);
    app.use("/api/deceased", deceased);
    app.use("/api/quick-glace", quickGlace);
    app.use("/api/community-transfer", communityTransfer);
    app.use("/api/hospital", hospital);
    app.use("/api/admission", admission);
    app.use("/api/census", census);

    // Validation and question routes
    app.use("/api/validation", validation);
    app.use("/api/question", question);

    // Logging and filtering routes
    app.use("/api/log", log);
    app.use("/api/filter", filter);

    // Report and subscription routes
    app.use("/api/report-subscription", reportsSubscription);
    app.use("/api/percentage-against", percentageAgainst);
    app.use("/api/alerts-reports", alertReport);

    // Custom tab and card routes
    app.use("/api/custom-card", customCard);
    app.use("/api/custom-tab", customTab);
    app.use("/api/custom-tab-sharing", customTabShare);
    app.use("/api/dynamic-data-tab", dynamicDataTab);

    // Settings and configuration routes
    app.use("/api/settings", settings);
    app.use("/api/facility-manually-end-date-adt", facilityManuallyEndOfADT);
    app.use("/api/note", note);

    // Utility routes
    app.get("/api/version", (req, res) => res.send("v0.1.0"));
    app.get("/api/test", (req, res) => res.send("Test successful"));

    console.log("All routes configured successfully");
};

module.exports = { setupRoutes };
