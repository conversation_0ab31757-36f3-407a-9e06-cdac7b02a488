const mongoose = require("mongoose");
const { Schema, Types } = mongoose;

const SettingsSchema = new Schema(
  {
    accountId: { type: Types.ObjectId, ref: "account" },
    facilityId: { type: Types.ObjectId, ref: "facility", required: false },
    userId: { type: Types.ObjectId, ref: "user", required: false },
    type: { type: String, required: false }, // setting type (e.g. excelColumn, table, shortcut)
    pageType: { type: String, required: false },
    subPageType: { type: String, required: false },
    selectedColumns: { type: Array, required: false },
    selectedTab: { type: String, required: false },
    endDateOfADT: { type: Date, required: false, default: null },
    data: { type: Object, required: false },
  },
  { timestamps: true }
);

mongoose.model("settings", SettingsSchema);
