const Sentry = require("@sentry/node");

/**
 * Initialize Sentry for error tracking and performance monitoring
 */
const initSentry = (app) => {
    Sentry.init({
        dsn: "https://<EMAIL>/4505433086885888",
        integrations: [
            new Sentry.Integrations.Http({ tracing: true }),
            new Sentry.Integrations.Express({ app }),
            ...Sentry.autoDiscoverNodePerformanceMonitoringIntegrations(),
        ],
        environment: process.env.NODE_ENV,
        tracesSampleRate: 1.0,
    });
    
    console.log("Sentry initialized successfully");
};

/**
 * Apply Sentry middleware to Express app
 */
const applySentryMiddleware = (app) => {
    app.use(Sentry.Handlers.requestHandler());
    app.use(Sentry.Handlers.tracingHandler());
    app.use(Sentry.Handlers.errorHandler());
    
    // Custom error handler
    app.use(function onError(err, req, res, next) {
        res.statusCode = 500;
        res.end(res.sentry + "\n");
    });
    
    console.log("Sentry middleware applied successfully");
};

module.exports = { initSentry, applySentryMiddleware };
