import React from 'react';
import { Box, IconButton, Checkbox, ListItemText, MenuItem } from '@mui/material';
import DeleteIcon from '@mui/icons-material/Delete';
import LabeledSelect from './LabeledSelect';

const FilterItem = ({
    cardOptions = [],
    itemOptions = [],
    selectedCard = '',
    selectedItems = [],
    onCardChange,
    onItemsChange,
    onRemove,
    isNotMainCard = false,
    showRemove = false,
    cardError = false,
    itemsError = false,
    renderItemMenu,
    children,
    ...props
}) => (
    <Box display="flex" gap={2} flexDirection={{ xs: 'column', sm: 'row' }} mt={2} alignItems="flex-start">
        <LabeledSelect
            label="Select Card"
            value={selectedCard}
            onChange={onCardChange}
            options={cardOptions}
            error={cardError}
            size="medium"
            sx={{ flex: 1 }}
        />
        {isNotMainCard && (
            <LabeledSelect
                label="Select Items"
                value={selectedItems}
                onChange={onItemsChange}
                multiple
                error={itemsError}
                renderValue={(selected) =>
                    (Array.isArray(selected) ? selected : [])
                        .map((val) => itemOptions.find((i) => i.value === val)?.label || val)
                        .join(', ') || 'No items selected'
                }
                size="medium"
                sx={{ flex: 2 }}
            >
                {renderItemMenu
                    ? renderItemMenu()
                    : children
                        ? children
                        : [
                            <MenuItem value="__all__" dense sx={{ minHeight: 36 }} key="__all__">
                                <Checkbox
                                    size="small"
                                    checked={selectedItems.length === itemOptions.length && itemOptions.length > 0}
                                    indeterminate={selectedItems.length > 0 && selectedItems.length < itemOptions.length}
                                />
                                <ListItemText
                                    primaryTypographyProps={{ fontSize: 14 }}
                                    primary={selectedItems.length === itemOptions.length ? 'Deselect All' : 'Select All'}
                                />
                            </MenuItem>,
                            ...itemOptions.map((item) => (
                                <MenuItem key={item.value} value={item.value} dense sx={{ minHeight: 36 }}>
                                    <Checkbox size="small" checked={selectedItems.includes(item.value)} />
                                    <ListItemText primaryTypographyProps={{ fontSize: 16 }} primary={item.label} />
                                </MenuItem>
                            ))
                        ]}
            </LabeledSelect>
        )}
        {showRemove && (
            <IconButton
                aria-label="Remove Filter"
                color="error"
                size="small"
                onClick={onRemove}
                sx={{ mt: 1, width: 36, height: 36, padding: 1 }}
            >
                <DeleteIcon fontSize="small" />
            </IconButton>
        )}
    </Box>
);

export default FilterItem; 