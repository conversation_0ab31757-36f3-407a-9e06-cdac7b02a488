import { useEffect, useState } from "react";
import {
    <PERSON><PERSON>, DialogTitle, DialogContent,
    Button, Box,
    Typography,
    Paper,
    IconButton,
    DialogActions,
    CircularProgress
} from "@mui/material";
import DeleteIcon from '@mui/icons-material/Delete';
import { getAllCardForCustomTab } from "../../../services/hospital.service";
import { DEFAULT_CARD_FILTER } from "../../../store/reducers/hospital.slice";
import { DEFAULT_CARD_FILTER as DEFAULT_CARD_FILTER_ADMISSION } from "../../../store/reducers/admission.slice";
import { updateFilterListData } from "../../../utilis/hospital-common";
import { updateFilterListData as updateFilterListDataCommunity } from "../../../utilis/community-common";
import { updateFilterListData as updateFilterListDataDeceased } from "../../../utilis/deceased-common";
import { DEFAULT_COMMUNITY_DATA } from "../../../store/reducers/community-transfer.slice";
import { getAllCardForCommunity } from "../../../services/community-transfer.service";
import { getAllCardForDeceased } from "../../../services/deceased.service";
import { DEFAULT_DECEASED_DATA } from "../../../store/reducers/deceased.slice";
import { getAllCardForAdmissions } from "../../../services/admission.service";
import { updateFilterListDataBoth } from "../../../utilis/admission-common";
import { HOSPITAL_CARDS_LABELS, HOSPITAL_CARDS_TYPE } from "../../../types/hospital.type";
import { ADMISSION_CARDS_LABELS, ADMISSION_CARDS_TYPE } from "../../../types/admission.type";
import { CO_TRANSFER_CARDS_TYPE, COMMUNITY_CARD_LABELS } from "../../../types/community-transfer.type";
import { DECEASED_CARDS_LABELS, DECEASED_CARDS_TYPE } from "../../../types/deceased.type";
import { createCustomTab, getDynamicDataTabById } from "../../../services/custom-tab.service";
import { useDispatch, useSelector } from "react-redux";
import { ADD_NOTIFICATION } from "../../../store/types";
import { getQuestionsData } from "../../../services/dynamic-data-tab.service";
import { ADT_SUB_TYPES } from "../../../types/common.type";
import { setIsCustomTabAdded } from "../../../store/reducers/common.slice";
import moment from "moment";
import ErrorPopover from '../custom-popover/ErrorPopover';
import LabeledSelect from './LabeledSelect';
import LabeledTextField from './LabeledTextField';
import LabeledTextarea from './LabeledTextarea';
import DialogActionsRow from './DialogActionsRow';
import ItemAutocomplete from "./ItemAutocomplete";
import { selectedTabAccess } from "../../../utilis/common";
import { PAGE_TYPE } from "../../../types/pages.type";

const validPageTypes = Object.entries(PAGE_TYPE).filter(
    ([key]) => key !== "OVERALL" && key !== "ADT"
);

const CARD_CONFIG = {
    [PAGE_TYPE.HOSPITAL]: [
        { value: HOSPITAL_CARDS_TYPE.UNPLANNED, label: HOSPITAL_CARDS_LABELS.unplannedHospitalTransfer },
        { value: HOSPITAL_CARDS_TYPE.PLANNED, label: HOSPITAL_CARDS_LABELS.plannedHospitalTransfer },
        { value: HOSPITAL_CARDS_TYPE.TOTAL, label: HOSPITAL_CARDS_LABELS.total },
    ],
    [PAGE_TYPE.COMMUNITY_TRANSFER]: [
        { value: CO_TRANSFER_CARDS_TYPE.AMA, label: COMMUNITY_CARD_LABELS.AMA },
        { value: CO_TRANSFER_CARDS_TYPE.SNF, label: COMMUNITY_CARD_LABELS.SNF },
        { value: CO_TRANSFER_CARDS_TYPE.SAFE_DISCHARGE, label: COMMUNITY_CARD_LABELS.safeDischarge },
        { value: CO_TRANSFER_CARDS_TYPE.TOTAL, label: COMMUNITY_CARD_LABELS.total },
    ],
    [PAGE_TYPE.DECEASED]: [
        { value: DECEASED_CARDS_TYPE.TOTAL, label: DECEASED_CARDS_LABELS.total },
    ],
    [PAGE_TYPE.ADMISSION]: [
        { value: ADMISSION_CARDS_TYPE.ADMISSION, label: ADMISSION_CARDS_LABELS.admission },
        { value: ADMISSION_CARDS_TYPE.READMISSION, label: ADMISSION_CARDS_LABELS.readmission },
        { value: ADMISSION_CARDS_TYPE.TOTAL, label: ADMISSION_CARDS_LABELS.total },
    ],
};

const PAGE_CONFIG = {
    [PAGE_TYPE.HOSPITAL]: {
        cardFilter: DEFAULT_CARD_FILTER,
        getCards: getAllCardForCustomTab,
        updateFn: updateFilterListData,
        query: {
            forType: "transfer",
            forTransferType: ["hospitalTransfer"],
            isCustom: true,
        },
    },
    [PAGE_TYPE.COMMUNITY_TRANSFER]: {
        cardFilter: DEFAULT_COMMUNITY_DATA,
        getCards: getAllCardForCommunity,
        updateFn: updateFilterListDataCommunity,
        query: {
            forType: "transfer",
            forTransferType: [ADT_SUB_TYPES.AMA, ADT_SUB_TYPES.SAFE_DISCHARGE, ADT_SUB_TYPES.SNF],
            isCustom: true,
        },
    },
    [PAGE_TYPE.DECEASED]: {
        cardFilter: DEFAULT_DECEASED_DATA,
        getCards: getAllCardForDeceased,
        updateFn: updateFilterListDataDeceased,
        query: {
            forType: "transfer",
            forTransferType: ["deceased"],
            isCustom: true,
        },
    },
    [PAGE_TYPE.ADMISSION]: {
        cardFilter: DEFAULT_CARD_FILTER_ADMISSION,
        getCards: getAllCardForAdmissions,
        updateFn: updateFilterListDataBoth,
        query: {
            isCustom: true,
        },
    },
};

const LABEL_MAP = {
    [PAGE_TYPE.ADMISSION]: ADMISSION_CARDS_LABELS,
    [PAGE_TYPE.COMMUNITY_TRANSFER]: COMMUNITY_CARD_LABELS,
    [PAGE_TYPE.DECEASED]: DECEASED_CARDS_LABELS,
    [PAGE_TYPE.HOSPITAL]: HOSPITAL_CARDS_LABELS,
};

const AddCustomTabDialog = ({ handleClose, filter, page, id = null, isCreator: isCreatorProp = true }) => {
    const selectedTab = selectedTabAccess();
    const isEditMode = Boolean(id);
    const [dataPointCount, setDataPointCount] = useState(1);
    const [inputValues, setInputValues] = useState([""]);
    const [cardItemData, setCardItemData] = useState([]);
    const [selectedCardItems, setSelectedCardItems] = useState([
        {
            filters: [
                {
                    card: { value: "", label: "", questionId: null },
                    items: [],
                    isMainCard: false
                }
            ],
            dashboard: ""
        }
    ]);
    const [cachedData, setCachedData] = useState({});
    const [errorPopover, setErrorPopover] = useState({ open: false, message: "" });
    const dispatch = useDispatch();
    const { isCustomTabAdded } = useSelector((state) => state.common);
    const currentUser = useSelector((state) => state.auth);
    const [title, setTitle] = useState("");
    const [description, setDescription] = useState("");
    const [errorMessages, setErrorMessages] = useState([]);
    const [fieldErrors, setFieldErrors] = useState({
        title: false,
        dataPoints: [], // For each data point
    });
    const [isSaving, setIsSaving] = useState(false);
    const [titleError, setTitleError] = useState(false);
    const [isCreator, setIsCreator] = useState(isCreatorProp); // Initialize with prop
    const [isLoading, setIsLoading] = useState(true);

    useEffect(() => {
        if (isEditMode && id) {
            const fetchTabDetails = async () => {
                try {
                    setIsLoading(true);
                    // Get the tab details
                    const tabData = await getDynamicDataTabById(id);

                    if (tabData) {
                        // Use the prop instead of checking
                        setIsCreator(isCreatorProp);

                        // Pre-fill the form data
                        setTitle(tabData.title || "");
                        setDescription(tabData.description || "");
                        if (tabData.filters) {
                            setSelectedCardItems(tabData.filters);
                            setDataPointCount(tabData.filters.length);
                            setInputValues(tabData.filters.map(f => f.dashboard));
                        }
                    }
                } catch (error) {
                    console.error("Error fetching tab details:", error);
                    dispatch({
                        type: ADD_NOTIFICATION,
                        payload: {
                            type: "error",
                            label: "Failed to load tab details",
                            id: "tabLoadError",
                        },
                    });
                } finally {
                    setIsLoading(false);
                }
            };
            fetchTabDetails();
        } else {
            // For new tabs, use the prop value
            setIsCreator(isCreatorProp);
            setIsLoading(false);
        }
    }, [id, isEditMode, currentUser, dispatch, isCreatorProp]);

    useEffect(() => {
        if (isEditMode) {
            const fetchTabData = async () => {
                try {
                    const response = await getDynamicDataTabById(id);
                    const tabData = response;

                    if (tabData) {
                        setTitle(tabData.title || "");
                        setDescription(tabData.description || "");

                        const filters = tabData.filters || [];
                        setDataPointCount(filters.length || 1);

                        const inputValuesEdit = [];
                        const newCachedData = [];
                        const pendingFilters = [];

                        await Promise.all(
                            filters.map(async (filterGroup, index) => {
                                const dashboard = filterGroup.dashboard;
                                inputValuesEdit.push(dashboard);

                                // Set loading=true before fetch
                                setCardItemData((prev) => {
                                    const updated = [...prev];
                                    updated[index] = { loading: true };
                                    return updated;
                                });

                                let result = cachedData[dashboard];
                                if (!result) {
                                    result = await fetchCardAndItemOptions(dashboard);
                                    newCachedData[dashboard] = result;
                                }

                                // Set loading=false after fetch
                                setCardItemData((prev) => {
                                    const updated = [...prev];
                                    updated[index] = { loading: false, ...result };
                                    return updated;
                                });

                                const filtersArray = (filterGroup.filters || []).map((filter, filterIndex) => {
                                    if (!filter.card?.value && filter.card?.label) {
                                        filter.card.value = filter.card.label;
                                    }

                                    return {
                                        card: filter.card,
                                        items: filter.items || [],
                                        isMainCard:
                                            index === filters.length - 1 &&
                                            filterIndex === (filterGroup.filters.length - 1),
                                    };
                                });

                                pendingFilters[index] = {
                                    dashboard,
                                    filters: filtersArray,
                                };
                            })
                        );

                        // Wait for a small delay to ensure cardItemData is updated
                        setTimeout(() => {
                            setSelectedCardItems(pendingFilters);
                        }, 100);

                        setCachedData((prevCache) => ({ ...prevCache, ...newCachedData }));
                        setInputValues(inputValuesEdit);
                    }
                } catch (err) {
                    console.error("Failed to fetch tab data", err);
                }
            };

            fetchTabData();
        }
    }, [id]); // eslint-disable-line react-hooks/exhaustive-deps

    async function fetchCardAndItemOptions(dataPoint) {
        const cards = [];
        const items = [];

        const config = PAGE_CONFIG[dataPoint];
        if (!config) return { cards, items };

        const updatedFilter = {
            ...filter,
            isSkipCustomCard: true,
            ...(filter?.startDate && filter?.endDate
                ? {
                    startDate: moment(filter.endDate).subtract(30, "days").toDate(),
                    endDate: filter.endDate,
                }
                : {}),
        };

        const res = await config.getCards(updatedFilter);

        const query = { page, ...config.query };
        const dynamicCards = await getQuestionsData(query);

        const labelMap = LABEL_MAP[dataPoint] ?? {};
        const cardFilter = config.cardFilter;

        let result;

        if (dataPoint === PAGE_TYPE.ADMISSION) {
            result = await config.updateFn(
                { ...res?.data, dynamicCards },
                cardFilter,
                null,
                cardFilter?.mainPriorityData,
                [],
                false
            );
        } else if (dataPoint === PAGE_TYPE.HOSPITAL) {
            result = await config.updateFn(
                cardFilter,
                null,
                { ...res, dynamicCards },
                cardFilter?.priorityData,
                [],
                false
            );
        } else {
            result = await config.updateFn(
                cardFilter,
                { ...res?.data, dynamicCards },
                cardFilter?.priorityData,
                [],
                false
            );
        }

        const latestListData = dataPoint === PAGE_TYPE.ADMISSION ? result?.patientList : result;
        if (!latestListData) return { cards, items };

        for (const [key, value] of Object.entries(latestListData)) {
            if (!Array.isArray(value) || /_?customtab/i.test(key)) continue;

            const dynamicCard = dynamicCards.find(dc => dc.accessor === key);
            const label = dynamicCard?.label ?? dynamicCard?.tableLabel ?? labelMap[key] ?? key;

            cards.push({
                value: key,
                label,
                questionId: dynamicCard?._id ?? null,
                dataPoint,
            });

            for (const ele of value) {
                if (ele?.isSpacialItem) continue;

                items.push({
                    value: ele._id,
                    label: ele?.label ?? ele?.name ?? ele?.title,
                    parentId: key,
                    questionId: dynamicCard?._id ?? null,
                });
            }
        }

        if (CARD_CONFIG[dataPoint]) {
            cards.push(...CARD_CONFIG[dataPoint].map(card => ({
                ...card,
                questionId: null,
                dataPoint,
                isTotalCard: true,
            })));
        }

        return { cards, items };
    }

    const handleCountChange = (e) => {
        const value = e.target.value;

        // Allow clearing the input (intermediate empty state)
        if (value === "") {
            setDataPointCount("");
            return;
        }

        const newCount = parseInt(value);
        if (isNaN(newCount) || newCount < 0) return;

        setDataPointCount(newCount);

        setInputValues((prev) =>
            newCount > prev.length ? [...prev, ...Array(newCount - prev.length).fill("")] : prev.slice(0, newCount)
        );

        setCardItemData((prev) =>
            newCount > prev.length
                ? [...prev, ...Array(newCount - prev.length).fill({ loading: false, filters: [{ items: [], card: { value: "", label: "", questionId: null }, isMainCard: false }] })]
                : prev.slice(0, newCount)
        );

        setSelectedCardItems((prev) =>
            newCount > prev.length
                ? [...prev, ...Array(newCount - prev.length).fill({ filters: [{ card: "", items: [] }] })]
                : prev.slice(0, newCount)
        );
    };

    const handleInputChange = async (index, value) => {
        // Update input values
        const updatedInputs = [...inputValues];
        updatedInputs[index] = value;

        setInputValues(updatedInputs);

        // Reset the selected filters for this data point because the page type has changed
        setSelectedCardItems((prev) => {
            const updated = [...prev];
            updated[index] = {
                filters: [{ items: [], card: { value: "", label: "", questionId: null }, isMainCard: false }],
                dashboard: value,
            };
            return updated;
        });

        // Use cached if exists
        if (cachedData[value]) {
            setCardItemData((prev) => {
                const updated = [...prev];
                updated[index] = { loading: false, ...cachedData[value] };
                return updated;
            });
            return;
        }

        // Set loading while fetching
        setCardItemData((prev) => {
            const updated = [...prev];
            updated[index] = { loading: true, filters: [{ items: [], card: { value: "", label: "", questionId: null }, isMainCard: false }] };
            return updated;
        });

        // Fetch and set new data
        const result = await fetchCardAndItemOptions(value);
        setCachedData((prevCache) => ({ ...prevCache, [value]: result }));

        setCardItemData((prev) => {
            const updated = [...prev];
            updated[index] = { loading: false, ...result };
            return updated;
        });
    };


    const handleCardItemChange = (dpIndex, filterIndex, field, value) => {
        const isMainCard = dpIndex === dataPointCount - 1 && filterIndex === (selectedCardItems[dpIndex]?.filters.length - 1);

        setSelectedCardItems((prev) => {
            const updated = [...prev];

            // Ensure the data point exists
            const existingDataPoint = updated[dpIndex] || {
                filters: [],
                dashboard: inputValues[dpIndex],
            };

            // Ensure filters array is properly cloned
            const filters = [...(existingDataPoint.filters || [])];

            // Ensure the filter at filterIndex exists with safe defaults
            filters[filterIndex] = filters[filterIndex] || {
                card: { value: "", label: "", questionId: null },
                items: [],
                isMainCard,
            };

            if (field === "card") {
                filters[filterIndex] = {
                    ...filters[filterIndex],
                    card: value,
                    items: [], // Reset items when changing card
                    isMainCard,
                };
            } else if (field === "items") {
                filters[filterIndex] = {
                    ...filters[filterIndex],
                    items: value,
                    isMainCard,
                };
            }

            // Assign the updated filters back to the data point
            existingDataPoint.filters = filters;
            existingDataPoint.dashboard = inputValues[dpIndex]; // Ensure dashboard always syncs

            updated[dpIndex] = existingDataPoint;
            return updated;
        });
    };

    const handleConfirm = async () => {
        const errors = [];
        const newFieldErrors = {
            title: false,
            dataPoints: [],
        };

        if (dataPointCount < 1) {
            errors.push("At least one data point is required.");
        }

        for (let i = 0; i < dataPointCount; i++) {
            let dataPointError = {
                dashboard: false,
                filters: [],
            };

            if (!selectedCardItems[i]?.dashboard) {
                errors.push(`Please select a data point for Data Point ${i + 1}`);
                dataPointError.dashboard = true;
            }

            const filters = selectedCardItems[i]?.filters || [];

            filters.forEach((filter, filterIndex) => {
                let filterError = {
                    card: false,
                    items: false,
                };

                if (!filter?.card?.value) {
                    errors.push(`Please select a card for Filter ${filterIndex + 1} in Data Point ${i + 1}`);
                    filterError.card = true;
                }

                if (filter.items.length === 0) {
                    if (!filter?.card?.isTotalCard && !filter.isMainCard) {
                        errors.push(`Please select at least one item for Filter ${filterIndex + 1} in Data Point ${i + 1}`);
                        filterError.items = true;
                    }
                }

                dataPointError.filters.push(filterError);
            });

            newFieldErrors.dataPoints.push(dataPointError);
        }

        if (!title.trim()) {
            errors.push("Title is required.");
            newFieldErrors.title = true;
        }

        if (errors.length > 0) {
            setErrorMessages(errors);
            setErrorPopover({ open: true });
            setFieldErrors(newFieldErrors);
            setTitleError(newFieldErrors.title);
            return;
        }

        setIsSaving(true);
        try {
            let res = await createCustomTab({
                page,
                filters: selectedCardItems,
                title: title.trim(),
                description: description.trim(),
                id,
                isEditMode
            });

            if (res?.status === 403) {
                errors.push(res.message);
                setErrorMessages(errors);
                setErrorPopover({ open: true });
                return;
            }

            if (res?.status === 404) {
                errors.push(res.message);
                setErrorMessages(errors);
                setErrorPopover({ open: true });
                return;
            }

            if (res?.status === 400) {
                errors.push(res.message);
                setErrorMessages(errors);
                setErrorPopover({ open: true });
                setTitleError(true);
                return;
            }

            if (res?.status === 500) {
                errors.push(res.message);
                setErrorMessages(errors);
                setErrorPopover({ open: true });
                return;
            }

            if (res) {
                dispatch({
                    type: ADD_NOTIFICATION,
                    payload: {
                        type: "success",
                        label: isEditMode ? "Custom tab updated successfully!" : "Custom tab added successfully!",
                        id: "customTabAdded",
                    },
                });
            }
            dispatch(setIsCustomTabAdded(!isCustomTabAdded));
            handleClose();
        } catch (error) {
            console.error('Error saving custom tab:', error);
            dispatch({
                type: ADD_NOTIFICATION,
                payload: {
                    type: "error",
                    label: "Failed to save custom tab. Please try again.",
                    id: "customTabError",
                },
            });
        } finally {
            setIsSaving(false);
        }
    };

    const addMoreCardItem = (dpIndex) => {
        setSelectedCardItems((prev) => {
            const updated = [...prev];

            // Ensure existing data point
            const existingDataPoint = updated[dpIndex] || { filters: [], dashboard: inputValues[dpIndex] };

            existingDataPoint.filters = [...(existingDataPoint.filters || [])];

            // Add new filter
            existingDataPoint.filters.push({ card: { value: "", label: "", questionId: null }, items: [], isMainCard: false });

            // After adding, loop through all filters and set isMainCard only for last filter of last DP
            updated.forEach((dp, dpI) => {
                if (!dp) return;
                dp.filters = dp.filters.map((filter, filterI) => ({
                    ...filter,
                    isMainCard: dpI === dataPointCount - 1 && filterI === (dp.filters.length - 1),
                }));
            });

            updated[dpIndex] = existingDataPoint;
            return updated;
        });
    };

    const handleRemoveFilter = (cardIndex, filterIndex) => {
        const updated = [...selectedCardItems];
        updated[cardIndex].filters = updated[cardIndex].filters.filter((_, i) => i !== filterIndex);
        setSelectedCardItems(updated); // Assuming you're using useState
    };

    // Add new function to check if any data point is loading
    const isAnyDataPointLoading = () => {
        return cardItemData.some(data => data?.loading);
    };

    // Add handler for title change to clear error
    const handleTitleChange = (e) => {
        setTitle(e.target.value);
        if (titleError) {
            setTitleError(false);
        }
    };

    return (
        <>
            <ErrorPopover
                open={errorPopover.open}
                onClose={() => setErrorPopover({ open: false })}
                errorMessages={errorMessages}
            />

            <Dialog open={true} onClose={handleClose} maxWidth="md" fullWidth>
                <DialogTitle>
                    {isEditMode
                        ? (isCreator ? 'Edit Custom Tab' : 'View Custom Tab')
                        : 'Create Custom Tab'
                    }
                </DialogTitle>
                <DialogContent>
                    {isLoading ? (
                        <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: '200px' }}>
                            <CircularProgress />
                        </Box>
                    ) : (
                        <>
                            <LabeledSelect
                                label="Number of Data Points"
                                value={dataPointCount}
                                onChange={handleCountChange}
                                options={[
                                    { label: '1', value: 1 },
                                    { label: '2', value: 2 },
                                    { label: '3', value: 3 }
                                ]}
                                error={false}
                                sx={{ mt: 2, width: 160 }}
                                size="medium"
                                disabled={!isCreator}
                            />

                            {inputValues.map((val, index) => (
                                <Paper key={index} elevation={1} sx={{ p: 2, borderRadius: 2, backgroundColor: "#fafafa" }}>
                                    <Typography variant="subtitle1" gutterBottom>
                                        Data Point {index + 1}
                                    </Typography>

                                    <LabeledSelect
                                        label="Select Data Point"
                                        value={val}
                                        onChange={(e) => handleInputChange(index, e.target.value)}
                                        options={(() => {
                                            const filteredOptions = validPageTypes.filter(([_, value]) => selectedTab.includes(value));
                                            if (index === 0) {
                                                return filteredOptions
                                                    .filter(([_, value]) => value === page)
                                                    .map(([label, value]) => ({
                                                        label: label.charAt(0).toUpperCase() + label.slice(1).toLowerCase(),
                                                        value,
                                                    }));
                                            } else {
                                                return filteredOptions.map(([label, value]) => ({
                                                    label: label.charAt(0).toUpperCase() + label.slice(1).toLowerCase(),
                                                    value,
                                                }));
                                            }
                                        })()}
                                        error={fieldErrors?.dataPoints[index]?.dashboard}
                                        disabled={!isCreator}
                                    />

                                    {(selectedCardItems[index]?.filters || []).map((filter, filterIndex) => {
                                        const isNotMainCard = index !== dataPointCount - 1 || filterIndex !== (selectedCardItems[index]?.filters.length - 1);
                                        const isMainCard = index === dataPointCount - 1 && filterIndex === (selectedCardItems[index]?.filters.length - 1);

                                        return (
                                            <Box key={filterIndex} display="flex" gap={2} flexDirection={{ xs: "column", sm: "row" }} mt={2}>
                                                <LabeledSelect
                                                    disabled={!isCreator}
                                                    label="Select Card"
                                                    value={filter?.card?.value || ""}
                                                    onChange={(ele) => {
                                                        const selectedValue = ele.target.value;
                                                        const selectedCard = cardItemData[index]?.cards?.find((card) => card.value === selectedValue);
                                                        handleCardItemChange(index, filterIndex, "card", selectedCard, isMainCard);
                                                    }}
                                                    options={cardItemData[index]?.cards
                                                        ?.filter((card) => {
                                                            const selectedValues = (selectedCardItems[index]?.filters || [])
                                                                .filter((_, i) => i !== filterIndex)
                                                                .map((f) => f.card?.value);
                                                            return !selectedValues.includes(card.value);
                                                        })
                                                        ?.map(card => ({ label: card.label, value: card.value })) || []}
                                                    error={fieldErrors?.dataPoints[index]?.filters[filterIndex]?.card}
                                                    size="medium"
                                                />

                                                {isNotMainCard && !filter?.card?.isTotalCard && (
                                                    <ItemAutocomplete
                                                        label="Select Items"
                                                        value={Array.isArray(filter?.items) ? filter.items : []}
                                                        onChange={(e, newValue) => {
                                                            // Handled inside the component already — you can skip if you're using handleCardItemChange there
                                                        }}
                                                        error={fieldErrors?.dataPoints[index]?.filters[filterIndex]?.items}
                                                        helperText=""
                                                        cardItemData={cardItemData}
                                                        index={index}
                                                        filterIndex={filterIndex}
                                                        filter={filter}
                                                        isMainCard={isMainCard}
                                                        handleCardItemChange={handleCardItemChange}
                                                        fieldErrors={fieldErrors}
                                                        disabled={!isCreator}
                                                    />
                                                )}

                                                {selectedCardItems[index]?.filters.length > 1 && (
                                                    <IconButton
                                                        aria-label="Remove Filter"
                                                        color="error"
                                                        size="small"
                                                        onClick={() => handleRemoveFilter(index, filterIndex)}
                                                        sx={{
                                                            mt: 1,
                                                            width: 36,
                                                            height: 36,
                                                            padding: 1,
                                                            alignSelf: "flex-start" // If inside a flex, make sure it's aligned properly
                                                        }}
                                                    >
                                                        <DeleteIcon fontSize="small" />
                                                    </IconButton>
                                                )}
                                            </Box>
                                        )
                                    })}

                                    {isCreator && (
                                        <Button
                                            variant="text"
                                            onClick={() => addMoreCardItem(index)}
                                            sx={{ mt: 2 }}
                                        >
                                            Add More Filter
                                        </Button>
                                    )}
                                </Paper>
                            ))}

                            <LabeledTextField
                                label="Tab Title"
                                fullWidth
                                margin="normal"
                                value={title}
                                onChange={handleTitleChange}
                                sx={{ mt: 5 }}
                                error={titleError}
                                helperText={titleError ? "Title already exists or is required" : ""}
                                disabled={!isCreator}
                            />

                            <LabeledTextarea
                                label="Description"
                                minRows={4}
                                maxRows={8}
                                value={description}
                                onChange={(e) => setDescription(e.target.value)}
                                placeholder="Enter description"
                                style={{
                                    width: '100%',
                                    padding: '8px 12px',
                                    fontSize: '14px',
                                    borderRadius: '4px',
                                    borderColor: '#ccc',
                                    resize: 'vertical',
                                }}
                                disabled={!isCreator}
                            />
                        </>
                    )}
                </DialogContent>

                {isCreator ? (
                    <DialogActionsRow
                        onCancel={handleClose}
                        onSave={handleConfirm}
                        loading={isSaving}
                        disabled={isSaving || (isEditMode && isAnyDataPointLoading())}
                        cancelText="Cancel"
                        saveText="Save"
                        loadingText="Saving..."
                    />
                ) : (
                    <DialogActions>
                        <Button onClick={handleClose}>Close</Button>
                    </DialogActions>
                )}
            </Dialog>
        </>
    );
};

export default AddCustomTabDialog;
