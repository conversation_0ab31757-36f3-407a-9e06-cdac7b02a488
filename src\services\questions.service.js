import { getAllQuestions, getAccountQuestionsAPI, saveSelectedQuestionsAPI, removeArchivedQuestionsAPI } from "./api/question.api";

const getQuestions = async (type) => {
    try {
        const res = await getAllQuestions(type);
        return res;
    } catch (e) {
        console.log(e);
    }
};

const getAccountQuestions = async (type) => {
    try {
        const res = await getAccountQuestionsAPI(type);
        return res;
    } catch (e) {
        console.log(e);
    }
};

const saveSelectedQuestions = async (selectedQuestions) => {
    try {
        const res = await saveSelectedQuestionsAPI(selectedQuestions);
        return res;
    } catch (e) {
        console.log(e);
    }
};

const removeArchivedQuestions = async (selectedQuestions) => {
    try {
        const res = await removeArchivedQuestionsAPI(selectedQuestions);
        return res;
    } catch (e) {
        console.log(e);
    }
};


export {
    getQuestions,
    getAccountQuestions,
    saveSelectedQuestions,
    removeArchivedQuestions
}
