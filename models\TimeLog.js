const mongoose = require("mongoose");
const { Schema, Types } = mongoose;

const TimeLogSchema = new Schema(
	{
		userId: { type: Types.ObjectId, ref: "user" },
		accountId: { type: Types.ObjectId, ref: "account" },
		dates: [
			{
				date: String,
				times: [String],
			},
		],
	},
	{
		timestamps: true,
	}
);

// TimeLogSchema.index({ createdAtMinuteMark: 1, userId: 1 }, { unique: true });

mongoose.model("timeLog", TimeLogSchema);
