import { useCallback, useMemo } from 'react';
import { PAGE_TYPE } from '../../../../../types/pages.type';
import { getPositivePlusSign, itemPercentage } from '../../../../../utilis/common';
import { calculateDiffBetweenPercentages, pickComparisonColor } from '../../../../../utilis/percentage-comparison';

/**
 * Custom hook for handling percentage calculations and comparisons
 * @param {Object} config - Configuration object
 * @returns {Object} - Object containing calculation functions
 */
export const usePercentageCalculations = ({
	page,
	averageCensus,
	averageCensusComparison,
	admissionCompareAgainst,
	admissionCompareAgainstComparison,
	reverseColors,
	reverseColorsAdmissionPage,
	filter,
	filterComparison,
	projectionDays,
	computedValues
}) => {
	const { isSpecialPage, isPlusSign } = computedValues;

	// Memoized calculation for admission page
	const calculateAdmissionComparison = useCallback((item, itemComparison) => {
		const itemTotal = itemPercentage(item.total, admissionCompareAgainst, "number");
		const itemTotalComparison = itemPercentage(
			itemComparison?.total,
			admissionCompareAgainstComparison,
			"number"
		);

		const {
			percentageDiff,
			itemTotalComparisonScaled,
			numberOfDays
		} = calculateDiffBetweenPercentages(
			itemTotal,
			itemTotalComparison,
			reverseColorsAdmissionPage,
			filter,
			filterComparison,
			itemComparison?.total,
			projectionDays
		);

		return {
			numberOfDays,
			comparingAgainstScaled: itemTotalComparisonScaled,
			comparisonColor: pickComparisonColor(percentageDiff, isSpecialPage)
		};
	}, [
		admissionCompareAgainst,
		admissionCompareAgainstComparison,
		reverseColorsAdmissionPage,
		filter,
		filterComparison,
		projectionDays,
		isSpecialPage
	]);

	// Memoized calculation for other pages
	const calculateOtherPagesComparison = useCallback((item, itemComparison) => {
		const percentageOfAverageCensus = itemPercentage(item.total, averageCensus, "number");
		const percentageOfAverageCensusComparison = itemPercentage(
			itemComparison?.total || 0,
			averageCensusComparison,
			"number"
		);

		const {
			percentageDiff,
			itemTotalComparisonScaled,
			numberOfDays
		} = calculateDiffBetweenPercentages(
			percentageOfAverageCensus,
			percentageOfAverageCensusComparison,
			reverseColors,
			filter,
			filterComparison,
			itemComparison?.total,
			projectionDays
		);

		return {
			numberOfDays,
			comparingAgainstScaled: itemTotalComparisonScaled,
			comparisonColor: pickComparisonColor(percentageDiff, isSpecialPage)
		};
	}, [
		averageCensus,
		averageCensusComparison,
		reverseColors,
		filter,
		filterComparison,
		projectionDays,
		isSpecialPage
	]);

	// Main calculation function
	const calculateItemComparison = useCallback((item, itemComparison, comparingAgainstDifferentFacility) => {
		try {
			// Input validation
			if (!item) {
				console.warn('calculateItemComparison: item is required');
				return {
					numberOfDays: 0,
					comparingAgainstScaled: 0,
					comparisonColor: null
				};
			}

			let result;

			// Calculate based on page type
			if (page === PAGE_TYPE.ADMISSION) {
				result = calculateAdmissionComparison(item, itemComparison);
			} else {
				result = calculateOtherPagesComparison(item, itemComparison);
			}

			// Apply plus sign formatting if needed
			if (isPlusSign && result.comparingAgainstScaled) {
				result.comparingAgainstScaled = getPositivePlusSign(result.comparingAgainstScaled);
			}

			// Override comparison color if comparing against different facility
			if (comparingAgainstDifferentFacility) {
				result.comparisonColor = null;
			}

			return result;
		} catch (error) {
			console.error('Error in calculateItemComparison:', error);
			return {
				numberOfDays: 0,
				comparingAgainstScaled: 0,
				comparisonColor: null
			};
		}
	}, [
		page,
		calculateAdmissionComparison,
		calculateOtherPagesComparison,
		isPlusSign
	]);

	// Return the calculation functions
	return useMemo(() => ({
		calculateItemComparison,
		calculateAdmissionComparison,
		calculateOtherPagesComparison
	}), [
		calculateItemComparison,
		calculateAdmissionComparison,
		calculateOtherPagesComparison
	]);
};

export default usePercentageCalculations; 