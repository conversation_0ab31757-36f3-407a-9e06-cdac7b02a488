import React from 'react';
import { DialogActions, Button } from '@mui/material';
import CustomTabLoadingButton from './CustomTabLoadingButton';

const DialogActionsRow = ({
    onCancel,
    onSave,
    loading = false,
    disabled = false,
    cancelText = 'Cancel',
    saveText = 'Save',
    loadingText = 'Saving...',
    ...props
}) => (
    <DialogActions {...props}>
        <Button
            onClick={onCancel}
            disabled={loading}
        >
            {cancelText}
        </Button>
        <CustomTabLoadingButton
            variant="contained"
            onClick={onSave}
            loading={loading}
            disabled={disabled || loading}
            loadingText={loadingText}
        >
            {saveText}
        </CustomTabLoadingButton>
    </DialogActions>
);

export default DialogActionsRow; 