import _ from "lodash";
import { HOSPITAL_CARDS_LABELS, HOSPITAL_CARDS_TYPE } from "../types/hospital.type";
import {
    insuranceData,
    floorsData,
    DCERDataFilter,
    hospitalizationsFilter,
    returnsDataFilter,
    ninetyDaysDataFilter,
    dcErData,
    hospitalizationData,
    returnsData,
    ninetyDaysDataList,
    daysData,
    dxData,
    nurseData,
    doctorData,
    shiftData,
    customHospitalData
} from "./hospital-common";
import { filterCustomPatientData, itemPercentage, processDynamicCard } from "./common";
import { CO_TRANSFER_CARDS_TYPE, COMMUNITY_CARD_LABELS } from "../types/community-transfer.type";
import { safeDischargeAssLivData, sixtyDaysDataList, snfFacilityData } from "./community-common";
import { CUSTOM_TAB_PAGE_TYPE, PAGE_TYPE } from "../types/pages.type";
import { ADT_SUB_TYPES, ADT_TYPES, CUSTOM_TAB_TYPE } from "../types/common.type";
import { DECEASED_CARDS_LABELS, DECEASED_CARDS_TYPE } from "../types/deceased.type";
import { ADMISSION_CARDS_LABELS, ADMISSION_CARDS_TYPE } from "../types/admission.type";

// Constants for better maintainability
const TRANSFER_TYPES = [
    CO_TRANSFER_CARDS_TYPE.AMA,
    CO_TRANSFER_CARDS_TYPE.SAFE_DISCHARGE,
    CO_TRANSFER_CARDS_TYPE.SNF,
    HOSPITAL_CARDS_TYPE.UNPLANNED,
    HOSPITAL_CARDS_TYPE.PLANNED
];

const ADMISSION_TYPES = [
    ADMISSION_CARDS_TYPE.ADMISSION,
    ADMISSION_CARDS_TYPE.READMISSION
];

const SIMPLE_CARD_KEYS = [
    HOSPITAL_CARDS_TYPE.UNPLANNED,
    HOSPITAL_CARDS_TYPE.PLANNED,
    HOSPITAL_CARDS_TYPE.TOTAL,
    CO_TRANSFER_CARDS_TYPE.SNF,
    CO_TRANSFER_CARDS_TYPE.AMA,
    CO_TRANSFER_CARDS_TYPE.SAFE_DISCHARGE,
    CO_TRANSFER_CARDS_TYPE.TOTAL,
    DECEASED_CARDS_TYPE.TOTAL,
    ADMISSION_CARDS_TYPE.ADMISSION,
    ADMISSION_CARDS_TYPE.READMISSION,
    ADMISSION_CARDS_TYPE.TOTAL
];

// Input validation helper
function validateFilterInputs(patientData, customTab) {
    if (!Array.isArray(patientData)) {
        throw new Error('patientData must be an array');
    }

    if (!customTab || typeof customTab !== 'object') {
        throw new Error('customTab is required and must be an object');
    }

    if (!Array.isArray(customTab.filters)) {
        throw new Error('customTab.filters must be an array');
    }
}

// Optimized simple card key checker using Set for O(1) lookup
const simpleCardKeysSet = new Set(SIMPLE_CARD_KEYS);
function isSimpleCardKey(key) {
    return simpleCardKeysSet.has(key);
}

export async function totalSafeDischarge(data, type) {
    const totalCount = data.filter(
        d => d.type === "transfer" && d.transferType === type
    )?.length;
    return totalCount || 0;
}

export async function processFilterForCombineTab({
    filter,
    patientData,
    ninetyDaysData,
    result,
    customTab,
    sixtyDaysData,
    pageType,
    customCombineTabData = [],
    forComparison,
    totalFilterData,
}) {
    const filters = customTab?.filters ?? [];
    const customKey = customTab?.accessor;
    let totalForPercentage = totalFilterData?.totalForPercentage;

    let resultData = [{
        _id: customKey,
        id: customKey,
        label: customTab?.label,
        name: customTab?.label,
        total: 0,
        value: 0,
        totalByPage: {},
        parentCard: customTab?.page,
        patientIds: [],
        percentage: 0
    }];



    for (const filter of filters) {
        let filterDashboard = filter?.dashboard;

        if (filterDashboard === pageType || (pageType === PAGE_TYPE.OVERALL && (filterDashboard === CUSTOM_TAB_PAGE_TYPE.OVERALL_INCOMING || filterDashboard === CUSTOM_TAB_PAGE_TYPE.OVERALL_OUTGOING))) {
            for (const filterItem of filter?.filters) {
                const { card, items = [] } = filterItem;
                if (!card?.value) continue;

                if (card?.questionId) {
                    let objectCustomRes = {};

                    const question = filterItem?.question;
                    const key = card?.value;

                    await processDynamicCard(question, patientData, objectCustomRes, { ...totalFilterData });

                    const resultItems = objectCustomRes?.[key] || [];

                    const patientIds = resultItems.reduce((acc, ele) => {
                        if (_.includes(items, ele?._id)) {
                            acc.push(...(ele?.patientIds || []));
                        }
                        return acc;
                    }, []);

                    patientData = await filterCustomPatientData(
                        patientData,
                        { [key]: items },
                        {
                            type: key,
                            patientIds,
                            question: { customQuestionInputType: question.customQuestionInputType },
                        },
                        true
                    );
                } else {
                    patientData = await applyFilter(card?.value, items, patientData, ninetyDaysData, sixtyDaysData, forComparison);
                }
            }

            const total = patientData?.length || 0;
            const percentageTotal = totalForPercentage || total;
            resultData[0].total += total;
            resultData[0].value += total;
            resultData[0].patientIds = patientData?.map(item => String(item._id));
            resultData[0].totalByPage = {
                ...resultData[0].totalByPage,
                [filterDashboard]: {
                    total,
                    percentage: itemPercentage(total, percentageTotal, "number")
                }
            };
        }
        if (filterDashboard !== pageType && !(pageType === PAGE_TYPE.OVERALL && (filterDashboard === CUSTOM_TAB_PAGE_TYPE.OVERALL_INCOMING || filterDashboard === CUSTOM_TAB_PAGE_TYPE.OVERALL_OUTGOING))) {
            let dashboardData = customCombineTabData[filterDashboard]?.list ?? [];
            const dashboardNinetyDays = customCombineTabData[filterDashboard]?.ninetyDaysData ?? [];
            const dashboardSixtyDays = customCombineTabData[filterDashboard]?.sixtyDaysData ?? [];

            let objectCustomRes = {};

            for (const filterItem of filter?.filters) {
                const { card, items = [] } = filterItem;

                if (!card?.value) continue;



                if (card?.questionId) {
                    const question = filterItem?.question;
                    const key = card?.value;

                    if (!question) continue;

                    await processDynamicCard(question, dashboardData, objectCustomRes, { ...totalFilterData });

                    const resultItems = objectCustomRes?.[key] || [];

                    const patientIds = resultItems.reduce((acc, ele) => {
                        if (_.includes(items, ele?._id)) {
                            acc.push(...(ele?.patientIds || []));
                        }
                        return acc;
                    }, []);
                    dashboardData = filterCustomPatientData(
                        dashboardData,
                        { [key]: items },
                        {
                            type: key,
                            patientIds,
                            question: { customQuestionInputType: question.customQuestionInputType },
                        },
                        true
                    );
                } else {
                    dashboardData = await applyFilter(card?.value, items, dashboardData, dashboardNinetyDays, dashboardSixtyDays, forComparison);
                }
            }

            const total = dashboardData?.length || 0;

            const percentageTotal = totalForPercentage || total;
            resultData[0].total += total;
            resultData[0].value += total;
            resultData[0].totalByPage = {
                ...resultData[0].totalByPage,
                [filterDashboard]: {
                    total,
                    percentage: itemPercentage(total, percentageTotal, "number")
                }
            };
        }

    }
    if (resultData.length > 0) {
        resultData[0].percentage = itemPercentage(resultData[0].value, totalForPercentage || resultData[0].total, "number");
    }
    result[customKey] = resultData;

    return patientData;
}


export async function processFilterForTab({
    filter,
    patientData,
    totalFilterData,
    dynamicCards,
    ninetyDaysData,
    newSavedFilters,
    isCheckNewFilters,
    objectCustom,
    result,
    customTab,
    sixtyDaysData,
    pageType,
    diffDashboardPatients,
    forComparison,
    level
}) {
    const { filters = [] } = filter;

    for (const ele of filters) {
        const { card, isMainCard, items = [] } = ele;
        const key = card?.value;

        if (!key) continue;

        if (isMainCard) {
            const customKey = customTab?.accessor;
            const currentCardFilter = isCheckNewFilters && _.includes(newSavedFilters, customKey);

            if (card?.questionId) {
                if (currentCardFilter) {
                    result[customKey] = objectCustom?.[customKey];
                } else {
                    let objectCustomRes = {};
                    const question = filter?.question ?? dynamicCards?.find(({ _id }) => _id === card?.questionId);

                    if (pageType !== filter.dashboard) {
                        const patientIds = new Set(patientData.map(item => item._id));
                        const dashboardWiseData = diffDashboardPatients[filter.dashboard]?.list ?? [];
                        const filteredPatients = dashboardWiseData.filter(item => patientIds.has(item.refPatientId));

                        await processDynamicCard(question, filteredPatients, objectCustomRes, { ...totalFilterData, isOtherDashboard: true });

                        if (!_.isEmpty(objectCustomRes)) {
                            result[customKey] = objectCustomRes?.[key];
                        }
                    } else {
                        if (level > 0) {
                            const dashboardWiseData = diffDashboardPatients[filter.dashboard]?.list ?? [];
                            const patientIdsMainSet = new Set(patientData.map(item => item._id));
                            const filteredPatients = dashboardWiseData
                                .filter(item => item.level === level && patientIdsMainSet.has(item.refPatientId));
                            await processDynamicCard(
                                question,
                                filteredPatients,
                                objectCustomRes, {
                                ...totalFilterData,
                                level,
                                isOtherDashboard: true
                            }
                            );
                            if (!_.isEmpty(objectCustomRes)) {
                                result[customKey] = objectCustomRes?.[key];
                            }
                        } else {
                            await processDynamicCard(question, patientData, objectCustomRes, { ...totalFilterData, level });
                            if (!_.isEmpty(objectCustomRes)) {
                                result[customKey] = objectCustomRes?.[key];
                            }
                        }
                    }
                }
            } else {
                if (currentCardFilter) {
                    result[customKey] = objectCustom?.[customKey];
                } else {
                    if (pageType === filter.dashboard) {
                        if (level > 0) {
                            const dashboardWiseData = diffDashboardPatients[filter.dashboard]?.list ?? [];
                            const patientIdsMainSet = new Set(patientData.map(item => item._id));

                            const filteredPatients = dashboardWiseData
                                .filter(item => item.level === level && patientIdsMainSet.has(item.refPatientId));

                            result[customKey] = await handleMainCard(
                                key,
                                filteredPatients,
                                {
                                    ...totalFilterData,
                                    level: level,
                                    isOtherDashboard: true,
                                    filterDashboard: filter.dashboard
                                },
                                diffDashboardPatients[filter.dashboard]?.ninetyDaysData ?? [],
                                diffDashboardPatients[filter.dashboard]?.ninetyDaysData?.sixtyDaysData ?? [],
                            );
                        } else {
                            result[customKey] = await handleMainCard(
                                key,
                                patientData,
                                {
                                    ...totalFilterData,
                                    level,
                                    filterDashboard: filter.dashboard
                                },
                                ninetyDaysData,
                                sixtyDaysData
                            );
                        }
                    } else {
                        const patientIds = new Set(patientData.map(item => item._id));
                        const dashboardWiseData = diffDashboardPatients[filter.dashboard]?.list ?? [];
                        let filteredPatients = []
                        filteredPatients = dashboardWiseData?.filter(item => item.level === (level + 1) && patientIds.has(item.refPatientId));
                        result[customKey] = await handleMainCard(
                            key,
                            filteredPatients,
                            { ...totalFilterData, isOtherDashboard: true, level: (level + 1), filterDashboard: filter.dashboard },
                            diffDashboardPatients[filter.dashboard]?.ninetyDaysData,
                            diffDashboardPatients[filter.dashboard]?.sixtyDaysData
                        );
                    }
                }
            }
        } else {
            if (card?.questionId) {
                const question = filter?.question ?? dynamicCards?.find(({ _id }) => _id === card?.questionId);
                if (!question) continue;

                let objectCustomRes = {};

                if (pageType !== filter.dashboard) {
                    const patientIdsRef = new Set(patientData.map(item => item._id));
                    const dashboardWiseData = diffDashboardPatients[filter.dashboard]?.list ?? [];
                    let filteredPatients = dashboardWiseData.filter(item => patientIdsRef.has(item.refPatientId));

                    await processDynamicCard(question, filteredPatients, objectCustomRes, { ...totalFilterData });

                    const resultItems = objectCustomRes?.[key] || [];

                    const patientIds = resultItems.reduce((acc, ele) => {
                        if (_.includes(items, ele?._id)) {
                            acc.push(...(ele?.patientIds || []));
                        }
                        return acc;
                    }, []);

                    filteredPatients = filterCustomPatientData(
                        filteredPatients,
                        { [key]: items },
                        {
                            type: key,
                            patientIds,
                            question: { customQuestionInputType: question.customQuestionInputType },
                        },
                        true
                    );
                    const patientIdsRefOther = filteredPatients.map(item => item.refPatientId);
                    patientData = patientData.filter(item => patientIdsRefOther.includes(item._id));
                } else {
                    if (level > 0) {
                        let dashboardWiseData = diffDashboardPatients[filter.dashboard]?.list ?? [];
                        dashboardWiseData = dashboardWiseData?.filter(item => item.level === level);
                        const patientIdsMainSet = new Set(patientData.map(item => item._id));
                        dashboardWiseData = dashboardWiseData
                            .filter(item => item.level === level && patientIdsMainSet.has(item.refPatientId));


                        await processDynamicCard(question, dashboardWiseData, objectCustomRes, { ...totalFilterData, level, isOtherDashboard: true });

                        const resultItems = objectCustomRes?.[key] || [];

                        const patientIds = resultItems.reduce((acc, ele) => {
                            if (_.includes(items, ele?._id)) {
                                acc.push(...(ele?.patientIds || []));
                            }
                            return acc;
                        }, []);

                        dashboardWiseData = await filterCustomPatientData(
                            dashboardWiseData,
                            { [key]: items },
                            {
                                type: key,
                                patientIds,
                                question: { customQuestionInputType: question.customQuestionInputType },
                            },
                            true
                        );
                        const patientIdsRefOther = dashboardWiseData.map(item => item.refPatientId);
                        patientData = patientData.filter(item => patientIdsRefOther.includes(item._id));
                    } else {
                        await processDynamicCard(question, patientData, objectCustomRes, { ...totalFilterData });

                        const resultItems = objectCustomRes?.[key] || [];

                        const patientIds = resultItems.reduce((acc, ele) => {
                            if (_.includes(items, ele?._id)) {
                                acc.push(...(ele?.patientIds || []));
                            }
                            return acc;
                        }, []);

                        patientData = await filterCustomPatientData(
                            patientData,
                            { [key]: items },
                            {
                                type: key,
                                patientIds,
                                question: { customQuestionInputType: question.customQuestionInputType },
                            },
                            true
                        );
                    }
                }
            } else {
                if (pageType === filter.dashboard) {
                    if (level > 0) {
                        const dashboardWiseData = diffDashboardPatients[filter.dashboard]?.list ?? [];
                        const levelWiseIds = dashboardWiseData
                            .filter(item => item.level === level)
                            .map(item => item.refPatientId);
                        const levelWiseIdsSet = new Set(levelWiseIds);
                        patientData = patientData.filter(item => levelWiseIdsSet.has(item.id));
                    }
                    patientData = await applyFilter(key, items, patientData, ninetyDaysData, sixtyDaysData, forComparison);
                } else {
                    let dashboardWiseData = diffDashboardPatients[filter.dashboard]?.list ?? [];
                    if (level > 0) {
                        dashboardWiseData = diffDashboardPatients?.filter(item => item.level === level - 1);
                    }
                    dashboardWiseData = await applyFilter(key, items, dashboardWiseData, dashboardWiseData?.ninetyDaysData ?? [], dashboardWiseData?.ninetyDaysData?.sixtyDaysData ?? [], forComparison);
                    const patientIds = dashboardWiseData.map(item => item.refPatientId);
                    patientData = patientData.filter(item => patientIds.includes(item._id));
                }
            }
        }
    }

    return patientData;
}

export async function getDashboardWiseData(diffDashboardPatients, pageType) {

    const typeMapping = {
        [PAGE_TYPE.ADMISSION]: {
            types: [ADT_TYPES.ADMISSIONS, ADT_TYPES.READMISSIONS]
        },
        [PAGE_TYPE.COMMUNITY_TRANSFER]: {
            type: ADT_TYPES.TRANSFER,
            subTypes: [
                ADT_SUB_TYPES.SAFE_DISCHARGE,
                ADT_SUB_TYPES.SNF,
                ADT_SUB_TYPES.AMA
            ]
        },
        [PAGE_TYPE.DECEASED]: {
            type: ADT_TYPES.TRANSFER,
            subTypes: [ADT_SUB_TYPES.DECEASED]
        },
        [PAGE_TYPE.HOSPITAL]: {
            type: ADT_TYPES.TRANSFER,
            subTypes: [
                ADT_SUB_TYPES.PLANNED_HOSPITAL_TRANSFER,
                ADT_SUB_TYPES.UNPLANNED_HOSPITAL_TRANSFER
            ]
        }
    };

    const config = typeMapping[pageType];
    if (!config) return [];

    return diffDashboardPatients.filter(patient => {
        if (config.types) {
            return config.types.includes(patient.type);
        } else if (config.type && config.subTypes) {
            return patient.type === config.type && config.subTypes.includes(patient.transferType);
        }
        return false;
    });
}

export async function getCustomTabsCards(
    patientFilterData,
    customTabs,
    totalFilterData,
    {
        ninetyDaysData,
        dynamicCards,
        newSavedFilters,
        isCheckNewFilters = false,
        objectCustom,
        sixtyDaysData,
        diffDashboardPatients = [],
        pageType,
        forComparison,
        customCombineTabData = []
    }) {

    const result = {};
    for (const customTab of customTabs) {
        let patientData = [...patientFilterData];

        if (customTab?.type === CUSTOM_TAB_TYPE.combineTab) {
            patientData = await processFilterForCombineTab({
                customTab,
                patientData,
                totalFilterData,
                customCombineTabData,
                pageType,
                ninetyDaysData,
                sixtyDaysData,
                isCheckNewFilters,
                newSavedFilters,
                result,
                forComparison,
            });
        } else {
            let index = 0;
            let dataPoints = [];
            let level = 0;

            for (const filter of customTab.filters) {
                if (index > 0) {
                    if (dataPoints.includes(filter?.dashboard)) {
                        level = level + 1;
                    }
                }
                dataPoints.push(filter?.dashboard);

                const customKey = customTab?.accessor;
                const currentCardFilter = isCheckNewFilters && _.includes(newSavedFilters, customKey);

                if (!currentCardFilter) {
                    patientData = await processFilterForTab({
                        level,
                        customTab,
                        filter,
                        patientData,
                        totalFilterData,
                        dynamicCards,
                        ninetyDaysData,
                        newSavedFilters,
                        isCheckNewFilters,
                        objectCustom,
                        result,
                        sixtyDaysData,
                        pageType,
                        diffDashboardPatients,
                        forComparison
                    });
                }
                index++;
            }
        }
    }

    return result;
}

const mainCardHandlers = {
    [HOSPITAL_CARDS_TYPE.INSURANCE_DATA]: insuranceData,
    [HOSPITAL_CARDS_TYPE.FLOORS_DATA]: floorsData,
    [HOSPITAL_CARDS_TYPE.DCER_DATA]: dcErData,
    [HOSPITAL_CARDS_TYPE.HOSPITALIZATIONS]: hospitalizationData,
    [HOSPITAL_CARDS_TYPE.RETURNS_DATA]: returnsData,
    [HOSPITAL_CARDS_TYPE.NINETY_DAYS_DATA]: ninetyDaysDataList,
    [CO_TRANSFER_CARDS_TYPE.SIXTY_DAYS_DATA]: sixtyDaysDataList,
    [HOSPITAL_CARDS_TYPE.DAYS_DATA]: daysData,
    [HOSPITAL_CARDS_TYPE.DX_DATA]: dxData,
    [HOSPITAL_CARDS_TYPE.NURSE_DATA]: nurseData,
    [HOSPITAL_CARDS_TYPE.DOCTOR_DATA]: doctorData,
    [HOSPITAL_CARDS_TYPE.SHIFT_DATA]: shiftData,
    [HOSPITAL_CARDS_TYPE.HOSPITAL_DATA]: customHospitalData,
    [CO_TRANSFER_CARDS_TYPE.SAFE_DISCHARGE_ASS_LIV_DATA]: safeDischargeAssLivData,
    [CO_TRANSFER_CARDS_TYPE.SNF_FACILITY_DATA]: snfFacilityData,

};

function buildSimpleCard(key, patientData, totalFilterData) {
    let filteredPatients = patientData;
    const { filterDashboard } = totalFilterData;

    if (key !== HOSPITAL_CARDS_TYPE.TOTAL) {
        if ([ADMISSION_CARDS_TYPE.ADMISSION, ADMISSION_CARDS_TYPE.READMISSION].includes(key)) {
            filteredPatients = patientData.filter(item => item.type === key);
        } else {
            filteredPatients = patientData.filter(item => item.type === "transfer" && item.transferType === key);
        }
    }

    const total = filteredPatients.length;
    const patientIds = filteredPatients.map(item => totalFilterData.isOtherDashboard ? item.refPatientId : item._id);
    let label = key;
    if (filterDashboard === PAGE_TYPE.COMMUNITY_TRANSFER) {
        label = COMMUNITY_CARD_LABELS?.[key];
    } else if (filterDashboard === PAGE_TYPE.HOSPITAL) {
        label = HOSPITAL_CARDS_LABELS?.[key];
    } else if (filterDashboard === PAGE_TYPE.DECEASED) {
        label = DECEASED_CARDS_LABELS?.[key];
    } else {
        label = ADMISSION_CARDS_LABELS?.[key];
    }
    return [{
        _id: key,
        id: key,
        label,
        name: key,
        total,
        value: total,
        originalTotal: total,
        percentage: itemPercentage(total, totalFilterData.totalForPercentage, "number"),
        patientIds,
        level: totalFilterData?.level,
        ...(totalFilterData.isOtherDashboard && { otherDashboardIds: filteredPatients.map(item => item._id) })
    }];
}

export async function handleMainCard(key, patientData, totalFilterData, ninetyDaysData, sixtyDaysData) {
    if (isSimpleCardKey(key)) {
        return buildSimpleCard(key, patientData, totalFilterData);
    }

    const handler = await mainCardHandlers[key];

    if (!handler) return null;
    if (key === HOSPITAL_CARDS_TYPE.NINETY_DAYS_DATA) {
        return await handler(ninetyDaysData, patientData, totalFilterData);
    } else if (key === CO_TRANSFER_CARDS_TYPE.SIXTY_DAYS_DATA) {
        return await handler(sixtyDaysData, patientData, totalFilterData);
    } else {
        if (CO_TRANSFER_CARDS_TYPE.SAFE_DISCHARGE_ASS_LIV_DATA === key) {
            return await handler(patientData, { ...totalFilterData, isSpacialItem: true });
        } else {
            return await handler(patientData, totalFilterData);
        }
    }
}

const directMatchFilters = {
    [HOSPITAL_CARDS_TYPE.INSURANCE_DATA]: 'insuranceId',
    [HOSPITAL_CARDS_TYPE.FLOORS_DATA]: 'floorId',
    [HOSPITAL_CARDS_TYPE.DOCTOR_DATA]: 'doctorId',
    [HOSPITAL_CARDS_TYPE.SHIFT_DATA]: 'shiftName',
    [HOSPITAL_CARDS_TYPE.NURSE_DATA]: 'nurseId',
    [HOSPITAL_CARDS_TYPE.HOSPITAL_DATA]: 'hospitalId',
    [CO_TRANSFER_CARDS_TYPE.SAFE_DISCHARGE_ASS_LIV_DATA]: 'assistantLivId',
    [CO_TRANSFER_CARDS_TYPE.SNF_FACILITY_DATA]: 'facilityId',
};

const customSyncFilters = {
    [HOSPITAL_CARDS_TYPE.DCER_DATA]: (items) => ({
        wasAdmitted: DCERDataFilter({ DCERData: items }),
    }),
    [HOSPITAL_CARDS_TYPE.HOSPITALIZATIONS]: (items) => ({
        reHospitalization: hospitalizationsFilter({ hospitalizations: items }),
    }),
    [HOSPITAL_CARDS_TYPE.RETURNS_DATA]: (items) => ({
        reHospitalization: returnsDataFilter({ returnsData: items }),
    }),
};

/**
 * Enhanced filter application with comprehensive error handling and validation
 * @param {string} key - Filter key identifier
 * @param {Array} items - Items to filter by
 * @param {Array} patientData - Patient data to filter
 * @param {Array} ninetyDaysData - Ninety days data for special filters
 * @param {Array} sixtyDaysData - Sixty days data for special filters
 * @param {boolean} forComparison - Whether this is for comparison purposes
 * @returns {Promise<Array>} Filtered patient data
 */
export async function applyFilter(key, items, patientData, ninetyDaysData, sixtyDaysData, forComparison = false) {
    // Input validation
    if (!Array.isArray(patientData)) {
        console.warn('applyFilter: patientData is not an array, returning empty array');
        return [];
    }

    // Early return for empty data
    if (patientData.length === 0) {
        return patientData;
    }


    // Handle simple card keys first
    if (isSimpleCardKey(key)) {
        return handleSimpleCardFilter(key, patientData);
    }

    // Return original data if no items to filter
    if (!items || items.length === 0) {
        return patientData;
    }

    // Handle direct field matching filters
    if (directMatchFilters[key]) {
        return handleDirectMatchFilter(key, items, patientData);
    }

    // Handle custom sync filters (single item only)
    if (customSyncFilters[key] && items.length === 1) {
        return handleCustomSyncFilter(key, items, patientData);
    }

    // Handle special case filters
    return await handleSpecialFilters(key, items, patientData, ninetyDaysData, sixtyDaysData);
}

// Optimized simple card filter with constants
function handleSimpleCardFilter(key, patientData) {
    if (TRANSFER_TYPES.includes(key)) {
        return patientData.filter(item => item.type === "transfer" && item.transferType === key);
    }

    if (key === HOSPITAL_CARDS_TYPE.TOTAL) {
        return patientData;
    }

    if (ADMISSION_TYPES.includes(key)) {
        return patientData.filter(item => item.type === key);
    }

    return patientData;
}

// Extracted direct match filter logic with error handling
function handleDirectMatchFilter(key, items, patientData) {
    const field = directMatchFilters[key];

    if (!field) {
        console.warn(`handleDirectMatchFilter: No field mapping found for key: ${key}`);
        return patientData;
    }

    try {
        return patientData.filter((p) => {
            const fieldValue = p[field];
            return fieldValue !== undefined && fieldValue !== null && _.includes(items, fieldValue);
        });
    } catch (error) {
        console.error(`Error in handleDirectMatchFilter for key ${key}:`, error);
        return patientData;
    }
}

// Extracted custom sync filter logic
function handleCustomSyncFilter(key, items, patientData) {
    try {
        const filter = customSyncFilters[key](items);
        return _.isEmpty(filter) ? patientData : _.filter(patientData, filter) ?? [];
    } catch (error) {
        console.error(`Error in handleCustomSyncFilter for key ${key}:`, error);
        return patientData;
    }
}

// Enhanced special filters with better error handling
async function handleSpecialFilters(key, items, patientData, ninetyDaysData, sixtyDaysData) {
    try {
        switch (key) {
            case HOSPITAL_CARDS_TYPE.DAYS_DATA:
                return patientData.filter(({ day }) => day && _.includes(items, day));

            case HOSPITAL_CARDS_TYPE.DX_DATA:
                return patientData.filter(({ dxIds }) =>
                    Array.isArray(dxIds) && _.intersection(items, dxIds).length > 0
                );

            case CO_TRANSFER_CARDS_TYPE.SIXTY_DAYS_DATA:
                if (!Array.isArray(sixtyDaysData)) {
                    console.warn('sixtyDaysData is not an array, returning original data');
                    return patientData;
                }
                return await ninetyDaysDataFilter(items, patientData, sixtyDaysData);

            case HOSPITAL_CARDS_TYPE.NINETY_DAYS_DATA:
                if (!Array.isArray(ninetyDaysData)) {
                    console.warn('ninetyDaysData is not an array, returning original data');
                    return patientData;
                }
                return await ninetyDaysDataFilter(items, patientData, ninetyDaysData);

            default:
                return patientData;
        }
    } catch (error) {
        console.error(`Error in handleSpecialFilters for key ${key}:`, error);
        return patientData;
    }
}

export const filterPatientDataByLevel = async ({ responseComparison, response, filters, forComparison, pageType }) => {
    const dashboardType = filters?.question?.dashboardType;
    const customTab = filters?.question?.customTab;
    const questionType = customTab?.type ?? null;

    const level = filters?.question?.level;
    const isAnotherDashboard = filters?.question?.isAnotherDashboard;
    const isCustomTab = filters?.question?.isCustomTab;

    if (!dashboardType || !isCustomTab) {
        return forComparison ? responseComparison : response;
    }
    // Use the appropriate response object based on forComparison flag
    let targetResponse = forComparison ? responseComparison : response;

    const anotherDashboard = targetResponse?.diffDashboardPatients?.[dashboardType];
    const dashboardWiseData = targetResponse?.diffDashboardPatients?.[dashboardType]?.list ?? [];

    if (questionType === CUSTOM_TAB_TYPE.combineTab) {
        const combineData = await applyCombineFilters({
            patientData: targetResponse?.data ?? [],
            ninetyDaysData: targetResponse?.ninetyDaysData ?? [],
            customCombineTabData: targetResponse?.customCombineTabData ?? [],
            pageType: pageType,
            customTab: customTab,
            forComparison: forComparison
        });
        if (!forComparison) {
            targetResponse.data = combineData;
        }
    } else {
        if (isAnotherDashboard) {
            // Case 1: From another dashboard
            if (level > 0) {
                targetResponse.data = dashboardWiseData.filter(item => item.level === level);
                targetResponse.ninetyDaysData = anotherDashboard?.ninetyDaysData ?? [];
                targetResponse.sixtyDaysData = anotherDashboard?.sixtyDaysData ?? [];

            } else {
                targetResponse.data = dashboardWiseData;
                targetResponse.ninetyDaysData = anotherDashboard?.ninetyDaysData ?? [];
                targetResponse.sixtyDaysData = anotherDashboard?.sixtyDaysData ?? [];
            }
        } else if (level > 0) {

            // Case 2: From same dashboard and level > 0
            const patientData = targetResponse?.data ?? [];
            const patientIdsSet = new Set(patientData.map(item => item.id));

            targetResponse.data = dashboardWiseData.filter(item =>
                item.level === level &&
                patientIdsSet.has(item.refPatientId)
            );

            targetResponse.ninetyDaysData = anotherDashboard?.ninetyDaysData ?? [];
            targetResponse.sixtyDaysData = anotherDashboard?.sixtyDaysData ?? [];
        }
    }

    // Always return the modified response
    return targetResponse;
};

export async function applyCombineFilters({
    patientData,
    ninetyDaysData,
    customTab,
    sixtyDaysData,
    pageType,
    customCombineTabData = [],
    forComparison = false,
    totalFilterData = {}
}) {
    try {
        // Input validation
        validateFilterInputs(patientData, customTab);

        const filters = customTab.filters;
        let filteredPatientData = [...patientData];
        let allDashboardData = [];

        // Process filters in parallel where possible
        const filterPromises = filters.map(async (filter) => {
            const filterDashboard = filter?.dashboard;

            if (filterDashboard === pageType || (pageType === PAGE_TYPE.OVERALL && (filterDashboard === CUSTOM_TAB_PAGE_TYPE.OVERALL_INCOMING || filterDashboard === CUSTOM_TAB_PAGE_TYPE.OVERALL_OUTGOING))) {
                return {
                    type: 'currentPage',
                    data: await processCurrentPageFilters(
                        filter.filters,
                        filteredPatientData,
                        ninetyDaysData,
                        sixtyDaysData,
                        totalFilterData,
                        forComparison
                    )
                };
            } else if (filterDashboard !== pageType) {
                return {
                    type: 'otherDashboard',
                    data: await processOtherDashboardFilters(
                        filter.filters,
                        customCombineTabData[filterDashboard],
                        totalFilterData,
                        forComparison
                    )
                };
            }
            return null;
        });

        const results = await Promise.all(filterPromises);

        // Process results
        for (const result of results) {
            if (!result) continue;

            if (result.type === 'currentPage') {
                filteredPatientData = result.data;
            } else if (result.type === 'otherDashboard' && Array.isArray(result.data) && result.data.length > 0) {
                allDashboardData.push(...result.data);
            }
        }

        // Merge all filtered data with deduplication
        const res = mergeAndDeduplicateData(filteredPatientData, allDashboardData);
        return res;
    } catch (error) {
        console.error('Error in applyCombineFilters:', error);
        return Array.isArray(patientData) ? patientData : [];
    }
}

// Helper function to merge and deduplicate data
function mergeAndDeduplicateData(filteredPatientData, allDashboardData) {
    const mergedData = [
        ...(Array.isArray(filteredPatientData) ? filteredPatientData : []),
        ...allDashboardData
    ];

    // Remove duplicates based on ID
    // Do not deduplicate, return all merged data as is
    const uniqueData = mergedData;

    return uniqueData;
}

// Enhanced helper functions with better error handling
async function processCurrentPageFilters(
    filterItems,
    patientData,
    ninetyDaysData,
    sixtyDaysData,
    totalFilterData,
    forComparison
) {
    if (!Array.isArray(filterItems) || !Array.isArray(patientData)) {
        return patientData;
    }

    let processedData = [...patientData];

    for (const filterItem of filterItems) {
        try {
            const { card, items = [] } = filterItem;
            if (!card?.value) continue;

            if (card?.questionId) {
                processedData = await processQuestionFilter(
                    filterItem,
                    processedData,
                    totalFilterData,
                    items
                );
            } else {
                processedData = await applyFilter(
                    card?.value,
                    items,
                    processedData,
                    ninetyDaysData,
                    sixtyDaysData,
                    forComparison
                );
            }
        } catch (error) {
            console.error('Error processing filter item:', error);
            continue; // Continue with next filter item
        }
    }

    return processedData;
}

async function processOtherDashboardFilters(
    filterItems,
    dashboardCombineData,
    totalFilterData,
    forComparison
) {
    if (!Array.isArray(filterItems)) {
        return [];
    }

    let dashboardData = dashboardCombineData?.list ?? [];
    if (!Array.isArray(dashboardData)) {
        return [];
    }

    const dashboardNinetyDays = dashboardCombineData?.ninetyDaysData ?? [];
    const dashboardSixtyDays = dashboardCombineData?.sixtyDaysData ?? [];

    for (const filterItem of filterItems) {
        try {
            const { card, items = [] } = filterItem;
            if (!card?.value) continue;

            if (card?.questionId) {
                dashboardData = await processQuestionFilter(
                    filterItem,
                    dashboardData,
                    totalFilterData,
                    items
                );
            } else {
                dashboardData = await applyFilter(
                    card?.value,
                    items,
                    dashboardData,
                    dashboardNinetyDays,
                    dashboardSixtyDays,
                    false
                );
            }
        } catch (error) {
            console.error('Error processing dashboard filter item:', error);
            continue; // Continue with next filter item
        }
    }

    return dashboardData;
}

async function processQuestionFilter(filterItem, data, totalFilterData, items) {
    try {
        const { question } = filterItem;
        const key = filterItem.card?.value;

        if (!question || !key || !Array.isArray(data)) {
            return data;
        }

        let objectCustomRes = {};
        processDynamicCard(question, data, objectCustomRes, { ...totalFilterData });

        const resultItems = objectCustomRes[key] || [];
        const patientIds = resultItems.reduce((acc, ele) => {
            if (ele?._id && _.includes(items, ele._id)) {
                acc.push(...(ele?.patientIds || []));
            }
            return acc;
        }, []);

        return await filterCustomPatientData(
            data,
            { [key]: items },
            {
                type: key,
                patientIds,
                question: { customQuestionInputType: question.customQuestionInputType },
            },
            true
        );
    } catch (error) {
        console.error('Error in processQuestionFilter:', error);
        return data;
    }
}
