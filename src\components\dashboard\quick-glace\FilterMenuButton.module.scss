.filterMenuButton {
    width: 100%;

    .addFilterButton {
        display: flex;
        justify-content: space-between;
        width: 100%;
        height: 50px;
        color: #000;
        background: #FFFFFF;
        box-shadow: 0px 6px 12px rgba(0, 0, 0, 0.04);
        border-radius: 8px;
    }
}

.addButton {
    border: 1px solid #4879F5;
    border-radius: 4px;
    min-width: 47px;
    height: 25px;
}

.parentTitle {
    font-family: 'manrope-bold';
    font-size: 14px;
    line-height: 19px;
    color: #444652;
}

.parentMenuItemText {
    margin-bottom: 5px;
    text-align: center;
    font-family: 'manrope-med';
    font-size: 12px;
    line-height: 16px;
    text-align: center;
}

.removeText {
    font-family: 'manrope-med';
    font-size: 12px;
    line-height: 16px;
    text-align: center;
    color: #F93B5F;
}