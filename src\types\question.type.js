export const QUESTION_INPUT_TYPE = {
    UNLIMITED_ANSWERS: 'unlimitedAnswers',
    LIMITED_ANSWERS: 'limitedAnswers',
    NUMBER_RANGE: 'numberRange',
    NUMBER_RANGE_LIMITED_ANSWERS: 'numberRangeLimitedAnswers',
    RESIDENCE_LIST: 'residenceList',
    DATE : 'date',
    TIME_TAB_RESIDENT_LIST: 'timeTabResidentList',
    TIME_TAB_RANGE: 'timeTabRange',
}

export const QUESTION_TYPE_OPTIONS = [
    { value: QUESTION_INPUT_TYPE.UNLIMITED_ANSWERS, label: 'Unlimited answers' },
    { value: QUESTION_INPUT_TYPE.LIMITED_ANSWERS, label: 'Limited Answers' },
    { value: QUESTION_INPUT_TYPE.NUMBER_RANGE, label: 'Number' },
    { value: QUESTION_INPUT_TYPE.NUMBER_RANGE_LIMITED_ANSWERS, label: 'Number range / limited answers' },
    { value: QUESTION_INPUT_TYPE.RESIDENCE_LIST, label: 'Residence list' },
    { value: QUESTION_INPUT_TYPE.DATE, label: 'Date' },
    { value: QUESTION_INPUT_TYPE.TIME_TAB_RESIDENT_LIST, label: 'Time Tab - Resident List Only' },
    { value: QUESTION_INPUT_TYPE.TIME_TAB_RANGE, label: 'Time Tab - Time Range' },
]

export const NUMBER_RANGE_TYPE = {
    RANGE: "range",
    AVERAGE: "average",
    TOTAL: "total",
}

export const QUESTIONS_TEMPLATE_TYPE = {
    DX_CARD: "dxCard",
    DAY_CARD: "dayCard",
    ANALYSIS: "analysis",
    USER_LIST: "userList",
    FLOOR_CARD: "floorCard",
}

export const QUESTIONS_TEMPLATES = [
    { value: QUESTIONS_TEMPLATE_TYPE.DX_CARD, label: "Template 1", image: "dx_card.png" },
    { value: QUESTIONS_TEMPLATE_TYPE.DAY_CARD, label: "Template 2", image: "day_card.png" },
    { value: QUESTIONS_TEMPLATE_TYPE.ANALYSIS, label: "Template 3", image: "analysis.png" },
    { value: QUESTIONS_TEMPLATE_TYPE.USER_LIST, label: "Template 4", image: "doctor_card.png" },
    { value: QUESTIONS_TEMPLATE_TYPE.FLOOR_CARD, label: "Template 5", image: "floor_card.png" },
]

export const TIME_TAB_MODE = {
    RESIDENT_LIST_ONLY: 'residentListOnly',
    TIME_RANGE: 'timeRange',
}

export const TIME_RANGE_TYPE = {
    HOURLY: 'hourly',
    CUSTOM: 'custom',
}

export const DEFAULT_TIME_RANGES = [
    { label: 'Morning', startTime: '06:00', endTime: '12:00' },
    { label: 'Afternoon', startTime: '12:00', endTime: '18:00' },
    { label: 'Evening', startTime: '18:00', endTime: '00:00' },
    { label: 'Night', startTime: '00:00', endTime: '06:00' },
];

export const HOURLY_TIME_RANGES = [
    { label: '12 AM', value: '00:00' },
    { label: '1 AM', value: '01:00' },
    { label: '2 AM', value: '02:00' },
    { label: '3 AM', value: '03:00' },
    { label: '4 AM', value: '04:00' },
    { label: '5 AM', value: '05:00' },
    { label: '6 AM', value: '06:00' },
    { label: '7 AM', value: '07:00' },
    { label: '8 AM', value: '08:00' },
    { label: '9 AM', value: '09:00' },
    { label: '10 AM', value: '10:00' },
    { label: '11 AM', value: '11:00' },
    { label: '12 PM', value: '12:00' },
    { label: '1 PM', value: '13:00' },
    { label: '2 PM', value: '14:00' },
    { label: '3 PM', value: '15:00' },
    { label: '4 PM', value: '16:00' },
    { label: '5 PM', value: '17:00' },
    { label: '6 PM', value: '18:00' },
    { label: '7 PM', value: '19:00' },
    { label: '8 PM', value: '20:00' },
    { label: '9 PM', value: '21:00' },
    { label: '10 PM', value: '22:00' },
    { label: '11 PM', value: '23:00' },
];