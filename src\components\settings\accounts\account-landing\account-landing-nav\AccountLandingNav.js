import { Link } from "react-router-dom";
import FacilitiesSVG from "../../../../../assets/svgs/facilities.svg";
import UsersSVG from "../../../../../assets/svgs/users.svg";
import styles from "./AccountLandingNav.module.scss";

const AccountLandingNav = (props) => {
  return (
    <>
      <Link to={"/settings"}>Back</Link>
      <div className={`df acc ${styles.accountLandingNav}`}>
        <Link to={`facilities`} className={`df acc ffc ${styles.sec}`}>
          <div className={`m-b-15 ${styles.secTitle}`}>
            <h2 className={`ffmm fs18`}>Facilities</h2>
          </div>
          <FacilitiesSVG fill="#494949"></FacilitiesSVG>
        </Link>
        <Link to={`users`} className={`df acc ffc ${styles.sec}`}>
          <div className={`m-b-15 ${styles.secTitle}`}>
            <h2 className={`ffmm fs18`}>Users</h2>
          </div>
          <UsersSVG fill="#494949" />
        </Link>
      </div>
    </>
  );
};

export default AccountLandingNav;
