import api from './api';

const API_PREFIX = "api/dynamic-data-tab";

const fetchData = async (url, method = 'get', data = {}, config = {}) => {
    try {
        if (['get', 'delete'].includes(method)) {
            return await api[method](url, { ...config, params: data });
        } else {
            return await api[method](url, data, config);
        }
    } catch (error) {
        console.error(`API ${method.toUpperCase()} ${url} failed:`, error);
        throw error;
    }
};

const getAllDynamicDataTabs = (params = {}) =>
    fetchData(`${API_PREFIX}/all`, 'get', params);

const getDynamicQuestions = (params = {}) =>
    fetchData(`${API_PREFIX}/questions`, 'get', params);

const saveDynamicDataTab = (body = {}) =>
    fetchData(API_PREFIX, 'post', body);

const saveCustomDynamicDataTab = (body = {}) =>
    fetchData(`${API_PREFIX}/custom`, 'post', body);

const deleteDynamicDataTabData = (id) => {
    if (!id) throw new Error('ID is required to delete a dynamic data tab.');
    return fetchData(`${API_PREFIX}/custom-alert/${id}`, 'delete');
};

const updateDynamicDataTabData = (id, body = {}) => {
    if (!id) throw new Error('ID is required to update a dynamic data tab.');
    return fetchData(`${API_PREFIX}/${id}`, 'put', body);
};

export {
    getDynamicQuestions,
    getAllDynamicDataTabs,
    saveDynamicDataTab,
    saveCustomDynamicDataTab,
    deleteDynamicDataTabData,
    updateDynamicDataTabData,
};