const mongoose = require("mongoose");
const Patient = mongoose.model("patient");
const moment = require("moment");
const _ = require("lodash");
const { ResStatusEnum, ADT_TYPES, ADT_SUB_TYPES } = require("../../types");
const { enrichDiffDashboardPatients, getReturnAndDidNotReturn, getNinetyDaysChartCount, applyCustomQuestionPatientFilter, getSixtyDaysChartCount, flagsForTransfer } = require("../../utils/common");
const {
    getShiftName,
    momentDateFormat,
    getDayNameFromDate,
    getDaysBefore,
    checkADTDuplicate,
    toStartFilterDate,
    toEndFilterDate,
    toDateOfADTFilter,
} = require("../utilis/date-format");
const { getCensusAverageInfo, getCensusAverageByPeriod } = require("./census");
const { HOSPITAL_CARDS_TYPE } = require("../../types/hospital.type");
const { findSavedReportConfig } = require("./reportsSubscription");
const { PAGE_TYPE, RELATIONS, CUSTOM_TAB_TYPE } = require("../../types/common.type");
const { getCustomTabsByPage } = require("./custom-tab");
const { formatPatientData } = require("../utilis/common");
const {
    initializeGetAllCount,
    setupFacilityFilter,
    setupDateFilter,
    getCensusInfo,
    finalizeGetAllCount
} = require("./common-dashboard");

const createDiffDashboardPatients = async () => {
    return {
        admission: [],
        hospital: [],
        communityTransfer: [],
        deceased: [],
        overall: [],
        overallIncoming: [],
        overallOutgoing: [],
    };
}

const getCustomCombineTabData = async (query, page, facilityFilter, customCombineTabData, customTabs, overallList = []) => {
    let relations = [
        RELATIONS.FACILITY,
        RELATIONS.DOCTOR,
        RELATIONS.UNIT,
        RELATIONS.PAYER_SOURCE_INSURANCE,
        RELATIONS.INSURANCE,
        RELATIONS.DX,
        RELATIONS.NURSE,
        RELATIONS.HOSPITAL,
        RELATIONS.SNF,
        RELATIONS.TRANSFER_TO_WHICH_ASSISTED_LIVING,
    ];

    let updatedQuery = { ...query };
    updatedQuery.type = { $in: [ADT_TYPES.ADMISSIONS, ADT_TYPES.READMISSIONS, ADT_TYPES.TRANSFER, ADT_TYPES.RETURN] };
    delete updatedQuery.transferType;

    let list = [];

    if (page === PAGE_TYPE.OVERALL) {
        list = overallList;
    } else {
        list = await Patient.find({ ...updatedQuery })
            .select("-logs -notes -middleInitial -highlighter -updatedAt")
            .populate(relations)
            .lean();
    }

    if (!list?.length) return customCombineTabData;

    await Promise.all(
        list.map(async (item) => {
            let reHospitalization = null;
            let wasReturned = null;

            if (item.transferType !== ADT_SUB_TYPES.DECEASED) {
                reHospitalization = await getReHospitalization(item, facilityFilter);
                wasReturned = await getReturnAndDidNotReturn(item, facilityFilter);
            }

            let formatted = await formatPatientData(item, {
                reHospitalization,
                wasReturned
            });

            const { type, transferType } = item;

            if (
                type === ADT_TYPES.TRANSFER &&
                [ADT_SUB_TYPES.PLANNED_HOSPITAL_TRANSFER, ADT_SUB_TYPES.UNPLANNED_HOSPITAL_TRANSFER].includes(transferType)
            ) {
                customCombineTabData.hospital.push(formatted);
            } else if (
                type === ADT_TYPES.TRANSFER &&
                [ADT_SUB_TYPES.SAFE_DISCHARGE, ADT_SUB_TYPES.SNF, ADT_SUB_TYPES.AMA].includes(transferType)
            ) {
                formatted.snfFacilityId = item.snf?._id?.toString() || null;
                formatted.assistantLivId = item.transferToWhichAssistedLiving?._id?.toString() || null;
                customCombineTabData.communityTransfer.push(formatted);
            } else if (
                [ADT_TYPES.ADMISSIONS, ADT_TYPES.READMISSIONS].includes(type)
            ) {
                formatted.insuranceId = item.payerSourceInsurance?._id?.toString() || null;
                formatted.insurance = item.payerSourceInsurance || null;
                customCombineTabData.admission.push(formatted);
            } else if (
                type === ADT_TYPES.TRANSFER &&
                transferType === ADT_SUB_TYPES.DECEASED
            ) {
                customCombineTabData.deceased.push(formatted);
            }
            if (type === ADT_TYPES.TRANSFER) {
                customCombineTabData.overallOutgoing.push(formatted);
            }
            if (type === ADT_TYPES.ADMISSIONS || type === ADT_TYPES.READMISSIONS || type === ADT_TYPES.RETURN) {
                customCombineTabData.overallIncoming.push(formatted);
            }

            customCombineTabData.overall.push(formatted);

            return formatted;
        })
    );

    customCombineTabData = await enrichDiffDashboardPatients(customCombineTabData, customTabs);
    return customCombineTabData;
}

async function getOtherDashboardData(patient, facilityFilter, diffDashboardPatientsByPage, seenPatientIds) {
    const duplicateCheck = await checkADTDuplicate(patient);

    const query = {
        ...duplicateCheck,
        ...facilityFilter,
        _id: { $ne: patient._id },
        dateOfADT: { $gt: await toEndFilterDate(patient.dateOfADT) },
    };

    const relations = [
        RELATIONS.FACILITY,
        RELATIONS.DOCTOR,
        RELATIONS.UNIT,
        RELATIONS.PAYER_SOURCE_INSURANCE,
        RELATIONS.INSURANCE,
        RELATIONS.DX,
        RELATIONS.NURSE,
        RELATIONS.HOSPITAL,
        RELATIONS.SNF,
        RELATIONS.TRANSFER_TO_WHICH_ASSISTED_LIVING,
    ];

    const items = await Patient.find(query)
        .populate(relations)
        .sort({ dateOfADT: 1, createdAt: 1 })
        .select("-logs -notes -middleInitial -highlighter -updatedAt")
        .lean();

    if (!items?.length) return [];


    const patientLevels = {
        hospital: new Map(),
        communityTransfer: new Map(),
        admission: new Map(),
        deceased: new Map(),
    };

    const formattedItems = [];

    for (const item of items) {
        const itemIdStr = item._id.toString();
        const patientId = patient?._id?.toString() || item.patient?._id?.toString() || itemIdStr;

        if (seenPatientIds.has(itemIdStr)) return null; // Skip duplicate
        seenPatientIds.add(itemIdStr);
        let reHospitalization = null;
        let wasReturned = null;

        if (item.transferType !== ADT_SUB_TYPES.DECEASED) {
            reHospitalization = await getReHospitalization(item, facilityFilter);
            wasReturned = await getReturnAndDidNotReturn(item, facilityFilter);
        }

        const formatted = await formatPatientData(item, {
            patientId,
            reHospitalization,
            wasReturned
        });

        const { type, transferType } = item;

        const addToCategory = (category, extraFields = {}) => {
            const levelMap = patientLevels[category];
            const currentLevel = levelMap.get(patientId) ?? 1;
            formatted.level = currentLevel;
            levelMap.set(patientId, currentLevel + 1);

            Object.assign(formatted, extraFields);
            diffDashboardPatientsByPage[category].push(formatted);

        };

        if (
            type === ADT_TYPES.TRANSFER &&
            [ADT_SUB_TYPES.PLANNED_HOSPITAL_TRANSFER, ADT_SUB_TYPES.UNPLANNED_HOSPITAL_TRANSFER].includes(transferType)
        ) {
            addToCategory('hospital');
        } else if (
            type === ADT_TYPES.TRANSFER &&
            [ADT_SUB_TYPES.SAFE_DISCHARGE, ADT_SUB_TYPES.SNF, ADT_SUB_TYPES.AMA].includes(transferType)
        ) {
            addToCategory('communityTransfer', {
                snfFacilityId: item.snf?._id?.toString() || null,
                assistantLivId: item.transferToWhichAssistedLiving?._id?.toString() || null,
            });
        } else if (
            [ADT_TYPES.ADMISSIONS, ADT_TYPES.READMISSIONS].includes(type)
        ) {
            addToCategory('admission', {
                insuranceId: item.payerSourceInsurance?._id?.toString() || null,
                insurance: item.payerSourceInsurance || null,
            });
        } else if (
            type === ADT_TYPES.TRANSFER &&
            transferType === ADT_SUB_TYPES.DECEASED
        ) {
            addToCategory('deceased');
        }

        formattedItems.push(formatted);
    }


    return formattedItems.filter(Boolean); // Remove nulls from skipped duplicates
};

const getDashboardData = async (req) => {
    const { facilityid, accountid } = req.headers;
    const { startDate, endDate, facilityIds = [] } = req.query;

    let startDateFilter = await toStartFilterDate(startDate)
    let endDateFilter = await toEndFilterDate(endDate);

    let query = {
        type: ADT_TYPES.TRANSFER,
        transferType: {
            $in: [
                ADT_SUB_TYPES.PLANNED_HOSPITAL_TRANSFER,
                ADT_SUB_TYPES.UNPLANNED_HOSPITAL_TRANSFER,
            ],
        },
    };

    let facilityFilter = null;
    const facilityData = [];

    if (facilityIds && facilityIds.length > 0) {
        query.facilityId = { $in: facilityIds };
        facilityIds.map((ele) => {
            facilityData.push(mongoose.Types.ObjectId(ele));
        });
        facilityFilter = { facilityId: { $in: facilityData } };
    } else {
        if (facilityid) {
            facilityFilter = { facilityId: mongoose.Types.ObjectId(facilityid) };
            query.facilityId = facilityid;
        }
    }

    if (startDate && endDate) {
        query.dateOfADT = await toDateOfADTFilter(startDate, endDate);
    }
    let list = await Patient.find({ ...query }).sort({ dateOfADT: -1 });
    let plannedHospitalTransfer = 0;
    let unplannedHospitalTransfer = 0;
    const total = list.length;

    if (total > 0) {
        const transferTypeTotal = _.countBy(list, "transferType");
        plannedHospitalTransfer = transferTypeTotal?.plannedHospitalTransfer || 0;
        unplannedHospitalTransfer =
            transferTypeTotal?.unplannedHospitalTransfer || 0;
    }

    //Getting census data
    let censusInfo = 0;
    if (startDate && endDate) {
        const facilityData = facilityIds.length > 0 ? facilityIds : [facilityid];
        censusInfo = await getCensusAverageInfo(
            startDateFilter,
            endDateFilter,
            facilityData
        );
    }
    const res = {
        total,
        plannedHospitalTransfer,
        unplannedHospitalTransfer,
        ...censusInfo,
    };
    return { status: ResStatusEnum.success, data: res };
};

const filterData = async (arr, callback) => {
    const fail = Symbol();
    return (
        await Promise.all(
            arr.map(async (item) => ((await callback(item)) ? item : fail))
        )
    ).filter((i) => i !== fail);
};

const getCardPatientChartData = async (req) => {
    const { body } = req;
    const { accountid } = req.headers;
    let {
        facilityId,
        facilityIds,
        cardFilter,
        type,
        ids = [],
        filter: { startDate, endDate },
        relation,
        transferType
    } = body;

    let startDateFilter = await toStartFilterDate(startDate);
    let endDateFilter = await toEndFilterDate(endDate);
    let diffDashboardPatients = await createDiffDashboardPatients();
    const customTabs = await getCustomTabsByPage({ accountid, page: PAGE_TYPE.HOSPITAL, userId: req.user._id });
    const isCustomCombineTab = customTabs?.some(tab => tab.type === CUSTOM_TAB_TYPE.COMBINE);
    let customCombineTabData = await createDiffDashboardPatients('combineTab');

    let query = {
        type: ADT_TYPES.TRANSFER,
        transferType: {
            $in: [
                ADT_SUB_TYPES.PLANNED_HOSPITAL_TRANSFER,
                ADT_SUB_TYPES.UNPLANNED_HOSPITAL_TRANSFER,
            ],
        },
    };
    if (type === HOSPITAL_CARDS_TYPE.TOTAL) {
    } else if (
        type === HOSPITAL_CARDS_TYPE.UNPLANNED ||
        type === HOSPITAL_CARDS_TYPE.PLANNED
    ) {
        query.transferType = { $in: [type] };
    } else {
        if (transferType) {
            query.transferType = transferType;
        }
    }

    let facilityFilter = null;
    const facilityData = [];

    if (facilityIds && facilityIds.length > 0) {
        query.facilityId = { $in: facilityIds };
        facilityIds.map((ele) => {
            facilityData.push(mongoose.Types.ObjectId(ele));
        });
        facilityFilter = { facilityId: { $in: facilityData } };
    } else {
        if (facilityId) {
            facilityFilter = { facilityId: mongoose.Types.ObjectId(facilityId) };
            query.facilityId = facilityId;
        }
    }

    if (startDate && endDate) {
        query.dateOfADT = {
            $gte: startDateFilter,
            $lt: endDateFilter,
        };
    }

    if (isCustomCombineTab) {
        customCombineTabData = await getCustomCombineTabData(query, PAGE_TYPE.HOSPITAL, facilityFilter, customCombineTabData, customTabs, true);
    }

    let patientList = [];
    let listData = [];
    let relations = [RELATIONS.FACILITY, RELATIONS.INSURANCE, RELATIONS.NURSE, RELATIONS.UNIT, RELATIONS.HOSPITAL, RELATIONS.DX, RELATIONS.DOCTOR, RELATIONS.PAYER_SOURCE_INSURANCE];

    let isMainFilter = false;
    if (
        type == HOSPITAL_CARDS_TYPE.PLANNED ||
        type == HOSPITAL_CARDS_TYPE.UNPLANNED ||
        type == HOSPITAL_CARDS_TYPE.TOTAL
    ) {
        isMainFilter = true;
    }

    if (!isMainFilter) {
        if (cardFilter.doctorData && cardFilter.doctorData.length > 0) {
            const doctorIds = cardFilter.doctorData.map((e) =>
                mongoose.Types.ObjectId(e)
            );
            query.doctor = { $in: doctorIds };
            //relations.push("doctor");
        }
        if (cardFilter.insuranceData && cardFilter.insuranceData.length > 0) {
            const insuranceIds = cardFilter.insuranceData.map((e) =>
                mongoose.Types.ObjectId(e)
            );
            query.insurance = { $in: insuranceIds };
            //relations.push("insurance");
        }
        if (cardFilter.nurseData && cardFilter.nurseData.length > 0) {
            const nurseIds = cardFilter.nurseData.map((e) =>
                mongoose.Types.ObjectId(e)
            );
            query.nurse = { $in: nurseIds };
            //relations.push("nurse");
        }
        if (cardFilter.floorsData && cardFilter.floorsData.length > 0) {
            const floorsIds = cardFilter.floorsData.map((e) =>
                mongoose.Types.ObjectId(e)
            );
            query.unit = { $in: floorsIds };
            //relations.push("unit");
        }
        if (cardFilter.hospitalData && cardFilter.hospitalData.length > 0) {
            const hospitalIds = cardFilter.hospitalData.map((e) =>
                mongoose.Types.ObjectId(e)
            );
            query.hospital = { $in: hospitalIds };
            //relations.push("hospital");
        }

        if (cardFilter && cardFilter.dxData.length > 0) {
            const dxIdsFilter = cardFilter.dxData.map((e) =>
                mongoose.Types.ObjectId(e)
            );
            query.dx = { $elemMatch: { $in: dxIdsFilter } };
            //relations.push("dx");
        }

        query = await applyCustomQuestionPatientFilter(query, cardFilter);

    }

    if (type == HOSPITAL_CARDS_TYPE.PLANNED) {
        query.transferType = {
            $in: [ADT_SUB_TYPES.PLANNED_HOSPITAL_TRANSFER],
        };
    } else if (type == HOSPITAL_CARDS_TYPE.UNPLANNED) {
        query.transferType = {
            $in: [ADT_SUB_TYPES.UNPLANNED_HOSPITAL_TRANSFER],
        };
    }
    patientList = await Patient.find({ ...query })
        .populate(relations)
        .sort({ dateOfADT: -1 })
        .exec();

    const seenPatientIds = new Set();

    await filterData(patientList, async (item) => {
        if (
            type == HOSPITAL_CARDS_TYPE.PLANNED ||
            type == HOSPITAL_CARDS_TYPE.UNPLANNED ||
            type == HOSPITAL_CARDS_TYPE.TOTAL
        ) {
        } else {
            if (
                cardFilter.daysData &&
                cardFilter.daysData.length > 0 &&
                type !== HOSPITAL_CARDS_TYPE.DAYS_DATA
            ) {
                const dayName = await getDayNameFromDate(item._doc.dateOfADT);
                if (!_.includes(cardFilter.daysData, dayName)) {
                    return false;
                }
            }
            if (
                cardFilter.shiftData &&
                cardFilter.shiftData.length > 0 &&
                type !== HOSPITAL_CARDS_TYPE.SHIFT_DATA
            ) {
                const shiftName = await getShiftName(item.transferTime);
                if (!_.includes(cardFilter.shiftData, shiftName)) {
                    return false;
                }
            }
            if (
                cardFilter.hospitalizations &&
                cardFilter.hospitalizations.length > 0 &&
                type !== HOSPITAL_CARDS_TYPE.HOSPITALIZATIONS
            ) {
                if (cardFilter.hospitalizations.length == 1) {
                    const selectedHospitalization = cardFilter.hospitalizations[0];
                    if (selectedHospitalization === "newHospitalizations") {
                        const reHospitalization = await getReHospitalization(
                            item._doc,
                            facilityFilter
                        );
                        if (reHospitalization) {
                            return false;
                        }
                    }
                    if (selectedHospitalization === "reHospitalizations") {
                        const reHospitalization = await getReHospitalization(
                            item._doc,
                            facilityFilter
                        );
                        if (!reHospitalization) {
                            return false;
                        }
                    }
                }
            }
            if (
                cardFilter.DCERData &&
                cardFilter.DCERData.length > 0 &&
                type !== HOSPITAL_CARDS_TYPE.DCER_DATA
            ) {
                if (cardFilter.DCERData.length == 1) {
                    const selectedDCERData = cardFilter.DCERData[0];
                    if (selectedDCERData === "ER") {
                        if (item.wasAdmitted) {
                            return false;
                        }
                    }
                    if (selectedDCERData === "DC") {
                        if (!item.wasAdmitted) {
                            return false;
                        }
                    }
                }
            }
            if (
                cardFilter.returnsData &&
                cardFilter.returnsData.length > 0 &&
                type !== HOSPITAL_CARDS_TYPE.RETURNS_DATA
            ) {
                if (cardFilter.returnsData.length == 1) {
                    const selectedReturnsData = cardFilter.returnsData[0];
                    if (selectedReturnsData === "Returned") {
                        const returnData = await getReturnAndDidNotReturn(
                            item._doc,
                            facilityFilter
                        );
                        if (!returnData) {
                            return false;
                        }
                    }
                    if (selectedReturnsData === "Didn't Return") {
                        const notReturnData = await getReturnAndDidNotReturn(
                            item._doc,
                            facilityFilter
                        );
                        if (notReturnData) {
                            return false;
                        }
                    }
                }
            }
        }

        let latestObj = new Object();
        if (customTabs?.length > 0) {
            await getOtherDashboardData(item, facilityFilter, diffDashboardPatients, seenPatientIds);
        }
        latestObj = item._doc;
        if (relation) {
            latestObj.filterId = item[relation]?._id || null;
        }
        const reHospitalization = await getReHospitalization(item._doc, facilityFilter);
        const wasReturned = await getReturnAndDidNotReturn(item._doc, facilityFilter);

        let formatted = await formatPatientData(latestObj, {
            reHospitalization,
            wasReturned
        });

        listData.push(formatted);
    });

    diffDashboardPatients = await enrichDiffDashboardPatients(diffDashboardPatients, customTabs);

    let ninetyDaysData = []
    const res = await getNinetyDaysChartCount(listData);
    ninetyDaysData = res?.ninetyDaysDataChart ?? [];
    listData = res?.patientListRes ?? listData;
    
    const filterDataDev = type === HOSPITAL_CARDS_TYPE.NINETY_DAYS_DATA ? ["a", "b", "c", "d", "e"] : cardFilter.ninetyDaysData;
    if (filterDataDev.length > 0 || type === HOSPITAL_CARDS_TYPE.NINETY_DAYS_DATA) {
        listData = await ninetyDaysDataFilter(filterDataDev, listData, ninetyDaysData);
    }

    let censusAverage = 0;
    let censusByPeriod = [];
    let censusByFacility = [];
    let censusAsOfNowByFacility = [];
    let bedCapacity = 0;
    let bedByFacility = [];
    if (startDate && endDate) {
        const facilityData = facilityIds.length > 0 ? facilityIds : [facilityId];
        if (
            type === HOSPITAL_CARDS_TYPE.TOTAL ||
            type === HOSPITAL_CARDS_TYPE.UNPLANNED ||
            type === HOSPITAL_CARDS_TYPE.PLANNED
        ) {
            censusByPeriod = await getCensusAverageByPeriod(
                startDateFilter,
                endDateFilter,
                facilityData
            );
        }

        const censusInfo = await getCensusAverageInfo(
            startDateFilter,
            endDateFilter,
            facilityData,
            false,
            censusByFacility,
            bedByFacility,
            censusAsOfNowByFacility
        );
        censusAverage = censusInfo.censusAverage;
        bedCapacity = censusInfo.bedCapacity;
    }
    return {
        data: listData,
        censusAverage,
        bedCapacity,
        censusByPeriod,
        ninetyDaysData,
        censusByFacility,
        bedByFacility,
        censusAsOfNowByFacility,
        diffDashboardPatients,
        customCombineTabData
    };
};

async function ninetyDaysDataFilter(cardFilter, patientData, ninetyDaysData) {
    let ninetyDaysDataIds = [];
    let ninetyDaysDataFilter = _.filter(ninetyDaysData, ({ _id }) =>
        _.every([_.includes(cardFilter, _id)])
    );
    if (ninetyDaysDataFilter && ninetyDaysDataFilter.length > 0) {
        ninetyDaysDataFilter.map(
            (item) => (ninetyDaysDataIds = [...ninetyDaysDataIds, ...item.ids])
        );
    }
    patientData = _.filter(patientData, ({ id }) =>
        _.every([_.includes(ninetyDaysDataIds, id)])
    );
    return patientData;
}

const getReHospitalization = async (transFerPatient, facilityFilter) => {
    const past30Days = getDaysBefore(transFerPatient.dateOfADT, 30);
    if (transFerPatient.firstName && transFerPatient.lastName) {
        let query = {
            _id: { $ne: transFerPatient._id },
            ...(await checkADTDuplicate(transFerPatient, true)),
            dateOfADT: {
                $gte: past30Days,
                $lt: await toEndFilterDate(transFerPatient.dateOfADT),
            },
            type: {
                $in: [ADT_TYPES.TRANSFER],
            },
            ...facilityFilter,
        };

        const reHospitalizationArr = await Patient.aggregate([
            {
                $match: {
                    $and: [query],
                },
            },
            {
                $group: {
                    _id: "$_id",
                    total: {
                        $sum: 1,
                    },
                },
            },
        ]);
        if (reHospitalizationArr.length > 0) {
            return true;
        } else {
            if (transFerPatient.hasOwnProperty("isHospitalPrior")) {
                return transFerPatient.isHospitalPrior;
            } else {
                return false;
            }
        }
    }
};

const getHospitalDays = async (
    transFerPatient,
    facilityFilter,
    endDate = null
) => {
    if (transFerPatient.firstName && transFerPatient.lastName) {
        let arrayInType = [ADT_TYPES.ADMISSIONS, ADT_TYPES.READMISSIONS, ADT_TYPES.RETURN];
        let dateOfADTFilter = {
            $gte: transFerPatient.dateOfADT,
            $lt: await toEndFilterDate(endDate),
        };
        let query = {
            _id: { $ne: transFerPatient._id },
            ...(await checkADTDuplicate(transFerPatient)),
            dateOfADT: dateOfADTFilter,
            type: { $in: arrayInType },
            createdAt: { $gte: transFerPatient.createdAt },
            ...facilityFilter,
        };

        const returnDataArr = await Patient.findOne({ ...query })
            .select(["_id", "firstName", "lastName", "DOB", "dateOfADT", "createdAt", "type"])
            .sort({ dateOfADT: 1 })
            .exec();

        let days = 0
        if (returnDataArr) {
            let matchedData = returnDataArr;
            let isSameDate = false;
            if (moment.utc(matchedData.dateOfADT).isSame(transFerPatient.dateOfADT) &&
                moment.utc(matchedData.createdAt).isAfter(transFerPatient.createdAt)
            ) {
                isSameDate = true;
            }
            if (
                moment.utc(matchedData.dateOfADT).isAfter(transFerPatient.dateOfADT) ||
                isSameDate == true
            ) {
                days = moment.utc(matchedData.dateOfADT).diff(transFerPatient.dateOfADT, "days");
                return days > 100 ? 100 : days;
            }
        }
        days = moment.utc(endDate).diff(transFerPatient.dateOfADT, "days");
        return days > 100 ? 100 : days;
    } else {
        return 0;
    }
};


const getAllCount = async (req) => {
    // Use common initialization
    let {
        user,
        facilityid,
        facilityIds,
        startDateFilter,
        endDateFilter,
        customTabs,
        diffDashboardPatients,
        customCombineTabData,
        isCustomCombineTab
    } = await initializeGetAllCount(req, PAGE_TYPE.HOSPITAL);

    // Use common facility filter setup
    let { facilityFilter, query, facilityData } = await setupFacilityFilter(facilityid, facilityIds);    
    // Hospital specific query setup
    query.type = ADT_TYPES.TRANSFER;
    query.transferType = {
        $in: [
            ADT_SUB_TYPES.PLANNED_HOSPITAL_TRANSFER,
            ADT_SUB_TYPES.UNPLANNED_HOSPITAL_TRANSFER,
        ],
    };
    // Use common date filter setup
    await setupDateFilter(query, startDateFilter, endDateFilter);

    if (isCustomCombineTab) {
        customCombineTabData = await getCustomCombineTabData(query, PAGE_TYPE.HOSPITAL, facilityFilter, customCombineTabData, customTabs);
    }

    const windowStart = moment(startDateFilter).subtract(30, 'days').toDate();

    const ALL_EVENT_TYPES = [
        ADT_TYPES.ADMISSIONS,
        ADT_TYPES.READMISSIONS,
        ADT_TYPES.RETURN,
        ADT_TYPES.TRANSFER
    ];
    const rawEvents = await Patient.find({
        type: { $in: ALL_EVENT_TYPES },
        dateOfADT: { $gte: windowStart },
        ...(facilityFilter || {})
    }).select(`
      _id type dateOfADT createdAt
      firstName lastName DOB
    `).lean();

    const norm = s => s
        .toLowerCase()
        .trim()
        .replace(/[~!#$%^&*+=\-[\]\\';,/{}|\\":<>\?\s]/g, '');

    const formatDOB = dob => moment(dob).format('YYYY-MM-DD');


    const bucketsByDob = new Map();

    for (const ev of rawEvents) {
        const dob = formatDOB(ev.DOB);
        const fn = norm(ev.firstName);
        const ln = norm(ev.lastName);

        const dobMap = bucketsByDob.get(dob) || bucketsByDob.set(dob, new Map()).get(dob);
        const fnMap = dobMap.get(fn) || dobMap.set(fn, new Map()).get(fn);
        const lnList = fnMap.get(ln) || fnMap.set(ln, []).get(ln);
        lnList.push(ev);
    }
    
    let list = await Patient.find({ ...query })
        .select("-logs -notes -middleInitial -highlighter -updatedAt")
        .populate([
            { path: "insurance", select: "_id label type facilityId" },
            { path: "unit", select: "_id label facilityId type" },
            { path: "dx", select: "_id label facilityId type" },
            { path: "doctor", select: "_id label facilityId type" },
            { path: "nurse", select: "_id label facilityId type" },
            { path: "hospital", select: "_id label facilityId type" },
        ])
        .sort({ dateOfADT: 1 })
        .lean();

    const seenPatientIds = new Set();

    let listData = await Promise.all(
        list.map(async (item) => {
            try {
                if (customTabs?.length > 0) {
                    await getOtherDashboardData(item, facilityFilter, diffDashboardPatients, seenPatientIds);
                }

                const latestObj = { ...item };
                const firstNameNorm = norm(item.firstName).match(/^\w+/)?.[0] || '';
                const lastNameNorm = norm(item.lastName).match(/^\w+/)?.[0] || '';
                const dobFormatted = formatDOB(item.DOB);

                const matchingBuckets = getBucketsByPrefix(
                    bucketsByDob,
                    firstNameNorm,
                    lastNameNorm,
                    dobFormatted
                );

                const rawEventsForItem = matchingBuckets.flat();

                const flags = await flagsForTransfer(item, rawEventsForItem);
                const reHospitalization = flags.reHospitalization;
                const wasReturned = flags.wasReturned;

                latestObj.id = item._id.toString();
                latestObj.reHospitalization = reHospitalization;
                latestObj.wasReturned = wasReturned;
                latestObj.dateOfADTOriginal = item.dateOfADT;
                latestObj.DOBOriginal = item.DOB;
                latestObj.dateOfADT = await momentDateFormat(item.dateOfADT, "YYYY-MM-DD");
                latestObj.DOB = await momentDateFormat(item.DOB, "YYYY-MM-DD");
                latestObj.day = await getDayNameFromDate(item.dateOfADT);
                latestObj.shiftName = item.transferTime ? await getShiftName(item.transferTime) : null;
                latestObj.insuranceId = item.insurance?._id?.toString() || null;
                latestObj.floorId = item.unit?._id?.toString() || null;
                latestObj.nurseId = item.nurse?._id?.toString() || null;
                latestObj.doctorId = item.doctor?._id?.toString() || null;
                latestObj.hospitalId = item.hospital?._id?.toString() || null;
                latestObj.hospitalIdStr = item.hospital?._id?.toString() || null;
                latestObj.hospitalName = item.hospital?.label || null;
                latestObj.dxIds = item.dx && item.dx.length > 0 ? item.dx.map((user) => user._id.toString()) : [];
                return latestObj;
            } catch (err) {
                console.error('Error processing item:', item, err);
                return null; // or a default object
            }
        })
    );

    listData = listData?.filter(item => item !== null) ?? [];

    const resArr = await getNinetyDaysChartCount(listData);
    const ninetyDaysDataRes = resArr?.ninetyDaysDataChart ?? [];
    listData = resArr?.patientListRes ?? listData;

    const hospitalSlidesData = await _.groupBy(listData, "hospitalId");

    let hospitalsListData = [];

    if (!_.isEmpty(hospitalSlidesData)) {
        for (const key in hospitalSlidesData) {
            if (hospitalSlidesData.hasOwnProperty(key)) {
                if (hospitalSlidesData[key].length > 0) {
                    let patientList = hospitalSlidesData[key];
                    let patient = hospitalSlidesData[key][0];
                    let isDidNotReturn = 0;
                    await filterData(patientList, async (item) => {
                        if (!item.wasReturned) {
                            isDidNotReturn++;
                        }
                    });

                    let object = {
                        _id: key,
                        hospitalId: key,
                        name: patient?.hospitalName || "",
                        label: patient?.hospitalName || "",
                        totalTransfer: hospitalSlidesData[key].length,
                        totalDidNotReturn: isDidNotReturn,
                        ids: _.map(patientList, (ele) => {
                            return ele._id.toString();
                        }),
                        graphData: [
                            {
                                id: "Total Transfer",
                                label: "Transferred",
                                value: hospitalSlidesData[key].length,
                                color: "#15BDB2",
                            },
                            {
                                id: "Total DidNot Return",
                                label: "Didn't Return",
                                value: isDidNotReturn,
                                color: "#076673",
                            },
                        ],
                    };

                    hospitalsListData.push(object);
                }
            }
        }
    }

    let plannedHospitalTransfer = 0;
    let unplannedHospitalTransfer = 0;
    const total = list.length;

    // Get census info using common utility
    const censusInfo = await getCensusInfo(startDateFilter, endDateFilter, facilityData);

    if (total > 0) {
        const transferTypeTotal = _.countBy(list, "transferType");
        plannedHospitalTransfer = transferTypeTotal?.plannedHospitalTransfer || 0;
        unplannedHospitalTransfer =
            transferTypeTotal?.unplannedHospitalTransfer || 0;
    }

    // Use common finalization
    const { enrichedDiffDashboardPatients, isAutomaticReportSaved } = await finalizeGetAllCount(
        diffDashboardPatients,
        customTabs,
        PAGE_TYPE.HOSPITAL,
        user?.id
    );

    return {
        customCombineTabData,
        diffDashboardPatients: enrichedDiffDashboardPatients,
        customTabs,
        list: listData,
        ninetyDaysData: ninetyDaysDataRes,
        hospitalData: hospitalsListData,
        totals: {
            ...censusInfo,
            total,
            plannedHospitalTransfer,
            unplannedHospitalTransfer,
            isAutomaticReportSaved
        },
    };
};

function getBucketsByPrefix(buckets, fnFrag, lnFrag, dobStr) {
    const result = [];
    const dobMap = buckets.get(dobStr);
    if (!dobMap) return result;

    for (const [fn, lnMap] of dobMap) {
        if (!fn.startsWith(fnFrag)) continue;
        for (const [ln, bucket] of lnMap) {
            if (ln.startsWith(lnFrag)) result.push(bucket);
        }
    }

    return result;
}

module.exports = {
    getDashboardData,
    getCardPatientChartData,
    getAllCount,
    getReturnAndDidNotReturn,
    getReHospitalization,
    getShiftName,
    ninetyDaysDataFilter,
    getHospitalDays,
    getOtherDashboardData,
    getCustomCombineTabData,
    createDiffDashboardPatients
};
