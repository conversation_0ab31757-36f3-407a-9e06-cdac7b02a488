/* eslint-disable array-callback-return */
import _ from "lodash";
import { store } from "..";
import { TYPES } from "../data/hospital.data";
import {
	setFacilityPercentage as setFacilityPercentageComparison,
	setFilterTotal as setFilterTotalComparison,
	setIsCensusTotalLocked as setIsCensusTotalLockedComparison,
	setLockedByFacility as setLockedByFacilityComparison,
	setLockedTotalBy as setLockedTotalByComparison,
	setLockTotal as setLockTotalComparison,
} from "../store/reducers/comparisonReducers/deceasedComparison.slice";
import {
	setFacilityPercentage,
	setFilterTotal,
	setIsCensusTotalLocked,
	setLockedByFacility,
	setLockedTotalBy,
	setLockTotal,
} from "../store/reducers/deceased.slice";
import { TOTAL_TYPE } from "../types/common.type";
import { DECEASED_CARDS_TYPE } from "../types/deceased.type";
import { dynamicCardFilter, filterCustomPatientData, getDynamicPercentageBy, processDynamicCard, updateListTotalValue } from "./common";
import { batch } from "react-redux";
import { getCustomTabsCards } from "./custom-tab-filter";
import { doctorData, floorsData, insuranceData } from "./hospital-common";
import { PAGE_TYPE } from "../types/pages.type";

export async function plannedFilter(patientData, oldFilter, type = null) {
	if (!Array.isArray(patientData) || !oldFilter) return [];

	const filterKeys = [
		{ key: "insuranceData", idKey: "insuranceId" },
		{ key: "doctorData", idKey: "doctorId" },
		{ key: "floorsData", idKey: "floorId" }
	];

	return patientData.filter(patient => {
		return filterKeys.every(({ key, idKey }) => {
			// Skip this filter if it's the excluded type
			if (type === key) return true;
			const filterArr = oldFilter[key];
			// If filter is not set or empty, don't filter by this key
			if (!Array.isArray(filterArr) || filterArr.length === 0) return true;
			return filterArr.includes(patient[idKey]);
		});
	});
}

// * added comparison
export async function updateFacilityPercentageTotal(data, forComparison) {
	if (!Array.isArray(data) || data.length === 0) {
		const emptyResult = [];
		if (forComparison) {
			store.dispatch(setFacilityPercentageComparison(emptyResult));
		} else {
			store.dispatch(setFacilityPercentage(emptyResult));
		}
		return;
	}

	const facilityWiseTotal = Object.values(
		data.reduce((acc, item, idx) => {
			const facilityId = item?.facilityId;
			if (facilityId == null) return acc;
			if (!acc[facilityId]) {
				acc[facilityId] = { id: facilityId, total: 0, firstIndex: idx };
			}
			acc[facilityId].total += 1;
			return acc;
		}, {})
	)
	.sort((a, b) => a.firstIndex - b.firstIndex)
	.map(({ id, total }) => ({ id, total }));

	if (forComparison) {
		store.dispatch(setFacilityPercentageComparison(facilityWiseTotal));
	} else {
		store.dispatch(setFacilityPercentage(facilityWiseTotal));
	}
}

// * added comparison
export async function updateFilterListData(cardFilter, patientList, priorityData = [], forComparison) {
	let objectCustom = {};
	let newSavedFilters = [];
	let patientFilterData = patientList.list;
	const diffDashboardPatients = patientList?.diffDashboardPatients ?? [];
	const {
		dbData,
		lockedTotal,
		transferType,
		lockedTotalBy
	} = forComparison ? store.getState().deceasedComparison : store.getState().deceased;

	const { dynamicCards: storeDynamicCards } = store.getState().deceased;
	const dynamicCards = patientList?.dynamicCards ?? storeDynamicCards;
	const customTabs = patientList?.customTabs ?? [];
	const customCombineTabData = patientList?.customCombineTabData ?? [];

	let censusAverage = dbData?.censusAverage;
	if (!forComparison) {
		censusAverage = await getDynamicPercentageBy(dbData);
	}

	let lockedTotalModified = lockedTotal;
	let isComparingAgainstAvgCensus = true;

	let totalFilterData = {
		originalData: patientList.list,
		totalType: TOTAL_TYPE.FILTER,
		totalForPercentage: !transferType ? censusAverage : patientFilterData.length,
	};
	let mainNumPercentage = censusAverage;

	await batch(async () => {
		if (transferType) {
			mainNumPercentage = null;
			isComparingAgainstAvgCensus = false;
			totalFilterData.totalForPercentage = patientFilterData.length;
		}

		const newPayload = [];
		const facilityPercentageSelector = forComparison
			? store.getState().deceasedComparison.facilityPercentage
			: store.getState().admission.facilityPercentage;
		if (!_.isEqual(facilityPercentageSelector, newPayload)) {
			forComparison
				? store.dispatch(setFacilityPercentageComparison(newPayload))
				: store.dispatch(setFacilityPercentage(newPayload));
		}

		if (!lockedTotalModified) {
			const newSetIsCensusTotalLocked = false;
			const isCensusTotalLockedSelector = forComparison
				? store.getState().deceasedComparison.isCensusTotalLocked
				: store.getState().admission.isCensusTotalLocked;
			if (!_.isEqual(isCensusTotalLockedSelector, newSetIsCensusTotalLocked)) {
				forComparison
					? store.dispatch(setIsCensusTotalLockedComparison(newSetIsCensusTotalLocked))
					: store.dispatch(setIsCensusTotalLocked(newSetIsCensusTotalLocked));
			}

			const newLockedTotalBy = null;
			const lockedTotalBySelector = forComparison
				? store.getState().deceasedComparison.lockedTotalBy
				: store.getState().admission.lockedTotalBy;
			if (!_.isEqual(lockedTotalBySelector, newLockedTotalBy)) {
				forComparison
					? store.dispatch(setLockedTotalByComparison(newLockedTotalBy))
					: store.dispatch(setLockedTotalBy(newLockedTotalBy));
			}
		}

		if (lockedTotalBy) {
			const newLockedTotalBy = null;
			const newLockTotal = null;
			if (lockedTotalBy === TYPES.ALL || lockedTotalBy === "census") {
				if (lockedTotalModified && transferType !== lockedTotalBy && lockedTotalBy !== "census") {
					const lockedTotalBySelector = forComparison
						? store.getState().deceasedComparison.lockedTotalBy
						: store.getState().admission.lockedTotalBy;
					if (!_.isEqual(lockedTotalBySelector, newLockedTotalBy)) {
						forComparison
							? store.dispatch(setLockedTotalByComparison(newLockedTotalBy))
							: store.dispatch(setLockedTotalBy(newLockedTotalBy));
					}

					const lockedTotalSelector = forComparison
						? store.getState().deceasedComparison.lockedTotal
						: store.getState().admission.lockedTotal;
					if (!_.isEqual(lockedTotalSelector, newLockTotal)) {
						forComparison
							? store.dispatch(setLockTotalComparison(newLockTotal))
							: store.dispatch(setLockTotal(newLockTotal));
					}
					lockedTotalModified = null;
				}
			} else {
				const lockedFilterRemoved = _.find(priorityData, { type: lockedTotalBy });
				if (!lockedFilterRemoved) {
					const lockedTotalBySelector = forComparison
						? store.getState().deceasedComparison.lockedTotalBy
						: store.getState().admission.lockedTotalBy;
					if (!_.isEqual(lockedTotalBySelector, newLockedTotalBy)) {
						forComparison
							? store.dispatch(setLockedTotalByComparison(newLockedTotalBy))
							: store.dispatch(setLockedTotalBy(newLockedTotalBy));
					}

					const lockedTotalSelector = forComparison
						? store.getState().deceasedComparison.lockedTotal
						: store.getState().admission.lockedTotal;
					if (!_.isEqual(lockedTotalSelector, newLockTotal)) {
						forComparison
							? store.dispatch(setLockTotalComparison(newLockTotal))
							: store.dispatch(setLockTotal(newLockTotal));
					}
					lockedTotalModified = null;
				}
			}
		}

		if (lockedTotalModified) {
			totalFilterData.totalForPercentage = lockedTotalModified;
		}
		if (lockedTotalModified && priorityData.length === 1 && !transferType) {
			totalFilterData.totalForPercentage = lockedTotalModified;
		}

		if (priorityData?.length > 0) {
			isComparingAgainstAvgCensus = false;
		}

		if (
			(priorityData?.length > 0 && (!lockedTotalBy || !lockedTotal)) ||
			(priorityData?.length > 0 && lockedTotalBy !== "census" && lockedTotalBy !== TYPES.ALL)
		) {
			totalFilterData.totalForPercentage = null;
		}
	});

	let dynamicCardsObj = {};
	let customTabsObj = {};

	if (priorityData?.length > 0) {
		let i = 0;
		for await (const ele of priorityData) {
			i++;
			if (priorityData.length === i && lockedTotalModified && !lockedTotalBy) {
				forComparison
					? store.dispatch(setLockedTotalByComparison(ele.type))
					: store.dispatch(setLockedTotalBy(ele.type));
			}
			if (ele?.question?.isCustom) {
				const customTabsRes = customTabs.filter((x) => x.accessor === ele.type);
				if (customTabsRes.length > 0) {
					customTabsObj = await getCustomTabsCards(
						patientFilterData,
						customTabsRes,
						totalFilterData,
						{
							ninetyDaysData: patientList.ninetyDaysData,
							dynamicCards,
							pageType: PAGE_TYPE.DECEASED,
							diffDashboardPatients,
							customCombineTabData
						}
					);
				}
				objectCustom = { ...objectCustom, ...dynamicCardsObj, ...customTabsObj };
				patientFilterData = filterCustomPatientData(patientFilterData, cardFilter, ele);
			}

			if (ele.type === DECEASED_CARDS_TYPE.INSURANCE_DATA) {
				patientFilterData = _.filter(patientFilterData, ({ insuranceId }) =>
					_.includes(cardFilter.insuranceData, insuranceId)
				);
			}

			if (ele.type === DECEASED_CARDS_TYPE.FLOORS_DATA) {
				objectCustom.floorsData = await floorsData(patientFilterData, {
					...totalFilterData,
					totalType: TOTAL_TYPE.MAIN,
					totalForPercentage: mainNumPercentage && i === 1 ? mainNumPercentage : totalFilterData.totalForPercentage,
				});
				patientFilterData = _.filter(patientFilterData, ({ floorId }) =>
					_.includes(cardFilter.floorsData, floorId)
				);
			}

			if (ele.type === DECEASED_CARDS_TYPE.DOCTOR_DATA) {
				objectCustom.doctorData = await doctorData(patientFilterData, {
					...totalFilterData,
					totalType: TOTAL_TYPE.MAIN,
					totalForPercentage: mainNumPercentage && i === 1 ? mainNumPercentage : totalFilterData.totalForPercentage,
				});
				patientFilterData = _.filter(patientFilterData, ({ doctorId }) =>
					_.includes(cardFilter.doctorData, doctorId)
				);
			}

			if (ele.type === DECEASED_CARDS_TYPE.NINETY_DAYS_DATA) {
				objectCustom.ninetyDaysData = await ninetyDaysDataList(
					patientList.ninetyDaysData,
					patientFilterData,
					{
						...totalFilterData,
						totalType: TOTAL_TYPE.MAIN,
						totalForPercentage: mainNumPercentage && i === 1 ? mainNumPercentage : totalFilterData.totalForPercentage,
					}
				);
				if (i === priorityData.length) {
					updateFacilityPercentageTotal(patientFilterData, forComparison);
				}
				patientFilterData = await ninetyDaysDataFilter(
					cardFilter.ninetyDaysData,
					patientFilterData,
					patientList.ninetyDaysData
				);
			}
			newSavedFilters.push(ele.type);
		}

		const totalCount = lockedTotalModified ? lockedTotalModified : patientFilterData.length;
		const filterTotalSelector = forComparison
			? store.getState().deceasedComparison.filterTotal
			: store.getState().admission.filterTotal;
		if (!_.isEqual(filterTotalSelector ?? 0, totalCount ?? 0)) {
			forComparison
				? store.dispatch(setFilterTotalComparison(totalCount))
				: store.dispatch(setFilterTotal(totalCount));
		}

		const addIfNotIncluded = async (type, fn) => {
			if (!_.includes(newSavedFilters, type)) {
				objectCustom[type] = await fn();
			}
		};

		await addIfNotIncluded(DECEASED_CARDS_TYPE.INSURANCE_DATA, async () =>
			insuranceData(patientFilterData, { ...totalFilterData, totalForPercentage: totalCount })
		);
		await addIfNotIncluded(DECEASED_CARDS_TYPE.FLOORS_DATA, async () =>
			floorsData(patientFilterData, { ...totalFilterData, totalForPercentage: totalCount })
		);
		await addIfNotIncluded(DECEASED_CARDS_TYPE.DOCTOR_DATA, async () =>
			doctorData(patientFilterData, { ...totalFilterData, totalForPercentage: totalCount })
		);
		await addIfNotIncluded(DECEASED_CARDS_TYPE.NINETY_DAYS_DATA, async () =>
			ninetyDaysDataList(patientList.ninetyDaysData, patientFilterData, { ...totalFilterData, totalForPercentage: totalCount })
		);

		if (dynamicCards.length > 0) {
			for (const item of dynamicCards) {
				if (!_.includes(newSavedFilters, item?.accessor)) {
					let objectCustomRes = {};
					await processDynamicCard(item, patientFilterData, objectCustomRes, {
						...totalFilterData,
						totalForPercentage: totalCount,
					});
					if (!_.isEmpty(objectCustomRes)) {
						objectCustom[item?.accessor] = objectCustomRes?.[item?.accessor];
					}
				}
			}
		}

		if (customTabs.length > 0) {
			const isCheckNewFilters = true;
			const customTabsObjRes = await getCustomTabsCards(
				patientFilterData,
				customTabs,
				{ ...totalFilterData, totalType: TOTAL_TYPE.FILTER, totalForPercentage: totalCount },
				{
					ninetyDaysData: patientList.ninetyDaysData,
					dynamicCards,
					newSavedFilters,
					isCheckNewFilters,
					objectCustom,
					pageType: PAGE_TYPE.DECEASED,
					diffDashboardPatients
				}
			);
			objectCustom = { ...objectCustom, ...customTabsObjRes };
		}

		if (!lockedTotalModified) {
			const lockedByFacilitySelector = forComparison
				? store.getState().deceasedComparison.lockedByFacility
				: store.getState().admission.lockedByFacility;
			if (!_.isEqual(lockedByFacilitySelector ?? [], patientFilterData ?? [])) {
				forComparison
					? store.dispatch(setLockedByFacilityComparison(patientFilterData))
					: store.dispatch(setLockedByFacility(patientFilterData));
			}
		}
	} else {
		await batch(async () => {
			let totalCount;
			if (mainNumPercentage) {
				totalCount = mainNumPercentage;
			} else if (transferType && transferType.length > 0) {
				totalCount = patientFilterData.length;
			} else {
				totalCount = censusAverage;
			}
			const filterTotal = lockedTotalModified ? lockedTotalModified : totalCount;
			forComparison
				? store.dispatch(setFilterTotalComparison(filterTotal))
				: store.dispatch(setFilterTotal(filterTotal));

			if (lockedTotalModified && !lockedTotalBy) {
				if (transferType) {
					forComparison
						? store.dispatch(setLockedTotalByComparison(transferType))
						: store.dispatch(setLockedTotalBy(transferType));
				} else {
					forComparison
						? store.dispatch(setLockedTotalByComparison("census"))
						: store.dispatch(setLockedTotalBy("census"));
				}
			}
			if (!transferType && lockedTotalModified) {
				const isCensusTotalLocked = true;
				const isCensusTotalLockedSelector = forComparison
					? store.getState().deceasedComparison.isCensusTotalLocked
					: store.getState().admission.isCensusTotalLocked;
				if (!_.isEqual(isCensusTotalLockedSelector, isCensusTotalLocked)) {
					forComparison
						? store.dispatch(setIsCensusTotalLockedComparison(isCensusTotalLocked))
						: store.dispatch(setIsCensusTotalLocked(isCensusTotalLocked));
				}
			}
			if (!lockedTotalModified) {
				const lockedByFacilitySelector = forComparison
					? store.getState().deceasedComparison.lockedByFacility
					: store.getState().admission.lockedByFacility;
				if (!_.isEqual(lockedByFacilitySelector ?? [], patientFilterData ?? [])) {
					forComparison
						? store.dispatch(setLockedByFacilityComparison(patientFilterData))
						: store.dispatch(setLockedByFacility(patientFilterData));
				}
			}
			updateFacilityPercentageTotal(patientFilterData, forComparison);
		});

		[
			objectCustom.insuranceData,
			objectCustom.floorsData,
			objectCustom.doctorData,
			objectCustom.ninetyDaysData
		] = await Promise.all([
			insuranceData(patientFilterData, totalFilterData),
			floorsData(patientFilterData, totalFilterData),
			doctorData(patientFilterData, totalFilterData),
			ninetyDaysDataList(patientList.ninetyDaysData, patientFilterData, totalFilterData),
		]);

		if (dynamicCards.length > 0) {
			dynamicCardsObj = await dynamicCardFilter(patientFilterData, dynamicCards, totalFilterData);
		}
		if (customTabs.length > 0) {
			customTabsObj = await getCustomTabsCards(
				patientFilterData,
				customTabs,
				totalFilterData,
				{
					ninetyDaysData: patientList.ninetyDaysData,
					dynamicCards,
					pageType: PAGE_TYPE.DECEASED,
					diffDashboardPatients,
					customCombineTabData
				}
			);
		}
		objectCustom = { ...objectCustom, ...dynamicCardsObj, ...customTabsObj };
	}

	objectCustom.isComparingAgainstAvgCensus = isComparingAgainstAvgCensus;
	return objectCustom;
}

async function ninetyDaysDataFilter(cardFilter, patientData, ninetyDaysData) {
	if (!Array.isArray(cardFilter) || !Array.isArray(patientData) || !Array.isArray(ninetyDaysData)) {
		return [];
	}

	// Collect all patient IDs from matching ninetyDaysData entries
	const matchingIds = new Set(
		ninetyDaysData
			.filter(item => cardFilter.includes(item._id) && Array.isArray(item.ids))
			.flatMap(item => item.ids)
	);

	// Return patients whose _id is in the matchingIds set
	return patientData.filter(patient => matchingIds.has(patient._id));
}

async function ninetyDaysDataList(data, patients, totalFilter) {
	const patientIds = Array.isArray(patients) ? patients.map(({ id }) => id) : [];
	const percentageTotal = totalFilter?.totalForPercentage ?? patientIds.length;

	return updateListTotalValue(data, patientIds, "value", percentageTotal, totalFilter);
}


