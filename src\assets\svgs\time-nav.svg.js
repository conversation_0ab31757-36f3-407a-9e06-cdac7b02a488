const TimeNavSVG = (props) => (
  <svg
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M10 0C7.34792 0 4.80414 1.05363 2.92894 2.92894C1.05363 4.80442 0 7.34791 0 10C0 12.6521 1.05363 15.1959 2.92894 17.0711C4.80441 18.9464 7.34792 20 10 20C12.6521 20 15.1959 18.9464 17.0711 17.0711C18.9464 15.1956 20 12.6521 20 10C19.9968 7.34881 18.9423 4.80687 17.0676 2.93245C15.1929 1.05776 12.6509 0.00319481 10 0V0ZM10 18.182C7.83006 18.182 5.74895 17.32 4.21466 15.7856C2.68025 14.2512 1.81824 12.1701 1.81824 10.0002C1.81824 7.83037 2.68025 5.74918 4.21466 4.21489C5.74907 2.68049 7.83014 1.81847 10 1.81847C12.1699 1.81847 14.251 2.68049 15.7853 4.21489C17.3197 5.7493 18.1818 7.83037 18.1818 10.0002C18.1792 12.1694 17.3162 14.2489 15.7825 15.7828C14.2487 17.3166 12.1691 18.1796 9.99984 18.1822L10 18.182Z"
      fill="#444652"
    />
    <path
      d="M10.0002 2.72725C8.07207 2.72953 6.22337 3.49642 4.86004 4.85975C3.49671 6.22323 2.7298 8.0719 2.72754 9.99989C2.72754 10.241 2.82342 10.4723 2.99387 10.6427C3.16433 10.8132 3.39552 10.9089 3.63673 10.9089H10.0003C10.2413 10.9089 10.4727 10.8132 10.6431 10.6427C10.8136 10.4723 10.9093 10.241 10.9093 9.99989V3.63637C10.9093 3.39514 10.8136 3.16396 10.6431 2.99351C10.4727 2.82305 10.2413 2.72717 10.0003 2.72717L10.0002 2.72725ZM9.09114 9.09077L4.62113 9.09092C4.81198 7.97476 5.34432 6.94549 6.14502 6.1448C6.94571 5.34411 7.97498 4.81174 9.09114 4.62091V9.09077Z"
      fill="#444652"
    />
  </svg>
);

export default TimeNavSVG;
