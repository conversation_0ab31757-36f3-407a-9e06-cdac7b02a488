const { createFilter, getFilters, deleteFilter } = require("../helpers/filter");
const authWithRole = require("../middleware/auth-with-role");

const route = require("express").Router();

// Create new account
route.post("/", authWithRole("manageDashboard"), async (req, res) => {
  try {
    let account = await createFilter(req);
    res.send(account);
  } catch (error) {
    res.status(500).send({ message: error.message });
  }
});

// Get accounts/account by ID
route.get("/:id?", authWithRole("manageDashboard"), async (req, res) => {
  try {
    let account = await getFilters(req);
    res.send(account);
  } catch (error) {    
    res.status(500).send(error);
  }
});

route.delete("/:id", authWithRole("manageDashboard"), async (req, res) => {
  try {
    const filter = await deleteFilter(req.params.id);
    res.send(filter);
  } catch (error) {
    res.status(500).send(error);
  }
});

module.exports = route;
