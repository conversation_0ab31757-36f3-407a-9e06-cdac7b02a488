const mongoose = require("mongoose");
const Role = mongoose.model("role");
const Permission = mongoose.model("permission");
const RoleHasPermissions = mongoose.model("roleHasPermissions");

const asyncFilter = async (arr, predicate) => {
    const results = await Promise.all(arr.map(predicate));
    return arr.filter((_v, index) => results[index]);
}


const getOnlyRolesList = async (req) => {
    let slugs = [];
    if (req.user && req.user.role) {
        const roleName = req.user.role.slug;
        if (roleName == "super") {
            slugs = ["owner", "total", "regional", "admin", "user"];
        }
        if (roleName == "owner") {
            slugs = ["owner", "total", "regional", "admin", "user"];
        }
        if (roleName == "total") {
            slugs = ["total", "regional", "admin", "user"];
        }
        if (roleName == "regional") {
            slugs = ["regional", "admin", "user"];
        }
        if (roleName == "admin") {
            slugs = ["admin", "user"];
        }
    }

    const roles = await Role.find({ active: true, slug: { $in: slugs } }).sort({ order: 1 });
    return roles;
}

const getRolesList = async (req) => {
    let newResult = [];
    const roles = await Role.find({ active: true }).sort({ name: -1 });

    if (roles && roles.length > 0) {
        await asyncFilter(roles, async (item) => {
            const perData = await RoleHasPermissions.find({ roleId: item.id }).select('permissionId');
            let permissionIds = [];
            await asyncFilter(perData, async ele => {
                if (ele.permissionId) {
                    permissionIds.push(ele.permissionId);
                }
            });
            newResult.push({
                ...item._doc,
                permissionIds
            });
        });
    }

    const allPermissions = await Permission.find({ active: true });

    return { data: newResult, allPermissions };
};

const deleteRole = async (id) => {
    let deleted = await Role.findByIdAndDelete(id);
    return deleted;
};

const updateRole = async (id, data) => {
    let role = await Role.findById(id);
    role.name = data.name;
    await role.save();

    await RoleHasPermissions.findOne({ roleId: role.id }).remove().exec();
    if (data && data.permissions.length > 0) {
        let rolePermissionsData = [];
        data.permissions.map((item) => {
            rolePermissionsData.push({
                roleId: role.id,
                permissionId: item
            })
        });
        if (rolePermissionsData.length > 0) {
            await RoleHasPermissions.insertMany(rolePermissionsData).then(function () {
            }).catch(function (error) {
                console.log(error)
            });
        }
    }

    let roleData = await Role.findById(id);
    return roleData;
};

const getRole = async (id) => {
    const role = await Role.findOne(id);
    return role;
};

module.exports = {
    getRolesList,
    updateRole,
    deleteRole,
    getRole,  
    getOnlyRolesList
};
