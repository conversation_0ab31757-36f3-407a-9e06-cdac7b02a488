// types
import { createSlice } from "@reduxjs/toolkit";
import { DEFAULT_FILTER } from "../../data/common.data";

const DEFAULT_ADD_DB_DATA = {
	totalAdmissions: 0,
	totalReAdmissions: 0,
	total: 0,
	censusAverage: null,
};

export const DEFAULT_CARD_FILTER = {
	doctorData: [],
	daysData: [],
	dxData: [],
	insuranceData: [],
	floorsData: [],
	hospitalData: [],
	adtData: [],
	adtAdmitPatientIds: [],
	mainPriorityData: [],
	admissionTotal: 0,
};

// initial state
const initialState = {
	transferType: null,
	mainTotal: 0,
	selectedFilter: null,
	detailsDialog: {
		isOpen: false,
		type: null,
		item: null,
	},
	detailsADTDialog: {
		isOpen: false,
		type: null,
		item: null,
		admissionTotal: 0,
	},
	filter: DEFAULT_FILTER,
	cardFilter: DEFAULT_CARD_FILTER,
	dbData: DEFAULT_ADD_DB_DATA,
	selectedADTTableData: [],
	defaultADTData: [],
	dbSelectedFilters: null,
	isAdtLoading: false,
	filterTotal: null,
	filterTotalWithoutLock: null,
	mainNumPercentage: null,
	loading: false,
	adtFacilityPercentage: [],
	lockedByFacility: [],
	isCensusTotalLocked: false,
	facilityPercentage: [],
	lockedTotalBy: null,
	specialComparisonSet: false,
	lockeByADT: null,
	reversedColorAdmission: true,
	percentageAgainst: 0,
	lockFilterTotalState: { count: 0, isLocked: false, lockedFilterTotal: 0 },
	returnedSpecialState: { isLocked: false, lockedFilterTotal: 0 },
	dynamicCards : []
};

// ==============================|| SLICE - ADMISSIONS ||============================== //

const admission = createSlice({
	name: "admission",
	initialState,
	reducers: {
		setDynamicCards(state, action) {
			state.dynamicCards = action.payload;
		},
		setLockedlockeByADT(state, action) {
			state.lockeByADT = action.payload;
		},
		setLockedTotalBy(state, action) {
			state.lockedTotalBy = action.payload;
		},
		setLockedByFacility(state, action) {
			state.lockedByFacility = action.payload;
		},
		setFacilityPercentage(state, action) {
			state.facilityPercentage = action.payload;
		},
		setIsMainCensusPercentage(state, action) {
			state.mainNumPercentage = action.payload;
		},
		setIsCensusTotalLocked(state, action) {
			state.isCensusTotalLocked = action.payload;
		},
		setLockTotal(state, action) {
			state.lockedTotal = action.payload;
		},
		setDefaultAdmissionData(state, action) {
			state.transferType = null;
			state.cardFilter = DEFAULT_CARD_FILTER;
			state.mainTotal = 0;
			state.filter = DEFAULT_FILTER;
			state.dbData = DEFAULT_ADD_DB_DATA;
		},
		setIsAdtLoading(state, action) {
			state.isAdtLoading = action.payload;
		},
		setDBSelectedFilters(state, action) {
			state.dbSelectedFilters = action.payload;
		},
		setTransferType(state, action) {
			state.transferType = action.payload;
		},
		setMainTotal(state, action) {
			state.mainTotal = action.payload;
		},
		openADTDetailsDialog(state, action) {
			state.detailsADTDialog = { ...action.payload };
		},
		closeADTDetailsDialog(state, action) {
			state.detailsADTDialog = {
				isOpen: false,
				type: null,
				item: null,
				admissionTotal: 0,
			};
		},
		openDetailsDialog(state, action) {
			state.detailsDialog = { ...action.payload };
		},
		closeDetailsDialog(state, action) {
			state.detailsDialog = {
				isOpen: false,
				type: null,
				item: null,
			};
		},

		setFilterDateRange(state, action) {
			state.filter = { ...state.filter, ...action.payload };
		},

		setFilterDBData(state, action) {
			state.percentageAgainst = action.payload?.censusAverage;
			state.dbData = { ...state.dbData, ...action.payload };
		},

		setLoading(state, action) {
			state.loading = action.payload;
		},

		setCardFilter(state, action) {
			state.cardFilter = {
				...state.cardFilter,
				...action.payload,
			};
		},
		setPercentageAgainst(state, action) {
			state.percentageAgainst = action.payload;
		},
		setSelectedFilter(state, action) {
			if (action.payload) {
				// eslint-disable-next-line eqeqeq
				if (action.payload != "all") {
					const { filter } = action.payload;
					if (filter && filter.cardFilter) {
						state.cardFilter = {
							...DEFAULT_CARD_FILTER,
							...filter.cardFilter,
						};
					}
					if (filter && filter.transferType) {
						state.transferType = filter.transferType;
					} else {
						state.transferType = null;
					}
					if (filter && filter.filter) {
						state.filter = filter.filter;
					}
					state.selectedFilter = action.payload._id;
				} else {
					const existingUsers = JSON.parse(JSON.stringify(state.cardFilter.adtData));
					state.selectedFilter = action.payload;
					state.cardFilter = { ...DEFAULT_CARD_FILTER, adtData: existingUsers };
					state.filter = { ...state.filter };
					state.transferType = null;
					state.selectedADTTableData = [];
					state.dbSelectedFilters = null;
					state.lockedTotal = null;
				}
			}
		},
		setSelectedADT(state, action) {
			state.selectedADT = action.payload;
		},
		setSelectedADTTableData(state, action) {
			state.selectedADTTableData = action.payload;
		},
		setDefaultADTData(state, action) {
			state.defaultADTData = action.payload;
		},
		setFilterTotal(state, action) {
			state.filterTotal = action.payload;
		},
		setFilterTotalWithoutLock(state, action) {
			state.filterTotalWithoutLock = action.payload;
		},
		setAdtFacilityPercentage(state, action) {
			state.adtFacilityPercentage = action.payload;
		},
		setIsSpecialComparison(state, action) {
			state.specialComparisonSet = action.payload;
		},
		setLockFilterTotalState(state, action) {
			state.lockFilterTotalState = action.payload;
		},
		setReturnedSpecialState(state, action) {
			state.returnedSpecialState = action.payload;
		},
		setReversedColorAdmission(state, action) {
			state.reversedColorAdmission = action.payload;
		},
	},
});

export default admission.reducer;

export const {
	setIsMainCensusPercentage,
	setLockedlockeByADT,
	setLockTotal,
	setFilterTotal,
	setSelectedADT,
	setTransferType,
	openDetailsDialog,
	closeDetailsDialog,
	setFilterDateRange,
	setFilterDBData,
	setMainTotal,
	setCardFilter,
	setDefaultADTData,
	setSelectedFilter,
	setDBSelectedFilters,
	setDefaultAdmissionData,
	setSelectedADTTableData,
	setIsAdtLoading,
	setFilterPatentIds,
	openADTDetailsDialog,
	closeADTDetailsDialog,
	setLoading,
	setAdtFacilityPercentage,
	setLockedByFacility,
	setIsCensusTotalLocked,
	setFacilityPercentage,
	setLockedTotalBy,
	setIsSpecialComparison,
	setLockFilterTotalState,
	setReversedColorAdmission,
	setReturnedSpecialState,
	setFilterTotalWithoutLock,
	setPercentageAgainst,
	setDynamicCards
} = admission.actions;
