const mongoose = require("mongoose");
const PercentageAgainst = mongoose.model("percentageAgainst");

// Create percentageAgainst
const createPercentageAgainst = async (req) => {
    const user = req?.user;
    const { accountid } = req.headers;

    const percentageAgainst = await PercentageAgainst.findOne({
        userId: mongoose.Types.ObjectId(user._id),
        accountId: mongoose.Types.ObjectId(accountid),
        label: req.body?.label
    });

    if (req.body._id) {        
        if (percentageAgainst && percentageAgainst?._id?.toString() !== req.body._id) {
            throw new Error("Label already taken, Please use another name.");
        } else {
            const percentageAgainst = await PercentageAgainst.findOne({ _id: req.body._id });
            percentageAgainst.customPercentage = req.body?.customPercentage ?? null
            percentageAgainst.label = req.body?.label ?? null
            const updated = await percentageAgainst.save();
            return { status: 200, data: updated }
        }

    } else {
        if (percentageAgainst) {
            throw new Error("Label already taken, Please use another name.");
        } else {
            const percentageAgainstSave = new PercentageAgainst({
                accountId: mongoose.Types.ObjectId(accountid),
                userId: user._id,
                customPercentage: req.body?.customPercentage,
                label: req.body?.label
            });
            const saved = await percentageAgainstSave.save();            
            return { status: 200, data: saved }
        }
    }
};

const getPercentageAgists = async (req) => {
    const user = req?.user;
    const { accountid } = req.headers;
    const percentageAgainstData = await PercentageAgainst.find({
        accountId: accountid,
        userId: user._id,
    });
    return percentageAgainstData;
};

const getPercentageAgainst = async (req) => {
    const { id } = req.query;
    const percentageAgainstData = await PercentageAgainst.findOne({ _id: mongoose.Types.ObjectId(id) });
    return percentageAgainstData;
};

const deletePercentageAgainst = async id => {
    let deleted = await PercentageAgainst.findByIdAndDelete(id);
    return deleted;
};

module.exports = { createPercentageAgainst, getPercentageAgainst, getPercentageAgists, deletePercentageAgainst };
