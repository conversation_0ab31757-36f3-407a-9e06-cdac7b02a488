import { useCallback, useEffect, useState } from 'react';
import { ErrorMessage } from 'formik';
import { FormControl, FormHelperText } from '@mui/material';
import TimezoneSelect from 'react-timezone-select';

const TimeZoneField = ({
    form: { setFieldValue, errors, touched },
    field: { name, value },
    label,
    className = '',
    defaultValue,
    ...props
}) => {
    const [selectedTimezone, setSelectedTimezone] = useState(
        Intl.DateTimeFormat().resolvedOptions().timeZone
    )
    const onChange = useCallback(
        (e) => {
            setFieldValue(name, e.value || '');
            setSelectedTimezone(e.value)
        },
        [setFieldValue, name]
    );

    useEffect(() => {
        if (defaultValue) {
            setFieldValue(name, defaultValue);
            setSelectedTimezone(defaultValue)
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [defaultValue]);

    return (
        <FormControl label={label}>
            <TimezoneSelect
                value={selectedTimezone}
                onChange={onChange}
                className={"timeZone-select"}
            />
            <FormHelperText color="error">
                {' '}
                <ErrorMessage name={name} />
            </FormHelperText>
        </FormControl>
    );
};

export default TimeZoneField;
