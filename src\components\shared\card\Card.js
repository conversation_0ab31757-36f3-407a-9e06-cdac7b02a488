import { useEffect, useState } from "react";
import ExpandPopupSVG from "../../../assets/svgs/expand-popup.svg";
import MinimizeSVG from "../../../assets/svgs/minimize.svg";
import PopupPlusSVG from "../../../assets/svgs/popup-plus.svg";
import styles from "./Card.module.scss";
import { Typography, IconButton, Skeleton } from "@mui/material";
import InfoIcon from "../../icon/InfoIcon";
import HtmlTooltip from "../HtmlTooltip";
import CardTooltipContent from "./CardTooltipContent";
import classNames from "classnames";

const Card = ({
  children,
  flex,
  title,
  secondaryTitle = "",
  minimizable,
  itemAlign,
  isDetailsOpen = true,
  isDisabled = false,
  handleOnClickAction,
  taggedList,
  loading = false,
  isSlider = false,
}) => {
  const [showDetails, setShowDetails] = useState(false);

  useEffect(() => {
    setShowDetails(isDisabled ? false : isDetailsOpen);
  }, [isDetailsOpen, isDisabled]);

  return (
    <div
      className={classNames(styles.card, isDisabled && styles.disabled)}
      style={{ flex, height: `${showDetails ? "100%" : "fit-content"}` }}
    >
      <div className={(classNames("ffmar fs15 fw700"), styles.cardHeaderContainer)}>
        <div className={classNames("df aic ffmar fs15 fw700", styles.cardHeader)}>
          <div className={styles.cardTooltipIcon}>
            <HtmlTooltip content={<CardTooltipContent />}>
              <IconButton disableFocusRipple={true} disableRipple={true}>
                <InfoIcon />
              </IconButton>
            </HtmlTooltip>
          </div>
          <Typography className={styles.title} variant="subtitle1" component="div">
            {title}
            {secondaryTitle && (
              <div className={classNames("fs10", styles.secondaryTitle)}>{secondaryTitle}</div>
            )}
          </Typography>
          <div className={`df aic mla`}>
            {!isDisabled && (
              <div className="cp" onClick={() => handleOnClickAction(title)}>
                <ExpandPopupSVG fill="#4879F5" />
              </div>
            )}
            {minimizable && (
              <div
                className="p-l-9 p-r-5 p-t-5 p-b-5 m-l-15"
                style={{ cursor: "pointer" }}
                onClick={() => setShowDetails(!showDetails)}
              >
                {showDetails ? <MinimizeSVG /> : <PopupPlusSVG />}
              </div>
            )}
          </div>
        </div>
        {taggedList && taggedList}
      </div>
      {!isSlider && showDetails && isDisabled === false && (
        <div className={classNames("df", styles.cardContent, itemAlign ? itemAlign : "aic")}>
          {loading ? <Skeleton variant="rectangular" width={"100%"} height={"100%"} /> : children}
        </div>
      )}
      {isSlider && showDetails && isDisabled === false && children}
    </div>
  );
};

export default Card;
