import {
    getQuickGlaceData,
    saveQuickGlaceData,
    quickGlaceFilterData
} from "./api/quick-glace.api";

const getQuickGlaces = async (type) => {
    try {
        const res = await getQuickGlaceData(type);
        return res;
    } catch (e) {
        console.log(e);
    }
};

const getQuickGlacesFilters = async (type) => {
    try {
        const res = await quickGlaceFilterData(type);
        return res;
    } catch (e) {
        console.log(e);
    }
};

const saveQuickGlace = async (body) => {
    try {
        const res = await saveQuickGlaceData(body);
        return res;
    } catch (e) {
        console.log(e);
    }
};


export {
    getQuickGlaces,
    saveQuickGlace,
    getQuickGlacesFilters
}
