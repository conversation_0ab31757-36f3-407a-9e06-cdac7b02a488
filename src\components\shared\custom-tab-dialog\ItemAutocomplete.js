import React from 'react';
import {
    Autocomplete,
    Checkbox,
    TextField,
    ListItemText,
} from '@mui/material';

const ItemAutocomplete = ({
    label,
    value,
    onChange,
    error = false,
    helperText = '',
    cardItemData = [],
    index,
    filterIndex,
    filter,
    isMainCard,
    handleCardItemChange,
    sx = {},
    disabled = false,
}) => {
    const filteredItems = cardItemData[index]?.items?.filter(
        (item) => item.parentId === filter?.card?.value
    ) || [];

    const allValues = filteredItems.map((item) => item.value);
    const isAllSelected = (filter?.items || []).length === allValues.length && allValues.length > 0;

    const toggleSelectAll = () => {
        if (isAllSelected) {
            handleCardItemChange(index, filterIndex, 'items', [], isMainCard);
        } else {
            handleCardItemChange(index, filterIndex, 'items', allValues, isMainCard);
        }
    };

    return (
        <Autocomplete
            multiple
            fullWidth
            disableCloseOnSelect
            options={['__all__', ...allValues]}
            value={value}
            onChange={(e, newValue) => {
                if (newValue.includes('__all__')) {
                    toggleSelectAll();
                } else {
                    handleCardItemChange(index, filterIndex, 'items', newValue, isMainCard);
                }
            }}
            componentsProps={{
                popper: {
                    sx: {
                        '& .MuiAutocomplete-listbox': {
                            padding: 0,
                        },
                        '& .MuiAutocomplete-option': {
                            minHeight: 32,
                            paddingTop: '2px',
                            paddingBottom: '2px',
                            paddingLeft: '8px',
                            paddingRight: '8px',
                        },
                        '& .MuiCheckbox-root': {
                            padding: '4px',
                        },
                    },
                },
            }}
            sx={{
                "& .MuiInputBase-root": {
                    minHeight: 40,          // Fix height same as select input
                    paddingTop: 0,
                    paddingBottom: 0,
                    display: "flex",
                    alignItems: "center",
                    background: "#fff",
                },
                "& .MuiAutocomplete-inputRoot": {
                    paddingTop: 0,
                    paddingBottom: 0,
                },
                "& .MuiAutocomplete-input": {
                    padding: "8.5px 14px", // Match padding of Select input
                },
                '&.MuiAutocomplete-root': {
                    marginTop: 0,
                },
                mt: 0, // Remove any top margin
                alignSelf: "flex-start",
            }}
            getOptionLabel={(option) => {
                if (option === '__all__') return isAllSelected ? 'Deselect All' : 'Select All';
                return filteredItems.find((i) => i.value === option)?.label || option;
            }}
            renderOption={(props, option, { selected }) => (
                <li {...props} style={{ minHeight: 32, paddingTop: 2, paddingBottom: 2, paddingLeft: 8, paddingRight: 8 }}>
                    <Checkbox
                        size="small"
                        checked={
                            option === '__all__'
                                ? isAllSelected
                                : (filter?.items || []).includes(option)
                        }
                        indeterminate={
                            option === '__all__' &&
                            (filter?.items || []).length > 0 &&
                            (filter?.items || []).length < allValues.length
                        }
                        style={{ marginRight: 8, padding: 4 }}
                    />
                    <ListItemText
                        dense
                        primaryTypographyProps={{
                            fontSize: option === '__all__' ? 14 : 16,
                        }}
                        primary={
                            option === '__all__'
                                ? isAllSelected
                                    ? 'Deselect All'
                                    : 'Select All'
                                : filteredItems.find((i) => i.value === option)?.label || option
                        }
                    />
                </li>
            )}
            renderInput={(params) => (
                <TextField
                    {...params}
                    label={label}
                    error={error}
                    helperText={helperText}
                    size="medium"
                    disabled={disabled}
                />
            )}
            renderTags={(selected) => {
                if (!selected || selected.length === 0) return null;

                const maxVisible = 2; // max tags to show before '...'
                const visibleTags = selected.slice(0, maxVisible);
                const hiddenCount = selected.length - maxVisible;

                const visibleLabels = visibleTags
                    .filter(val => val !== '__all__')
                    .map(val => filteredItems.find(i => i.value === val)?.label || val);

                return (
                    <span>
                        {visibleLabels.join(', ')}
                        {hiddenCount > 0 && ` +${hiddenCount} more`}
                    </span>
                );
            }}
        />
    );
};

export default ItemAutocomplete;
