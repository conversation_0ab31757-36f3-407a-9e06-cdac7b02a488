const mongoose = require('mongoose');
const Validation = mongoose.model("validation");
const { defaultInsuranceData } = require("../../data/common.data");

const seedValidations = async (facilityId) => {
    try {
        if (defaultInsuranceData().length > 0) {
            defaultInsuranceData().map(async item => {
              await Validation.create({
                facilityId: facilityId,
                isEditable: false,
                label: item,
                active: true,
                type: "insurance",
              });
            });
          }
        console.log('✅ Validations created successfully.');
    } catch (err) {
        console.error('❌ Error creating Validations:', err.message);
        throw err;
    }
};

module.exports = {
    seedValidations,
};
