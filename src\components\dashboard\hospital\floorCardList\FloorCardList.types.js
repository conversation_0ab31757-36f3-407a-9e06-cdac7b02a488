import PropTypes from 'prop-types';

// Shape definitions for complex objects
export const FloorCardItemShape = PropTypes.shape({
	_id: PropTypes.oneOfType([PropTypes.string, PropTypes.number]).isRequired,
	label: PropTypes.string.isRequired,
	total: PropTypes.number.isRequired,
	percentage: PropTypes.number,
	isSpacialItem: PropTypes.bool
});

export const FilterShape = PropTypes.shape({
	// Define filter shape based on your actual filter structure
	// This should match your application's filter interface
});

export const QuestionShape = PropTypes.shape({
	customQuestionInputType: PropTypes.string,
	isCustom: PropTypes.bool
});

export const CustomTabShape = PropTypes.shape({
	// Define custom tab shape based on your actual structure
	id: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
	name: PropTypes.string,
	accessor: PropTypes.string
});

// Main component prop types
export const FloorCardListPropTypes = {
	// Data props
	data: PropTypes.arrayOf(FloorCardItemShape),
	dataComparison: PropTypes.arrayOf(FloorCardItemShape),
	filter: FilterShape,
	filterComparison: FilterShape,
	
	// Selection props
	selectedItem: PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.string, PropTypes.number])),
	spacialSelectedItem: PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.string, PropTypes.number])),
	
	// Handler props
	handleToggle: PropTypes.func.isRequired,
	
	// Configuration props
	type: PropTypes.string,
	page: PropTypes.string,
	cardTitle: PropTypes.string,
	
	// Display props
	isPercentageShow: PropTypes.bool,
	reverseColors: PropTypes.bool,
	reverseColorsAdmissionPage: PropTypes.bool,
	
	// Comparison props
	averageCensusComparison: PropTypes.number,
	averageCensus: PropTypes.number,
	comparingAgainstDifferentFacility: PropTypes.bool,
	admissionCompareAgainst: PropTypes.number,
	admissionCompareAgainstComparison: PropTypes.number,
	isComparingAgainstAvgCensus: PropTypes.bool,
	
	// State props
	loading: PropTypes.bool,
	projectionDays: PropTypes.number,
	priorityNumber: PropTypes.number,
	transferType: PropTypes.string,
	lockedTotalBy: PropTypes.string,
	searchValue: PropTypes.string,
	
	// Custom props
	question: QuestionShape,
	isCustom: PropTypes.bool,
	isCustomTab: PropTypes.bool,
	customTab: CustomTabShape
};

// Default props for the component
export const FloorCardListDefaultProps = {
	// Data props
	data: [],
	dataComparison: [],
	filter: {},
	filterComparison: {},
	
	// Selection props
	selectedItem: [],
	spacialSelectedItem: [],
	
	// Configuration props
	type: '',
	page: '',
	cardTitle: '',
	
	// Display props
	isPercentageShow: true,
	reverseColors: false,
	reverseColorsAdmissionPage: false,
	
	// Comparison props
	averageCensusComparison: 0,
	averageCensus: 0,
	comparingAgainstDifferentFacility: false,
	admissionCompareAgainst: 0,
	admissionCompareAgainstComparison: 0,
	isComparingAgainstAvgCensus: false,
	
	// State props
	loading: false,
	projectionDays: 0,
	priorityNumber: 0,
	transferType: '',
	lockedTotalBy: '',
	searchValue: '',
	
	// Custom props
	question: null,
	isCustom: false,
	isCustomTab: false,
	customTab: null
};

// JSDoc type definitions for better IDE support
/**
 * @typedef {Object} FloorCardItem
 * @property {string|number} _id - Unique identifier for the item
 * @property {string} label - Display label for the item
 * @property {number} total - Total count for the item
 * @property {number} [percentage] - Optional percentage value
 * @property {boolean} [isSpacialItem] - Whether this is a special item
 */

/**
 * @typedef {Object} FloorCardListProps
 * @property {FloorCardItem[]} [data=[]] - Array of floor card items
 * @property {FloorCardItem[]} [dataComparison=[]] - Array of comparison data
 * @property {Object} [filter={}] - Filter configuration object
 * @property {Object} [filterComparison={}] - Comparison filter configuration
 * @property {Function} handleToggle - Function to handle item toggle
 * @property {(string|number)[]} [selectedItem=[]] - Array of selected item IDs
 * @property {(string|number)[]} [spacialSelectedItem=[]] - Array of special selected item IDs
 * @property {string} [type=''] - Type identifier
 * @property {string} [page=''] - Page identifier
 * @property {string} [cardTitle=''] - Title for the card
 * @property {boolean} [isPercentageShow=true] - Whether to show percentages
 * @property {boolean} [reverseColors=false] - Whether to reverse color scheme
 * @property {boolean} [reverseColorsAdmissionPage=false] - Whether to reverse colors on admission page
 * @property {number} [averageCensusComparison=0] - Average census for comparison
 * @property {number} [averageCensus=0] - Average census value
 * @property {boolean} [comparingAgainstDifferentFacility=false] - Whether comparing against different facility
 * @property {number} [admissionCompareAgainst=0] - Admission comparison value
 * @property {number} [admissionCompareAgainstComparison=0] - Admission comparison against value
 * @property {boolean} [loading=false] - Loading state
 * @property {number} [projectionDays=0] - Number of projection days
 * @property {number} [priorityNumber=0] - Priority number
 * @property {string} [transferType=''] - Transfer type
 * @property {boolean} [isComparingAgainstAvgCensus=false] - Whether comparing against average census
 * @property {string} [lockedTotalBy=''] - What the total is locked by
 * @property {string} [searchValue=''] - Search filter value
 * @property {Object} [question=null] - Question configuration object
 * @property {boolean} [isCustom=false] - Whether this is a custom component
 * @property {boolean} [isCustomTab=false] - Whether this is a custom tab
 * @property {Object} [customTab=null] - Custom tab configuration
 */

const FloorCardListTypes = {
	FloorCardItemShape,
	FilterShape,
	QuestionShape,
	CustomTabShape,
	FloorCardListPropTypes,
	FloorCardListDefaultProps
};

export default FloorCardListTypes; 