import React from 'react';
import {
    Accordion,
    AccordionSummary,
    AccordionDetails,
    Checkbox,
    FormControlLabel,
    FormLabel,
    IconButton,
    TextField,
    Typography
} from '@mui/material';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import DeleteOutlineOutlinedIcon from '@mui/icons-material/DeleteOutlineOutlined';
import { NUMBER_RANGE_TYPE, QUESTION_INPUT_TYPE, QUESTION_TYPE_OPTIONS } from '../../../../../../types/question.type';


const QuestionAccordion = ({
    question,
    isSelected,
    onCheckboxChange,
    onDeleteClick,
    isPreview = false
}) => {
    const renderOptions = () => {
        const { customQuestionOptions = [], customQuestionInputType } = question;

        switch (customQuestionInputType) {
            case QUESTION_INPUT_TYPE.LIMITED_ANSWERS:
                return customQuestionOptions.map((option, index) => (
                    <TextField
                        key={index}
                        variant="outlined"
                        size="small"
                        value={option}
                        fullWidth
                        label={`Option ${index + 1}`}
                        disabled
                        className="m-t-10"
                    />
                ));

            case QUESTION_INPUT_TYPE.NUMBER_RANGE:
            case QUESTION_INPUT_TYPE.NUMBER_RANGE_LIMITED_ANSWERS:
                return customQuestionOptions.map((option, index) => (
                    <div key={index} className="df aic m-b-10">
                        {option.type === NUMBER_RANGE_TYPE.TOTAL || option.type === NUMBER_RANGE_TYPE.AVERAGE ? (
                            <TextField
                                variant="outlined"
                                size="small"
                                value={option.title}
                                fullWidth
                                label={`Range Type ${index + 1}`}
                                disabled
                            />
                        ) : (
                            <>
                                <TextField
                                    variant="outlined"
                                    size="small"
                                    value={option.min}
                                    fullWidth
                                    label={`Min Value ${index + 1}`}
                                    disabled
                                />
                                <TextField
                                    variant="outlined"
                                    size="small"
                                    value={option.max}
                                    fullWidth
                                    label={`Max Value ${index + 1}`}
                                    disabled
                                    className="m-l-10"
                                />
                            </>
                        )}
                    </div>
                ));

            case QUESTION_INPUT_TYPE.UNLIMITED_ANSWERS:
            default:
                return null;
        }
    };

    return (
        <Accordion key={question._id} disableGutters={false} {...(isPreview && { expanded: true })}>
            {!isPreview && (
                <>
                    <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                        <FormControlLabel
                            control={
                                <Checkbox
                                    checked={isSelected}
                                    onChange={() => onCheckboxChange(question._id)}
                                />
                            }
                            label={<FormLabel>{question?.label}</FormLabel>}
                        />
                        <IconButton
                            edge="end"
                            onClick={(e) => onDeleteClick(e, question._id)}
                            aria-label="delete"
                            size="small"
                            color="error"
                            sx={{ marginLeft: 'auto' }}
                        >
                            <DeleteOutlineOutlinedIcon sx={{ fontSize: 'small' }} />
                        </IconButton>
                    </AccordionSummary>
                </>
            )}

            <AccordionDetails>
                <TextField
                    variant="outlined"
                    size="small"
                    value={question.label}
                    fullWidth
                    label="Question Label"
                    InputProps={{ readOnly: true }}
                    className="m-t-10"
                    disabled
                />
                <TextField
                    variant="outlined"
                    size="small"
                    value={question.tableLabel}
                    fullWidth
                    label="Table Question Label"
                    InputProps={{ readOnly: true }}
                    className="m-t-10"
                    disabled
                />
                <Typography className="m-t-10">
                    Question Type:{' '}
                    {
                        QUESTION_TYPE_OPTIONS.find(
                            (option) => option.value === question?.customQuestionInputType
                        )?.label
                    }
                </Typography>

                {renderOptions()}
            </AccordionDetails>
        </Accordion>
    );
};

export default QuestionAccordion;
