const {
  addPatient,
  getOpenTransfer,
  getPatient,
  getHasAdmission,
  getPatientDetails,
  getPatientAutoComplete,
  getLatestADT,
  deletePatient,
  checkHospitalPriorData,
  getPatientDetailsRelation,
  getSameDateTransfer,
  updateAccountToFacility,
  checkAdmissionPriorData
} = require("../helpers/patient");
const authWithRole = require("../middleware/auth-with-role");
const route = require("express").Router();


route.post("/details", authWithRole("manageADT"), async (req, res) => {
  try {
    let list = await getPatientDetails(req.body);
    res.send(list);
  } catch (e) {
    res.send(500);
  }
});

route.post("/details-relations", authWithRole("manageADT"), async (req, res) => {
  try {
    let list = await getPatientDetailsRelation(req.body);
    res.send(list);
  } catch (e) {
    res.send(500);
  }
});

route.post("/hasOpenTransfer", authWithRole("manageADT"), async (req, res) => {
  try {
    let p = await getOpenTransfer(req.body);
    res.send(p);
  } catch (e) {
    res.send(500);
  }
});

route.post("/hasAdmission", authWithRole("manageADT"), async (req, res) => {
  try {
    let p = await getHasAdmission(req.body);

    res.send(p);
  } catch (e) {
    res.send(500);
  }
});

route.post("/most-recent-adt/", authWithRole("manageADT"), async (req, res) => {
  try {
    const p = await getLatestADT(req.body);
    res.send(p);
  } catch (e) {
    res.send(500);
  }
});

route.post("/check-hospital-prior-data/", authWithRole("manageADT"), async (req, res) => {
  try {
    const p = await checkHospitalPriorData(req.body);
    res.send(p);
  } catch (e) {
    res.send(500);
  }
});

route.post("/check-admission-prior-data/", authWithRole("manageADT"), async (req, res) => {
  try {
    const p = await checkAdmissionPriorData(req.body);
    res.send(p);
  } catch (e) {
    res.send(500);
  }
});

route.post("/get-same-date-transfer/", authWithRole("manageADT"), async (req, res) => {
  try {    
    let list = await getSameDateTransfer(req.body);
    res.send(list);
  } catch (e) {
    console.log(e, "error");
    res.send(e);
  }
});

route.post("/:id?", authWithRole("manageADT"), async (req, res) => {
  try {
    let p = await addPatient(req.body, req.params, req?.user);
    res.send(p);
  } catch (e) {
    console.log(e, "error");
    res.send(500);
  }
});

route.get("/:facilityId", authWithRole("manageADT"), async (req, res) => {
  try {
    let list = await getPatient(
      req.params.facilityId,
      req.query.type,
      req.query.fromdate,
      req.query.todate,
      req.query.transfertype,
      req.query.activeFacilities,
      req.query.admissionType
    );
    res.send(list);
  } catch (e) {
    console.log(e, "error for patient");
    res.send(e);
  }
});

route.get("/autocomplete/:facilityId", authWithRole("manageADT"), async (req, res) => {
  try {
    let list = await getPatientAutoComplete(
      req.params.facilityId,
      req.query.type,
      req.query.search
    );
    res.send(list);
  } catch (e) {
    res.send(500);
  }
});

route.delete("/:id", authWithRole("manageADT"), async (req, res) => {
  try {
    const facility = await deletePatient(req);
    res.send(facility);
  } catch (error) {
    res.status(500).send({ message: error.message });
  }
});

route.post("/update-account-to-facility/account/", authWithRole("manageADT"), async (req, res) => {
  try {
    let p = await updateAccountToFacility(req.body);
    res.send(p);
  } catch (e) {
    console.log(e, "error");
    res.send(500);
  }
});

module.exports = route;
