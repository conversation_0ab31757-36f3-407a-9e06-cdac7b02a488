/**
 * Server configuration and startup
 */
let isShutdownHandlersRegistered = false;

const startServer = (app) => {
    const PORT = process.env.PORT || 3000;

    const server = app.listen(PORT, () => {
        console.log(`ADT server is up and running on port ${PORT}`);
        console.log(`Environment: ${process.env.NODE_ENV || 'development'}`);
    });

    // Configure server timeouts
    server.keepAliveTimeout = 200;
    server.headersTimeout = 200;

    // Only register shutdown handlers once to prevent memory leaks
    if (!isShutdownHandlersRegistered) {
        // Graceful shutdown handling
        const gracefulShutdown = async (signal) => {
            console.log(`${signal} received, shutting down gracefully`);

            try {
                // Cleanup production configurations
                const { cleanupProductionConfig } = require('./production');
                await cleanupProductionConfig();
            } catch (error) {
                console.error('Error during cleanup:', error);
            }

            server.close(() => {
                console.log('Server closed');
                process.exit(0);
            });
        };

        process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
        process.on('SIGINT', () => gracefulShutdown('SIGINT'));

        // Handle uncaught exceptions
        process.on('uncaughtException', (error) => {
            console.error('Uncaught Exception:', error);
            gracefulShutdown('UNCAUGHT_EXCEPTION');
        });

        // Handle unhandled promise rejections
        process.on('unhandledRejection', (reason, promise) => {
            console.error('Unhandled Rejection at:', promise, 'reason:', reason);
            gracefulShutdown('UNHANDLED_REJECTION');
        });

        isShutdownHandlersRegistered = true;
        console.log('Graceful shutdown handlers registered');
    }

    return server;
};

/**
 * Clean up process listeners (useful for testing or development)
 */
const cleanupListeners = () => {
    process.removeAllListeners('SIGTERM');
    process.removeAllListeners('SIGINT');
    process.removeAllListeners('uncaughtException');
    process.removeAllListeners('unhandledRejection');
    isShutdownHandlersRegistered = false;
    console.log('Process listeners cleaned up');
};

module.exports = { startServer, cleanupListeners };
