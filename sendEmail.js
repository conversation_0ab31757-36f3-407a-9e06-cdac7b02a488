const postmark = require("postmark");


const sendPostmarkEmail = async (email, templateAlias, templateModel) => {
  try {
    const client = new postmark.ServerClient(process.env.POSTMARK_API_TOKEN);

    await client.sendEmailWithTemplate({
      From: process.env.POSTMARK_API_EMAIL || "<EMAIL>",
      To: email,
      TemplateAlias: templateAlias,
      TemplateModel: templateModel,
    });
  } catch (error) {
    throw new Error("Something went wrong, Please try again later.");
  }
}

const sendAlertEmail = async (to, textBody, Subject) => {
  try {
    const client = new postmark.ServerClient(process.env.POSTMARK_API_TOKEN);
    const res = await client.sendEmail({
      From: process.env.POSTMARK_API_EMAIL || "<EMAIL>",
      //To: to,
      //To: "<EMAIL>",
      To: "<EMAIL>",
      //Bcc: "<EMAIL>",
      TextBody: textBody,
      Subject: Subject,
    });


    return res;
  } catch (error) {
    console.log(error, 'mail error');
    throw new Error("Something went wrong, Please try again later.");
  }
}

const sendEmail = async (to, textBody, Subject) => {
  try {
    const client = new postmark.ServerClient(process.env.POSTMARK_API_TOKEN);
    const res = await client.sendEmail({
      From: process.env.POSTMARK_API_EMAIL || "<EMAIL>",
      To: to,
      // To: "<EMAIL>",
      // Bcc: "<EMAIL>",
      TextBody: textBody,
      Subject: Subject,
    });
    return res;
  } catch (error) {
    console.log(error, 'mail error');
    throw new Error("Something went wrong, Please try again later.");
  }
}

const sendPostmarkEmailWithAttachment = async (to, textBody, Subject, interval, bufferData = []) => {
  try {
    const client = new postmark.ServerClient(process.env.POSTMARK_API_TOKEN);
    await client.sendEmail({
      From: process.env.POSTMARK_API_EMAIL || "<EMAIL>",
      To: "<EMAIL>",
      //To: to,
      // Bcc: "<EMAIL>",
      TextBody: textBody,
      Subject: Subject,
      ...bufferData && bufferData.length > 0 && {
        Attachments: bufferData
      }
    });
  } catch (error) {
    console.log(error, 'mail error');
    throw new Error("Something went wrong, Please try again later.");
  }
}

const sendTestEmail = async (TextBody = "Test mail", to = "<EMAIL>", bufferData) => {
  try {
    const client = new postmark.ServerClient(process.env.POSTMARK_API_TOKEN);
    const res = await client.sendEmail({
      From: process.env.POSTMARK_API_EMAIL || "<EMAIL>",
      To: to,
      TextBody: TextBody,
      Subject: "This is test mail from SNF run report cronjob",
      ...bufferData && {
        Attachments: [
          {
            name: `test_pdf.pdf`,
            content: bufferData,
            ContentType: "application/pdf",
          },
        ]
      }
    });
  } catch (error) {
    console.log(error, 'mail error');
    throw new Error("Something went wrong, Please try again later.");
  }
}

module.exports = {
  sendTestEmail,
  sendPostmarkEmailWithAttachment,
  sendPostmarkEmail,
  sendEmail,
  sendAlertEmail
};
