const dayjs = require("dayjs");
const { find } = require("lodash");
const mongoose = require("mongoose");
const { toStartFilterDate } = require("../utilis/date-format");
const moment = require("moment");
const {
	chartFilterPermission,
	getMonthsFromDates,
	getYearFromDates,
	get30DaysFromDates,
} = require("../utilis/common");
const { FILTER_TYPES } = require("../../types/common.type");
const _ = require("lodash");

const setCensus = async ({ count, date, accountId, facilityId, bedCapacity }) => {
	const Census = mongoose.model("census");
	let census;

	const existingCensus = await Census.findOne({
		accountId,
		facilityId,
	});

	if (!existingCensus) {
		census = new Census({
			bedCapacity,
			count,
			date: date,
			accountId,
			facilityId,
		});
		await census?.save();

		return true;
	} else {
		throw new Error("Existing census.");
	}
};

const getTotalDataByType = async (filtersTransfer, censusDate, today, facilityId) => {
	const Patient = mongoose.model("patient");
	const allTransferPatientData = await Patient.aggregate([
		{
			$match: {
				$and: [
					{
						dateOfADT: {
							$gte: await toStartFilterDate(censusDate),
							$lte: await toStartFilterDate(today),
						},
					},
					{
						facilityId: mongoose.Types.ObjectId(facilityId),
					},
					{
						...filtersTransfer,
					},
				],
			},
		},
		{
			$group: {
				_id: {
					$dateToString: {
						format: "%Y-%m-%d",
						date: "$dateOfADT",
					},
				},
				total: {
					$sum: 1,
				},
			},
		},
		{
			$project: {
				dateOfADT: "$_id",
				total: 1,
			},
		},
		{ $sort: { dateOfADT: 1 } },
	]);
	return allTransferPatientData;
};

const updateBedCapacity = async (req) => {
	const Census = mongoose.model("census");
	const { facilityId, bedCapacity } = req;

	const censusData = await Census.find({
		facilityId,
	}).sort({ date: 1 });

	if (censusData && censusData.length > 0) {
		await filterData(censusData, async (item) => {
			await Census.updateOne({ _id: item._id }, {
				$set: {
					bedCapacity: Number(bedCapacity)
				},
			});
		});
	}
	return "success";
}

const updateCensus = async (facilityId, req) => {
	const Census = mongoose.model("census");
	const { date, count } = req.body;
	let today = new Date();
	const censusData = await Census.find({
		facilityId,
	}).sort({ date: 1 });

	if (censusData && censusData.length > 0) {
		const newCount = Number(count);

		let filtersTransfer = {
			type: { $in: ["transfer"] },
		};
		let filtersAdmissions = {
			type: { $in: ["admission", "readmission", "return"] },
		};
		const allTransferPatientData = await getTotalDataByType(filtersTransfer, date, today, facilityId);

		const allAdmissionPatientData = await getTotalDataByType(filtersAdmissions, date, today, facilityId);
		let lastUpdatedCount = newCount;

		await filterData(censusData, async (item) => {
			const dbDate = dayjs(item.date).format("YYYY-MM-DD");

			const existDataTransfer = find(allTransferPatientData, {
				dateOfADT: dbDate,
			});
			let totalTransferByDay = existDataTransfer ? existDataTransfer.total : 0;

			const existDataAdmissions = find(allAdmissionPatientData, {
				dateOfADT: dbDate,
			});
			let notTransferByDay = existDataAdmissions ? existDataAdmissions.total : 0;

			let latestCount = lastUpdatedCount + notTransferByDay - totalTransferByDay;
			lastUpdatedCount = latestCount;

		});
	}
	return req.body;
};

const firstCensusDate = async (facilityId) => {
	const Census = mongoose.model("census");
	return await Census.findOne({ facilityId }).sort({ created_at: -1 });
};

const filterData = async (arr, callback) => {
	const fail = Symbol();
	return (await Promise.all(arr.map(async (item) => ((await callback(item)) ? item : fail)))).filter((i) => i !== fail);
};

const getBedCapacityByFacilityIds = async (facilityIds) => {
	const Census = mongoose.model("census");
	const facilities = await Census.find({ facilityId: { $in: facilityIds } });
	return facilities.map((x) => ({ id: x.facilityId, total: x.bedCapacity }));
};

const getCensusAverageByPeriod = async (startDate, endDate, facilityids) => {
	const filterChartButtons = await chartFilterPermission({
		startDate,
		endDate,
	});
	let cercusByMonths = [];
	let cercusBy30Days = [];
	let cercusByYears = [];
	if (filterChartButtons.length > 0) {
		if (_.includes(filterChartButtons, FILTER_TYPES.YEARLY)) {
			const betweenYears = await getYearFromDates(startDate, endDate);
			const betweenMonths = await getMonthsFromDates(startDate, endDate);

			if (betweenMonths.length > 0) {
				await filterData(betweenMonths, async (item) => {
					const censusInfo = await getCensusAverageInfo(item.startDate, item.endDate, facilityids);
					cercusByMonths.push({
						...item,
						...censusInfo,
						startDate: moment(item.startDate).startOf("month").format("YYYY-MM-DD"),
					});
				});
			}
			if (betweenYears.length > 0) {
				await filterData(betweenYears, async (item) => {
					const censusInfo = await getCensusAverageInfo(item.startDate, item.endDate, facilityids);
					cercusByYears.push({
						...item,
						...censusInfo,
						startDate: moment(item.startDate).startOf("month").format("YYYY-MM-DD"),
					});
				});
			}
		} else if (_.includes(filterChartButtons, FILTER_TYPES.MONTHLY)) {
			const betweenMonths = await getMonthsFromDates(startDate, endDate);
			if (betweenMonths.length > 0) {
				await filterData(betweenMonths, async (item) => {
					const censusInfo = await getCensusAverageInfo(item.startDate, item.endDate, facilityids);
					cercusByMonths.push({
						...item,
						...censusInfo,
						startDate: moment(item.startDate).startOf("month").format("YYYY-MM-DD"),
					});
				});
			}
		} else if (_.includes(filterChartButtons, FILTER_TYPES.THIRTY_DAYS)) {
			const betweenMonths = await get30DaysFromDates(startDate, endDate);
			if (betweenMonths.length > 0) {
				await filterData(betweenMonths, async (item) => {
					const censusInfo = await getCensusAverageInfo(item.startDate, item.endDate, facilityids);
					cercusBy30Days.push({
						...item,
						...censusInfo,
						startDate: moment(item.startDate).format("YYYY-MM-DD"),
					});
				});
			}
		}
	}
	if (cercusByMonths.length > 0) {
		cercusByMonths = _.sortBy(cercusByMonths, "startDate");
	}
	if (cercusByYears.length > 0) {
		cercusByYears = _.sortBy(cercusByYears, "startDate");
	}
	if (cercusBy30Days.length > 0) {
		cercusBy30Days = _.sortBy(cercusBy30Days, "startDate");
	}

	return { monthly: cercusByMonths, yearly: cercusByYears, thirtyDays: cercusBy30Days };
};

const getCensusAverageInfo = async (startDate, endDate, facilityids, isCensusAsOfNow = false, censusByFacility, bedByFacility, censusAsOfNowByFacility) => {
	let censusTotal = 0;
	let censusAsOfNow = 0;
	let censusAverageTotal = 0;
	let bedCapacity = 0;
	let censusEndDate = endDate;
	let days = 0;
	let filtersTransfer = { type: { $in: ["transfer"] } };
	let filtersAdmissions = {
		type: { $in: ["admission", "readmission", "return"] },
	};

	await Promise.allSettled(
		facilityids.map(async (facilityId) => {
			let censusTotalOneFacility = 0;
			let dayData = 0;
			const Census = mongoose.model("census");
			const census = await Census.findOne({ facilityId: facilityId }).sort({
				created_at: -1,
			});
			if (!census) {
				return;
			}
			bedCapacity += census?.bedCapacity || 0;
			const censusInitialDate = census.date;
			const allTransferPatientData = await getTotalDataByType(filtersTransfer, censusInitialDate, endDate, facilityId);
			const allAdmissionPatientData = await getTotalDataByType(
				filtersAdmissions,
				censusInitialDate,
				endDate,
				facilityId
			);
			let patientsForTheDay = census.count;
			// finding our census
			// for how much days we are calculating
			days += moment(endDate).diff(moment(startDate), "days") + 1;
			dayData = moment(endDate).diff(moment(startDate), "days") + 1;
			const daysMovementData = {};

			// for every date that we have transfers we add the total - for the day.
			for (let i = 0; i < allTransferPatientData.length; i += 1) {
				daysMovementData[allTransferPatientData[i]._id] =
					(daysMovementData[allTransferPatientData[i]._id] || 0) - allTransferPatientData[i]?.total || 0;
			}

			// we do the same for admissions, but total + this time.
			for (let i = 0; i < allAdmissionPatientData.length; i += 1) {
				daysMovementData[allAdmissionPatientData[i]._id] =
					(daysMovementData[allAdmissionPatientData[i]._id] || 0) + allAdmissionPatientData[i]?.total || 0;
			}

			// this is the calculation for the days before our start date.
			// All we are doing here, is adjusting the initial start number, before the starting of the start date
			for (let m = moment(censusInitialDate); m.isBefore(startDate); m.add(1, "days")) {
				const formattedDate = m.format("YYYY-MM-DD");
				if (daysMovementData[formattedDate]) {
					patientsForTheDay += daysMovementData[formattedDate];
				}
			}

			// with the proper people calculated at the time of the our start period, we do calculations for
			// average census for the period.
			for (let m = moment(startDate); m.isBefore(endDate); m.add(1, "days")) {
				censusTotal += patientsForTheDay;
				censusTotalOneFacility += patientsForTheDay;
				const formattedDate = m.format("YYYY-MM-DD");
				if (daysMovementData[formattedDate]) {
					patientsForTheDay += daysMovementData[formattedDate];
				}
			}
			const censusTotalData = censusTotalOneFacility / dayData;
			censusAverageTotal += censusTotalData;
			if (censusByFacility && !isCensusAsOfNow) {
				censusByFacility.push({ id: facilityId, total: censusTotalData ? censusTotalData.toFixed(1) : 0 });
			}
			if (bedByFacility && !isCensusAsOfNow) {
				bedByFacility.push({ id: facilityId, total: census?.bedCapacity ? census?.bedCapacity : 0 });
			}
			if (censusAsOfNowByFacility && !isCensusAsOfNow) {
				let startDateAsNow = moment.utc(endDate).startOf("day").toISOString();
				const censusOfDateData = await getCensusAverageInfo(startDateAsNow, endDate, [facilityId], true, [{ id: facilityId, total: censusTotalData ? censusTotalData.toFixed(1) : 0 }]);
				censusAsOfNow = censusOfDateData?.censusAverage;
				censusAsOfNowByFacility.push({ id: facilityId, total: censusAsOfNow ?? 0 });
			}
		})
	);

	if (censusTotal === 0) {
		return { censusTotal, censusAverage: 0, bedCapacity };
	}
	censusAverage = `${parseInt((censusTotal / days) * 10) / 10}`;
	censusAverageTotal = censusAverageTotal ? censusAverageTotal.toFixed(1) : 0;
	if (!isCensusAsOfNow) {
		let startDateAsNow = moment.utc(endDate).startOf("day").toISOString();
		const censusOfDateData = await getCensusAverageInfo(startDateAsNow, endDate, facilityids, true, censusByFacility);
		censusAsOfNow = censusOfDateData?.censusAverage;
	}
	return {
		censusTotal,
		censusAverage: censusAverageTotal,
		bedCapacity,
		censusAsOfNow,
		censusEndDate,
		censusByFacility,
	};
};

const getFirstAndLastDateOfInput = async (body, req) => {
	const Census = mongoose.model("census");
	const Patient = mongoose.model("patient");
	const PercentageAgainst = mongoose.model("percentageAgainst");
	const { facilityIds } = body;
	const user = req?.user;
	const { accountid } = req.headers;
	const percentageAgainst = await PercentageAgainst.findOne({
		userId: mongoose.Types.ObjectId(user._id),
		accountId: mongoose.Types.ObjectId(accountid),
	});
	let censusDate = null;
	if (facilityIds.length > 1) {

		const latestDateOfADT = await Patient.findOne({
			facilityId: { $in: facilityIds },
			dateOfADT: { $ne: null },
		})
			.sort({ dateOfADT: -1 })
			.exec();

		const oldestDateOfADT = await Patient.findOne({
			facilityId: { $in: facilityIds },
			dateOfADT: { $ne: null },
		})
			.sort({ dateOfADT: 1 })
			.exec();

		const endDateOfADTs = [];

		const censusDateData = [];
		for (const facilityId of facilityIds) {
			const latestDateOfADTDates = await Patient.findOne({
				facilityId: facilityId,
				dateOfADT: { $ne: null },
			})
				.sort({ dateOfADT: -1 })
				.exec();

			const census = await Census.findOne({
				facilityId: facilityId,
				isBaseline: true,
			});
			if (latestDateOfADTDates) {
				endDateOfADTs.push({
					endDateOfADT: latestDateOfADTDates.dateOfADT,
					facilityId,
					censusDate: census?.date,
				});
			}

			censusDateData.push(census?.date);
		}
		const lowestDate = censusDateData.reduce((minDate, currentDate) => {
			return moment(currentDate).isBefore(moment(minDate)) ? currentDate : minDate;
		});

		return testOverlapDates = {
			fromDate: lowestDate ? lowestDate : oldestDateOfADT.dateOfADT,
			toDate: latestDateOfADT.dateOfADT,
			isOverlap: true,
			isAdtData: true,
			customPercentage: percentageAgainst?.customPercentage || null,
			customPercentageLabel: percentageAgainst?.label || null,
			endDateOfADTs,
		};
	} else {
		let query = {
			facilityId: { $in: facilityIds },
			dateOfADT: { $ne: null },
		};

		const census = await Census.findOne({
			facilityId: facilityIds?.[0],
			isBaseline: true,
		});

		censusDate = census?.date;
		const latestData = await Patient.findOne({
			...query,
		}).sort({ dateOfADT: -1 });

		const oldestData = await Patient.findOne({
			...query,
		}).sort({ dateOfADT: 1 });

		const endDateOfADTs = [];
		for (const facilityId of facilityIds) {
			const latestDateOfADTDates = await Patient.findOne({
				facilityId: facilityId,
				dateOfADT: { $ne: null },
			})
				.sort({ dateOfADT: -1 })
				.exec();
			if (latestDateOfADTDates) {
				endDateOfADTs.push({ endDateOfADT: latestDateOfADTDates.dateOfADT, facilityId });
			}
		}

		if (latestData && oldestData) {
			return {
				fromDate: censusDate,
				toDate: latestData?.dateOfADT,
				isOverlap: true,
				isAdtData: true,
				customPercentage: percentageAgainst?.customPercentage || null,
				customPercentageLabel: percentageAgainst?.label || null,
				endDateOfADTs,
				censusDate
			};
		}
	}
	return {
		fromDate: null,
		toDate: null,
		isOverlap: false,
		isAdtData: false,
		customPercentage: percentageAgainst?.customPercentage || null,
		customPercentageLabel: percentageAgainst?.label || null,
		censusDate
	};
};

module.exports = {
	setCensus,
	updateCensus,
	firstCensusDate,
	getCensusAverageInfo,
	getBedCapacityByFacilityIds,
	getCensusAverageByPeriod,
	getFirstAndLastDateOfInput,
	updateBedCapacity
};
