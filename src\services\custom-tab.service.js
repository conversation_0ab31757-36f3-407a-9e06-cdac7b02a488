import { deleteCustomTab, getCustomTabs, getDynamicTabById, saveCustomTab } from "./api/custom-tab.api";

const getAllCustomTabs = async (type) => {
    try {
        const res = await getCustomTabs(type);
        return res;
    } catch (e) {
        console.log(e);
    }
};

const createCustomTab = async (body) => {
    try {
        const res = await saveCustomTab(body);
        if (res?.status === 403) {
            return {
                status: 403,
                message: "You don't have permission to edit this tab"
            };
        }
        if (res?.status === 404) {
            return {
                status: 404,
                message: "Custom tab not found"
            };
        }
        if (res?.status === 400) {
            return {
                status: 400,
                message: "Custom tab with this title already exists"
            };
        }
        return res;
    } catch (e) {
        console.log(e.message);
        return {
            status: 500,
            message: "An error occurred while saving the custom tab"
        };
    }
};

const deleteCustomTabById = async (id) => {
    try {
        const res = await deleteCustomTab(id);
        if (res?.status === 'success') {
            return {
                success: true,
                message: res.message
            };
        }
        return res; // For creator deletion, return the deleted tab object
    } catch (e) {
        console.log(e);
        return {
            success: false,
            message: 'Failed to delete custom tab'
        };
    }
};

const getDynamicDataTabById = async (id) => {
    try {
        const res = await getDynamicTabById(id);
        return res;
    } catch (e) {
        console.log(e);
    }
};

export {
    getDynamicDataTabById,
    deleteCustomTabById,
    getAllCustomTabs,
    createCustomTab
};
