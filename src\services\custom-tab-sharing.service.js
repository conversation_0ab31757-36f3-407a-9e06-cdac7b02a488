import api from "./api/api";

// Base URL for custom tab sharing APIs
const CUSTOM_TAB_SHARING_API = 'api/custom-tab-sharing';

// Share a custom tab with users
export const shareCustomTab = async (customTabId, recipientIds, shareMessage = '', isUniversal = false) => {
    try {
        const response = await api.post(`${CUSTOM_TAB_SHARING_API}/share`, {
            customTabId,
            recipientIds,
            shareMessage,
            isUniversal
        });
        return response;
    } catch (error) {
        console.error('Error sharing custom tab:', error);
        throw error;
    }
};

// Respond to a share invitation
export const respondToShare = async (shareId, response, newTitle = null) => {
    try {
        const result = await api.post(`${CUSTOM_TAB_SHARING_API}/share/respond`, {
            shareId,
            response, // 'accept', 'keep', or 'delete'
            newTitle // Optional new title for conflict resolution
        });
        return result;
    } catch (error) {
        console.error('Error responding to share:', error);
        throw error;
    }
};

// Unshare a tab from specific users
export const unshareCustomTab = async (customTabId, recipientIds) => {
    try {
        const response = await api.post(`${CUSTOM_TAB_SHARING_API}/unshare`, {
            customTabId,
            recipientIds
        });
        return response;
    } catch (error) {
        console.error('Error unsharing custom tab:', error);
        throw error;
    }
};

// Get tabs shared with current user
export const getSharedTabs = async (status = null, showHidden = false) => {
    try {
        const params = {};
        if (status) params.status = status;
        if (showHidden) params.showHidden = 'true';

        const response = await api.get(`${CUSTOM_TAB_SHARING_API}/shared-with-me`, { params });
        return response;
    } catch (error) {
        console.error('Error getting shared tabs:', error);
        throw error;
    }
};

// Get users who have access to a specific tab
export const getTabShares = async (customTabId) => {
    try {
        const response = await api.get(`${CUSTOM_TAB_SHARING_API}/${customTabId}/shares`);
        return response;
    } catch (error) {
        console.error('Error getting tab shares:', error);
        throw error;
    }
};

// Get notifications for current user
export const getNotifications = async (limit = 20, skip = 0, unreadOnly = false, page = null) => {
    try {
        const params = {
            limit,
            skip,
            unreadOnly
        };
        
        if (page) {
            params.page = page;
        }
        
        const response = await api.get(`${CUSTOM_TAB_SHARING_API}/notifications`, {
            params
        });
        return response;
    } catch (error) {
        console.error('Error getting notifications:', error);
        throw error;
    }
};

// Mark notification as read
export const markNotificationRead = async (notificationId) => {
    try {
        const response = await api.put(`${CUSTOM_TAB_SHARING_API}/notifications/${notificationId}/read`);
        return response;
    } catch (error) {
        console.error('Error marking notification as read:', error);
        throw error;
    }
};

// Mark all notifications as read
export const markAllNotificationsRead = async () => {
    try {
        // Get all unread notifications first
        const unreadNotifications = await getNotifications(100, 0, true);

        if (unreadNotifications.data?.notifications?.length > 0) {
            // Mark each one as read
            const promises = unreadNotifications.data.notifications.map(notification =>
                markNotificationRead(notification._id)
            );
            await Promise.all(promises);
        }

        return { success: true };
    } catch (error) {
        console.error('Error marking all notifications as read:', error);
        throw error;
    }
};

// Hide/Unhide shared tab
export const toggleSharedTabVisibility = async (shareId, isHidden) => {
    try {
        const response = await api.put(`${CUSTOM_TAB_SHARING_API}/shared/${shareId}/toggle-visibility`, {
            isHidden
        });
        return response;
    } catch (error) {
        console.error('Error toggling tab visibility:', error);
        throw error;
    }
};