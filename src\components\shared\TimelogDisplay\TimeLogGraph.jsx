import React, { useMemo } from "react";
import { ResponsiveLine } from "@nivo/line";
import { parse, format } from "date-fns";
import { fmtMin } from "../../../utilis/time-format";

export default function TimeLogGraph({ groups, breakdown, height = 380 }) {
  const { data, tickValues, tickFormat } = useMemo(() => {
    const reversed = [...groups].reverse();

    const points = [];
    const labelMap = {};

    reversed.forEach((g) => {
      let shortLabel;
      let dateObj;

      if (breakdown === "daily") {
        dateObj = parse(g.key, "MM/dd/yyyy", new Date());
        shortLabel = format(dateObj, "d MMM");
      } else if (breakdown === "weekly") {
        dateObj = parse(g.key, "yyyy-MM-dd", new Date());
        shortLabel = format(dateObj, "d MMM");
      } else {
        const [y, m] = g.key.split("-").map(Number);
        dateObj = new Date(y, m, 1);
        shortLabel = format(dateObj, "MMM yy");
      }

      const xVal = g.title;
      labelMap[xVal] = shortLabel;

      points.push({
        x: xVal,
        y: breakdown === "daily" ? g.minutes : g.days.length,
      });
    });

    const maxTicks = 10;
    const step = Math.ceil(points.length / maxTicks);
    const ticks = points
      .filter((_, idx) => idx % step === 0)
      .map((p) => p.x);

    return {
      data: [
        {
          id: breakdown === "daily" ? "Minutes worked" : "Active days",
          data: points,
        },
      ],
      tickValues: ticks,
      tickFormat: (v) => labelMap[v] ?? v,
    };
  }, [groups, breakdown]);

  const commonAxis = {
    tickPadding: 6,
    tickSize: 0,
    tickRotation: -45,
    legendOffset: 48,
    legendPosition: "middle",
  };

  return (
    <div style={{ width: "100%", height }}>
      <ResponsiveLine
        data={data}
        margin={{ top: 20, right: 30, bottom: 80, left: 50 }}
        xScale={{ type: "point" }}
        yScale={{ type: "linear", stacked: false, min: "auto", max: "auto" }}
        axisBottom={{
          ...commonAxis,
          legend: "Period",
          tickValues,
          format: tickFormat,
        }}
        axisLeft={{
          legend: breakdown === "daily" ? "Minutes" : "Days",
          legendOffset: -40,
          legendPosition: "middle",
        }}
        colors={{ scheme: "category10" }}
        enablePoints={false}
        useMesh
        tooltip={({ point }) => (
          <div
            style={{
              background: "white",
              padding: "6px 9px",
              border: "1px solid #ddd",
              fontSize: 12,
            }}
          >
            <strong>{point.data.xFormatted}</strong>
            <br />
            {breakdown === "daily"
              ? fmtMin(point.data.y)
              : `${point.data.yFormatted} day(s)`}
          </div>
        )}
      />
    </div>
  );
}