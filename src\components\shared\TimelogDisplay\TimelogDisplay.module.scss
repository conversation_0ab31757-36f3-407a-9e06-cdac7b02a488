:root {
	--accent: #007bff;
	--accent-hover: rgba(0, 123, 255, 0.08);
	--text-light: #555;
	--border-base: #ddd;
	--border-radius: 6px;
}

.container {
	width: 100%;
	max-width: 1400px;
	margin: 0 auto;
	padding: 24px 16px 48px;
	box-sizing: border-box;
	display: flex;
	flex-direction: column;
	gap: 32px;
	font-family: system-ui, sans-serif;
	color: #222;
}

.controlsTitle {
	margin-bottom: 8px;
	font-size: 1.2rem;
	font-weight: 600;
}

.dateInputs {
	display: flex;
	gap: 16px;
	flex-wrap: wrap;
	margin-bottom: 16px;
}

.dateInput {
	display: flex;
	flex-direction: column;
	font-size: 0.95rem;

	label {
		margin-bottom: 4px;
		color: var(--text-light);
	}

	input[type="date"] {
		padding: 6px 8px;
		border: 1px solid var(--border-base);
		border-radius: var(--border-radius);
		font-size: 1rem;

		&:focus {
			outline: none;
			border-color: var(--accent);
			box-shadow: 0 0 0 2px var(--accent-hover);
		}
	}
}

.breakdownSelector {
	display: flex;
	gap: 8px;
}

.viewSelector {
	display: flex;
	gap: 8px;
}

.breakdownBtn,
.breakdownBtnActive {
	padding: 6px 14px;
	font-size: 0.9rem;
	border: 1px solid var(--accent);
	border-radius: 999px;
	background: transparent;
	cursor: pointer;

	&:hover {
		background: var(--accent-hover);
	}
}

.breakdownBtnActive {
	background: var(--accent);
	color: #fff;

	&:hover {
		background: var(--accent);
	}
}

.summaryCard {
	border: 1px solid var(--border-base);
	border-left: 4px solid var(--accent);
	border-radius: var(--border-radius);
	padding: 16px 20px;
	background: #fafafa;

	h3 {
		margin: 0 0 6px;
		font-size: 1rem;
	}

	p {
		margin: 0;
		font-size: 0.9rem;
		color: var(--text-light);
	}
}

.loadingContainer {
	display: flex;
	align-items: center;
	justify-content: center;
	min-height: 160px;
}

.cardsGrid {
	display: grid;
	grid-template-columns: repeat(4, 1fr);
	gap: 16px;
	grid-auto-rows: auto;
}

.emptyToggle {
	display: flex;
	align-items: center;
	gap: 4px;
	margin-left: 12px;
	font-size: 0.85rem;
	color: var(--text-light);

	input {
		accent-color: var(--accent);
		cursor: pointer;
	}
}

.card {
	border: 1px solid var(--border-base);
	border-radius: var(--border-radius);
	padding: 14px 16px;
	display: flex;
	flex-direction: column;
	gap: 10px;
}

.modalContent {
	display: flex;
	flex-direction: column;
	gap: 4px;
	font-size: 0.85rem;
}

.cardHeader {
	display: flex;
	gap: 8px;
	justify-content: space-between;
	align-items: center;
	font-size: 0.9rem;
	font-weight: 600;
	flex-shrink: 0;
	flex-wrap: nowrap;
}

.cardHeader span:first-child {
	flex: 1 1 auto;
	/* take remaining space */
	white-space: nowrap;
	/* keep on one line */
	overflow: visible;
	/* show full text */
	text-overflow: unset;
	/* no truncation */
}

.badge {
	font-size: 0.75rem;
	padding: 2px 8px;
	border-radius: 999px;
	background: var(--accent);
	color: #fff;
	min-width: 64px;
	text-align: center;
	font-variant-numeric: tabular-nums;
}

.toggleBtn {
	background: none;
	border: none;
	color: var(--accent);
	cursor: pointer;
	font-size: 0.8rem;
	padding: 2px 6px;
	white-space: nowrap;

	&:hover {
		text-decoration: underline;
	}
}

.timeItems,
.subList {
	flex: 1 1 auto;
	/* take remaining height */
	overflow-y: auto;
	/* independent scroll */
	font-size: 0.85rem;
	display: flex;
	flex-direction: column;
	gap: 4px;
	padding-right: 4px;
	/* space for scrollbar */
}

.subItem {
	display: flex;
	justify-content: space-between;
}

.placeholder {
	text-align: center;
	color: var(--text-light);
	font-style: italic;
}

@media (max-width: 600px) {
	.cardsGrid {
		grid-template-columns: 1fr;
		grid-auto-rows: 220px;
	}

	.card {
		height: 220px;
	}
}

.summaryRow {
	display: flex;
	flex-wrap: wrap;
	gap: 12px;
	align-items: center;
	font-size: 0.95rem;
  }
  
  .summaryStats {
	color: #222;
  }
  
  .summaryContext {
	font-weight: 600;
	color: var(--accent); /* keeps brand colour */
  }