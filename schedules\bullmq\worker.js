const { Worker } = require("bullmq");
const configModule = require("../../config/redis-connection");
const { jobProcessor } = require("./report-processor");
const { createReportLog } = require("../../api/helpers/report-log");

let worker;

const setUpWorker = () => {
    console.log("Send Report setting Up....")
    worker = new Worker("REPORTS",
        async (job) => {
            console.log(`🔄 Processing Job ID: ${job.id} - ${job.name}`);
            try {
                const result = await jobProcessor(job);
                console.log(`✅ Job ID: ${job.id} Completed ${job.name}`);
                return result;
            } catch (error) {
                console.error(`❌ Job ID: ${job.id} Failed:`, error);
                createReportLog({ event: "WORKER_FAILED_REPORT_ERROR", data: error, description: `Send Report setting Up ${job.name}` })
                throw error;
            }
        },
        {
            connection: configModule.createRedisConnection(),
            autorun: true,
            concurrency: 5, // Allow multiple jobs to run at once
            maxStalledCount: 10
        });

    worker.on("active", (job) => {
        console.log(`${job?.data?.index} => Preparing... Processing send report to user with id ${job?.data._id} ${job?.data?.name}`);
    });

    worker.on("completed", (job) => {
        console.log(`${job?.data?.index} => 🎉 Job Completed: ${job?.id}... Successfully Sent report to user with id ${job?.data._id} ${job?.data?.name}`);
    });

    worker.on("failed", (job, err) => {
        console.error(`❌ Job Failed: ${job?.data.index} - ${job?.data?.name} ${job?.data?._id}`, err);
        console.log(failedReason, `failedReason ${job?.id}`);        
        createReportLog({ event: "WORKER_FAILED_REPORT", data: failedReason, description: `Send Report setting Up failed ${job?.id}` })
    });

    worker.on("error", (err) => {
        console.log(err, 'failedReason');        
        createReportLog({ event: "WORKER_FAILED_REPORT_ERROR", data: err, description: "Send Report setting Up error" })
        console.error("⚠️ Worker encountered an error:", err);
    });
};

module.exports = setUpWorker;