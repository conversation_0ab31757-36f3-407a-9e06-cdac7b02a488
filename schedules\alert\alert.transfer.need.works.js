async function findHighestReasonAndSendAlert(currentWeek, last4Weeks, handleEmptyData, dataTypes = [], allDataLast4Weeks = [], alertReport, pageLabel) {
    const result = [];

    // Helper function to find the highest reason based on a field (average for last4Weeks, total for currentWeek)
    const findHighest = (data, field) => {
        let highestItem = null;
        let highestValue = -Infinity;

        data.forEach(item => {
            if (item[field] > highestValue) {
                highestValue = item[field];
                highestItem = item;
            }
        });

        return highestItem;
    };

    // Helper function to send an alert/log when a transfer needs work
    const sendAlert = (category, highestCurrent, highestLockBack) => {
        const roundedLastAverage = Math.round(highestLockBack?.average);
        const roundedCurrentTotal = Math.round(highestCurrent?.total);        
        const message = `${highestCurrent.label} averaged ${roundedLastAverage} ${pageLabel} a week the previous four weeks and has sent out ${roundedCurrentTotal} ${pageLabel} this past week`;
        const name = highestCurrent?.name || highestLockBack?.name;
        result.push({
            category,
            _id: highestCurrent._id,
            label: highestCurrent.label,
            total: roundedCurrentTotal,
            message,
            ...name && { name }
        });
    };

    // Process each category
    for (const category of dataTypes) {
        let last4WeeksTypeData = allDataLast4Weeks.map(x => x[category]);

        // Merge the arrays from each week into a single array
        last4WeeksTypeData = last4WeeksTypeData?.flat() ?? [];

        const last4WeeksData = last4Weeks && last4Weeks[category] ? last4Weeks[category] : [];
        const currentWeekData = currentWeek && currentWeek[category] ? currentWeek[category] : [];

        if (last4WeeksData.length === 0 || currentWeekData.length === 0) {
            handleEmptyData && handleEmptyData(category);
            continue; // Skip to the next category if there's no data
        }

        // Step 1: Find the highest average in the last 4 weeks
        const last4WeeksHighest = findHighest(last4WeeksData, 'average');

        // Step 2: Find the highest total in the current week
        const currentWeekHighest = findHighest(currentWeekData, 'total');

        if (!last4WeeksHighest || !currentWeekHighest) {
            handleEmptyData && handleEmptyData(category);
            continue; // Skip to the next category if we couldn't find a highest
        }

        // Step 3: Compare the two and check if the highest reason for transfer remains the same
        if (last4WeeksHighest._id === currentWeekHighest._id && currentWeekHighest.total > last4WeeksHighest.average) {
            // Case 1: If the same reason for transfer is the highest in both highestLockBack and current week
            sendAlert(category, currentWeekHighest, last4WeeksHighest);
        }        
    }

    return result;
}

module.exports = {
    findHighestReasonAndSendAlert
}