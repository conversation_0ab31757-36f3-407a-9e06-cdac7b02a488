# Database Configuration
MONGO_URI=mongodb://localhost:27017/adt-tech

# Server Configuration
PORT=3000
NODE_ENV=development

# CORS Configuration
CORS_ORIGIN=http://localhost:3007

# Application URL
APP_URL=http://localhost:3007

# Feature Flags
# Enable/disable report functionality (default: true)
# Set to 'false' to disable reports
ENABLE_REPORTS=true

# Enable/disable alert functionality (default: true)  
# Set to 'false' to disable alerts
ENABLE_ALERTS=true

# Email Configuration (for production)
# POSTMARK_API_KEY=your_postmark_api_key

# Redis Configuration (for production job queues)
# REDIS_URL=redis://localhost:6379

# Sentry Configuration (for error tracking)
# SENTRY_DSN=your_sentry_dsn

# JWT Configuration
# JWT_SECRET=your_jwt_secret

# Other environment-specific configurations
# Add your environment variables here
