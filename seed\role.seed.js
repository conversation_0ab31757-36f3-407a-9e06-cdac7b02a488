const Role = require("../models/Role");

const seedRoles = async () => {
    const seedRoles = [
        {
            name: 'Super',
            active: false,
            order: 1
        },
        {
            name: 'Owner',
            order: 2
        },
        {
            name: 'Total',
            order: 3
        },
        {
            name: 'Regional',
            order: 4

        },
        {
            name: 'Admin',
            order: 5
        },
        {
            name: 'User',
            order: 6
        }
    ];

    await Role.deleteMany({});

    const createdRoles = seedRoles.map(async (item) => {
        const role = await Role.create(item);
        return role;
    });

    await Promise.all(createdRoles);

};

module.exports = { seedRoles };
