const ReportTransferTypeTitleEnum = {
    all: "all",
    unplannedHospitalTransfer: "Unplanned Transfers",
    plannedHospitalTransfer: "Planned Transfers",
    admission: "New Admissions",
    readmission: "Re-Admissions",
    totalOutgoing: "Total Outgoing Transfers",
    totalIncoming: "Total Incoming Transfers",
    doctorData: "Per Doctor",
    daysData: "Per Day",
    dxData: "Per Diagnosis",
    insuranceData: "Per Insurance",
    floorsData: "Per Floor",
    hospitalData: "Per Hospital",
    sixtyDaysData: "60 Days Analysis",
};

const CHART_FILTER_DAY_OF = {
    DAY: "day",
    MONTH: "month",
    WEEK: "week",
    QUARTER: "quarter",
    YEAR: "year",
    THIRTY: "30",
    SEVEN: "7",
};

const CHART_FILTER_TYPES = {
    DAILY: "daily",
    WEEKLY: "weekly",
    MONTHLY: "monthly",
    QUARTERLY: "quarterly",
    YEARLY: "yearly",
    THIRTY_DAYS: "thirtyDays",
    SEVEN_DAYS: "sevenDays",
};

const CHART_TAB_BUTTON = {
    CHART: "Chart",
    TABLE: "Table",
    BUILDING: "Building",
};

module.exports = {
    CHART_FILTER_TYPES,
    CHART_TAB_BUTTON,
    CHART_FILTER_DAY_OF,
    ReportTransferTypeTitleEnum,
}