const moment = require("moment");
const _ = require("lodash");

const { ALERT_TYPE_TYPE } = require("../../types/common.type");

function hospitalizationsFilter(oldFilter) {
    if (oldFilter.hospitalizations[0] === "newHospitalizations") {
        return false;
    }
    if (oldFilter.hospitalizations[0] === "reHospitalizations") {
        return true;
    }
}

function getCurrentDateRange(type, startDate) {
    const dateRanges = [];
    const baseDate = moment(startDate, "YYYY-MM-DD");

    // Align baseDate to the previous Sunday if it's not already a Sunday
    if (baseDate.day() !== 0) {
        baseDate.subtract(baseDate.day(), 'days');  // Move to the previous Sunday
    }

    let rangeStart, rangeEnd;

    switch (type) {
        case ALERT_TYPE_TYPE.WEEKS:
            // Start from Monday and end on Sunday of the same week
            rangeStart = baseDate.clone().startOf('week').format('YYYY-MM-DD');
            rangeEnd = baseDate.clone().endOf('week').format('YYYY-MM-DD');
            break;
        case ALERT_TYPE_TYPE.BI_WEEKS:
            // Start from Monday of the current bi-week and end on Sunday two weeks later
            rangeStart = baseDate.clone().subtract(1, 'weeks').startOf('week').format('YYYY-MM-DD');
            rangeEnd = baseDate.clone().endOf('week').format('YYYY-MM-DD');
            break;
        case ALERT_TYPE_TYPE.MONTHS:
            // Start and end of the month
            rangeStart = baseDate.clone().startOf('month').format('YYYY-MM-DD');
            rangeEnd = baseDate.clone().endOf('month').format('YYYY-MM-DD');
            break;
        default:
            throw new Error("Invalid range type. Use 'WEEKS', 'BI_WEEKS', or 'MONTHS'.");
    }

    dateRanges.push({ startDate: rangeStart, endDate: rangeEnd });

    return dateRanges;
}


function getDateRangesFromGivenDate(type, periods, startDate) {
    const dateRanges = [];
    const baseDate = moment(startDate, "YYYY-MM-DD");

    // Align baseDate to the previous Sunday if it's not already a Sunday
    if (baseDate.day() !== 0) {
        baseDate.subtract(baseDate.day(), 'days');  // Move to the previous Sunday
    }

    for (let i = periods; i > 0; i--) {
        let rangeStart, rangeEnd;

        switch (type) {
            case ALERT_TYPE_TYPE.WEEKS:
                rangeStart = baseDate.clone().subtract(i - 1, 'weeks').startOf('week').format('YYYY-MM-DD');
                rangeEnd = baseDate.clone().subtract(i - 1, 'weeks').endOf('week').format('YYYY-MM-DD');
                break;
            case ALERT_TYPE_TYPE.BI_WEEKS:
                rangeStart = baseDate.clone().subtract(i * 2 - 1, 'weeks').startOf('week').day(1).format('YYYY-MM-DD'); // Start from the Sunday of 2 weeks ago
                rangeEnd = baseDate.clone().subtract(i * 2 - 2, 'weeks').startOf('week').day(6).format('YYYY-MM-DD');    // End on Sunday of the next week
                break;
            case ALERT_TYPE_TYPE.MONTHS:
                rangeStart = baseDate.clone().subtract(i - 1, 'months').startOf('month').format('YYYY-MM-DD');
                rangeEnd = baseDate.clone().subtract(i - 1, 'months').endOf('month').format('YYYY-MM-DD');
                break;
            default:
                throw new Error("Invalid range type. Use 'weekly', 'bi-weekly', or 'monthly'.");
        }

        dateRanges.push({ startDate: rangeStart, endDate: rangeEnd });
    }

    return dateRanges;
}

function getDateRanges(type, periods) {
    const dateRanges = [];

    for (let i = periods; i > 0; i--) {
        let startDate, endDate;

        switch (type) {
            case ALERT_TYPE_TYPE.WEEKS:
                startDate = moment().subtract(i, 'weeks').startOf('isoWeek').format('YYYY-MM-DD');
                endDate = moment().subtract(i, 'weeks').endOf('isoWeek').format('YYYY-MM-DD');
                break;
            case ALERT_TYPE_TYPE.BI_WEEKS:
                startDate = moment().subtract(i * 2 - 1, 'weeks').startOf('isoWeek').format('YYYY-MM-DD');
                // End date of the bi-week is the end of the next week after the start date.
                endDate = moment().subtract(i * 2 - 2, 'weeks').endOf('isoWeek').format('YYYY-MM-DD');
                break;
            case ALERT_TYPE_TYPE.MONTHS:
                startDate = moment().subtract(i, 'months').startOf('month').format('YYYY-MM-DD');
                endDate = moment().subtract(i, 'months').endOf('month').format('YYYY-MM-DD');
                break;
            default:
                throw new Error("Invalid range type. Use 'weekly', 'bi-weekly', or 'monthly'.");
        }

        dateRanges.push({ startDate, endDate });
    }

    return dateRanges;
}

// Import Moment.js if it's not already included
// const moment = require('moment');

function getDateRangeFromCensusDate(censusDate, rangeType) {
    let startDate = moment(censusDate);
    let currentDate = moment();  // Get the current date for comparison

    // Move to the upcoming Sunday if censusDate is midweek for week/biweek cases
    if (rangeType !== ALERT_TYPE_TYPE.MONTHS && startDate.day() !== 0) {
        startDate.add(7 - startDate.day(), 'days');  // Move to the next Sunday
    }

    // Determine if the current period has completed and calculate the next start date
    let nextStartDate;
    if (rangeType === ALERT_TYPE_TYPE.WEEKS) {
        nextStartDate = startDate.clone().add(7, 'days');  // Next week start
        if (currentDate.isBefore(nextStartDate)) {
            return startDate.format('YYYY-MM-DD');  // Return the start date of the current week if incomplete
        }
    } else if (rangeType === ALERT_TYPE_TYPE.BI_WEEKS) {
        nextStartDate = startDate.clone().add(14, 'days');  // Next bi-week start
        if (currentDate.isBefore(nextStartDate)) {
            return startDate.format('YYYY-MM-DD');  // Return the start date of the current bi-week if incomplete
        }
    } else if (rangeType === ALERT_TYPE_TYPE.MONTHS) {
        startDate = startDate.startOf('month').add(1, 'months');  // Move to the start of the next month
        nextStartDate = startDate.clone().add(1, 'months');  // Next month start
        if (currentDate.isBefore(nextStartDate)) {
            return startDate.format('YYYY-MM-DD');  // Return the start date of the current month if incomplete
        }
    } else {
        throw new Error("Invalid range type. Use 'WEEKS', 'BI_WEEKS', or 'MONTHS'.");
    }

    // If the period has completed, return the start date of the next period
    return nextStartDate.format('YYYY-MM-DD');
}

function isTodayAfterPeriodEnd(censusDate, periodType) {
    const startDate = moment(censusDate);
    const today = moment();  // Get today's date for comparison
    let endDate;

    // Calculate the end date based on the period type
    if (periodType === ALERT_TYPE_TYPE.WEEKS) {
        // ISO week starts on Monday and ends on Sunday
        endDate = startDate.clone().endOf('isoWeek'); // End of ISO week
    } else if (periodType === ALERT_TYPE_TYPE.BI_WEEKS) {
        // A bi-week period is considered as two weeks (14 days) from the start date
        endDate = startDate.clone().add(13, 'days').endOf('day'); // End of 2nd week (14th day)
    } else if (periodType === ALERT_TYPE_TYPE.MONTHS) {
        // End of the month
        endDate = startDate.clone().endOf('month');
    } else {
        throw new Error("Invalid period type. Use 'isoWeek', 'biWeek', or 'month'.");
    }

    // Check if today's date is after the calculated end date
    return today.isAfter(endDate);
}

function DCERDataFilter(oldFilter) {
    if (oldFilter.DCERData[0] === "DC") {
        return true;
    }
    if (oldFilter.DCERData[0] === "ER") {
        return false;
    }
}

function returnsDataFilter(oldFilter) {
	if (oldFilter.returnsData[0] === "Returned") {
		return true;
	}
	if (oldFilter.returnsData[0] === "Didn't Return") {
		return false;
	}
}

async function ninetyDaysDataFilter(cardFilter, patientData, ninetyDaysData) {
    let ninetyDaysDataIds = [];
    let ninetyDaysDataFilter = _.filter(ninetyDaysData, ({ _id }) => _.every([_.includes(cardFilter, _id)]));
    if (ninetyDaysDataFilter && ninetyDaysDataFilter.length > 0) {
        ninetyDaysDataFilter.map((item) => (ninetyDaysDataIds = [...ninetyDaysDataIds, ...item.ids]));
    }
    patientData = _.filter(patientData, ({ _id }) => _.every([_.includes(ninetyDaysDataIds, _id)]));
    return patientData;
}

module.exports = {
    getDateRanges,
    getDateRangeFromCensusDate,
    getDateRangeFromCensusDate,
    isTodayAfterPeriodEnd,
    getDateRangesFromGivenDate,
    getCurrentDateRange,
    hospitalizationsFilter,
    DCERDataFilter,
    returnsDataFilter,
    ninetyDaysDataFilter
}