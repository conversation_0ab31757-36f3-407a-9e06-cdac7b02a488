import { ResponsiveBar } from "@nivo/bar";
import classNames from "classnames";
import _ from "lodash";
import { Fragment } from "react";
import { PAGE_TYPE } from "../../../../types/pages.type";
import { calcProPercentsBasedOnFilterAndDays, itemPercentage } from "../../../../utilis/common";
import { calculateDiffBetweenPercentages, pickComparisonColor } from "../../../../utilis/percentage-comparison";
import CardItemTooltip from "../../../shared/CardItemTooltip";
import ColorBox from "../../../shared/dashboard/ColorBox";
import NoRecordFound from "../../../shared/NoRecordFound";
import CheckboxButton from "../../shared/checkboxButton/CheckboxButton";
import styles from "./DayCardList.module.scss";
import DayCardSkeletonList from "./skeleton/DayCardSkeletonList";
import useLoadingToggle from "../../../hooks/useLoadingToggle";
import { Box, Tooltip } from "@mui/material";
import CheckboxLoader from "../../../shared/checkbox-loader/CheckboxLoader";
import CheckboxCircle from "../../shared/checkboxButton/CheckboxCircle";

const DayCardList = ({
	data,
	dataComparison,
	filter,
	filterComparison,
	handleToggle,
	selectedItem = [],
	type,
	page,
	averageCensusComparison,
	averageCensus,
	cardTitle,
	admissionCompareAgainst,
	admissionCompareAgainstComparison,
	reverseColorsAdmissionPage,
	loading,
	projectionDays,
	spacialSelectedItem = [],
	question,
	isCustom = false,
	isCustomTab = false,
	customTab
}) => {
	const specialPages = [PAGE_TYPE.ADMISSION];
	const isSpecialPage = specialPages.find((x) => x === page)?.length > 0;
	const { loadingItems, handleToggleWithLoader } = useLoadingToggle();

	data = data.map((day, index) => {
		let comparisonColor;
		let comparingAgainstScaled;
		let numberOfDays;
		const itemComparison = _.find(dataComparison, { _id: day._id });

		if (page === PAGE_TYPE.ADMISSION) {
			const valueForAdmission = itemPercentage(day.value, admissionCompareAgainst, "number");
			const valueForAdmissionComparison = itemPercentage(
				itemComparison?.value,
				admissionCompareAgainstComparison,
				"number"
			);
			const {
				percentageDiff,
				itemTotalComparisonScaled,
				numberOfDays: numberOfDaysTemp,
			} = calculateDiffBetweenPercentages(
				valueForAdmission,
				valueForAdmissionComparison,
				reverseColorsAdmissionPage,
				filter,
				filterComparison,
				itemComparison?.value,
				projectionDays
			);

			numberOfDays = numberOfDaysTemp;
			comparingAgainstScaled = itemTotalComparisonScaled;
			comparisonColor = pickComparisonColor(percentageDiff, isSpecialPage);
		} else {
			const percentageOfAverageCensus = itemPercentage(day.value, averageCensus, "number");
			const percentageOfAverageCensusComparison = itemPercentage(
				itemComparison?.value || 0,
				averageCensusComparison,
				"number"
			);
			const {
				percentageDiff,
				itemTotalComparisonScaled,
				numberOfDays: numberOfDaysTemp,
			} = calculateDiffBetweenPercentages(
				percentageOfAverageCensus,
				percentageOfAverageCensusComparison,
				false,
				filter,
				filterComparison,
				itemComparison?.value,
				projectionDays
			);

			numberOfDays = numberOfDaysTemp;
			comparisonColor = pickComparisonColor(percentageDiff, isSpecialPage);
			comparingAgainstScaled = itemTotalComparisonScaled;
		}

		return {
			...day,
			comparisonColor,
			comparingAgainst: itemComparison?.value,
			dayComparison: itemComparison,
			comparingAgainstScaled,
			numberOfDays,
		};
	});

	const defaultColor = "#FFECA6";

	return (
		<div className={`w100 ${styles.dayCardList} ${isCustom && styles.custom}`}>
			<div style={{
				minWidth: `${data?.length * 55}px`,
				height: "245px",
				paddingTop: "40px",
			}}>
				{loading ? (
					<DayCardSkeletonList />
				) : (
					data &&
					data.length > 0 && (
						<ResponsiveBar
							data={[...data]
								.sort((a, b) => {
									return (a.isSpacialItem === true ? 1 : 0) - (b.isSpacialItem === true ? 1 : 0);
								})
							}
							keys={["value"]}
							indexBy="label"
							margin={{ top: 0, right: isCustom ? 15 : 0, bottom: 40, left: 0 }}
							padding={isCustom ? data?.length <= 2 ? 0.5 : 0.2 : 0.3}
							valueScale={{ type: "linear" }}
							indexScale={{ type: "band", round: true }}
							colors={({ data: d }) => {
								const ID = d?._id ?? null;
								return selectedItem.includes(ID) ? "#FFC700" : d?.color ?? defaultColor;
							}}
							enableArcLabels={false}
							enableArcLinkLabels={false}
							enableGridY={false}
							enableGridX={false}
							borderRadius="4"
							axisBottom={{
								tickSize: 0,
								tickPadding: 10,
								tickRotation: 0,
								renderTick: ({ value, x, y, opacity }) => {
									const shortLabel = value?.slice(0, 3);
									return (
										<g transform={`translate(${x - 16},${y + 15})`} style={{ opacity }} arrow placement="bottom">
											<foreignObject width={40} height={30}>
												<Tooltip title={value} placement="top">
													<div style={{ display: "flex", justifyContent: "center" }}>
														<span style={{
															fontSize: 12,
															color: "#444",
															textAlign: "center",
															pointerEvents: "auto"
														}}>
															{shortLabel}
														</span>
													</div>
												</Tooltip>
											</foreignObject>
										</g>
									);
								}
							}}
							label={(_label) => {
								return _label.data.percentage
									? `${_label.data.percentage}%`
									: `${itemPercentage(_label?.value, null, "number", page)}%`;
							}}
							barComponent={({ bar }) => {
								return (
									<g transform={`translate(${bar.x},${bar.y})`}>
										<rect
											width={bar.width}
											height={bar.height}
											fill={bar?.data?.data?.color ?? defaultColor}
											strokeWidth="0"
											stroke={bar.data.data.color}
											focusable="false"
											rx={4}
											ry={4}
										></rect>
										{bar.data.data.percentage && (
											<text
												x={bar.width / 2}
												y={bar.height / 2}
												textAnchor="middle"
												dominantBaseline="central"
												style={{
													fontFamily: "manrope",
													fontSize: "11px",
													pointerEvents: "none",
												}}
											>
												{Math.round(bar.data.data.percentage)}%
											</text>
										)}
									</g>
								);
							}}
						/>
					)
				)}
			</div>
			<NoRecordFound data={data} />
			<div className={`${styles.footerWrapper}`}>
				<div className={`ffmar fs14 fw500  ${styles.itemsWrpr} ${isCustom && styles.customItemsWrpr}`}>
					{data?.length > 0 &&
						[...data]
							.sort((a, b) => {
								return (a.isSpacialItem === true ? 1 : 0) - (b.isSpacialItem === true ? 1 : 0);
							})
							.map((item, index) => {
								const ID = item._id;
								const labelId = `checkbox-list-return-label-${ID}`;
								const selected = selectedItem.indexOf(ID) !== -1;
								const itemComparison = item.dayComparison
									? { ...item.dayComparison, total: item.dayComparison.value }
									: null;
								const total = item?.value ?? item?.total ?? 0;

								const isSpacialItem = item?.isSpacialItem ?? false;

								return (
									<Fragment key={`${index}dayList`}>
										<div
											className={classNames("df",
												"aic",
												styles.sec,
												selected && styles.selected,
												loading && `skeleton`,
												isCustom && styles.customSec,
												data?.length <= 2 && styles.customSecSmall,
												data?.length > 2 && data?.length <= 4 && styles.customSecMidSmall
											)}
										>
											{loading ? null : (
												<>
													<CardItemTooltip item={item}>
														<div style={{ textAlign: "center" }} className={`${Number(total) <= 10 ? styles.valueLabel : ""}`}>
															{calcProPercentsBasedOnFilterAndDays(total, filter, projectionDays)}
														</div>

														<div className={`${styles.dayListCheckbox}`}>
															<Box position="relative" display="inline-flex" alignItems="center">
																{isSpacialItem ? (
																	<CheckboxCircle
																		className={styles.checkboxButton}
																		labelId={labelId}
																		handleToggle={() =>
																			handleToggleWithLoader(() => handleToggle({
																				item: item,
																				type,
																				itemComparison: itemComparison,
																				isChecked: spacialSelectedItem?.indexOf(ID) !== -1,
																				cardTitle,
																				isSpacialItem: item?.isSpacialItem ?? false,
																				isCustomTab,
																				customTab
																			}), ID)
																		}
																		sx={{
																			...(loadingItems[ID] && { opacity: 0 }),
																			marginTop: "8px",
																			marginLeft: "7px",
																			textAlign: "center",
																			padding: "6px"
																		}}
																		checked={spacialSelectedItem?.indexOf(ID) !== -1 || selectedItem.indexOf(ID) !== -1}
																	/>
																) : (
																	<CheckboxButton
																		labelId={labelId}
																		className={`${styles.dayListLabel}`}
																		handleToggle={() =>
																			!isSpacialItem ? handleToggleWithLoader(() => handleToggle({
																				item: { ...item, total: total },
																				itemComparison,
																				type,
																				isChecked: selected,
																				cardTitle,
																				question: isCustom
																					? { isCustom: true, customQuestionInputType: question?.customQuestionInputType, isCustomTab, cardTitle, customTab }
																					: null
																			}), item?._id) : undefined
																		}
																		checked={selected}
																		sx={{
																			...(loadingItems[item?._id] && { opacity: 0 }),
																			marginTop: "2px",
																		}}
																	/>
																)}
																<CheckboxLoader
																	className={"small-dots-day-list"}
																	isLoading={loadingItems[item?._id]}
																/>
															</Box>
															<ColorBox
																type="button"
																color={item.comparisonColor}
																comparingAgainst={item.comparingAgainst}
																comparingAgainstScaled={item.comparingAgainstScaled}
																numberOfDays={item.numberOfDays}
																sx={{ marginTop: "10px", marginLeft: "5px" }}
															/>
														</div>
													</CardItemTooltip>
												</>
											)}
										</div>
									</Fragment>
								);
							})}
				</div>
			</div>
		</div>
	);
};

export default DayCardList;
