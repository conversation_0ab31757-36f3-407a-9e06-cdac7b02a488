const mongoose = require("mongoose");
const { Schema, Types } = mongoose;

const LogSchema = new Schema(
  {
    data: Object,
    type: String,
    userId: { type: Types.ObjectId, ref: "user", required: false },
    accountId: { type: Types.ObjectId, ref: "account", required: false },
    facilityId: { type: Types.ObjectId, ref: "facility", required: false },
    description: { type: String, required: false },
  },
  { timestamps: true }
);

mongoose.model("log", LogSchema);
