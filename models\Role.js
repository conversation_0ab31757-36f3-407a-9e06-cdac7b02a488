const mongoose = require("mongoose");
const { Schema } = mongoose;
var slug = require("mongoose-slug-generator");

mongoose.plugin(slug);


const RoleSchema = new Schema(
    {
        name: { type: String, required: true },
        active: { type: Boolean, default: true },
        order: { type: Number, default: 1 },
        slug: { type: String, slug: "name", unique: true },
    },
    { timestamps: true }
);

const Role = mongoose.model("role", RoleSchema);

module.exports = Role
