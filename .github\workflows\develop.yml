name: Deploy website

on:
  workflow_dispatch:
  push:
    branches: [ develop ]

jobs:
  build-and-deploy:
    name: Build and deploy
    runs-on: ubuntu-latest

    environment: dev
    
    steps:
    - uses: actions/checkout@v4
      
    - name: Install Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '20.x'
      
    - name: Build website
      run: |
        npm install
        npm run build
        
    - name: Deploy to Azure
      uses: TravisSpomer/deploy-to-azure-storage@v1.5.0
      with:
        source-path: build
        sas-url: ${{ secrets.DEPLOY_SAS_URL }}
