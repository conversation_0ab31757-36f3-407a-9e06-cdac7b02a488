import {
    Box,
    Dialog,
    DialogContent,
    DialogTitle,
    Grid,
    IconButton,
    Typography,
    Card,
    CardContent,
    CardMedia,
    Chip,
    Fade,
    useTheme,
    alpha,
} from "@mui/material";
import CloseIcon from '@mui/icons-material/Close';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import { QUESTIONS_TEMPLATES } from "../../../../../../types/question.type";

const TemplateInfoDialog = ({
    open,
    onClose,
    templateType,
    handleSelectTemplate,
}) => {
    const theme = useTheme();

    return (
        <Dialog 
            open={open} 
            onClose={onClose} 
            maxWidth="lg" 
            fullWidth
            PaperProps={{
                sx: {
                    borderRadius: 3,
                    background: 'white',
                    backdropFilter: 'blur(10px)',
                    boxShadow: `0 24px 48px ${alpha(theme.palette.common.black, 0.12)}`,
                    maxHeight: '90vh',
                    overflow: 'visible',
                }
            }}
        >
            <DialogTitle sx={{ pb: 2, pt: 3 }}>
                <Box display="flex" alignItems="center" justifyContent="space-between">
                    <Box>
                        <Typography variant="h4" fontWeight="bold" color="primary.main">
                            Choose Your Template
                        </Typography>
                        <Typography variant="body2" color="text.secondary" mt={0.5}>
                            Select a template that best fits your survey needs
                        </Typography>
                    </Box>
                    <IconButton 
                        aria-label="close" 
                        onClick={onClose}
                        sx={{
                            bgcolor: alpha(theme.palette.grey[500], 0.1),
                            '&:hover': {
                                bgcolor: alpha(theme.palette.grey[500], 0.2),
                                transform: 'scale(1.1)',
                            },
                            transition: 'all 0.2s ease-in-out',
                        }}
                    >
                        <CloseIcon />
                    </IconButton>
                </Box>
            </DialogTitle>
            <DialogContent sx={{ px: 3, py: 2, overflow: 'auto' }}>
                <Grid container spacing={3} sx={{ mt: 0 }}>
                    {QUESTIONS_TEMPLATES.map((template, index) => {
                        const isSelected = templateType === template.value;
                        return (
                            <Grid item xs={12} sm={6} md={4} key={template.value}>
                                <Fade in={open} timeout={300 + index * 100}>
                                    <Card
                                        onClick={() => handleSelectTemplate(template.value)}
                                        sx={{
                                            cursor: 'pointer',
                                            height: '100%',
                                            display: 'flex',
                                            flexDirection: 'column',
                                            position: 'relative',
                                            border: 2,
                                            borderColor: isSelected 
                                                ? 'primary.main' 
                                                : 'transparent',
                                            borderRadius: 3,
                                            transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                                            transform: isSelected ? 'translateY(-4px)' : 'translateY(0)',
                                            boxShadow: isSelected 
                                                ? `0 12px 24px ${alpha(theme.palette.primary.main, 0.15)}` 
                                                : `0 4px 12px ${alpha(theme.palette.grey[500], 0.1)}`,
                                            '&:hover': {
                                                transform: 'translateY(-8px)',
                                                boxShadow: `0 16px 32px ${alpha(theme.palette.primary.main, 0.2)}`,
                                                borderColor: 'primary.main',
                                                '& .template-image': {
                                                    transform: 'scale(1.05)',
                                                },
                                                '& .template-overlay': {
                                                    opacity: 1,
                                                }
                                            },
                                        }}
                                    >
                                        {/* Selection Indicator */}
                                        {isSelected && (
                                            <Box
                                                sx={{
                                                    position: 'absolute',
                                                    top: 12,
                                                    right: 12,
                                                    zIndex: 2,
                                                    bgcolor: 'primary.main',
                                                    borderRadius: '50%',
                                                    p: 0.5,
                                                    display: 'flex',
                                                    alignItems: 'center',
                                                    justifyContent: 'center',
                                                }}
                                            >
                                                <CheckCircleIcon sx={{ color: 'white', fontSize: 20 }} />
                                            </Box>
                                        )}

                                        {/* Image Container */}
                                        <Box
                                            sx={{
                                                position: 'relative',
                                                overflow: 'hidden',
                                                borderRadius: '12px 12px 0 0',
                                            }}
                                        >
                                            <CardMedia
                                                component="img"
                                                image={`/assets/template/${template.image}`}
                                                alt={template.label}
                                                className="template-image"
                                                sx={{
                                                    height: 220,
                                                    width: '100%',
                                                    objectFit: 'contain',
                                                    p: 1,
                                                    transition: 'transform 0.4s cubic-bezier(0.4, 0, 0.2, 1)',
                                                }}
                                            />
                                            
                                            {/* Hover Overlay */}
                                            <Box
                                                className="template-overlay"
                                                sx={{
                                                    position: 'absolute',
                                                    top: 0,
                                                    left: 0,
                                                    right: 0,
                                                    bottom: 0,
                                                    background: `linear-gradient(45deg, ${alpha(theme.palette.primary.main, 0.8)}, ${alpha(theme.palette.secondary.main, 0.8)})`,
                                                    display: 'flex',
                                                    alignItems: 'center',
                                                    justifyContent: 'center',
                                                    opacity: 0,
                                                    transition: 'opacity 0.3s ease-in-out',
                                                }}
                                            >
                                                <Typography 
                                                    variant="h6" 
                                                    color="white" 
                                                    fontWeight="bold"
                                                    textAlign="center"
                                                >
                                                    Select Template
                                                </Typography>
                                            </Box>
                                        </Box>

                                        {/* Content */}
                                        <CardContent sx={{ flexGrow: 1, p: 2.5 }}>
                                            <Box display="flex" alignItems="center" justifyContent="space-between" mb={1}>
                                                <Typography 
                                                    variant="h6" 
                                                    fontWeight="600" 
                                                    color="text.primary"
                                                    sx={{ 
                                                        fontSize: '1.1rem',
                                                        lineHeight: 1.3,
                                                    }}
                                                >
                                                    {template.label}
                                                </Typography>
                                                {isSelected && (
                                                    <Chip 
                                                        label="Selected" 
                                                        size="small" 
                                                        color="primary"
                                                        sx={{ 
                                                            fontWeight: 'bold',
                                                            fontSize: '0.75rem',
                                                        }}
                                                    />
                                                )}
                                            </Box>
                                        </CardContent>
                                    </Card>
                                </Fade>
                            </Grid>
                        );
                    })}
                </Grid>
                
                {/* Empty State */}
                {QUESTIONS_TEMPLATES.length === 0 && (
                    <Box 
                        display="flex" 
                        flexDirection="column" 
                        alignItems="center" 
                        justifyContent="center" 
                        minHeight={300}
                        textAlign="center"
                    >
                        <Typography variant="h6" color="text.secondary" gutterBottom>
                            No templates available
                        </Typography>
                        <Typography variant="body2" color="text.disabled">
                            Please check back later for available templates.
                        </Typography>
                    </Box>
                )}
            </DialogContent>
        </Dialog>
    );
};

export default TemplateInfoDialog;
