const dayjs = require("dayjs");
const moment = require("moment");

async function momentDateFormat(date, format = "MM/DD/YYYY") {
    return moment.utc(date).format(format);
}

async function nowDate() {
    return moment().startOf('day').utc().toDate();
}

function getStartOfDate(date) {
    return new Date(dayjs(new Date(date)).startOf("day"))
}

function getEndOfDate(date) {
    return new Date(dayjs(new Date(date)).endOf("day"))
}

function getDaysAfter(date, days = 30) {
    return moment.utc(date).add(days, 'day').toDate();
    // const dateAdd = dayjs(date).add(days, 'day');
    // return new Date(dateAdd.startOf("day"));
}

function getDaysBefore(date, days = 30, format = null) {
    if (format) {
        return moment.utc(date, format).subtract(days, 'day').toDate();
    } else {
        return moment.utc(date).subtract(days, 'day').toDate();
    }
    // const dateAdd = dayjs(date).subtract(days, 'day');
    // momentutc(date).subtract(30, 'day').toDate();
    // return new Date(dateAdd.startOf("day"));
}

async function getDayNameFromDate(date) {
    if (date) {
        return moment.utc(date).format('ddd')
    } else {
        return null
    }
}
async function getShiftName(time) {
    if (time) {
        const format = 'HH:mm';

        const morningStart = moment("06:59", format);
        const morningEnd = moment("15:00", format);

        const eveningStart = moment("14:59", format);
        const eveningEnd = moment("23:00", format);

        const timeFormat = moment(time, 'h:mm a').format(format);

        const givenTime = moment(timeFormat, format);
        const givenTimeNextDay = moment(timeFormat, format);
        givenTimeNextDay.add(1, "days");

        if (givenTime.isAfter(morningStart) && givenTime.isBefore(morningEnd)) {
            return "Morning";
        }
        if (givenTime.isAfter(eveningStart) && givenTime.isBefore(eveningEnd)) {
            return "Evening";
        }
        return "Night";
    } else {
        return null;
    }
}

async function checkADTDuplicate(patient, isDobCheck = true) {
    if (patient.firstName && patient.lastName) {
        let firstName = patient.firstName.trim().toLowerCase();
        let lastName = patient.lastName.trim().toLowerCase();
        const specialChars = !/[~`!#$%\^&*+=\-\[\]\\';,/{}|\\":<>\?]/;
        firstName = firstName.split(specialChars).filter(element => element).join(".{1}");
        lastName = lastName.split(specialChars).filter(element => element).join(".{1}");

        let query =
        {
            firstName: { $regex: new RegExp("^" + firstName, "gi") },
            lastName: { $regex: new RegExp("^" + lastName, "gi") },
            ...isDobCheck && {
                DOB: {
                    $gte: await toStartFilterDate(patient.DOB),
                    $lte: await toEndFilterDate(patient.DOB),
                }
            }
        }
        return query;
    } else {
        return null
    }
}

async function toStartFilterDate(date, format = null) {
    if (format) {
        const newDateOfADT = moment(date).format("YYYY-MM-DD");
        const latestStringDate = moment.utc(newDateOfADT).toDate();
        return latestStringDate;
    } else {
        return moment.utc(date).toDate();
    }
}

async function toSevenDate(date, type = "start") {
    if (type === "start") {
        return moment.utc(date).startOf("day").toDate();
    } else {
        return moment.utc(date).add(7, "day").toDate();
    }
}

async function toWeekDate(date, type = "start") {
    if (type === "start") {
        return moment.utc(date).startOf("week").toDate();
    } else {
        return moment.utc(date).endOf("week").toDate();
    }
}

async function toQuatreDate(date, type = "start") {
    if (type === "start") {
        return moment.utc(date).startOf("quarter").toDate();
    } else {
        return moment.utc(date).endOf("quarter").toDate();
    }
}

async function toMonthDate(date, type = "start") {
    if (type === "start") {
        return moment.utc(date).startOf("month").toDate();
    } else {
        return moment.utc(date).endOf("month").toDate();
    }
}

async function toYearDate(date, type = "start") {
    if (type === "start") {
        return moment.utc(date).startOf("year").toDate();
    } else {
        return moment.utc(date).endOf("year").toDate();
    }
}

async function toDailyDate(date, type = "start") {
    if (type === "start") {
        return moment.utc(date).startOf("day").toDate();
    } else {
        return moment.utc(date).endOf("day").toDate();
    }
}

async function toStarDateNextDay(date, format = null) {
    if (format) {
        const newDateOfADT = moment(date).format("YYYY-MM-DD");
        const latestStringDate = moment.utc(newDateOfADT).toDate();
        return latestStringDate;
    } else {
        return moment.utc(date).add(1, "day").toDate();
    }
}

async function toStarDatePreviousDay(date, format = null) {
    if (format) {
        const newDateOfADT = moment(date).format("YYYY-MM-DD");
        const latestStringDate = moment.utc(newDateOfADT).toDate();
        return latestStringDate;
    } else {
        return moment.utc(date).subtract(1, "day").toDate();
    }
}

async function toEndFilterDate(date, format = null) {
    if (format) {
        const newDateOfADT = moment(date, format).format("YYYY-MM-DD");
        const latestStringDate = moment.utc(newDateOfADT).endOf("day").toDate();
        return latestStringDate;
    } else {
        return moment.utc(date).endOf("day").toDate();
    }
}

async function toSaveDate(date, format = null) {
    if (format) {
        const newDateOfADT = moment(date, format).format("YYYY-MM-DD");
        const latestStringDate = moment.utc(newDateOfADT).toISOString();
        return latestStringDate;
    } else {
        const newDateOfADT = moment(date).format("YYYY-MM-DD");
        const latestStringDate = moment.utc(newDateOfADT).toISOString();
        return latestStringDate;
    }
}

async function toDisplayTime(date, format = "MM/DD/YYYY") {
    return moment.utc(date).format(format)
}

async function toSaveEndDate(date, format = null) {
    if (format) {
        const newDateOfADT = moment(date, format).format("YYYY-MM-DD");
        const latestStringDate = moment.utc(newDateOfADT).endOf("day").toISOString();
        return latestStringDate;
    } else {
        const latestStringDate = moment.utc(date).endOf("day").toISOString();
        return latestStringDate;
    }
}

async function toDateOfADTFilter(startDate, endDate) {
    return {
        $gte: await toStartFilterDate(startDate),
        $lte: await toEndFilterDate(endDate),
    }
}

async function dateRangeOverlaps(a_start, a_end, b_start, b_end) {
    const start1 = moment(a_start);
    const end1 = moment(a_end);
    const start2 = moment(b_start);
    const end2 = moment(b_end);

    const overlap = end1.isBetween(start2, end2) || start1.isBetween(start2, end2) || (start1.isBefore(start2) && end1.isAfter(end2));
    return overlap;
}
async function multipleDateRangeOverlaps(arguments) {
    var i, j;
    if (arguments.length % 2 !== 0)
        throw new TypeError('Arguments length must be a multiple of 2');
    for (i = 0; i < arguments.length - 2; i += 2) {
        for (j = i + 2; j < arguments.length; j += 2) {
            if (
                await dateRangeOverlaps(
                    arguments[i], arguments[i + 1],
                    arguments[j], arguments[j + 1]
                )
            ) return true;
        }
    }
    return false;
}

async function findOverlapDates(dateArrays) {
    // Sort the date arrays by start date
    const sortedArrays = dateArrays.sort((a, b) => a[0] - b[0]);

    // Create an empty array to store overlapping dates
    let overlaps = [];

    // Iterate through the sorted date arrays and compare each pair
    for (let i = 0; i < sortedArrays.length - 1; i++) {
        const [currStart, currEnd] = sortedArrays[i];
        const [nextStart, nextEnd] = sortedArrays[i + 1];

        // Check for an overlap between the current and next date ranges
        if (nextStart <= currEnd) {
            const overlapStart = Math.max(currStart, nextStart);
            const overlapEnd = Math.min(currEnd, nextEnd);
            overlaps = [];
            overlaps.push([moment.utc(overlapStart).toDate(), moment.utc(overlapEnd).toDate()]);
        }
    }

    return {
        latestDate: overlaps[0][0],
        earliestDate: overlaps[0][1]
    }
}

async function isDateRangeAvailable(startDate, endDate, existingDateRanges) {
    let matchedCnt = 0;
    for (let i = 0; i < existingDateRanges.length; i++) {
        const range = existingDateRanges[i];
        if (moment.utc(startDate).isBetween(range[0], range[1]) ||
            moment.utc(endDate).isBetween(range[0], range[1]) ||
            moment.utc(range[0]).isBetween(startDate, endDate) ||
            moment.utc(range[1]).isBetween(startDate, endDate) ||
            (moment.utc(startDate).isSame(range[0]) || moment.utc(endDate).isSame(range[1]))
        ) {
            matchedCnt++;
        }
    }
    if (matchedCnt === existingDateRanges.length) {
        return true
    } else {
        return false
    }
}

function areArraysEqual(arrays) {
    if (!Array.isArray(arrays) || arrays.length === 0) {
        return false; // Not an array or empty array
    }

    // Check if all sub-arrays have the same length
    const length = arrays[0].length;
    if (!arrays.every(arr => Array.isArray(arr) && arr.length === length)) {
        return false; // Sub-arrays have different lengths
    }

    // Check if all sub-arrays are equal
    for (let i = 1; i < arrays.length; i++) {
        for (let j = 0; j < length; j++) {
            if (!moment(arrays[i][j]).isSame(arrays[0][j])) {
                return false; // Sub-arrays are not equal
            }
        }
    }

    return true; // All sub-arrays are equal
}

async function dateDeferenceDay({ startDate, endDate }) {
    var a = moment.utc(startDate).startOf("day");
    var b = moment.utc(endDate).endOf("day");
    let days = b.diff(a, "days") + 1;
    return days;
}

const dateFormat = (date, format = "MM/DD/YYYY") => {
	return date ? moment(date).format(format) : null;
}

module.exports = {
    dateFormat,
    dateDeferenceDay,
    areArraysEqual,
    toQuatreDate,
    toWeekDate,
    toMonthDate,
    toYearDate,
    toStarDateNextDay,
    toStarDatePreviousDay,
    findOverlapDates,
    isDateRangeAvailable,
    multipleDateRangeOverlaps,
    toDateOfADTFilter,
    toStartFilterDate,
    toEndFilterDate,
    toSaveEndDate,
    toSaveDate,
    toDisplayTime,
    momentDateFormat,
    getStartOfDate,
    getEndOfDate,
    getShiftName,
    getDayNameFromDate,
    getDaysAfter,
    getDaysBefore,
    checkADTDuplicate,
    nowDate,
    toSevenDate,
    toDailyDate
};
