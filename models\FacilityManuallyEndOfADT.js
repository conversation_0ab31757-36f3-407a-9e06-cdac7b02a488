const mongoose = require("mongoose");
const { Schema, Types } = mongoose;

const facilityManuallyEndOfADTSchema = new Schema(
  {
    accountId: { type: Types.ObjectId, ref: "account" },
    facilityId: { type: Types.ObjectId, ref: "facility", required: false },
    userId: { type: Types.ObjectId, ref: "user", required: false },    
    endDateOfADT: { type: Date, required: false, default: null },
  },
  { timestamps: true }
);

mongoose.model("facilityManuallyEndOfADT", facilityManuallyEndOfADTSchema);
