const _ = require("lodash");
const { PAGE_TYPE, ADMISSION_TYPES, CO_TRANSFER_CARDS_TYPE, ADMISSION_CARDS_TYPE, DECEASED_CARDS_TYPE } = require("../../types/common.type");
const { hospitalCustomAlertFilterListData, communityTransferCustomAlertFilterListData, deceasedCustomAlertFilterListData, dynamicCardFilterAlerts, getCustomTabsCards } = require("../../api/utilis/reports/report-common");
const { HOSPITAL_CARDS_TYPE } = require("../../types/hospital.type");
const { admissionCustomAlertFilterListData } = require("../../api/utilis/reports/report-common-admission");


async function handleCustomFilter(cardFilterData, resData = [], name, objectCustom, page, res, dynamicCards, isDebug) {
    let customFilterObj;
    // console.log(cardFilterData, 'cardFilterData');
    const slug = cardFilterData?.slug;
    const cardType = cardFilterData?.filter?.cardType;
    const cardFilterArr = cardFilterData?.cardFilter?.[cardType] ?? [];
    const cardFilterSpacial = cardFilterData?.cardFilter?.[`${cardType}_spacial`] ?? [];

    const cardFilter = [...cardFilterArr, ...cardFilterSpacial];

    let cardTypeHandlers;

    let patientFilterData = [];
    let ninetyDaysData = [];
    let hospitalDBData = [];
    let sixtyDaysData = [];

    if (page === PAGE_TYPE.HOSPITAL) {
        patientFilterData = res.list;
        ninetyDaysData = res.ninetyDaysData;
        hospitalDBData = res.hospitalData;
    } else {
        patientFilterData = res?.data?.list;
        ninetyDaysData = res?.data?.ninetyDaysData;
        hospitalDBData = res?.data?.hospitalData;
        sixtyDaysData = res?.data?.sixtyDaysData;
    }

    // Mapping card types to respective data processing functions
    if (page === PAGE_TYPE.HOSPITAL) {
        cardTypeHandlers = {
            [HOSPITAL_CARDS_TYPE.HOSPITALIZATIONS]: () => hospitalizationData(resData, name),
            [HOSPITAL_CARDS_TYPE.DCER_DATA]: () => dcErData(resData),
            [HOSPITAL_CARDS_TYPE.INSURANCE_DATA]: () => insuranceData(resData),
            [HOSPITAL_CARDS_TYPE.RETURNS_DATA]: () => returnsData(resData),
            [HOSPITAL_CARDS_TYPE.NINETY_DAYS_DATA]: () => ninetyDaysDataList(ninetyDaysData, resData),
            [HOSPITAL_CARDS_TYPE.FLOORS_DATA]: () => floorsData(resData),
            [HOSPITAL_CARDS_TYPE.DOCTOR_DATA]: () => doctorData(resData),
            [HOSPITAL_CARDS_TYPE.DAYS_DATA]: () => daysData(resData),
            [HOSPITAL_CARDS_TYPE.DX_DATA]: () => dxData(resData),
            [HOSPITAL_CARDS_TYPE.SHIFT_DATA]: () => shiftData(resData),
            [HOSPITAL_CARDS_TYPE.NURSE_DATA]: () => nurseData(resData),
            [HOSPITAL_CARDS_TYPE.HOSPITAL_DATA]: () => hospitalDataList(hospitalDBData, resData),
        };
    } else if (page === PAGE_TYPE.COMMUNITY_TRANSFER) {
        cardTypeHandlers = {
            [CO_TRANSFER_CARDS_TYPE.SIXTY_DAYS_DATA]: () => ninetyDaysDataList(sixtyDaysData, resData),
            [CO_TRANSFER_CARDS_TYPE.DOCTOR_DATA]: () => doctorData(resData),
            [CO_TRANSFER_CARDS_TYPE.INSURANCE_DATA]: () => insuranceData(resData),
            [CO_TRANSFER_CARDS_TYPE.RETURNS_DATA]: () => returnsData(resData),
            [CO_TRANSFER_CARDS_TYPE.FLOORS_DATA]: () => floorsData(resData),
            [CO_TRANSFER_CARDS_TYPE.SNF]: () => snfData(resData),
            [CO_TRANSFER_CARDS_TYPE.AMA]: () => amaData(resData),
            [CO_TRANSFER_CARDS_TYPE.SAFE_DISCHARGE]: () => safeDischargeData(resData),
            [CO_TRANSFER_CARDS_TYPE.TOTAL]: () => totalData(resData),
        };
    } else if (page === PAGE_TYPE.ADMISSION) {
        cardTypeHandlers = {
            [ADMISSION_CARDS_TYPE.HOSPITAL_DATA]: () => admissionHospitalData(resData),
            [ADMISSION_CARDS_TYPE.FLOORS_DATA]: () => floorsData(resData),
            [ADMISSION_CARDS_TYPE.INSURANCE_DATA]: () => admissionInsuranceData(resData),
            [ADMISSION_CARDS_TYPE.DAYS_DATA]: () => daysData(resData),
            [ADMISSION_CARDS_TYPE.DX_DATA]: () => dxData(resData),
            [ADMISSION_CARDS_TYPE.DOCTOR_DATA]: () => doctorData(resData),
            [ADMISSION_CARDS_TYPE.ADMISSION]: () => admissionData(resData),
            [ADMISSION_CARDS_TYPE.READMISSION]: () => readmissionData(resData),
            [ADMISSION_CARDS_TYPE.TOTAL]: () => totalData(resData),
        };
    } else if (page === PAGE_TYPE.DECEASED) {
        cardTypeHandlers = {
            [DECEASED_CARDS_TYPE.NINETY_DAYS_DATA]: () => ninetyDaysDataList(ninetyDaysData, resData),
            [DECEASED_CARDS_TYPE.FLOORS_DATA]: () => floorsData(resData),
            [DECEASED_CARDS_TYPE.DOCTOR_DATA]: () => doctorData(resData),
            [DECEASED_CARDS_TYPE.INSURANCE_DATA]: () => insuranceData(resData),
            [DECEASED_CARDS_TYPE.TOTAL]: () => totalData(resData),
        };
    }

    if (cardTypeHandlers[cardType]) {
        const customFilterObjArr = await cardTypeHandlers[cardType]();

        if (Array.isArray(customFilterObjArr) && customFilterObjArr.length > 0 && cardFilter.length > 0) {
            const filtered = customFilterObjArr.filter(ele => cardFilter.includes(String(ele._id)));

            customFilterObj = (cardFilter.length > 1)
                ? [{
                    _id: slug,
                    label: filtered.map(ele => ele.label).join(', '),
                    total: filtered.reduce((sum, ele) => sum + ele.total, 0)
                }]
                : filtered;
        } else {
            customFilterObj = customFilterObjArr;
        }

    } else if (Array.isArray(dynamicCards) && dynamicCards.length > 0) {
        const filterCustomCards = dynamicCards.filter((ele) => ele.accessor === cardType);

        if (filterCustomCards.length > 0) {

            const dynamicCardsObj = await dynamicCardFilterAlerts(resData, filterCustomCards, res, page);

            const customFilterObjArr = dynamicCardsObj?.[cardType] ?? [];

            if (Array.isArray(customFilterObjArr) && customFilterObjArr.length > 0 && cardFilter.length > 0) {
                const filtered = customFilterObjArr.filter(ele => cardFilter.includes(String(ele._id)));
                customFilterObj = cardFilter.length > 1
                    ? [{
                        _id: slug,
                        label: filtered.map(ele => ele.label).join(', '),
                        total: filtered.reduce((sum, ele) => sum + ele.total, 0)
                    }]
                    : filtered;
            } else {
                customFilterObj = customFilterObjArr;
            }
        } else {
            console.warn(`No matching cards found for cardType: ${cardType}`);
        }

    }

    // Update objectCustom with processed filter object
    if (customFilterObj && customFilterObj.length > 0) {
        if (name) {
            customFilterObj = customFilterObj.map((ele) => ({
                ...ele,
                name
            }));
        }

        objectCustom[cardFilterData.slug] = customFilterObj;
    }
    // console.log(objectCustom, 'objectCustom');

}

async function processCustomEnabledObject(customEnabledObject, customAlertsArr, res, objectCustom, page, dynamicCards = [], isDebug) {
    if (customEnabledObject && !_.isEmpty(customEnabledObject)) {
        // console.log(customEnabledObject, 'customEnabledObject');
        // console.log(customAlertsArr, 'customAlertsArr -------------------');

        for (const [key, value] of Object.entries(customEnabledObject)) {
            if (value) {
                const cardFilterData = customAlertsArr.find(ele => ele.slug === key);
                // console.log(cardFilterData, 'cardFilterData jjjjj');

                if (cardFilterData) {
                    const { filter, name } = cardFilterData;

                    // Get resData for the current filter
                    let resData;
                    if (page === PAGE_TYPE.HOSPITAL) {
                        resData = await hospitalCustomAlertFilterListData(res, filter);
                    } else if (page === PAGE_TYPE.COMMUNITY_TRANSFER) {
                        resData = await communityTransferCustomAlertFilterListData(res, filter);
                    } else if (page === PAGE_TYPE.ADMISSION) {
                        resData = await admissionCustomAlertFilterListData(res, filter);
                    } else if (page === PAGE_TYPE.DECEASED) {
                        resData = await deceasedCustomAlertFilterListData(res, filter);
                    }

                    // Handle custom filter logic
                    await handleCustomFilter(cardFilterData, resData, name, objectCustom, page, res, dynamicCards, isDebug);
                }
            }
        }
    }
}

async function processDynamicAndCustomCards({
    dynamicCards = [],
    customAlertsArr = [],
    patientFilterData,
    alertReport = {},
    context = {},
    pageType,
    isDebug = false,
}) {
    const { ninetyDaysData, diffDashboardPatients, hospitalDBData, sixtyDaysData } = context;
    let dynamicCardsObj = {};
    let customTabsObj = {};
    // console.log(dynamicCards, 'dynamicCards');

    // Filter and process dynamic cards
    const dynamicDataArr = []
    for (const ele of dynamicCards) {
        let accessor = ele.accessor;
        if (accessor) {
            if (alertReport.alerts && Object.prototype.hasOwnProperty.call(alertReport.alerts, accessor) && alertReport.alerts[accessor]) {
                dynamicDataArr.push({ ...ele, slug: accessor });
            }
        }
    }

    if (dynamicDataArr.length > 0) {
        dynamicCardsObj = await dynamicCardFilterAlerts(patientFilterData, dynamicDataArr);
    }

    // Filter and process custom tab cards
    const customDataArray = customAlertsArr?.filter(ele => ele?.isCustomtab) ?? [];

    if (customDataArray.length > 0) {
        customTabsObj = await getCustomTabsCards(
            patientFilterData,
            customDataArray,
            { totalForPercentage: 0 },
            {
                ...context,
                dynamicCards,
                pageType,
                isDebug,
            }
        );
    }

    return { ...dynamicCardsObj, ...customTabsObj };
}

// Common utility to process data fields based on alert presence
const populateObjectCustomFromFields = (fields, alertReport, patientFilterData, objectCustomRef) => {
    fields.forEach(async ({ key, func, slug }) => {
        // if (
        //     alertReport?.alerts &&
        //     Object.prototype.hasOwnProperty.call(alertReport.alerts, slug) &&
        //     alertReport.alerts[slug]
        // ) {

        // }
        objectCustomRef[key] = await func(patientFilterData);
    });
};

// Main handler per page type

/**
 * Processes the raw data and compiles it into a structured object.
 * @param {Object} res - The raw data response from getAllCount.
 * @returns {Object} - An object containing processed data for various categories.
 */
async function updateFilterListData(res, page, alertReport, dynamicCards, customTabs, isDebug = false) {
    let patientFilterData = [];
    let ninetyDaysData = [];
    let hospitalDBData = [];
    let sixtyDaysData = [];
    let diffDashboardPatients = [];
    let customCombineTabData = [];
    if (page === PAGE_TYPE.HOSPITAL) {
        diffDashboardPatients = res?.diffDashboardPatients ?? [];
        customCombineTabData = res?.customCombineTabData ?? [];
        patientFilterData = res.list;
        ninetyDaysData = res.ninetyDaysData;
        hospitalDBData = res.hospitalData;
    } else {
        diffDashboardPatients = res?.data?.diffDashboardPatients ?? [];
        patientFilterData = res?.data?.list;
        ninetyDaysData = res?.data?.ninetyDaysData;
        hospitalDBData = res?.data?.hospitalData;
        sixtyDaysData = res?.data?.sixtyDaysData;
        customCombineTabData = res?.data?.customCombineTabData ?? [];
    }

    const pageFieldMap = {
        [PAGE_TYPE.ADMISSION]: [
            { key: 'hospitalData', func: admissionHospitalData, slug: 'hospitalData' },
            { key: 'floorsData', func: floorsData, slug: 'floorsData' },
            { key: 'insuranceData', func: admissionInsuranceData, slug: 'insuranceData' },
            { key: 'daysData', func: daysData, slug: 'daysData' },
            { key: 'dxData', func: dxData, slug: 'dxData' },
            { key: 'doctorData', func: doctorData, slug: 'doctorData' },
            { key: 'admission', func: admissionData, slug: 'admission' },
            { key: 'readmission', func: readmissionData, slug: 'readmission' },
            { key: 'total', func: totalData, slug: 'total' },
        ],
        [PAGE_TYPE.HOSPITAL]: [
            { key: 'hospitalizations', func: hospitalizationData, slug: 'hospitalizations' },
            { key: 'DCERData', func: dcErData, slug: 'DCERData' },
            { key: 'insuranceData', func: insuranceData, slug: 'insuranceData' },
            { key: 'returnsData', func: returnsData, slug: 'returnsData' },
            { key: 'ninetyDaysData', func: () => ninetyDaysDataList(ninetyDaysData, patientFilterData), slug: 'ninetyDaysData' },
            { key: 'floorsData', func: floorsData, slug: 'floorsData' },
            { key: 'doctorData', func: doctorData, slug: 'doctorData' },
            { key: 'daysData', func: daysData, slug: 'daysData' },
            { key: 'dxData', func: dxData, slug: 'dxData' },
            { key: 'shiftData', func: shiftData, slug: 'shiftData' },
            { key: 'nurseData', func: nurseData, slug: 'nurseData' },
            { key: 'hospitalData', func: () => hospitalDataList(hospitalDBData, patientFilterData), slug: 'hospitalData' },
            { key: 'unplannedHospitalTransfer', func: unplannedHospitalTransferData, slug: 'unplannedHospitalTransfer' },
            { key: 'plannedHospitalTransfer', func: plannedHospitalTransferData, slug: 'plannedHospitalTransfer' },
            { key: 'total', func: totalData, slug: 'total' },
        ],
        [PAGE_TYPE.DECEASED]: [
            { key: 'ninetyDaysData', func: () => ninetyDaysDataList(ninetyDaysData, patientFilterData), slug: 'ninetyDaysData' },
            { key: 'floorsData', func: floorsData, slug: 'floorsData' },
            { key: 'doctorData', func: doctorData, slug: 'doctorData' },
            { key: 'insuranceData', func: insuranceData, slug: 'insuranceData' },
            { key: 'total', func: totalData, slug: 'total' },
        ],
        [PAGE_TYPE.COMMUNITY_TRANSFER]: [
            { key: 'sixtyDaysData', func: () => ninetyDaysDataList(sixtyDaysData, patientFilterData), slug: 'sixtyDaysData' },
            { key: 'doctorData', func: doctorData, slug: 'doctorData' },
            { key: 'insuranceData', func: insuranceData, slug: 'insuranceData' },
            { key: 'returnsData', func: returnsData, slug: 'returnsData' },
            { key: 'floorsData', func: floorsData, slug: 'floorsData' },
            { key: 'SNF', func: SNFData, slug: 'SNF' },
            { key: 'AMA', func: AMAData, slug: 'AMA' },
            { key: 'safeDischarge', func: safeDischargeData, slug: 'safeDischarge' },
            { key: 'total', func: totalData, slug: 'total' },
        ],
    };

    let objectCustom = {};
    const customAlertsArr = [];
    let customEnabledObject = {}
    let dynamicCardsArr = [];
    if (dynamicCards && (dynamicCards.length > 0 || customTabs.length > 0)) {
        const dynamicDataArr = [
            ...dynamicCards,
            ...customTabs?.map(ele => ({ ...ele, isCustomtab: true })) || []
        ];
        for (const ele of dynamicDataArr) {
            let accessor = ele.accessor;
            if (accessor) {
                // Check if `ele.slug` exists as a key in `alertReport.alerts`
                dynamicCardsArr.push({ ...ele, slug: accessor });

                if (alertReport.alerts && Object.prototype.hasOwnProperty.call(alertReport.alerts, accessor) && alertReport.alerts[accessor]) {
                    customAlertsArr.push({ ...ele, slug: accessor });
                    customEnabledObject = { ...customEnabledObject, [accessor]: true };
                }
            }
        }
    }

    if (alertReport.customAlert && alertReport.customAlert.length > 0) {
        for (const ele of alertReport.customAlert) {
            if (ele.slug) {
                // Check if `ele.slug` exists as a key in `alertReport.alerts`
                if (alertReport.alerts && Object.prototype.hasOwnProperty.call(alertReport.alerts, ele.slug) && alertReport.alerts[ele.slug]) {
                    customAlertsArr.push(ele);
                    customEnabledObject = { ...customEnabledObject, [ele.slug]: true };
                }
            }
        }
    }

    if (pageFieldMap[page]) {
        await populateObjectCustomFromFields(pageFieldMap[page], alertReport, patientFilterData, objectCustom);

        const cardData = await processDynamicAndCustomCards({
            dynamicCards,
            customAlertsArr,
            patientFilterData,
            alertReport,
            context: {
                sixtyDaysData,
                hospitalDBData,
                ninetyDaysData,
                diffDashboardPatients,
                customCombineTabData,
                ...page === PAGE_TYPE.COMMUNITY_TRANSFER && { sixtyDaysData },
                ...page === PAGE_TYPE.HOSPITAL && { hospitalDBData },
            },
            pageType: page,
            isDebug,
        });

        objectCustom = { ...objectCustom, ...cardData };

        await processCustomEnabledObject(customEnabledObject, customAlertsArr, res, objectCustom, page, dynamicCardsArr, isDebug);
    }

    return objectCustom;
}

/**
 * Returns the intersection of two arrays.
 * @param {Array} array1
 * @param {Array} array2
 * @returns {Array}
 */
function matchedArray(array1, array2) {
    return _.intersection(array1, array2);
}

/**
 * Processes hospital data and returns a sorted list.
 * @param {Array} data
 * @param {Array} patients
 * @returns {Array}
 */
function hospitalDataList(data, patients) {
    const hospitalIdStrIds = patients.map((item) => item.hospitalIdStr);
    const patientIds = patients.map((item) => item.id);

    const patientData = data.filter(({ _id }) => hospitalIdStrIds.includes(_id));

    const newHospitalData = patientData.map((ele) => {
        const intersection = matchedArray(patientIds, ele.ids);

        const patientDataDidNot = patients.filter(({ id }) => intersection.includes(id));

        const wasReturnedCount = _.countBy(patientDataDidNot, "wasReturned");
        const wasAdmittedCount = _.countBy(patientDataDidNot, "wasAdmitted");

        return {
            ...ele,
            totalTransfer: intersection.length || 0,
            total: intersection.length || 0,
            totalDidNotReturn: wasReturnedCount.false || 0,
            totalDCCount: wasAdmittedCount.true || 0,
        };
    });

    return _.orderBy(newHospitalData, "totalTransfer", "desc");
}


/**
 * total hospital transfers.
 * @param {Array} data
 * @returns {Array}
 */
function totalData(data) {
    const totalTransfers = data?.length || 0;

    return [
        { _id: "total", label: "Total", total: totalTransfers }
    ];
}


/**
 * Processes re-admission data.
 * @param {Array} data
 * @returns {Array}
 */
function readmissionData(data) {
    const totalTransfers = data.filter(item => item.type === ADMISSION_TYPES.READMISSION)?.length || 0;

    return [
        { _id: ADMISSION_TYPES.READMISSION, label: "Re-Admissions", total: totalTransfers }
    ];
}

/**
 * Processes admission data.
 * @param {Array} data
 * @returns {Array}
 */
function admissionData(data) {
    const totalTransfers = data.filter(item => item.type === ADMISSION_TYPES.ADMISSION)?.length || 0;

    return [
        { _id: ADMISSION_TYPES.ADMISSION, label: "New Admissions", total: totalTransfers }
    ];
}

/**
 * Processes unplanned hospital transfers.
 * @param {Array} data
 * @returns {Array}
 */
function plannedHospitalTransferData(data) {
    const totalTransfers = data.filter(item => item.transferType === "plannedHospitalTransfer")?.length || 0;

    return [
        { _id: "plannedHospitalTransfer", label: "Planned Transfers", total: totalTransfers }
    ];
}

function safeDischargeData(data) {
    const totalTransfers = data.filter(item => item.transferType === "safeDischarge")?.length || 0;

    return [
        { _id: "safeDischarge", label: "Safe Discharge", total: totalTransfers }
    ];
}

function AMAData(data) {
    const totalTransfers = data.filter(item => item.transferType === "AMA")?.length || 0;

    return [
        { _id: "AMA", label: "AMA", total: totalTransfers }
    ];
}

function SNFData(data) {
    const totalTransfers = data.filter(item => item.transferType === "SNF")?.length || 0;

    return [
        { _id: "SNF", label: "SNF", total: totalTransfers }
    ];
}

function totalIncomingData(data) {
    let total = 0;

    if (data.length > 0) {
        const transferTypeTotal = _.countBy(data, "isOutgoing");
        let totalOutgoing = 0;
        let totalIncoming = 0;
        totalOutgoing = transferTypeTotal?.true || 0;
        totalIncoming = transferTypeTotal?.false || 0;
        total = totalIncoming;
    }

    return [
        { _id: "totalIncoming", label: "Total Incoming", total: total }
    ];
}

function totalOutGoingData(data) {
    let total = 0;
    if (data.length > 0) {
        const transferTypeTotal = _.countBy(data, "isOutgoing");
        let totalOutgoing = 0;
        let totalIncoming = 0;
        totalOutgoing = transferTypeTotal?.true || 0;
        totalIncoming = transferTypeTotal?.false || 0;
        total = totalOutgoing;
    }

    return [
        { _id: "totalOutgoing", label: "Total Outgoing", total: total }
    ];
}

/**
 * Processes unplanned hospital transfers.
 * @param {Array} data
 * @returns {Array}
 */
function unplannedHospitalTransferData(data) {
    const totalTransfers = data.filter(item => item.transferType === "unplannedHospitalTransfer")?.length || 0;

    return [
        { _id: "unplannedHospitalTransfer", label: "Unplanned Transfers", total: totalTransfers }
    ];
}

/**
 * Processes shift data.
 * @param {Array} data
 * @returns {Array}
 */
function shiftData(data) {
    const shiftCounts = _.countBy(data, "shiftName");
    return [
        { _id: "Morning", label: "Morning", total: shiftCounts.Morning || 0 },
        { _id: "Evening", label: "Evening", total: shiftCounts.Evening || 0 },
        { _id: "Night", label: "Night", total: shiftCounts.Night || 0 },
    ];
}

/**
 * General function to process items by grouping and counting.
 * @param {Array} items
 * @param {String} groupByKey
 * @param {String} labelKey
 * @returns {Array}
 */
function processItemsByKey(items, groupByKey, labelKey) {
    if (!items || items.length === 0) {
        return [];
    }

    const groupedData = _.groupBy(items, groupByKey);

    const result = Object.entries(groupedData).map(([key, groupItems]) => {
        const firstItem = groupItems[0];
        return {
            _id: key,
            label: firstItem[labelKey],
            total: groupItems.length,
        };
    });

    return _.orderBy(result, 'total', 'desc');
}

/**
 * Processes diagnosis (dx) data.
 * @param {Array} data
 * @returns {Array}
 */
function dxData(data) {
    if (!data || data.length === 0) {
        return [];
    }

    const dxDataArr = data
        .filter((item) => item.dx && item.dx.length > 0)
        .flatMap((item) => item.dx);

    if (dxDataArr.length === 0) {
        return [];
    }

    return processItemsByKey(dxDataArr, '_id', 'label');
}

/**
 * Counts occurrences of a key and formats the result.
 * @param {Array} data
 * @param {String} countKey
 * @param {Array} labels
 * @returns {Array}
 */
function processCountByKey(data, countKey, labels) {
    const countedData = _.countBy(data, countKey);

    return labels.map(({ _id, label }) => ({
        _id,
        label,
        total: countedData[_id] || 0,
    }));
}

/**
 * Processes day data.
 * @param {Array} data
 * @returns {Array}
 */
function daysData(data) {
    const daysLabels = [
        { _id: "Sun", label: "Sun" },
        { _id: "Mon", label: "Mon" },
        { _id: "Tue", label: "Tue" },
        { _id: "Wed", label: "Wed" },
        { _id: "Thu", label: "Thu" },
        { _id: "Fri", label: "Fri" },
        { _id: "Sat", label: "Sat" },
    ];

    return processCountByKey(data, "day", daysLabels);
}


/**
 * Processes admission hospital data.
 * @param {Array} data
 * @returns {Array}
 */
function admissionHospitalData(data) {
    return processDataGroupByKey(data, "hospitalId", "hospital");
}

/**
 * Processes nurse data.
 * @param {Array} data
 * @returns {Array}
 */
function nurseData(data) {
    return processDataGroupByKey(data, "nurseId", "nurse");
}

/**
 * Processes doctor data.
 * @param {Array} data
 * @returns {Array}
 */
function doctorData(data) {
    return processDataGroupByKey(data, "doctorId", "doctor");
}


/**
 * Processes Admission Insurance data.
 * @param {Array} data
 * @returns {Array}
 */
function admissionInsuranceData(data) {
    return processDataGroupByKey(data, "insuranceId", "payerSourceInsurance");
}

/**
 * Processes insurance data.
 * @param {Array} data
 * @returns {Array}
 */
function insuranceData(data) {
    return processDataGroupByKey(data, "insuranceId", "insurance");
}

/**
 * Processes floor data.
 * @param {Array} data
 * @returns {Array}
 */
function floorsData(data) {
    return processDataGroupByKey(data, "floorId", "unit");
}

/**
 * General function to group and process data.
 * @param {Array} data
 * @param {String} groupByKey
 * @param {String} processKey
 * @returns {Array}
 */
function processDataGroupByKey(data, groupByKey, processKey) {
    if (!data || data.length === 0) {
        return [];
    }

    const groupedData = _.groupBy(data, groupByKey);

    return filterListDataItems(groupedData, processKey);
}

/**
 * Processes 90 days data list.
 * @param {Array} data
 * @param {Array} patients
 * @returns {Array}
 */
function ninetyDaysDataList(data, patients) {
    const patientIds = patients.map((item) => item.id);

    return updateListTotalValue(data, patientIds);
}

/**
 * Updates total values and matched IDs in the list data.
 * @param {Array} listData
 * @param {Array} matchedIds
 * @param {String} type
 * @returns {Array}
 */
function updateListTotalValue(listData, matchedIds, type = "total") {
    if (!listData || listData.length === 0) {
        return [];
    }

    return listData.map((RItem) => {
        const intersection = matchedArray(matchedIds, RItem.ids);
        return {
            ...RItem,
            [type]: intersection.length,
        };
    });
}

/**
 * Processes returns data.
 * @param {Array} data
 * @returns {Array}
 */
function returnsData(data) {
    return processDataByKey(data, "wasReturned", {
        trueLabel: "Returned",
        falseLabel: "Didn't Return",
    });
}

/**
 * General function to process data based on a key and labels.
 * @param {Array} data
 * @param {String} key
 * @param {Object} labels
 * @returns {Array}
 */
function processDataByKey(data, key, labels) {
    const result = _.countBy(data, key);

    return [
        {
            _id: labels.trueLabel,
            label: labels.trueLabel,
            total: result.true || 0,
        },
        {
            _id: labels.falseLabel,
            label: labels.falseLabel,
            total: result.false || 0,
        },
    ];
}

/**
 * Filters and processes grouped data items.
 * @param {Object} dataGroupBy
 * @param {String} type
 * @returns {Array}
 */
function filterListDataItems(dataGroupBy, type) {
    if (!dataGroupBy) {
        return [];
    }

    const listGroup = Object.entries(dataGroupBy).map(([key, value]) => {
        const firstItem = value[0];
        if (key && firstItem && firstItem[type]) {
            return {
                _id: key,
                label: firstItem[type].label,
                total: value.length,
            };
        }
        return null;
    }).filter(Boolean);

    return _.orderBy(listGroup, "total", "desc");
}

/**
 * Processes DC/ER data.
 * @param {Array} data
 * @returns {Array}
 */
function dcErData(data) {
    return processDataByKey(data, "wasAdmitted", {
        trueLabel: "DC",
        falseLabel: "ER",
    });
}

/**
 * Processes hospitalization data.
 * @param {Array} data
 * @returns {Array}
 */
function hospitalizationData(data) {
    let newHospitalizationsTotal = 0;
    let reHospitalizationsTotal = 0;
    let newHospitalizationsIds = [];
    let reHospitalizationsIds = [];

    data.forEach((ele) => {
        if (ele.reHospitalization) {
            reHospitalizationsTotal++;
            reHospitalizationsIds.push(ele.id);
        } else {
            newHospitalizationsTotal++;
            newHospitalizationsIds.push(ele.id);
        }
    });

    return [
        {
            _id: "newHospitalizations",
            label: "New Hospitalizations",
            total: newHospitalizationsTotal,
        },
        {
            _id: "reHospitalizations",
            label: "Re-Hospitalizations",
            total: reHospitalizationsTotal,
        },
    ];
}

module.exports = {
    updateFilterListData
}