const mongoose = require("mongoose");
const Patient = mongoose.model("patient");
const moment = require("moment");
const _ = require("lodash");
const { ACCOUNT_PERCENTAGE_BY, PAGE_TYPE } = require("../types/common.type");
const { ADT_TYPES } = require("../types");
const { checkADTDuplicate, toEndFilterDate, getDaysBefore, toStartFilterDate } = require("../api/utilis/date-format");
const { defaultNineteenDaysData, defaultSixtyDaysData } = require("../types/hospital.type");
const Account = mongoose.model("account");

const filterData = async (arr, callback) => {
    const fail = Symbol();
    return (
        await Promise.all(
            arr.map(async (item) => ((await callback(item)) ? item : fail))
        )
    ).filter((i) => i !== fail);
};

const createDiffDashboardPatients = async (type = 'default') => {
    return {
        admission: [],
        hospital: [],
        communityTransfer: [],
        deceased: [],
        overall: [],
        ...(type === 'combineTab' && {
            overallIncoming: [],
            overallOutgoing: [],
        }),
    };
}

async function getSixtyDaysChartCount(patientList = [], customTab = false) {
    const sixtyDaysDataChart = await defaultSixtyDaysData();
    let patientListRes = [];
    if (patientList.length > 0) {
        patientListRes = await filterData(patientList, async (item) => {
            let query = {
                ...(await checkADTDuplicate({
                    ...item,
                    DOB: item.DOBOriginal ? item.DOBOriginal : item.DOB,
                })),
                dateOfADT: {
                    $lte: await toEndFilterDate(item.dateOfADTOriginal),
                },
                facilityId: item.facilityId,
                type: { $in: [ADT_TYPES.ADMISSIONS, ADT_TYPES.READMISSIONS] },
            };

            const matchedData = await Patient.findOne({ ...query })
                .sort({ dateOfADT: -1 })
                .exec();

            if (!_.isEmpty(matchedData)) {
                let countDay = 0;
                let isSameDate = false;
                let matchedDateOfADT = matchedData.dateOfADT;

                if (
                    moment.utc(matchedDateOfADT).isSame(item.dateOfADTOriginal) &&
                    moment(matchedData.createdAt).isBefore(item.createdAt)
                ) {
                    isSameDate = true;
                }
                if (
                    moment.utc(matchedDateOfADT).isBefore(item.dateOfADTOriginal) ||
                    isSameDate == true
                ) {
                    const date1 = moment.utc(matchedDateOfADT);
                    const date2 = moment.utc(item.dateOfADTOriginal);
                    countDay = date1.diff(date2, "days");
                    countDay = Math.abs(countDay);
                    item.countDay = countDay;
                    await updateSixtyDaysDataChart(
                        sixtyDaysDataChart,
                        countDay,
                        item._id,
                        matchedData.id,
                        customTab,
                        item?.refPatientId
                    );
                }
            } else {
                let matchedDateOfADT = item?.dateOfLatestAdmission || null;
                if (matchedDateOfADT) {
                    let countDay = 0;
                    let isSameDate = false;
                    if (moment.utc(matchedDateOfADT).isSame(item.dateOfADTOriginal)) {
                        isSameDate = true;
                    }
                    if (
                        moment.utc(matchedDateOfADT).isBefore(item.dateOfADTOriginal) ||
                        isSameDate == true
                    ) {
                        const date1 = moment.utc(matchedDateOfADT);
                        const date2 = moment.utc(item.dateOfADTOriginal);
                        countDay = date1.diff(date2, "days");
                        countDay = Math.abs(countDay);
                        item.countDay = countDay;
                        await updateSixtyDaysDataChart(
                            sixtyDaysDataChart,
                            countDay,
                            item._id,
                            matchedDateOfADT.id,
                            customTab,
                            item?.refPatientId
                        );
                    }
                }
            }
            return true;
        });
    }
    return { sixtyDaysDataChart, patientList };
};

async function getNinetyDaysChartCount(patientList = [], customTab = false) {
    const ninetyDaysDataChart = await defaultNineteenDaysData();
    let patientListRes = [];
    if (patientList.length > 0) {
        let index = 0
        patientListRes = await filterData(patientList, async (item) => {
            let query = {
                ...(await checkADTDuplicate({ ...item, DOB: item.DOBOriginal ? item.DOBOriginal : item.DOB })),
                dateOfADT: {
                    $lte: await toEndFilterDate(item.dateOfADTOriginal),
                },
                facilityId: item.facilityId,
                type: { $in: [ADT_TYPES.ADMISSIONS, ADT_TYPES.READMISSIONS] },
            };

            const matchedData = await Patient.findOne({ ...query }).sort({ dateOfADT: -1 });

            const patientADTDate = item.dateOfADTOriginal

            if (matchedData && !_.isEmpty(matchedData)) {
                let matchedDateOfADT = matchedData.dateOfADT;
                let countDay = 0;
                let isSameDate = false;
                if (
                    moment.utc(matchedDateOfADT).isSame(patientADTDate) &&
                    moment(matchedData.createdAt).isBefore(item.createdAt)
                ) {
                    isSameDate = true;
                }
                if (
                    moment.utc(matchedDateOfADT).isBefore(patientADTDate) ||
                    isSameDate == true
                ) {
                    index = index + 1
                    const date1 = moment.utc(matchedDateOfADT);
                    const date2 = moment.utc(patientADTDate);
                    countDay = date1.diff(date2, "days");
                    countDay = Math.abs(countDay);
                    item.countDay = countDay;
                    await updateNinetyDaysDataChart(
                        ninetyDaysDataChart,
                        countDay,
                        item._id,
                        matchedData.id,
                        customTab,
                        item?.refPatientId,
                    );
                }
            } else {
                let matchedDateOfADT = item?.dateOfLatestAdmission || null;
                if (matchedDateOfADT) {
                    let countDay = 0;
                    let isSameDate = false;
                    if (moment.utc(matchedDateOfADT).isSame(patientADTDate)) {
                        isSameDate = true;
                    }
                    if (moment.utc(matchedDateOfADT).isBefore(patientADTDate) || isSameDate == true) {
                        const date1 = moment.utc(matchedDateOfADT);
                        const date2 = moment.utc(patientADTDate);
                        countDay = date1.diff(date2, "days");
                        countDay = Math.abs(countDay);
                        item.countDay = countDay;
                        await updateNinetyDaysDataChart(
                            ninetyDaysDataChart,
                            countDay,
                            item._id,
                            matchedDateOfADT.id,
                            customTab,
                            item?.refPatientId
                        );
                    }
                }
            }
            return true
        });
    }

    return { ninetyDaysDataChart, patientListRes };
};

const getPercentageByTotal = async (total) => {
    const selectedAccount = await getAccountById(accountId)
    if (selectedAccount && selectedAccount?.percentageBy === ACCOUNT_PERCENTAGE_BY.BED) {
        return total?.bedCapacity
    } else {
        return total?.censusAverage
    }
}

const getPercentageByType = async (type = null) => {
    const selectedAccount = await getAccountById(accountId)
    if (selectedAccount && selectedAccount?.percentageBy === ACCOUNT_PERCENTAGE_BY.BED) {
        return type ? ACCOUNT_PERCENTAGE_BY.BED : _.capitalize(selectedAccount?.percentageBy)
    } else {
        return type ? ACCOUNT_PERCENTAGE_BY.CENSUS : _.capitalize(ACCOUNT_PERCENTAGE_BY.CENSUS);
    }
}

const isOnlyHospitalTabAccess = async (accountId) => {
    const selectedAccount = await getAccountById(accountId);
    const dashboardAccess = selectedAccount?.dashboardAccess ?? [];
    if ([PAGE_TYPE.HOSPITAL, PAGE_TYPE.COMMUNITY_TRANSFER, PAGE_TYPE.DECEASED, PAGE_TYPE.ADMISSION, PAGE_TYPE.OVERALL].length === dashboardAccess.length) {
        return false;
    } else {
        return true;
    }
    // if (selectedAccount && selectedAccount?.dashboardAccess && selectedAccount?.dashboardAccess.length === 1) {
    //     return true
    // } else {
    //     return false;
    // }
}

const onlyHospitalTabAccess = async (accountId) => {
    const selectedAccount = await getAccountById(accountId);
    const dashboardAccess = selectedAccount?.dashboardAccess ?? [];
    let percentageBy = ACCOUNT_PERCENTAGE_BY.CENSUS;
    let isOnlyHospitalTabAccess = false;
    if ([PAGE_TYPE.HOSPITAL, PAGE_TYPE.COMMUNITY_TRANSFER, PAGE_TYPE.DECEASED, PAGE_TYPE.ADMISSION, PAGE_TYPE.OVERALL].length !== dashboardAccess.length) {
        percentageBy = ACCOUNT_PERCENTAGE_BY.BED;
        isOnlyHospitalTabAccess = true;
    }
    return { percentageBy, isOnlyHospitalTabAccess }

    //if (selectedAccount && selectedAccount?.dashboardAccess && selectedAccount?.dashboardAccess.length === 1) {
}

const getAccountById = async (accountId) => {
    const account = await Account.findOne({ _id: accountId });
    if (account) {
        return account;
    }
    return null;
}

async function getReturnAndDidNotReturn(
    transFerPatient,
    facilityFilter,
    typeData = "all",
    endDate = null
) {
    if (transFerPatient.firstName && transFerPatient.lastName) {
        let arrayInType =
            typeData == "all"
                ? [
                    ADT_TYPES.ADMISSIONS,
                    ADT_TYPES.READMISSIONS,
                    ADT_TYPES.RETURN,
                    ADT_TYPES.TRANSFER,
                ]
                : [ADT_TYPES.ADMISSIONS, ADT_TYPES.READMISSIONS, ADT_TYPES.RETURN];
        let dateOfADTFilter =
            typeData == "all"
                ? { $gte: transFerPatient.dateOfADT }
                : {
                    $gte: transFerPatient.dateOfADT,
                    $lt: await toEndFilterDate(endDate),
                };
        let query = {
            _id: { $ne: transFerPatient._id },
            ...(await checkADTDuplicate(transFerPatient)),
            dateOfADT: dateOfADTFilter,
            type: {
                $in: arrayInType,
            },
            ...facilityFilter,
            createdAt: { $gte: transFerPatient.createdAt }
        };
        const returnDataArr = await Patient.aggregate([
            {
                $match: {
                    $and: [query],
                },
            },
            {
                $project: {
                    _id: { $toString: "$_id" },
                    firstName: 1,
                    lastName: 1,
                    DOB: 1,
                    dateOfADT: 1,
                    createdAt: 1,
                    type: 1,
                },
            },
            {
                $sort: {
                    dateOfADT: 1,
                },
            },
        ]);

        if (returnDataArr && returnDataArr.length > 0) {
            const isTransferData = _.find(returnDataArr, { type: "transfer" });
            if (isTransferData) {
                return await getReturnAndDidNotReturn(
                    transFerPatient,
                    facilityFilter,
                    "transfer",
                    isTransferData.dateOfADT
                );
            }
            let updatedData = returnDataArr.filter(
                (item) => item.type !== "transfer"
            );
            if (updatedData.length > 0) {
                let matchedData = updatedData[0];
                let isSameDate = false;
                if (moment.utc(matchedData.dateOfADT).isSame(transFerPatient.dateOfADT) &&
                    moment.utc(matchedData.createdAt).isAfter(transFerPatient.createdAt)
                ) {
                    isSameDate = true;
                }
                if (
                    moment.utc(matchedData.dateOfADT).isAfter(transFerPatient.dateOfADT) ||
                    isSameDate == true
                ) {
                    return true;
                }
            }
        }
        return false;
    } else {
        return false;
    }
};


async function enrichDiffDashboardPatients(diffDashboardPatients, customTabs) {
    if (!customTabs?.length) return diffDashboardPatients;

    const enriched = {};

    for (const [category, patientsVal] of Object.entries(diffDashboardPatients)) {
        let list = patientsVal || [];
        let ninetyDaysData = [];
        let sixtyDaysData = [];

        if (list.length > 0) {
            if (category === 'hospital' || category === 'deceased' || category === 'overallOutgoing') {
                const res = await getNinetyDaysChartCount(list, true);
                ninetyDaysData = res?.ninetyDaysDataChart ?? [];
                list = res?.patientListRes ?? list;
            } else if (category === 'communityTransfer') {
                const res = await getSixtyDaysChartCount(list, true);
                sixtyDaysData = res?.sixtyDaysDataChart ?? [];
                list = res?.patientListRes ?? list
            }
        }

        enriched[category] = {
            list,
            ninetyDaysData,
            sixtyDaysData,
        };
    }

    return enriched;
};

const updateChartArrayObject = async (arr, type, id, admissionPatientID, customTab = false, refPatientId = null) => {
    const foundIndex = arr.findIndex((x) => x.key == type);
    let latestObj = new Object();
    latestObj = arr[foundIndex];
    latestObj.value = latestObj.value + 1;
    latestObj.originalTotal = latestObj.originalTotal + 1;
    latestObj.ids = [...latestObj.ids, id?.toString()];
    latestObj.patientIds = [...latestObj?.patientIds ?? [], id?.toString()];
    if (customTab && refPatientId) {
        latestObj.otherDashboardIds = [...latestObj?.otherDashboardIds ?? [], refPatientId?.toString()];
    }
    latestObj.admissionIds = [...latestObj.admissionIds, admissionPatientID];
    arr[foundIndex] = latestObj;
};

const updateNinetyDaysDataChart = async (ninetyDaysDataChart, countDay, itemId, admissionPatientID, customTab = false, refPatientId = null) => {
    if (countDay >= 0 && countDay <= 7) {
        await updateChartArrayObject(ninetyDaysDataChart, "a", itemId, admissionPatientID, customTab, refPatientId);
    } else if (countDay >= 8 && countDay <= 14) {
        await updateChartArrayObject(ninetyDaysDataChart, "b", itemId, admissionPatientID, customTab, refPatientId);
    } else if (countDay >= 15 && countDay <= 30) {
        await updateChartArrayObject(ninetyDaysDataChart, "c", itemId, admissionPatientID, customTab, refPatientId);
    } else if (countDay >= 31 && countDay <= 60) {
        await updateChartArrayObject(ninetyDaysDataChart, "d", itemId, admissionPatientID, customTab, refPatientId);
    } else if (countDay >= 61 && countDay <= 90) {
        await updateChartArrayObject(ninetyDaysDataChart, "e", itemId, admissionPatientID, customTab, refPatientId);
    }
};

async function updateSixtyDaysDataChart(ninetyDaysDataChart, countDay, itemId, admissionPatientID, customTab = false, refPatientId = null) {
    if (countDay >= 0 && countDay <= 7) {
        await updateChartArrayObject(ninetyDaysDataChart, "a", itemId, admissionPatientID, customTab, refPatientId);
    } else if (countDay >= 8 && countDay <= 14) {
        await updateChartArrayObject(ninetyDaysDataChart, "b", itemId, admissionPatientID, customTab, refPatientId);
    } else if (countDay >= 15 && countDay <= 30) {
        await updateChartArrayObject(ninetyDaysDataChart, "c", itemId, admissionPatientID, customTab, refPatientId);
    } else if (countDay >= 31 && countDay <= 60) {
        await updateChartArrayObject(ninetyDaysDataChart, "d", itemId, admissionPatientID, customTab, refPatientId);
    }
};

async function applyCustomQuestionPatientFilter(query, cardFilter) {
    let patientIds = [];
    let shouldUpdateQuery = false;

    if (Array.isArray(cardFilter?.priorityData) && cardFilter.priorityData.length > 0) {
        for (const { question, patientIds: ids = [] } of cardFilter.priorityData) {
            if (question?.isCustom) {
                shouldUpdateQuery = true;
                patientIds = ids;
            }
        }

        if (shouldUpdateQuery && patientIds.length > 0) {
            const uniqueIds = [...new Set(patientIds.map(id => String(id)))];
            const ids = uniqueIds.map(id => mongoose.Types.ObjectId(id));
            query._id = { $in: ids };
        }
    }

    return query;
}

async function flagsForTransfer(tr, bucket) {
    let reHospitalization = false;
    let wasReturned = false;

    if (!tr.firstName || !tr.lastName){
        return { reHospitalization, wasReturned };
    }

    if (!bucket) {
        if (tr.hasOwnProperty('isHospitalPrior'))
            reHospitalization = !!tr.isHospitalPrior;
        return { reHospitalization, wasReturned };
    }

    const past30Days = getDaysBefore(tr.dateOfADT, 30);
    const endDate = await toEndFilterDate(tr.dateOfADT);

    let firstName = tr.firstName.trim().toLowerCase();
    let lastName = tr.lastName.trim().toLowerCase();
    const specialChars = !/[~`!#$%\^&*+=\-\[\]\\';,/{}|\\":<>\?]/;
    firstName = firstName.split(specialChars).filter(element => element).join(".{1}");
    lastName = lastName.split(specialChars).filter(element => element).join(".{1}");

    let baseList = bucket.filter(entry => (
        String(entry._id) !== String(tr._id) &&
        entry.firstName.toLowerCase().startsWith(firstName) &&
        entry.lastName.toLowerCase().startsWith(lastName) &&
        entry.DOB >= tr.DOB &&
        entry.DOB <= tr.DOB
    ));



    const hasReHospitalization = baseList.some(entry => (
        entry.type === ADT_TYPES.TRANSFER &&
        entry.dateOfADT >= past30Days &&
        entry.dateOfADT < endDate
        // entry.dateOfADT < endDateFilter
        // ...facilityFilter,
    ));

    if (hasReHospitalization) {
        reHospitalization = true;
    } else if (tr.hasOwnProperty('isHospitalPrior')) {
        reHospitalization = !!tr.isHospitalPrior;
    }

    const allTypes = [
        ADT_TYPES.ADMISSIONS,
        ADT_TYPES.READMISSIONS,
        ADT_TYPES.RETURN,
        ADT_TYPES.TRANSFER
    ]

    let transFerPatient = tr;

    let wasReturnedList = baseList.filter(entry => (
        allTypes.includes(entry.type) &&
        entry.dateOfADT >= tr.dateOfADT &&
        entry.createdAt >= tr.createdAt 
        // ...facilityFilter,
    )).sort((a, b) => new Date(a.dateOfADT) - new Date(b.dateOfADT));;

    const isTransferData = _.find(wasReturnedList, { type: "transfer" });

    if (isTransferData) {
        const typesWithoutTransfer = [
            ADT_TYPES.ADMISSIONS,
            ADT_TYPES.READMISSIONS,
            ADT_TYPES.RETURN
        ]
        const transferEndDate = await toEndFilterDate(isTransferData.dateOfADT)
        wasReturnedList = wasReturnedList.filter(entry => (
            typesWithoutTransfer.includes(entry.type) &&
            entry.dateOfADT < transferEndDate
        )).sort((a, b) => new Date(a.dateOfADT) - new Date(b.dateOfADT));
    }

    let updatedData = wasReturnedList.filter(
        (item) => item.type !== "transfer"
    );

    if (updatedData.length > 0) {
        let matchedData = updatedData[0];
        let isSameDate = false;
        if (moment.utc(matchedData.dateOfADT).isSame(transFerPatient.dateOfADT) &&
            moment.utc(matchedData.createdAt).isAfter(transFerPatient.createdAt)
        ) {
            isSameDate = true;
        }
        if (
            moment.utc(matchedData.dateOfADT).isAfter(transFerPatient.dateOfADT) ||
            isSameDate == true
        ) {
            wasReturned = true;
        }
    }

    return { reHospitalization, wasReturned };
}


module.exports = {
    applyCustomQuestionPatientFilter,
    getReturnAndDidNotReturn,
    enrichDiffDashboardPatients,
    isOnlyHospitalTabAccess,
    onlyHospitalTabAccess,
    getPercentageByType,
    getPercentageByTotal,
    updateNinetyDaysDataChart,
    updateSixtyDaysDataChart,
    getNinetyDaysChartCount,
    getSixtyDaysChartCount,
    createDiffDashboardPatients,
    flagsForTransfer
};
