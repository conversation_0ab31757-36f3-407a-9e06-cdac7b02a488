import { filterBodyDate } from '../../utilis/date-formats';
import api from './api';

const API_PREFIX = "api/quick-glace";

const getQuickGlaceData = async (params) => {
    const response = await api.get(`${API_PREFIX}`, { params });
    return response;
};

const quickGlaceFilterData = async (body) => {
    body = filterBodyDate(body)
    const response = await api.post(`${API_PREFIX}/glace-filter-data`, body);
    return response;
};

const saveQuickGlaceData = async (body) => {
    const response = await api.post(`${API_PREFIX}`, body);
    return response;
};

export {
    getQuickGlaceData,
    saveQuickGlaceData,
    quickGlaceFilterData
};
