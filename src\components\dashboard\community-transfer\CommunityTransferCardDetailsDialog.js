/* eslint-disable no-unused-vars */
/* eslint-disable eqeqeq */
/* eslint-disable react-hooks/exhaustive-deps */
import React, { useCallback, useEffect, useMemo, useRef, useState } from "react";
import { Grid, Stack } from "@mui/material";
import { useDispatch, useSelector } from "react-redux";
import { closeDetailsDialog } from "../../../store/reducers/community-transfer.slice";
import { cardPatientAndChartData } from "../../../services/community-transfer.service";
import _ from "lodash";
import CardDetailChart from "../../shared/chart-dialog-common/chart-tab/CardDetailChart";
import {
	chartFilterPermission,
	chartGroupBy,
	chartsData,
	dayDifferent,
	getCommunicationFieldNames,
	isUpdateDefaultFilter,
} from "../../../utilis/charts-common";
import { CHART_FILTER_DAY_OF, CUSTOM_TAB_TYPE, DASHBOARD_FILTER_TYPE, FILTER_TYPES } from "../../../types/common.type";
import { CHART_POINT_TYPE, CHART_TAB_BUTTON } from "../../../types/chart-dialog.type";
import ChartDialogLeftSidebar from "../../shared/chart-dialog-common/ChartDialogLeftSidebar";
import ChartDetailsTab from "../../shared/chart-dialog-common/ChartDetailsTab";
import ChartTabLoader from "../../shared/chart-dialog-common/ChartTabLoader";
import ChartDialogContainer from "../../shared/chart-dialog-common/ChartDialogContainer";
import ChartTableList from "../../shared/chart-dialog-common/chart-table/ChartTableList";
import { CO_TRANSFER_CARDS_TYPE } from "../../../types/community-transfer.type";
import { DEFAULT_CHART_DATA, DEFAULT_CHART_FILTER, communityTransferOptions } from "../../../data/common.data";
import ChartBuildingList from "../../shared/chart-dialog-common/chart-building/ChartBuildingList";
import { getAccountQuestions } from "../../../services/api/user.api";
import DropDown from "../../ui/drop-down/DropDown";
import { getChartFacilityPercentageBy, patientDataOrderBy, processCustomChartFilters } from "../../../utilis/common";
import { PAGE_TYPE } from "../../../types/pages.type";
import ChartDetailsSearchBar from "../../shared/chart-dialog-common/ChartDetailsSearchBar";
import NoteContainer from "../../shared/NoteContainer";
import CommunityHeaderFilterList from "./CommunityHeaderFilterList";
import { filterPatientDataByLevel } from "../../../utilis/custom-tab-filter";


export default function CommunityTransferCardDetailsDialog({ handleOnClickReport, handleGenerateExcelReport }) {
	const tableElementRef = useRef();
	const buildingElementRef = useRef();
	const [selectedItem, setSelectedItem] = useState([]);
	const [loading, setLoading] = useState(false);
	const [buttonFilterType, setButtonFilterType] = useState(CHART_FILTER_DAY_OF.DAY);
	const [censusAverage, setCensusAverage] = useState([]);
	const [censusByPeriod, setCensusByPeriod] = useState([]);
	const [queryFilters, setQueryFilters] = useState(DEFAULT_CHART_FILTER);
	const [defaultPatientData, setDefaultPatientData] = useState([]);
	const [filterData, setFilterData] = useState([]);
	const [chartData, setChartData] = useState(DEFAULT_CHART_DATA);
	const dispatch = useDispatch();
	const [selectedFilter, setSelectedFilter] = useState(FILTER_TYPES.DAILY);
	const {
		detailsDialog: { isOpen, title, data, filters, dbData },
		lockedTotal, lockedTotalBy
	} = useSelector((state) => state.communityTransfer);
	const [isShowPercentage, setIsShowPercentage] = useState(false);
	const [censusByFacility, setCensusByFacility] = useState([]);
	const { auth } = useSelector(({ auth }) => ({ auth }));
	const [defaultQuestions, setDefaultQuestions] = React.useState([]);
	const [questions, setQuestions] = React.useState([]);
	const activeFacilities = useSelector((state) => state.activeFacilities.facilities);
	const { rangesSet } = useSelector((state) => state.comparison);
	const { percentageBy } = useSelector((state) => state.common);

	const [selectedFacility, setSelectedFacility] = useState(activeFacilities);
	const [isUpdateRefreshMemo, setIsUpdateRefreshMemo] = useState(false);
	// COMPARISON STATES ---------->
	const {
		detailsDialog: { data: dataComparison, filters: filtersComparison },
	} = useSelector((state) => state.communityTransferComparison);
	const [chartDataComparison, setChartDataComparison] = useState(DEFAULT_CHART_DATA);
	const [censusAverageComparison, setCensusAverageComparison] = useState([]);
	const [censusByPeriodComparison, setCensusByPeriodComparison] = useState([]);
	const [censusByFacilityComparison, setCensusByFacilityComparison] = useState([]);
	const [queryFiltersComparison, setQueryFiltersComparison] = useState(DEFAULT_CHART_FILTER);
	const [defaultPatientDataComparison, setDefaultPatientDataComparison] = useState([]);
	const [selectedTab, setSelectedTab] = useState(filters?.defaultTab ? filters?.defaultTab : CHART_TAB_BUTTON.TABLE);
	const [filterDataComparison, setFilterDataComparison] = useState([]);
	const [selectedItemComparison, setSelectedItemComparison] = useState([]);
	const [tableFilterType, setTableFilterType] = React.useState("all");
	const [orderBy, setOrderBy] = useState('dateOfADT'); // Default sort column
	const [order, setOrder] = useState('asc'); // Default sort direction

	const isTotalCard = useMemo(() => {
		return filters?.type === CO_TRANSFER_CARDS_TYPE.TOTAL ||
		filters?.type === CO_TRANSFER_CARDS_TYPE.AMA ||
		filters?.type === CO_TRANSFER_CARDS_TYPE.SAFE_DISCHARGE ||
		filters?.type === CO_TRANSFER_CARDS_TYPE.SNF;
	}, [filters]);

	const getQuestions = async (activeFacilitiesData) => {
		if (filters?.question?.customTab?.type === CUSTOM_TAB_TYPE.combineTab) {
			setQuestions([]);
			return;
		}
		setQuestions([]);
		const res = await getAccountQuestions({
			facilityid: activeFacilitiesData,
			accountId: auth.accountId,
			pageType: filters?.question?.isAnotherDashboard ? filters?.question?.dashboardType : DASHBOARD_FILTER_TYPE.COMMUNITY_TRANSFER,
		})
		if (res && res.length > 0) {
			setDefaultQuestions(res);
		}
	};

	React.useEffect(() => {
		getQuestions(activeFacilities);
		// eslint-disable-next-line
	}, [activeFacilities]);

	React.useEffect(() => {
		const handleUpdate = async () => {
			if (tableFilterType === "all") {
				setQuestions([]);
			} else {
				setQuestions(_.filter(defaultQuestions, question => question.forTransferType === tableFilterType));
			}
			const filterPayload = {
				...filters?.filter,
				tableFilterType,
			};

			// Trigger both filterOptions sequentially (or in parallel if they don't depend on each other)
			await filterOptions(selectedItem, chartData?.mainData, buttonFilterType, filterPayload, undefined, undefined, false);
			await filterOptions(selectedItemComparison, chartDataComparison?.mainData, buttonFilterType, { ...filtersComparison.filter, tableFilterType }, undefined, undefined, true);
		}
		handleUpdate();
	}, [tableFilterType, defaultQuestions]);

	// * added comparison
	const getPatientChartsData = async () => {
		try {
			setLoading(true);
			if (
				filters.type === CO_TRANSFER_CARDS_TYPE.TOTAL ||
				filters.type === CO_TRANSFER_CARDS_TYPE.AMA ||
				filters.type === CO_TRANSFER_CARDS_TYPE.SAFE_DISCHARGE ||
				filters.type === CO_TRANSFER_CARDS_TYPE.SNF
			) {
				setIsShowPercentage(true);
			}
			let [response, responseComparison] = await Promise.all([
				cardPatientAndChartData({ ...filters }, "main"),
				filtersComparison && cardPatientAndChartData({ ...filtersComparison }, "comparison"),
			]);
			let latestButtonFilterType = buttonFilterType;
			const isUpdateFilter = isUpdateDefaultFilter(filters.filter);
			if (isUpdateFilter) {
				setSelectedFilter(FILTER_TYPES.WEEKLY);
				latestButtonFilterType = CHART_FILTER_DAY_OF.WEEK;
				setButtonFilterType(CHART_FILTER_DAY_OF.WEEK);
			}

			const resData = await filterPatientDataByLevel({ response, filters, pageType: PAGE_TYPE.COMMUNITY_TRANSFER });
			if (resData) {
				response.data = resData?.data;
				response.ninetyDaysData = resData?.ninetyDaysData || [];
				response.sixtyDaysData = resData?.sixtyDaysData || [];
			}
			const responseData = response?.data?.map((ele) => { return { ...ele, facilityName: ele?.facility?.name } }) ?? []

			if (response && responseData) {
				setCensusAverage(response.censusAverage);
				setCensusByPeriod(response?.censusByPeriod || []);
				const censusByData = await getChartFacilityPercentageBy(response)
				setCensusByFacility(censusByData);
				setDefaultPatientData(responseData);
				const chartDataArr = await chartsData(responseData, filters.filter);
				const latestChartData = await chartGroupBy(
					chartDataArr,
					latestButtonFilterType,
					response.censusAverage,
					response?.censusByPeriod,
					filters.filter
				);
				setChartData({
					mainData: responseData,
					filterPatients: responseData,
					filterData: latestChartData,
				});
			}
			await setFilterData(data);
			if (filters.selectedFilterData && filters.selectedFilterData.length > 0) {
				setSelectedItem([...filters.selectedFilterData]);
				filterOptions(filters.selectedFilterData, responseData, latestButtonFilterType, {
					...filters.filter,
					filterData: data,
				});
			} else {
				const selectedIds = _.map(data, "_id") || [];
				setSelectedItem(selectedIds);
				setIsUpdateRefreshMemo((prevState) => !prevState);
			}
			if (responseComparison && responseComparison.data) {
				const resDataComparison = await filterPatientDataByLevel({ responseComparison, filters, pageType: PAGE_TYPE.COMMUNITY_TRANSFER, forComparison: true });
				if (resDataComparison) {
					responseComparison.data = resDataComparison?.data;
					responseComparison.ninetyDaysData = resDataComparison?.ninetyDaysData || [];
					responseComparison.sixtyDaysData = resDataComparison?.sixtyDaysData || [];
				}
				setCensusAverageComparison(responseComparison.censusAverage);
				setCensusByPeriodComparison(responseComparison?.censusByPeriod || []);
				if (responseComparison.censusByFacility) {
					setCensusByFacilityComparison(responseComparison?.censusByFacility || []);
				}
				const responseDataComparison = responseComparison.data;
				setDefaultPatientDataComparison(responseDataComparison);
				const chartDataArr = await chartsData(responseDataComparison, filtersComparison?.filter);
				const latestChartData = await chartGroupBy(
					chartDataArr,
					latestButtonFilterType,
					responseComparison.censusAverage,
					responseComparison?.censusByPeriod,
					filtersComparison?.filter
				);
				setChartDataComparison({
					mainData: responseDataComparison,
					filterPatients: responseDataComparison,
					filterData: latestChartData,
				});
			}
			setFilterDataComparison(dataComparison);
			if (filtersComparison?.selectedFilterData && filtersComparison?.selectedFilterData.length > 0) {
				setSelectedItemComparison([...filtersComparison?.selectedFilterData]);
				filterOptions(
					filtersComparison?.selectedFilterData,
					responseComparison.data,
					latestButtonFilterType,
					{
						...filtersComparison?.filter,
						filterData: dataComparison,
					},
					undefined,
					undefined,
					true
				);
			} else {
				const selectedIds = _.map(dataComparison, "_id") || [];
				setSelectedItemComparison(selectedIds);
			}
		} catch (e) {
			console.log(e);
		} finally {
			setLoading(false);
		}
	};

	// * added comparison
	const reloadChartData = useCallback(
		async (startDate, endDate, startDateComparison, endDateComparison) => {
			try {
				setLoading(true);
				let filterObj = { ...filters, filter: { startDate, endDate } };
				let filterObjComparison = { ...filtersComparison, filter: { startDateComparison, endDateComparison } };

				const isUpdateFilter = isUpdateDefaultFilter(filterObj.filter);
				let latestButtonFilterType = buttonFilterType;
				if (isUpdateFilter) {
					setSelectedFilter(FILTER_TYPES.WEEKLY);
					latestButtonFilterType = CHART_FILTER_DAY_OF.WEEK;
					setButtonFilterType(CHART_FILTER_DAY_OF.WEEK);
				}
				let [response, responseComparison] = await Promise.all([
					cardPatientAndChartData({ ...filterObj }, "reload"),
					cardPatientAndChartData({ ...filterObjComparison }, "reloadCompare"),
				]);
				const resData = await filterPatientDataByLevel({ response, filters, pageType: PAGE_TYPE.COMMUNITY_TRANSFER });
				if (resData) {
					response.data = resData?.data;
					response.ninetyDaysData = resData?.ninetyDaysData || [];
					response.sixtyDaysData = resData?.sixtyDaysData || [];
				}
				const responseData = response?.data?.map((ele) => { return { ...ele, facilityName: ele?.facility?.name } }) ?? []
				if (response && responseData && responseData.length > 0) {
					setCensusAverage(response.censusAverage);
					setCensusByPeriod(response?.censusByPeriod || []);
					const censusByData = await getChartFacilityPercentageBy(response)
					setCensusByFacility(censusByData);
					setDefaultPatientData(responseData);
					const chartDataArr = await chartsData(responseData, filterObj.filter);
					const latestChartData = await chartGroupBy(
						chartDataArr,
						latestButtonFilterType,
						response.censusAverage,
						response?.censusByPeriod,
						filterObj.filter
					);
					setChartData({
						mainData: responseData,
						filterPatients: responseData,
						filterData: latestChartData,
					});

					let originalChartData = [];
					if (response.sixtyDaysData && response.sixtyDaysData.length > 0 && filters.type === "sixtyDaysData") {
						originalChartData = response.sixtyDaysData;
					}
					if (filterData.length > 0 && originalChartData.length > 0) {
						const latestFilterData = filterData
							.map((eleFilter) => {
								const selectedEle = _.find(originalChartData, { _id: eleFilter._id });
								if (selectedEle) {
									return {
										...eleFilter,
										admissionIds: selectedEle.admissionIds,
										ids: selectedEle.ids,
									};
								} else {
									return null;
								}
							})
							.filter((item) => item);
						setFilterData(latestFilterData);
					}
					if (selectedItem.length > 0) {
						await filterOptions(
							selectedItem,
							responseData,
							latestButtonFilterType,
							filterObj.filter,
							"reload",
							response
						);
					}
				}

				// * comparison block
				if (responseComparison && responseComparison.data && responseComparison.data.length > 0) {
					const resDataComparison = await filterPatientDataByLevel({ responseComparison, filters, pageType: PAGE_TYPE.COMMUNITY_TRANSFER, forComparison: true });
					if (resDataComparison) {
						responseComparison.data = resDataComparison?.data;
						responseComparison.ninetyDaysData = resDataComparison?.ninetyDaysData || [];
						responseComparison.sixtyDaysData = resDataComparison?.sixtyDaysData || [];
					}
					const responseDataComparison = responseComparison.data;
					setCensusAverageComparison(responseComparison.censusAverage);
					setCensusByPeriodComparison(responseComparison.censusByPeriod || []);
					if (responseComparison.censusByFacility) {
						setCensusByFacilityComparison(responseComparison.censusByFacility || []);
					}
					setDefaultPatientDataComparison(responseDataComparison);
					const chartDataArrComparison = await chartsData(responseDataComparison, filterObjComparison?.filter);
					const latestChartDataComparison = await chartGroupBy(
						chartDataArrComparison,
						latestButtonFilterType,
						responseComparison.censusAverage,
						responseComparison.censusByPeriod,
						filterObjComparison?.filter
					);
					setChartDataComparison({
						mainData: responseDataComparison,
						filterPatients: responseDataComparison,
						filterData: latestChartDataComparison,
					});

					let originalChartDataComparison = [];
					if (
						responseComparison.sixtyDaysData &&
						responseComparison.sixtyDaysData.length > 0 &&
						filtersComparison.type === "sixtyDaysData"
					) {
						originalChartDataComparison = responseComparison.sixtyDaysData;
					}
					if (filterDataComparison?.length > 0 && originalChartDataComparison.length > 0) {
						const latestFilterDataComparison = filterDataComparison
							.map((eleFilter) => {
								const selectedEle = _.find(originalChartDataComparison, { _id: eleFilter._id });
								if (selectedEle) {
									return {
										...eleFilter,
										admissionIds: selectedEle.admissionIds,
										ids: selectedEle.ids,
									};
								} else {
									return null;
								}
							})
							.filter((item) => item);
						setFilterDataComparison(latestFilterDataComparison);
					}
					if (selectedItemComparison.length > 0) {
						await filterOptions(
							selectedItemComparison,
							responseDataComparison,
							latestButtonFilterType,
							filterObjComparison?.filter,
							"reload",
							responseComparison
						);
					}
				}
			} catch (e) {
				console.log(e);
			} finally {
				setLoading(false);
			}
		},
		[buttonFilterType, selectedItem, filters, selectedItemComparison, filtersComparison]
	);

	const updateChartArrData = useCallback(
		async (dataArr, filterType = null, type = null, filterObj, forComparison) => {
			let filterBy = filterType;
			if (!filterType) {
				filterBy = buttonFilterType;
			}
			let chartDataArr = [];
			if (type == "filter" && filterObj) {
				chartDataArr = await chartsData(dataArr, filterObj);
			} else {
				chartDataArr = await chartsData(dataArr, queryFilters);
			}
			let filtersLatest = filterObj ? filterObj : forComparison ? queryFiltersComparison : queryFilters;
			const latestChartData = await chartGroupBy(
				chartDataArr,
				filterBy,
				!forComparison ? censusAverage : censusAverageComparison,
				!forComparison ? censusByPeriod : censusByPeriodComparison,
				filtersLatest
			);
			if (type == "filter") {
				forComparison
					? setChartDataComparison((prevState) => ({
						...prevState,
						filterData: latestChartData,
						filterPatients: dataArr,
					}))
					: setChartData((prevState) => ({
						...prevState,
						filterData: latestChartData,
						filterPatients: dataArr,
					}));
			} else {
				forComparison
					? setChartDataComparison((prevState) => ({
						...prevState,
						filterData: latestChartData,
					}))
					: setChartData((prevState) => ({
						...prevState,
						filterData: latestChartData,
					}));
			}
		},
		[buttonFilterType, censusAverage, censusByPeriod, queryFilters]
	);

	// * added comparison
	const reset = () => {
		setSelectedItem([]);
		setDefaultPatientData([]);
		setFilterData([]);

		setSelectedItemComparison([]);
		setFilterDataComparison([]);
		setDefaultPatientDataComparison([]);
	};

	// * added comparison
	const filterOptions = async (
		newChecked,
		chartMainDataArr,
		latestButtonFilterType = null,
		filterObj = null,
		filterType = "initial",
		originalData,
		forComparison
	) => {
		let chartMainData = chartMainDataArr;
		const tableFilterType = filterObj?.tableFilterType;

		if (tableFilterType === "safeDischarge" || tableFilterType === "SNF" || tableFilterType === "AMA" || tableFilterType === "deceased") {
			chartMainData = _.filter(chartMainData, ({ transferType }) => _.includes([tableFilterType], transferType));
		}

		if (activeFacilities.length > 1) {
			const filterPatientsByFacility = _.filter(chartMainData, ({ facilityId }) => _.every([_.includes(selectedFacility, facilityId)]));
			chartMainData = filterPatientsByFacility
		}
		if (filters.type === CO_TRANSFER_CARDS_TYPE.SIXTY_DAYS_DATA) {
			let ninetyDaysDataIds = [];
			let dataOriginal = filterObj?.filterData
				? filterObj.filterData
				: forComparison
					? filterDataComparison
					: filterData;
			if (filterType === "reload") {
				dataOriginal = originalData.sixtyDaysData;
			}
			let ninetyDaysDataFilter = _.filter(dataOriginal, ({ _id }) => _.every([_.includes(newChecked, _id)]));
			if (ninetyDaysDataFilter && ninetyDaysDataFilter.length > 0) {
				ninetyDaysDataFilter.map((item) => (ninetyDaysDataIds = [...ninetyDaysDataIds, ...item.ids]));
			}
			let newChartFilters = _.filter(chartMainData, ({ _id }) => _.every([_.includes(ninetyDaysDataIds, _id)]));
			await updateChartArrData(newChartFilters, latestButtonFilterType, "filter", filterObj, forComparison);
		} else if (filters.type === CO_TRANSFER_CARDS_TYPE.RETURNS_DATA) {
			let filter = Object();
			if (newChecked.length === 1) {
				if (newChecked[0] === "Returned") {
					filter.wasReturned = true;
				}
				if (newChecked[0] === "Didn't Return") {
					filter.wasReturned = false;
				}
				let newChartFilters = _.filter(chartMainData, filter);
				await updateChartArrData(newChartFilters, latestButtonFilterType, "filter", filterObj, forComparison);
			} else {
				await updateChartArrData(chartMainData, latestButtonFilterType, "filter", filterObj, forComparison);
			}
		} else {
			const { question } = filters;
			if (question?.isCustom) {
				if (filters?.isTotalCard || filters?.question?.customTab?.type === CUSTOM_TAB_TYPE.combineTab) {
					await updateChartArrData(chartMainData, latestButtonFilterType, "filter", filterObj, forComparison);
				} else {
					const newChartFilters = await processCustomChartFilters(
						chartMainData,
						filters,
						newChecked
					);
					await updateChartArrData(newChartFilters, latestButtonFilterType, "filter", filterObj, forComparison);
				}
			} else {
				let newChartFilters = filters?.isTotalCard ? chartMainData : _.filter(chartMainData, ({ filterId }) => {
					return _.every([_.includes(newChecked, filterId)]);
				});
				await updateChartArrData(newChartFilters, latestButtonFilterType, "filter", filterObj, forComparison);
			}
		}
	};

	useMemo(() => {
		filterOptions(selectedItem,
			chartData?.mainData,
			buttonFilterType,
			{
				...filters?.filter,
			},
			undefined,
			undefined,
			false
		);
		// eslint-disable-next-line react-hooks/exhaustive-deps
	}, [selectedFacility, isUpdateRefreshMemo]);
	// * added comparison


	// * added comparison
	const handleClickFilter = useCallback(
		async (type, filterType = null) => {
			setSelectedFilter(type);
			setButtonFilterType(filterType);
			await Promise.all([
				updateChartArrData(chartData.filterPatients, filterType),
				updateChartArrData(chartDataComparison.filterPatients, filterType, undefined, undefined, true),
			]);
			if (selectedItem.length > 0) {
				await Promise.all([
					filterOptions(selectedItem, chartData.mainData, filterType),
					filterOptions(selectedItem, chartDataComparison.mainData, filterType, undefined, undefined, undefined, true),
				]);
			}
		},
		[data, chartData, chartDataComparison, selectedItem, filterData, filterDataComparison]
	);

	// * added comparison
	const handleToggle = useCallback(
		async (value) => {
			const currentIndex = selectedItem.indexOf(value);
			const newChecked = [...selectedItem];

			if (currentIndex === -1) {
				newChecked.push(value);
			} else {
				newChecked.splice(currentIndex, 1);
			}
			if (newChecked.length > 0) {
				await Promise.all([
					filterOptions(newChecked, chartData.mainData, buttonFilterType),
					filterOptions(
						newChecked,
						chartDataComparison.mainData,
						buttonFilterType,
						undefined,
						undefined,
						undefined,
						true
					),
				]);
			} else {
				setChartData((prevState) => ({
					...prevState,
					filterData: [],
					filterPatients: [],
				}));
				setChartDataComparison((prevState) => ({
					...prevState,
					filterData: [],
					filterPatients: [],
				}));
			}
			setSelectedItem(newChecked);
		},
		[selectedItem, chartData, chartDataComparison, buttonFilterType, defaultPatientData]
	);

	const handleChartLabelType = useCallback(
		async (type) => {
			setQueryFilters((prevState) => ({
				...prevState,
				chartPointType: type,
			}));
			await updateChartArrData(chartData.filterPatients, null);
		},
		[setQueryFilters, chartData]
	);

	const getFieldNames = (row) => {
		return getCommunicationFieldNames(row, filters?.relation, filters?.type, data, filters);
	};

	const handleClose = useCallback(() => dispatch(closeDetailsDialog()), [dispatch]);

	const handleTabButton = useCallback((type) => setSelectedTab(type), []);

	const handleToggleAll = useCallback(
		async (value) => {
			if (value == "all") {
				const ids = filterData?.map((a) => a._id);
				await filterOptions(ids, chartData.mainData, buttonFilterType);
				setSelectedItem([...ids]);
			} else {
				setChartData((prevState) => ({
					...prevState,
					filterData: [],
					filterPatients: [],
				}));
				setSelectedItem([]);
			}
		},
		[filterData, chartData, buttonFilterType]
	);

	const onClickReport = useCallback(
		(title, automaticallyReport) => {
			handleOnClickReport({
				selectedTab,
				filters,
				chartData: {
					...chartData,
					filterPatients: patientDataOrderBy(chartData?.filterPatients, order, orderBy)
				},
				selectedFilter,
				tableElementRef,
				buildingElementRef,
				title,
				queryFilters,
				selectedItem,
				filterData,
				automaticallyReport,
				buttonFilterType,
				tableFilterType,
				orderBy,
				order,
				activeFacilitiesCount: activeFacilities?.length
			});
		},
		[selectedTab, filters, chartData, tableElementRef, buildingElementRef, queryFilters, selectedItem, filterData, tableFilterType, orderBy, order, activeFacilities]
	);

	useEffect(() => {
		reset();
		if (filters && filters.filter) {
			const daysFilter = chartFilterPermission(filters.filter);
			const daysDiff = dayDifferent(filters.filter);
			setQueryFilters({
				filterButtons: daysFilter,
				days: daysDiff,
				startDate: filters.filter.startDate,
				endDate: filters.filter.endDate,
				chartPointType: CHART_POINT_TYPE.TOTAL,
			});
			getPatientChartsData();
		}
		if (filtersComparison && filtersComparison.filter && rangesSet) {
			const daysFilter = chartFilterPermission(filtersComparison.filter);
			const daysDiff = dayDifferent(filtersComparison.filter);
			setQueryFiltersComparison({
				filterButtons: daysFilter,
				days: daysDiff,
				startDate: filtersComparison.filter.startDate,
				endDate: filtersComparison.filter.endDate,
				chartPointType: CHART_POINT_TYPE.TOTAL,
			});
			getPatientChartsData();
		}
	}, [filters, filtersComparison, rangesSet]);

	const handleExcelReport = useCallback((selectedColumns = []) => {
		handleGenerateExcelReport({
			data: chartData?.filterPatients,
			filters,
			pageType: DASHBOARD_FILTER_TYPE.COMMUNITY_TRANSFER,
			getFieldNames,
			title,
			censusAverage,
			bedCapacity: dbData?.bedCapacity || 0,
			questions,
			selectedColumns,
			orderBy,
			order,
			activeFacilitiesCount: activeFacilities?.length
		});
	}, [chartData?.filterPatients, filters, getFieldNames, handleGenerateExcelReport, title, censusAverage, dbData, questions, orderBy, order, activeFacilities]);

	const dropDownComponent = () => <DropDown
		options={communityTransferOptions}
		selected={tableFilterType}
		setOption={setTableFilterType}
		width={`275`}
		style={{ zIndex: 5 }}
		size={`medium`}
		className={"table-filter-dropdown"}
	/>

	// below code use for search filter in table
	const [searchTerm, setSearchTerm] = useState('');
	const [totalHighlightedCount, setTotalHighlightedCount] = useState(0);
	const [debounceHighlightedCount, setDebounceHighlightedCount] = useState(0);
	const [debouncedSearchTerm, setDebouncedSearchTerm] = useState(searchTerm);
	const [highlightedCountDefaultValue, setHighlightedCountDefaultValue] = useState(0);
	const [selectedHighlightedColumns, setSelectedHighlightedColumns] = useState([]);

	// useEffect(() => {
	// 	if (questions && questions.length > 0) {
	// 		setSelectedHighlightedColumns([...questions?.map((ele) => ele?.question?.accessor), ...tableDefaultQuestions.map((ele) => ele?.question?.accessor), "selectAll"]);
	// 	}
	// }, [questions]);

	const [filterType, setFilterType] = useState({ type: '', filter: '', operation: '' });

	useEffect(() => {
		const handlerCount = _.debounce(() => setDebounceHighlightedCount(totalHighlightedCount), 200);
		handlerCount();
		return () => handlerCount.cancel();
	}, [totalHighlightedCount]);

	useEffect(() => {
		const handler = _.debounce(() => {
			setDebouncedSearchTerm(searchTerm);
			setFilterType({ type: typeof searchTerm, filter: searchTerm, operation: '' });
		}, 300);
		handler();
		return () => handler.cancel();
	}, [searchTerm]);

	const handleSearch = (event) => {
		const value = event.target.value;
		setSearchTerm(isNaN(value) || !value ? value : Number(value));
	};

	const handleHighlightedCount = useCallback((count) => {
		setTotalHighlightedCount(searchTerm ? count : 0);
	}, [searchTerm]);

	// end of below code use for search filter in table

	const headerFilterOptions = () => {
		if (isTotalCard) {
			return null;
		}
		return <Grid
			direction={"row"}
			justifyContent={"flex-start"}
			display={"flex"}
			justifyItems={"flex-start"}
			alignItems="center"
		>
			<Grid item>
				<CommunityHeaderFilterList
					filterListData={filters?.filterListData}
					cardFilter={filters?.cardFilter}
					transferType={filters?.transferType}
					openFrom="detailView"
					percentageBy={percentageBy}
					lockedTotal={lockedTotal}
					lockedTotalBy={lockedTotalBy}
				/>
			</Grid>
		</Grid>
	}

	return (
		<ChartDialogContainer handleClose={handleClose} isOpen={isOpen}>
			<ChartDialogLeftSidebar
				loading={loading}
				filterData={filterData}
				selectedItem={selectedItem}
				handleToggle={handleToggle}
				title={title}
				handleToggleAll={handleToggleAll}
				selectedTab={selectedTab}
				isFilterList={filters?.question?.customTab?.type !== CUSTOM_TAB_TYPE.combineTab}
			/>
			<Grid item xs={10.7} style={{ padding: "30px" }}>
				<ChartDetailsTab
					selectedTab={selectedTab}
					handleTabButton={handleTabButton}
					queryFilters={queryFilters}
					handleOnClickReport={onClickReport}
					loading={loading}
					handleExcelReport={handleExcelReport}
					{...(selectedTab === CHART_TAB_BUTTON.TABLE && { tableFilterTypeOptions: dropDownComponent() })}
					setSelectedFacility={setSelectedFacility}
					selectedFacility={selectedFacility}
					questions={questions}
					tableFilterType={filters?.question?.customTab?.type === CUSTOM_TAB_TYPE.combineTab ? "all" : tableFilterType}
					pageType={PAGE_TYPE.COMMUNITY_TRANSFER}
					filterHeaderContainer={() => headerFilterOptions()}
				/>
				{selectedTab === CHART_TAB_BUTTON.TABLE &&
					<ChartDetailsSearchBar
						searchTerm={searchTerm}
						handleSearch={handleSearch}
						setSearchTerm={setSearchTerm}
						highlightedCount={debounceHighlightedCount}
						filterType={filterType}
						setFilterType={setFilterType}
						selectedHighlightedColumns={selectedHighlightedColumns}
						setSelectedHighlightedColumns={setSelectedHighlightedColumns}
						selectedQuestions={questions}
						isSelectColumn={filters?.question?.customTab?.type !== CUSTOM_TAB_TYPE.combineTab}
					/>
				}
				{!loading && (
					<NoteContainer page={PAGE_TYPE.COMMUNITY_TRANSFER} openFrom="dialog">
						{selectedTab === CHART_TAB_BUTTON.BUILDING && (
							<Stack direction={"row"} sx={{ mt: 2, height: "99%" }}>
								<ChartBuildingList
									page={PAGE_TYPE.COMMUNITY_TRANSFER}
									data={chartData?.filterPatients || []}
									relation={filters?.relation}
									getFieldNames={getFieldNames}
									chartData={chartData}
									censusByFacility={censusByFacility}
									filterSelected={filters.filterSelected}
									filter={filters}
									buildingElementRef={buildingElementRef}
									dataComparison={chartDataComparison?.filterPatients || []}
									relationComparison={filtersComparison?.relation}
									chartDataComparison={chartDataComparison}
									censusByFacilityComparison={censusByFacilityComparison}
									filterSelectedComparison={filtersComparison?.filterSelected}
									filterComparison={filtersComparison}
									isTotalCard={filters?.isTotalCard}
									filterListData={filters?.filterListData}
									typeCard={filters?.type}
								/>
							</Stack>
						)}
						{selectedTab === CHART_TAB_BUTTON.TABLE && (
							<Stack direction={"row"} sx={{ mt: 2, height: "99%" }}>
								<ChartTableList
									data={chartData?.filterPatients || []}
									relation={filters?.relation}
									getFieldNames={getFieldNames}
									filters={filters}
									tableElementRef={tableElementRef}
									pageType={DASHBOARD_FILTER_TYPE.COMMUNITY_TRANSFER}
									questions={questions}
									searchTerm={debouncedSearchTerm}
									handleHighlightedCount={handleHighlightedCount}
									highlightedCountDefaultValue={highlightedCountDefaultValue}
									filterType={filterType}
									selectedHighlightedColumns={selectedHighlightedColumns}
									orderData={{
										orderBy,
										setOrderBy,
										order,
										setOrder
									}}
									activeFacilities={activeFacilities}
									isNotCombineTab={filters?.question?.customTab?.type !== CUSTOM_TAB_TYPE.combineTab}
								/>
							</Stack>
						)}
						{selectedTab === CHART_TAB_BUTTON.CHART && (
							<Stack direction={"row"} sx={{ mt: 2, height: "90%" }}>
								<CardDetailChart
									totalText="Total Community Transfers"
									data={chartData?.filterData || []}
									filters={filters}
									handleChartLabelType={handleChartLabelType}
									censusAverage={censusAverage}
									total={chartData?.filterPatients?.length || 0}
									isChartTotalButton={isShowPercentage}
									setQueryFilters={setQueryFilters}
									setQueryFiltersComparison={setQueryFiltersComparison}
									queryFilters={queryFilters}
									handleClickFilter={handleClickFilter}
									selectedFilter={selectedFilter}
									reloadChartData={reloadChartData}
									selectedFacility={selectedFacility}
									dbData={dbData}
								/>
							</Stack>
						)}
					</NoteContainer>
				)}
				<ChartTabLoader loading={loading} />
			</Grid>
		</ChartDialogContainer>
	);
}
