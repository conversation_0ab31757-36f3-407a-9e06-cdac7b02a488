require("dotenv").config();
const express = require("express");

// Increase the maximum number of listeners to prevent memory leak warnings
// BullMQ workers add many process listeners, so we need a high limit
process.setMaxListeners(100);

// Remove all existing process listeners to prevent accumulation
process.removeAllListeners('exit');
process.removeAllListeners('SIGINT');
process.removeAllListeners('SIGTERM');
process.removeAllListeners('SIGHUP');
process.removeAllListeners('uncaughtException');
process.removeAllListeners('unhandledRejection');

// Import configuration modules
const { connectDatabase } = require("../config/database");
const { loadModels } = require("../config/models");
const { initSentry, applySentryMiddleware } = require("../config/sentry");
const { applyMiddleware } = require("../config/middleware");
const { setupRoutes } = require("../config/routes");
const { setupProductionConfig } = require("../config/production");
const { startServer } = require("../config/server");

// Import other required modules
const puppeteerTest = require("./puppeteer-test");


// require("../api/utilis/common");

/**
 * Global error handlers to prevent server crashes from Redis connection issues
 */
const setupGlobalErrorHandlers = () => {
    // Handle unhandled promise rejections (like Redis connection failures)
    process.on('unhandledRejection', (reason) => {
        if (reason && (reason.message?.includes('Redis') || reason.message?.includes('ECONNREFUSED') || reason.code === 'ECONNREFUSED')) {
            console.warn('⚠️  Redis connection issue (unhandled rejection):', reason.message || reason);
            console.warn('🔧 Server will continue running - Redis features may be limited');
        } else {
            console.warn('⚠️  Unhandled Promise Rejection:', reason);
            console.warn('🔧 Server will continue running - check logs for details');
        }
        // Don't exit the process - just log the warning
    });

    // Handle uncaught exceptions
    process.on('uncaughtException', (error) => {
        if (error.message?.includes('Redis') || error.message?.includes('ECONNREFUSED') || error.code === 'ECONNREFUSED') {
            console.warn('⚠️  Redis connection error (uncaught exception):', error.message);
            console.warn('🔧 Server will continue without Redis features');
            // Don't exit for Redis errors
        } else {
            console.error('❌ Critical uncaught exception:', error);
            console.error('💥 Server will exit due to critical error');
            process.exit(1);
        }
    });

    console.log('🛡️  Global error handlers configured - Redis failures won\'t crash server');
};

const initializeServer = async () => {
    try {
        // Setup global error handlers first
        setupGlobalErrorHandlers();

        console.log("\n🚀 ===== ADT TECH BACKEND STARTUP =====");
        console.log("📅 Starting at:", new Date().toISOString());
        console.log("🌍 Environment:", process.env.NODE_ENV || 'development');
        console.log("🔧 Node Version:", process.version);
        console.log("=======================================\n");

        // Initialize Express app
        console.log("⚡ Initializing Express application...");
        const app = express();
        console.log("✅ Express app created successfully");

        // Connect to database
        console.log("\n📊 Connecting to database...");
        await connectDatabase();

        // Load all models
        console.log("📋 Loading database models...");
        loadModels();

        // Initialize Sentry
        console.log("🔍 Initializing error tracking...");
        initSentry(app);
        applySentryMiddleware(app);

        // Apply middleware
        console.log("🛡️  Applying middleware...");
        applyMiddleware(app);

        // Setup routes
        console.log("🛣️  Setting up API routes...");
        setupRoutes(app);

        // Setup production-specific configurations
        console.log("⚙️  Configuring production features...");
        await setupProductionConfig(app);

        // Initialize puppeteer test
        console.log("🎭 Initializing browser automation...");
        puppeteerTest();

        // Start server
        console.log("🌐 Starting HTTP server...");
        startServer(app);

        console.log("\n🎉 ===== SERVER READY =====");
        console.log("✨ All systems operational!");
        console.log("🔗 Ready to accept connections");        
    } catch (error) {
        console.error("\n❌ ===== STARTUP FAILED =====");
        console.error("💥 Error during server initialization:");
        console.error(error);        
        process.exit(1);
    }
};

// Start the server
initializeServer();

